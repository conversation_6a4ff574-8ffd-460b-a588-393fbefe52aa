<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Calcul Automatique Nombre de Jours</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #6f42c1;
        }
        
        .form-group {
            margin-bottom: 20px;
            padding: 12px;
            border-radius: 8px;
            border-width: 1px;
            border-style: solid;
        }
        
        .form-group.client {
            background: #f0fff0;
            border-color: #28a745;
        }
        
        .form-group.periode {
            background: #fff8f0;
            border-color: #ffc107;
        }
        
        .form-group.jours {
            background: #f0fff4;
            border-color: #28a745;
        }
        
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .form-label.client {
            color: #28a745;
        }
        
        .form-label.periode {
            color: #ffc107;
        }
        
        .form-label.jours {
            color: #6f42c1;
        }
        
        .form-select, .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            background: white;
            transition: all 0.3s;
        }
        
        .form-input.auto-filled {
            background: #f0fff4;
            border-color: #28a745;
            border-width: 2px;
        }
        
        .auto-filled-info {
            font-size: 12px;
            color: #28a745;
            font-style: italic;
            margin-top: 5px;
            text-align: center;
        }
        
        .calculation-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .example-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .example-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .example-card.calculation {
            border-color: #6f42c1;
            background: #f8f6ff;
        }
        
        .example-card h4 {
            color: #6f42c1;
            margin-bottom: 15px;
        }
        
        .calculation-step {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        
        .calculation-step:last-child {
            border-bottom: none;
            font-weight: bold;
            color: #28a745;
        }
        
        .formula-explanation {
            background: #fff3cd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }
        
        .formula-explanation h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .formula-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .formula-step {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid #ffc107;
        }
        
        .step-number {
            font-weight: bold;
            color: #ffc107;
            margin-bottom: 8px;
        }
        
        .step-description {
            font-size: 0.9em;
            color: #666;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-purple {
            background: #6f42c1;
            color: white;
        }
        
        .btn-purple:hover {
            background: #5a32a3;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 Calcul Automatique Nombre de Jours</h1>
            <p>Démonstration du calcul basé sur la différence entre périodes</p>
        </div>
        
        <div class="content">
            <div class="demo-form">
                <h3 style="color: #6f42c1; margin-bottom: 20px;">📱 Simulation du Calcul Automatique</h3>
                
                <div class="form-group client">
                    <label class="form-label client">👤 Client Sélectionné</label>
                    <select id="clientSelect" class="form-select" onchange="updateClient()">
                        <option value="">Sélectionner un client</option>
                        <option value="1">Benali Fatima</option>
                        <option value="2">Alami Mohammed</option>
                        <option value="4">Benjelloun Youssef</option>
                        <option value="5">Lahlou Khadija</option>
                    </select>
                    <div id="clientInfo" class="auto-filled-info" style="display: none;">
                        📅 Dernière période: 2024-11 (Novembre 2024)
                    </div>
                </div>
                
                <div class="form-group periode">
                    <label class="form-label periode">📅 Période Actuelle</label>
                    <input 
                        type="text" 
                        id="periodeInput" 
                        class="form-input" 
                        placeholder="2024-12"
                        maxlength="7"
                        oninput="calculateDays()"
                    >
                </div>
                
                <div class="form-group jours">
                    <label class="form-label jours">📅 Nombre de jours</label>
                    <input 
                        type="text" 
                        id="joursInput" 
                        class="form-input" 
                        placeholder="Sélectionnez client et période"
                        readonly
                    >
                    <div id="calculationInfo" class="auto-filled-info" style="display: none;">
                        ✅ Calculé automatiquement : [période] - dernière période
                    </div>
                </div>
            </div>
            
            <div class="calculation-examples">
                <div class="example-card calculation">
                    <h4>🧮 Exemple : Décembre 2024</h4>
                    <div class="calculation-step">📅 Période actuelle : 2024-12</div>
                    <div class="calculation-step">📅 Dernière période : 2024-11</div>
                    <div class="calculation-step">📊 Date fin actuelle : 31 déc 2024</div>
                    <div class="calculation-step">📊 Date fin dernière : 30 nov 2024</div>
                    <div class="calculation-step">✅ Résultat : 31 jours</div>
                </div>
                
                <div class="example-card calculation">
                    <h4>🧮 Exemple : Janvier 2025</h4>
                    <div class="calculation-step">📅 Période actuelle : 2025-01</div>
                    <div class="calculation-step">📅 Dernière période : 2024-11</div>
                    <div class="calculation-step">📊 Date fin actuelle : 31 jan 2025</div>
                    <div class="calculation-step">📊 Date fin dernière : 30 nov 2024</div>
                    <div class="calculation-step">✅ Résultat : 62 jours</div>
                </div>
                
                <div class="example-card calculation">
                    <h4>🧮 Exemple : Février 2025</h4>
                    <div class="calculation-step">📅 Période actuelle : 2025-02</div>
                    <div class="calculation-step">📅 Dernière période : 2024-11</div>
                    <div class="calculation-step">📊 Date fin actuelle : 28 fév 2025</div>
                    <div class="calculation-step">📊 Date fin dernière : 30 nov 2024</div>
                    <div class="calculation-step">✅ Résultat : 90 jours</div>
                </div>
            </div>
            
            <div class="formula-explanation">
                <h3>🧮 Formule de Calcul</h3>
                <div class="formula-steps">
                    <div class="formula-step">
                        <div class="step-number">Étape 1</div>
                        <div class="step-description">Récupérer la dernière période du client (ex: 2024-11)</div>
                    </div>
                    
                    <div class="formula-step">
                        <div class="step-number">Étape 2</div>
                        <div class="step-description">Obtenir la période actuelle saisie (ex: 2024-12)</div>
                    </div>
                    
                    <div class="formula-step">
                        <div class="step-number">Étape 3</div>
                        <div class="step-description">Convertir en dates de fin de mois</div>
                    </div>
                    
                    <div class="formula-step">
                        <div class="step-number">Étape 4</div>
                        <div class="step-description">Calculer la différence en jours</div>
                    </div>
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-primary" target="_blank">
                    📱 Tester l'Application Réelle
                </a>
                <button class="btn btn-purple" onclick="simulateCalculation()">
                    🧪 Simuler le Calcul
                </button>
            </div>
            
            <div style="background: #d4edda; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #28a745;">
                <h3 style="color: #155724; margin-bottom: 15px;">✅ Calcul Automatique Nombre de Jours</h3>
                <ul style="color: #155724; line-height: 1.6;">
                    <li>✅ <strong>Calcul automatique :</strong> Basé sur la différence entre périodes</li>
                    <li>✅ <strong>Déclenchement :</strong> Quand client ET période sont sélectionnés</li>
                    <li>✅ <strong>Formule :</strong> Date fin période actuelle - Date fin dernière période</li>
                    <li>✅ <strong>Indication visuelle :</strong> Fond vert et message informatif</li>
                    <li>✅ <strong>Éditable :</strong> Le technicien peut modifier si nécessaire</li>
                    <li>✅ <strong>Validation :</strong> Résultat positif et raisonnable (≤ 365 jours)</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // Données des clients avec leurs dernières périodes
        const clientsData = {
            '1': { nom: 'Benali Fatima', dernierePeriode: '2024-11' },
            '2': { nom: 'Alami Mohammed', dernierePeriode: '2024-11' },
            '4': { nom: 'Benjelloun Youssef', dernierePeriode: '2024-11' },
            '5': { nom: 'Lahlou Khadija', dernierePeriode: '2024-11' }
        };
        
        function updateClient() {
            const clientId = document.getElementById('clientSelect').value;
            const clientInfo = document.getElementById('clientInfo');
            const joursInput = document.getElementById('joursInput');
            const calculationInfo = document.getElementById('calculationInfo');
            
            if (clientId && clientsData[clientId]) {
                clientInfo.style.display = 'block';
                calculateDays();
            } else {
                clientInfo.style.display = 'none';
                joursInput.value = '';
                joursInput.placeholder = 'Sélectionnez client et période';
                joursInput.classList.remove('auto-filled');
                calculationInfo.style.display = 'none';
            }
        }
        
        function calculateDays() {
            const clientId = document.getElementById('clientSelect').value;
            const periode = document.getElementById('periodeInput').value;
            const joursInput = document.getElementById('joursInput');
            const calculationInfo = document.getElementById('calculationInfo');
            
            if (!clientId || !periode || periode.length !== 7) {
                joursInput.value = '';
                joursInput.placeholder = clientId ? 'Saisissez une période (YYYY-MM)' : 'Sélectionnez client et période';
                joursInput.classList.remove('auto-filled');
                calculationInfo.style.display = 'none';
                return;
            }
            
            const client = clientsData[clientId];
            if (!client) return;
            
            // Simuler le calcul
            joursInput.placeholder = 'Calcul automatique en cours...';
            
            setTimeout(() => {
                const nombreJours = calculerNombreJours(periode, client.dernierePeriode);
                
                if (nombreJours !== null) {
                    joursInput.value = nombreJours;
                    joursInput.classList.add('auto-filled');
                    calculationInfo.textContent = `✅ Calculé automatiquement : ${periode} - ${client.dernierePeriode}`;
                    calculationInfo.style.display = 'block';
                } else {
                    joursInput.value = '30';
                    joursInput.placeholder = 'Erreur de calcul, valeur par défaut';
                    joursInput.classList.remove('auto-filled');
                    calculationInfo.style.display = 'none';
                }
            }, 500);
        }
        
        function calculerNombreJours(periodeActuelle, dernierePeriode) {
            try {
                const [anneeActuelle, moisActuel] = periodeActuelle.split('-').map(Number);
                const [anneeDerniere, moisDernier] = dernierePeriode.split('-').map(Number);
                
                // Créer des dates de fin de mois
                const dateActuelle = new Date(anneeActuelle, moisActuel, 0);
                const dateDerniere = new Date(anneeDerniere, moisDernier, 0);
                
                // Calculer la différence en jours
                const differenceMs = dateActuelle.getTime() - dateDerniere.getTime();
                const differenceJours = Math.round(differenceMs / (1000 * 60 * 60 * 24));
                
                // Validation
                if (differenceJours <= 0 || differenceJours > 365) {
                    return null;
                }
                
                return differenceJours;
            } catch (error) {
                return null;
            }
        }
        
        function simulateCalculation() {
            // Réinitialiser
            document.getElementById('clientSelect').value = '';
            document.getElementById('periodeInput').value = '';
            document.getElementById('joursInput').value = '';
            
            setTimeout(() => {
                document.getElementById('clientSelect').value = '1';
                updateClient();
                
                setTimeout(() => {
                    document.getElementById('periodeInput').value = '2024-12';
                    calculateDays();
                }, 1000);
            }, 500);
        }
        
        // Exemples de périodes pour test rapide
        function setPeriode(periode) {
            document.getElementById('periodeInput').value = periode;
            calculateDays();
        }
        
        // Ajouter des boutons pour test rapide
        window.addEventListener('load', function() {
            const buttonsDiv = document.querySelector('.buttons');
            
            const periodes = ['2024-12', '2025-01', '2025-02', '2025-03'];
            
            periodes.forEach(periode => {
                const button = document.createElement('button');
                button.className = 'btn btn-purple';
                button.textContent = `📅 ${periode}`;
                button.onclick = () => setPeriode(periode);
                buttonsDiv.appendChild(button);
            });
        });
    </script>
</body>
</html>
