{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"key\", \"routeNames\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { CommonActions } from '@react-navigation/routers';\nimport * as React from 'react';\nimport checkDuplicateRouteNames from \"./checkDuplicateRouteNames\";\nimport checkSerializable from \"./checkSerializable\";\nimport { NOT_INITIALIZED_ERROR } from \"./createNavigationContainerRef\";\nimport EnsureSingleNavigator from \"./EnsureSingleNavigator\";\nimport findFocusedRoute from \"./findFocusedRoute\";\nimport NavigationBuilderContext from \"./NavigationBuilderContext\";\nimport NavigationContainerRefContext from \"./NavigationContainerRefContext\";\nimport NavigationContext from \"./NavigationContext\";\nimport NavigationRouteContext from \"./NavigationRouteContext\";\nimport NavigationStateContext from \"./NavigationStateContext\";\nimport UnhandledActionContext from \"./UnhandledActionContext\";\nimport useChildListeners from \"./useChildListeners\";\nimport useEventEmitter from \"./useEventEmitter\";\nimport useKeyedChildListeners from \"./useKeyedChildListeners\";\nimport useOptionsGetters from \"./useOptionsGetters\";\nimport { ScheduleUpdateContext } from \"./useScheduleUpdate\";\nimport useSyncState from \"./useSyncState\";\nvar serializableWarnings = [];\nvar duplicateNameWarnings = [];\nvar _getPartialState = function getPartialState(state) {\n  if (state === undefined) {\n    return;\n  }\n  var key = state.key,\n    routeNames = state.routeNames,\n    partialState = _objectWithoutProperties(state, _excluded);\n  return _objectSpread(_objectSpread({}, partialState), {}, {\n    stale: true,\n    routes: state.routes.map(function (route) {\n      if (route.state === undefined) {\n        return route;\n      }\n      return _objectSpread(_objectSpread({}, route), {}, {\n        state: _getPartialState(route.state)\n      });\n    })\n  });\n};\nvar BaseNavigationContainer = React.forwardRef(function BaseNavigationContainer(_ref, ref) {\n  var initialState = _ref.initialState,\n    onStateChange = _ref.onStateChange,\n    onUnhandledAction = _ref.onUnhandledAction,\n    independent = _ref.independent,\n    children = _ref.children;\n  var parent = React.useContext(NavigationStateContext);\n  if (!parent.isDefault && !independent) {\n    throw new Error(\"Looks like you have nested a 'NavigationContainer' inside another. Normally you need only one container at the root of the app, so this was probably an error. If this was intentional, pass 'independent={true}' explicitly. Note that this will make the child navigators disconnected from the parent and you won't be able to navigate between them.\");\n  }\n  var _useSyncState = useSyncState(function () {\n      return _getPartialState(initialState == null ? undefined : initialState);\n    }),\n    _useSyncState2 = _slicedToArray(_useSyncState, 5),\n    state = _useSyncState2[0],\n    getState = _useSyncState2[1],\n    setState = _useSyncState2[2],\n    scheduleUpdate = _useSyncState2[3],\n    flushUpdates = _useSyncState2[4];\n  var isFirstMountRef = React.useRef(true);\n  var navigatorKeyRef = React.useRef();\n  var getKey = React.useCallback(function () {\n    return navigatorKeyRef.current;\n  }, []);\n  var setKey = React.useCallback(function (key) {\n    navigatorKeyRef.current = key;\n  }, []);\n  var _useChildListeners = useChildListeners(),\n    listeners = _useChildListeners.listeners,\n    addListener = _useChildListeners.addListener;\n  var _useKeyedChildListene = useKeyedChildListeners(),\n    keyedListeners = _useKeyedChildListene.keyedListeners,\n    addKeyedListener = _useKeyedChildListene.addKeyedListener;\n  var dispatch = React.useCallback(function (action) {\n    if (listeners.focus[0] == null) {\n      console.error(NOT_INITIALIZED_ERROR);\n    } else {\n      listeners.focus[0](function (navigation) {\n        return navigation.dispatch(action);\n      });\n    }\n  }, [listeners.focus]);\n  var canGoBack = React.useCallback(function () {\n    if (listeners.focus[0] == null) {\n      return false;\n    }\n    var _listeners$focus$ = listeners.focus[0](function (navigation) {\n        return navigation.canGoBack();\n      }),\n      result = _listeners$focus$.result,\n      handled = _listeners$focus$.handled;\n    if (handled) {\n      return result;\n    } else {\n      return false;\n    }\n  }, [listeners.focus]);\n  var resetRoot = React.useCallback(function (state) {\n    var _ref2;\n    var _keyedListeners$getSt, _keyedListeners$getSt2;\n    var target = (_ref2 = state === null || state === void 0 ? void 0 : state.key) != null ? _ref2 : (_keyedListeners$getSt = (_keyedListeners$getSt2 = keyedListeners.getState).root) === null || _keyedListeners$getSt === void 0 ? void 0 : _keyedListeners$getSt.call(_keyedListeners$getSt2).key;\n    if (target == null) {\n      console.error(NOT_INITIALIZED_ERROR);\n    } else {\n      listeners.focus[0](function (navigation) {\n        return navigation.dispatch(_objectSpread(_objectSpread({}, CommonActions.reset(state)), {}, {\n          target: target\n        }));\n      });\n    }\n  }, [keyedListeners.getState, listeners.focus]);\n  var getRootState = React.useCallback(function () {\n    var _keyedListeners$getSt3, _keyedListeners$getSt4;\n    return (_keyedListeners$getSt3 = (_keyedListeners$getSt4 = keyedListeners.getState).root) === null || _keyedListeners$getSt3 === void 0 ? void 0 : _keyedListeners$getSt3.call(_keyedListeners$getSt4);\n  }, [keyedListeners.getState]);\n  var getCurrentRoute = React.useCallback(function () {\n    var state = getRootState();\n    if (state == null) {\n      return undefined;\n    }\n    var route = findFocusedRoute(state);\n    return route;\n  }, [getRootState]);\n  var emitter = useEventEmitter();\n  var _useOptionsGetters = useOptionsGetters({}),\n    addOptionsGetter = _useOptionsGetters.addOptionsGetter,\n    getCurrentOptions = _useOptionsGetters.getCurrentOptions;\n  var navigation = React.useMemo(function () {\n    return _objectSpread(_objectSpread(_objectSpread({}, Object.keys(CommonActions).reduce(function (acc, name) {\n      acc[name] = function () {\n        return (dispatch(CommonActions[name].apply(CommonActions, arguments))\n        );\n      };\n      return acc;\n    }, {})), emitter.create('root')), {}, {\n      dispatch: dispatch,\n      resetRoot: resetRoot,\n      isFocused: function isFocused() {\n        return true;\n      },\n      canGoBack: canGoBack,\n      getParent: function getParent() {\n        return undefined;\n      },\n      getState: function getState() {\n        return stateRef.current;\n      },\n      getRootState: getRootState,\n      getCurrentRoute: getCurrentRoute,\n      getCurrentOptions: getCurrentOptions,\n      isReady: function isReady() {\n        return listeners.focus[0] != null;\n      },\n      setOptions: function setOptions() {\n        throw new Error('Cannot call setOptions outside a screen');\n      }\n    });\n  }, [canGoBack, dispatch, emitter, getCurrentOptions, getCurrentRoute, getRootState, listeners.focus, resetRoot]);\n  React.useImperativeHandle(ref, function () {\n    return navigation;\n  }, [navigation]);\n  var onDispatchAction = React.useCallback(function (action, noop) {\n    emitter.emit({\n      type: '__unsafe_action__',\n      data: {\n        action: action,\n        noop: noop,\n        stack: stackRef.current\n      }\n    });\n  }, [emitter]);\n  var lastEmittedOptionsRef = React.useRef();\n  var onOptionsChange = React.useCallback(function (options) {\n    if (lastEmittedOptionsRef.current === options) {\n      return;\n    }\n    lastEmittedOptionsRef.current = options;\n    emitter.emit({\n      type: 'options',\n      data: {\n        options: options\n      }\n    });\n  }, [emitter]);\n  var stackRef = React.useRef();\n  var builderContext = React.useMemo(function () {\n    return {\n      addListener: addListener,\n      addKeyedListener: addKeyedListener,\n      onDispatchAction: onDispatchAction,\n      onOptionsChange: onOptionsChange,\n      stackRef: stackRef\n    };\n  }, [addListener, addKeyedListener, onDispatchAction, onOptionsChange]);\n  var scheduleContext = React.useMemo(function () {\n    return {\n      scheduleUpdate: scheduleUpdate,\n      flushUpdates: flushUpdates\n    };\n  }, [scheduleUpdate, flushUpdates]);\n  var isInitialRef = React.useRef(true);\n  var getIsInitial = React.useCallback(function () {\n    return isInitialRef.current;\n  }, []);\n  var context = React.useMemo(function () {\n    return {\n      state: state,\n      getState: getState,\n      setState: setState,\n      getKey: getKey,\n      setKey: setKey,\n      getIsInitial: getIsInitial,\n      addOptionsGetter: addOptionsGetter\n    };\n  }, [state, getState, setState, getKey, setKey, getIsInitial, addOptionsGetter]);\n  var onStateChangeRef = React.useRef(onStateChange);\n  var stateRef = React.useRef(state);\n  React.useEffect(function () {\n    isInitialRef.current = false;\n    onStateChangeRef.current = onStateChange;\n    stateRef.current = state;\n  });\n  React.useEffect(function () {\n    var hydratedState = getRootState();\n    if (process.env.NODE_ENV !== 'production') {\n      if (hydratedState !== undefined) {\n        var serializableResult = checkSerializable(hydratedState);\n        if (!serializableResult.serializable) {\n          var location = serializableResult.location,\n            reason = serializableResult.reason;\n          var path = '';\n          var pointer = hydratedState;\n          var params = false;\n          for (var i = 0; i < location.length; i++) {\n            var curr = location[i];\n            var prev = location[i - 1];\n            pointer = pointer[curr];\n            if (!params && curr === 'state') {\n              continue;\n            } else if (!params && curr === 'routes') {\n              if (path) {\n                path += ' > ';\n              }\n            } else if (!params && typeof curr === 'number' && prev === 'routes') {\n              var _pointer;\n              path += (_pointer = pointer) === null || _pointer === void 0 ? void 0 : _pointer.name;\n            } else if (!params) {\n              path += ` > ${curr}`;\n              params = true;\n            } else {\n              if (typeof curr === 'number' || /^[0-9]+$/.test(curr)) {\n                path += `[${curr}]`;\n              } else if (/^[a-z$_]+$/i.test(curr)) {\n                path += `.${curr}`;\n              } else {\n                path += `[${JSON.stringify(curr)}]`;\n              }\n            }\n          }\n          var message = `Non-serializable values were found in the navigation state. Check:\\n\\n${path} (${reason})\\n\\nThis can break usage such as persisting and restoring state. This might happen if you passed non-serializable values such as function, class instances etc. in params. If you need to use components with callbacks in your options, you can use 'navigation.setOptions' instead. See https://reactnavigation.org/docs/troubleshooting#i-get-the-warning-non-serializable-values-were-found-in-the-navigation-state for more details.`;\n          if (!serializableWarnings.includes(message)) {\n            serializableWarnings.push(message);\n            console.warn(message);\n          }\n        }\n        var duplicateRouteNamesResult = checkDuplicateRouteNames(hydratedState);\n        if (duplicateRouteNamesResult.length) {\n          var _message = `Found screens with the same name nested inside one another. Check:\\n${duplicateRouteNamesResult.map(function (locations) {\n            return `\\n${locations.join(', ')}`;\n          })}\\n\\nThis can cause confusing behavior during navigation. Consider using unique names for each screen instead.`;\n          if (!duplicateNameWarnings.includes(_message)) {\n            duplicateNameWarnings.push(_message);\n            console.warn(_message);\n          }\n        }\n      }\n    }\n    emitter.emit({\n      type: 'state',\n      data: {\n        state: state\n      }\n    });\n    if (!isFirstMountRef.current && onStateChangeRef.current) {\n      onStateChangeRef.current(hydratedState);\n    }\n    isFirstMountRef.current = false;\n  }, [getRootState, emitter, state]);\n  var defaultOnUnhandledAction = React.useCallback(function (action) {\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n    var payload = action.payload;\n    var message = `The action '${action.type}'${payload ? ` with payload ${JSON.stringify(action.payload)}` : ''} was not handled by any navigator.`;\n    switch (action.type) {\n      case 'NAVIGATE':\n      case 'PUSH':\n      case 'REPLACE':\n      case 'JUMP_TO':\n        if (payload !== null && payload !== void 0 && payload.name) {\n          message += `\\n\\nDo you have a screen named '${payload.name}'?\\n\\nIf you're trying to navigate to a screen in a nested navigator, see https://reactnavigation.org/docs/nesting-navigators#navigating-to-a-screen-in-a-nested-navigator.`;\n        } else {\n          message += `\\n\\nYou need to pass the name of the screen to navigate to.\\n\\nSee https://reactnavigation.org/docs/navigation-actions for usage.`;\n        }\n        break;\n      case 'GO_BACK':\n      case 'POP':\n      case 'POP_TO_TOP':\n        message += `\\n\\nIs there any screen to go back to?`;\n        break;\n      case 'OPEN_DRAWER':\n      case 'CLOSE_DRAWER':\n      case 'TOGGLE_DRAWER':\n        message += `\\n\\nIs your screen inside a Drawer navigator?`;\n        break;\n    }\n    message += `\\n\\nThis is a development-only warning and won't be shown in production.`;\n    console.error(message);\n  }, []);\n  var element = React.createElement(NavigationContainerRefContext.Provider, {\n    value: navigation\n  }, React.createElement(ScheduleUpdateContext.Provider, {\n    value: scheduleContext\n  }, React.createElement(NavigationBuilderContext.Provider, {\n    value: builderContext\n  }, React.createElement(NavigationStateContext.Provider, {\n    value: context\n  }, React.createElement(UnhandledActionContext.Provider, {\n    value: onUnhandledAction != null ? onUnhandledAction : defaultOnUnhandledAction\n  }, React.createElement(EnsureSingleNavigator, null, children))))));\n  if (independent) {\n    element = React.createElement(NavigationRouteContext.Provider, {\n      value: undefined\n    }, React.createElement(NavigationContext.Provider, {\n      value: undefined\n    }, element));\n  }\n  return element;\n});\nexport default BaseNavigationContainer;", "map": {"version": 3, "names": ["CommonActions", "React", "checkDuplicateRouteNames", "checkSerializable", "NOT_INITIALIZED_ERROR", "EnsureSingleNavigator", "findFocusedRoute", "NavigationBuilderContext", "NavigationContainerRefContext", "NavigationContext", "NavigationRouteContext", "NavigationStateContext", "UnhandledActionContext", "useChildListeners", "useEventEmitter", "useKeyedChildListeners", "useOptionsGetters", "ScheduleUpdateContext", "useSyncState", "serializableWarnings", "duplicateName<PERSON><PERSON>nings", "getPartialState", "state", "undefined", "key", "routeNames", "partialState", "_objectWithoutProperties", "_excluded", "_objectSpread", "stale", "routes", "map", "route", "BaseNavigationContainer", "forwardRef", "_ref", "ref", "initialState", "onStateChange", "onUnhandledAction", "independent", "children", "parent", "useContext", "isDefault", "Error", "_useSyncState", "_useSyncState2", "_slicedToArray", "getState", "setState", "scheduleUpdate", "flushUpdates", "isFirstMountRef", "useRef", "navigator<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "useCallback", "current", "<PERSON><PERSON><PERSON>", "_useChildListeners", "listeners", "addListener", "_useKeyedChildListene", "keyedListeners", "addKeyedListener", "dispatch", "action", "focus", "console", "error", "navigation", "canGoBack", "_listeners$focus$", "result", "handled", "resetRoot", "_ref2", "_keyedListeners$getSt", "_keyedListeners$getSt2", "target", "root", "call", "reset", "getRootState", "_keyedListeners$getSt3", "_keyedListeners$getSt4", "getCurrentRoute", "emitter", "_useOptionsGetters", "addOptionsGetter", "getCurrentOptions", "useMemo", "Object", "keys", "reduce", "acc", "name", "apply", "arguments", "create", "isFocused", "getParent", "stateRef", "isReady", "setOptions", "useImperativeHandle", "onDispatchAction", "noop", "emit", "type", "data", "stack", "stackRef", "lastEmittedOptionsRef", "onOptionsChange", "options", "builderContext", "scheduleContext", "isInitialRef", "getIsInitial", "context", "onStateChangeRef", "useEffect", "hydratedState", "process", "env", "NODE_ENV", "serializableResult", "serializable", "location", "reason", "path", "pointer", "params", "i", "length", "curr", "prev", "_pointer", "test", "JSON", "stringify", "message", "includes", "push", "warn", "duplicateRouteNamesResult", "locations", "join", "defaultOnUnhandledAction", "payload", "element", "createElement", "Provider", "value"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\core\\src\\BaseNavigationContainer.tsx"], "sourcesContent": ["import {\n  CommonActions,\n  InitialState,\n  NavigationAction,\n  NavigationState,\n  ParamListBase,\n  PartialState,\n  Route,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport checkDuplicateRouteNames from './checkDuplicateRouteNames';\nimport checkSerializable from './checkSerializable';\nimport { NOT_INITIALIZED_ERROR } from './createNavigationContainerRef';\nimport EnsureSingleNavigator from './EnsureSingleNavigator';\nimport findFocusedRoute from './findFocusedRoute';\nimport NavigationBuilderContext from './NavigationBuilderContext';\nimport NavigationContainerRefContext from './NavigationContainerRefContext';\nimport NavigationContext from './NavigationContext';\nimport NavigationRouteContext from './NavigationRouteContext';\nimport NavigationStateContext from './NavigationStateContext';\nimport type {\n  NavigationContainerEventMap,\n  NavigationContainerProps,\n  NavigationContainerRef,\n} from './types';\nimport UnhandledActionContext from './UnhandledActionContext';\nimport useChildListeners from './useChildListeners';\nimport useEventEmitter from './useEventEmitter';\nimport useKeyedChildListeners from './useKeyedChildListeners';\nimport useOptionsGetters from './useOptionsGetters';\nimport { ScheduleUpdateContext } from './useScheduleUpdate';\nimport useSyncState from './useSyncState';\n\ntype State = NavigationState | PartialState<NavigationState> | undefined;\n\nconst serializableWarnings: string[] = [];\nconst duplicateNameWarnings: string[] = [];\n\n/**\n * Remove `key` and `routeNames` from the state objects recursively to get partial state.\n *\n * @param state Initial state object.\n */\nconst getPartialState = (\n  state: InitialState | undefined\n): PartialState<NavigationState> | undefined => {\n  if (state === undefined) {\n    return;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const { key, routeNames, ...partialState } = state;\n\n  return {\n    ...partialState,\n    stale: true,\n    routes: state.routes.map((route) => {\n      if (route.state === undefined) {\n        return route as Route<string> & {\n          state?: PartialState<NavigationState>;\n        };\n      }\n\n      return { ...route, state: getPartialState(route.state) };\n    }),\n  };\n};\n\n/**\n * Container component which holds the navigation state.\n * This should be rendered at the root wrapping the whole app.\n *\n * @param props.initialState Initial state object for the navigation tree.\n * @param props.onStateChange Callback which is called with the latest navigation state when it changes.\n * @param props.children Child elements to render the content.\n * @param props.ref Ref object which refers to the navigation object containing helper methods.\n */\nconst BaseNavigationContainer = React.forwardRef(\n  function BaseNavigationContainer(\n    {\n      initialState,\n      onStateChange,\n      onUnhandledAction,\n      independent,\n      children,\n    }: NavigationContainerProps,\n    ref?: React.Ref<NavigationContainerRef<ParamListBase>>\n  ) {\n    const parent = React.useContext(NavigationStateContext);\n\n    if (!parent.isDefault && !independent) {\n      throw new Error(\n        \"Looks like you have nested a 'NavigationContainer' inside another. Normally you need only one container at the root of the app, so this was probably an error. If this was intentional, pass 'independent={true}' explicitly. Note that this will make the child navigators disconnected from the parent and you won't be able to navigate between them.\"\n      );\n    }\n\n    const [state, getState, setState, scheduleUpdate, flushUpdates] =\n      useSyncState<State>(() =>\n        getPartialState(initialState == null ? undefined : initialState)\n      );\n\n    const isFirstMountRef = React.useRef<boolean>(true);\n\n    const navigatorKeyRef = React.useRef<string | undefined>();\n\n    const getKey = React.useCallback(() => navigatorKeyRef.current, []);\n\n    const setKey = React.useCallback((key: string) => {\n      navigatorKeyRef.current = key;\n    }, []);\n\n    const { listeners, addListener } = useChildListeners();\n\n    const { keyedListeners, addKeyedListener } = useKeyedChildListeners();\n\n    const dispatch = React.useCallback(\n      (\n        action:\n          | NavigationAction\n          | ((state: NavigationState) => NavigationAction)\n      ) => {\n        if (listeners.focus[0] == null) {\n          console.error(NOT_INITIALIZED_ERROR);\n        } else {\n          listeners.focus[0]((navigation) => navigation.dispatch(action));\n        }\n      },\n      [listeners.focus]\n    );\n\n    const canGoBack = React.useCallback(() => {\n      if (listeners.focus[0] == null) {\n        return false;\n      }\n\n      const { result, handled } = listeners.focus[0]((navigation) =>\n        navigation.canGoBack()\n      );\n\n      if (handled) {\n        return result;\n      } else {\n        return false;\n      }\n    }, [listeners.focus]);\n\n    const resetRoot = React.useCallback(\n      (state?: PartialState<NavigationState> | NavigationState) => {\n        const target = state?.key ?? keyedListeners.getState.root?.().key;\n\n        if (target == null) {\n          console.error(NOT_INITIALIZED_ERROR);\n        } else {\n          listeners.focus[0]((navigation) =>\n            navigation.dispatch({\n              ...CommonActions.reset(state),\n              target,\n            })\n          );\n        }\n      },\n      [keyedListeners.getState, listeners.focus]\n    );\n\n    const getRootState = React.useCallback(() => {\n      return keyedListeners.getState.root?.();\n    }, [keyedListeners.getState]);\n\n    const getCurrentRoute = React.useCallback(() => {\n      const state = getRootState();\n\n      if (state == null) {\n        return undefined;\n      }\n\n      const route = findFocusedRoute(state);\n\n      return route as Route<string> | undefined;\n    }, [getRootState]);\n\n    const emitter = useEventEmitter<NavigationContainerEventMap>();\n\n    const { addOptionsGetter, getCurrentOptions } = useOptionsGetters({});\n\n    const navigation: NavigationContainerRef<ParamListBase> = React.useMemo(\n      () => ({\n        ...Object.keys(CommonActions).reduce<any>((acc, name) => {\n          acc[name] = (...args: any[]) =>\n            // @ts-expect-error: this is ok\n            dispatch(CommonActions[name](...args));\n          return acc;\n        }, {}),\n        ...emitter.create('root'),\n        dispatch,\n        resetRoot,\n        isFocused: () => true,\n        canGoBack,\n        getParent: () => undefined,\n        getState: () => stateRef.current,\n        getRootState,\n        getCurrentRoute,\n        getCurrentOptions,\n        isReady: () => listeners.focus[0] != null,\n        setOptions: () => {\n          throw new Error('Cannot call setOptions outside a screen');\n        },\n      }),\n      [\n        canGoBack,\n        dispatch,\n        emitter,\n        getCurrentOptions,\n        getCurrentRoute,\n        getRootState,\n        listeners.focus,\n        resetRoot,\n      ]\n    );\n\n    React.useImperativeHandle(ref, () => navigation, [navigation]);\n\n    const onDispatchAction = React.useCallback(\n      (action: NavigationAction, noop: boolean) => {\n        emitter.emit({\n          type: '__unsafe_action__',\n          data: { action, noop, stack: stackRef.current },\n        });\n      },\n      [emitter]\n    );\n\n    const lastEmittedOptionsRef = React.useRef<object | undefined>();\n\n    const onOptionsChange = React.useCallback(\n      (options: object) => {\n        if (lastEmittedOptionsRef.current === options) {\n          return;\n        }\n\n        lastEmittedOptionsRef.current = options;\n\n        emitter.emit({\n          type: 'options',\n          data: { options },\n        });\n      },\n      [emitter]\n    );\n\n    const stackRef = React.useRef<string | undefined>();\n\n    const builderContext = React.useMemo(\n      () => ({\n        addListener,\n        addKeyedListener,\n        onDispatchAction,\n        onOptionsChange,\n        stackRef,\n      }),\n      [addListener, addKeyedListener, onDispatchAction, onOptionsChange]\n    );\n\n    const scheduleContext = React.useMemo(\n      () => ({ scheduleUpdate, flushUpdates }),\n      [scheduleUpdate, flushUpdates]\n    );\n\n    const isInitialRef = React.useRef(true);\n\n    const getIsInitial = React.useCallback(() => isInitialRef.current, []);\n\n    const context = React.useMemo(\n      () => ({\n        state,\n        getState,\n        setState,\n        getKey,\n        setKey,\n        getIsInitial,\n        addOptionsGetter,\n      }),\n      [\n        state,\n        getState,\n        setState,\n        getKey,\n        setKey,\n        getIsInitial,\n        addOptionsGetter,\n      ]\n    );\n\n    const onStateChangeRef = React.useRef(onStateChange);\n    const stateRef = React.useRef(state);\n\n    React.useEffect(() => {\n      isInitialRef.current = false;\n      onStateChangeRef.current = onStateChange;\n      stateRef.current = state;\n    });\n\n    React.useEffect(() => {\n      const hydratedState = getRootState();\n\n      if (process.env.NODE_ENV !== 'production') {\n        if (hydratedState !== undefined) {\n          const serializableResult = checkSerializable(hydratedState);\n\n          if (!serializableResult.serializable) {\n            const { location, reason } = serializableResult;\n\n            let path = '';\n            let pointer: Record<any, any> = hydratedState;\n            let params = false;\n\n            for (let i = 0; i < location.length; i++) {\n              const curr = location[i];\n              const prev = location[i - 1];\n\n              pointer = pointer[curr];\n\n              if (!params && curr === 'state') {\n                continue;\n              } else if (!params && curr === 'routes') {\n                if (path) {\n                  path += ' > ';\n                }\n              } else if (\n                !params &&\n                typeof curr === 'number' &&\n                prev === 'routes'\n              ) {\n                path += pointer?.name;\n              } else if (!params) {\n                path += ` > ${curr}`;\n                params = true;\n              } else {\n                if (typeof curr === 'number' || /^[0-9]+$/.test(curr)) {\n                  path += `[${curr}]`;\n                } else if (/^[a-z$_]+$/i.test(curr)) {\n                  path += `.${curr}`;\n                } else {\n                  path += `[${JSON.stringify(curr)}]`;\n                }\n              }\n            }\n\n            const message = `Non-serializable values were found in the navigation state. Check:\\n\\n${path} (${reason})\\n\\nThis can break usage such as persisting and restoring state. This might happen if you passed non-serializable values such as function, class instances etc. in params. If you need to use components with callbacks in your options, you can use 'navigation.setOptions' instead. See https://reactnavigation.org/docs/troubleshooting#i-get-the-warning-non-serializable-values-were-found-in-the-navigation-state for more details.`;\n\n            if (!serializableWarnings.includes(message)) {\n              serializableWarnings.push(message);\n              console.warn(message);\n            }\n          }\n\n          const duplicateRouteNamesResult =\n            checkDuplicateRouteNames(hydratedState);\n\n          if (duplicateRouteNamesResult.length) {\n            const message = `Found screens with the same name nested inside one another. Check:\\n${duplicateRouteNamesResult.map(\n              (locations) => `\\n${locations.join(', ')}`\n            )}\\n\\nThis can cause confusing behavior during navigation. Consider using unique names for each screen instead.`;\n\n            if (!duplicateNameWarnings.includes(message)) {\n              duplicateNameWarnings.push(message);\n              console.warn(message);\n            }\n          }\n        }\n      }\n\n      emitter.emit({ type: 'state', data: { state } });\n\n      if (!isFirstMountRef.current && onStateChangeRef.current) {\n        onStateChangeRef.current(hydratedState);\n      }\n\n      isFirstMountRef.current = false;\n    }, [getRootState, emitter, state]);\n\n    const defaultOnUnhandledAction = React.useCallback(\n      (action: NavigationAction) => {\n        if (process.env.NODE_ENV === 'production') {\n          return;\n        }\n\n        const payload: Record<string, any> | undefined = action.payload;\n\n        let message = `The action '${action.type}'${\n          payload ? ` with payload ${JSON.stringify(action.payload)}` : ''\n        } was not handled by any navigator.`;\n\n        switch (action.type) {\n          case 'NAVIGATE':\n          case 'PUSH':\n          case 'REPLACE':\n          case 'JUMP_TO':\n            if (payload?.name) {\n              message += `\\n\\nDo you have a screen named '${payload.name}'?\\n\\nIf you're trying to navigate to a screen in a nested navigator, see https://reactnavigation.org/docs/nesting-navigators#navigating-to-a-screen-in-a-nested-navigator.`;\n            } else {\n              message += `\\n\\nYou need to pass the name of the screen to navigate to.\\n\\nSee https://reactnavigation.org/docs/navigation-actions for usage.`;\n            }\n\n            break;\n          case 'GO_BACK':\n          case 'POP':\n          case 'POP_TO_TOP':\n            message += `\\n\\nIs there any screen to go back to?`;\n            break;\n          case 'OPEN_DRAWER':\n          case 'CLOSE_DRAWER':\n          case 'TOGGLE_DRAWER':\n            message += `\\n\\nIs your screen inside a Drawer navigator?`;\n            break;\n        }\n\n        message += `\\n\\nThis is a development-only warning and won't be shown in production.`;\n\n        console.error(message);\n      },\n      []\n    );\n\n    let element = (\n      <NavigationContainerRefContext.Provider value={navigation}>\n        <ScheduleUpdateContext.Provider value={scheduleContext}>\n          <NavigationBuilderContext.Provider value={builderContext}>\n            <NavigationStateContext.Provider value={context}>\n              <UnhandledActionContext.Provider\n                value={onUnhandledAction ?? defaultOnUnhandledAction}\n              >\n                <EnsureSingleNavigator>{children}</EnsureSingleNavigator>\n              </UnhandledActionContext.Provider>\n            </NavigationStateContext.Provider>\n          </NavigationBuilderContext.Provider>\n        </ScheduleUpdateContext.Provider>\n      </NavigationContainerRefContext.Provider>\n    );\n\n    if (independent) {\n      // We need to clear any existing contexts for nested independent container to work correctly\n      element = (\n        <NavigationRouteContext.Provider value={undefined}>\n          <NavigationContext.Provider value={undefined}>\n            {element}\n          </NavigationContext.Provider>\n        </NavigationRouteContext.Provider>\n      );\n    }\n\n    return element;\n  }\n);\n\nexport default BaseNavigationContainer;\n"], "mappings": ";;;;;;AAAA,SACEA,aAAa,QAOR,2BAA2B;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB;AAC/B,OAAOC,iBAAiB;AACxB,SAASC,qBAAqB;AAC9B,OAAOC,qBAAqB;AAC5B,OAAOC,gBAAgB;AACvB,OAAOC,wBAAwB;AAC/B,OAAOC,6BAA6B;AACpC,OAAOC,iBAAiB;AACxB,OAAOC,sBAAsB;AAC7B,OAAOC,sBAAsB;AAM7B,OAAOC,sBAAsB;AAC7B,OAAOC,iBAAiB;AACxB,OAAOC,eAAe;AACtB,OAAOC,sBAAsB;AAC7B,OAAOC,iBAAiB;AACxB,SAASC,qBAAqB;AAC9B,OAAOC,YAAY;AAInB,IAAMC,oBAA8B,GAAG,EAAE;AACzC,IAAMC,qBAA+B,GAAG,EAAE;AAO1C,IAAMC,gBAAe,GACnB,SADIA,eAAeA,CACnBC,KAA+B,EACe;EAC9C,IAAIA,KAAK,KAAKC,SAAS,EAAE;IACvB;EACF;EAGA,IAAQC,GAAG,GAAkCF,KAAK,CAA1CE,GAAG;IAAEC,UAAU,GAAsBH,KAAK,CAArCG,UAAU;IAAKC,YAAA,GAAAC,wBAAA,CAAiBL,KAAK,EAAAM,SAAA;EAElD,OAAAC,aAAA,CAAAA,aAAA,KACKH,YAAY;IACfI,KAAK,EAAE,IAAI;IACXC,MAAM,EAAET,KAAK,CAACS,MAAM,CAACC,GAAG,CAAE,UAAAC,KAAK,EAAK;MAClC,IAAIA,KAAK,CAACX,KAAK,KAAKC,SAAS,EAAE;QAC7B,OAAOU,KAAK;MAGd;MAEA,OAAAJ,aAAA,CAAAA,aAAA,KAAYI,KAAK;QAAEX,KAAK,EAAED,gBAAe,CAACY,KAAK,CAACX,KAAK;MAAA;IACvD,CAAC;EAAA;AAEL,CAAC;AAWD,IAAMY,uBAAuB,GAAGjC,KAAK,CAACkC,UAAU,CAC9C,SAASD,uBAAuBA,CAAAE,IAAA,EAQ9BC,GAAsD,EACtD;EAAA,IAPEC,YAAY,GAKaF,IAAA,CALzBE,YAAY;IACZC,aAAa,GAIYH,IAAA,CAJzBG,aAAa;IACbC,iBAAiB,GAGQJ,IAAA,CAHzBI,iBAAiB;IACjBC,WAAW,GAEcL,IAAA,CAFzBK,WAAW;IACXC,QAAA,GACyBN,IAAA,CADzBM,QAAA;EAIF,IAAMC,MAAM,GAAG1C,KAAK,CAAC2C,UAAU,CAACjC,sBAAsB,CAAC;EAEvD,IAAI,CAACgC,MAAM,CAACE,SAAS,IAAI,CAACJ,WAAW,EAAE;IACrC,MAAM,IAAIK,KAAK,CACb,0VAA0V,CAC3V;EACH;EAEA,IAAAC,aAAA,GACE7B,YAAY,CAAQ;MAAA,OAClBG,gBAAe,CAACiB,YAAY,IAAI,IAAI,GAAGf,SAAS,GAAGe,YAAY,CAAC;IAAA,EACjE;IAAAU,cAAA,GAAAC,cAAA,CAAAF,aAAA;IAHIzB,KAAK,GAAA0B,cAAA;IAAEE,QAAQ,GAAAF,cAAA;IAAEG,QAAQ,GAAAH,cAAA;IAAEI,cAAc,GAAAJ,cAAA;IAAEK,YAAY,GAAAL,cAAA;EAK9D,IAAMM,eAAe,GAAGrD,KAAK,CAACsD,MAAM,CAAU,IAAI,CAAC;EAEnD,IAAMC,eAAe,GAAGvD,KAAK,CAACsD,MAAM,EAAsB;EAE1D,IAAME,MAAM,GAAGxD,KAAK,CAACyD,WAAW,CAAC;IAAA,OAAMF,eAAe,CAACG,OAAO;EAAA,GAAE,EAAE,CAAC;EAEnE,IAAMC,MAAM,GAAG3D,KAAK,CAACyD,WAAW,CAAE,UAAAlC,GAAW,EAAK;IAChDgC,eAAe,CAACG,OAAO,GAAGnC,GAAG;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,IAAAqC,kBAAA,GAAmChD,iBAAiB,EAAE;IAA9CiD,SAAS,GAAAD,kBAAA,CAATC,SAAS;IAAEC,WAAA,GAAAF,kBAAA,CAAAE,WAAA;EAEnB,IAAAC,qBAAA,GAA6CjD,sBAAsB,EAAE;IAA7DkD,cAAc,GAAAD,qBAAA,CAAdC,cAAc;IAAEC,gBAAA,GAAAF,qBAAA,CAAAE,gBAAA;EAExB,IAAMC,QAAQ,GAAGlE,KAAK,CAACyD,WAAW,CAE9B,UAAAU,MAEkD,EAC/C;IACH,IAAIN,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MAC9BC,OAAO,CAACC,KAAK,CAACnE,qBAAqB,CAAC;IACtC,CAAC,MAAM;MACL0D,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,CAAE,UAAAG,UAAU;QAAA,OAAKA,UAAU,CAACL,QAAQ,CAACC,MAAM,CAAC;MAAA,EAAC;IACjE;EACF,CAAC,EACD,CAACN,SAAS,CAACO,KAAK,CAAC,CAClB;EAED,IAAMI,SAAS,GAAGxE,KAAK,CAACyD,WAAW,CAAC,YAAM;IACxC,IAAII,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MAC9B,OAAO,KAAK;IACd;IAEA,IAAAK,iBAAA,GAA4BZ,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,CAAE,UAAAG,UAAU;QAAA,OACxDA,UAAU,CAACC,SAAS,EAAE;MAAA,EACvB;MAFOE,MAAM,GAAAD,iBAAA,CAANC,MAAM;MAAEC,OAAA,GAAAF,iBAAA,CAAAE,OAAA;IAIhB,IAAIA,OAAO,EAAE;MACX,OAAOD,MAAM;IACf,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAACb,SAAS,CAACO,KAAK,CAAC,CAAC;EAErB,IAAMQ,SAAS,GAAG5E,KAAK,CAACyD,WAAW,CAChC,UAAApC,KAAuD,EAAK;IAAA,IAAAwD,KAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAC3D,IAAMC,MAAM,IAAAH,KAAA,GAAGxD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,GAAG,YAAAsD,KAAA,IAAAC,qBAAA,GAAI,CAAAC,sBAAA,GAAAf,cAAc,CAACf,QAAQ,EAACgC,IAAI,cAAAH,qBAAA,uBAA5BA,qBAAA,CAAAI,IAAA,CAAAH,sBAAA,CAAgC,CAACxD,GAAG;IAEjE,IAAIyD,MAAM,IAAI,IAAI,EAAE;MAClBX,OAAO,CAACC,KAAK,CAACnE,qBAAqB,CAAC;IACtC,CAAC,MAAM;MACL0D,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,CAAE,UAAAG,UAAU;QAAA,OAC5BA,UAAU,CAACL,QAAQ,CAAAtC,aAAA,CAAAA,aAAA,KACd7B,aAAa,CAACoF,KAAK,CAAC9D,KAAK,CAAC;UAC7B2D,MAAA,EAAAA;QAAA,EACD,CAAC;MAAA,EACH;IACH;EACF,CAAC,EACD,CAAChB,cAAc,CAACf,QAAQ,EAAEY,SAAS,CAACO,KAAK,CAAC,CAC3C;EAED,IAAMgB,YAAY,GAAGpF,KAAK,CAACyD,WAAW,CAAC,YAAM;IAAA,IAAA4B,sBAAA,EAAAC,sBAAA;IAC3C,QAAAD,sBAAA,GAAO,CAAAC,sBAAA,GAAAtB,cAAc,CAACf,QAAQ,EAACgC,IAAI,cAAAI,sBAAA,uBAA5BA,sBAAA,CAAAH,IAAA,CAAAI,sBAAA,CAAgC;EACzC,CAAC,EAAE,CAACtB,cAAc,CAACf,QAAQ,CAAC,CAAC;EAE7B,IAAMsC,eAAe,GAAGvF,KAAK,CAACyD,WAAW,CAAC,YAAM;IAC9C,IAAMpC,KAAK,GAAG+D,YAAY,EAAE;IAE5B,IAAI/D,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOC,SAAS;IAClB;IAEA,IAAMU,KAAK,GAAG3B,gBAAgB,CAACgB,KAAK,CAAC;IAErC,OAAOW,KAAK;EACd,CAAC,EAAE,CAACoD,YAAY,CAAC,CAAC;EAElB,IAAMI,OAAO,GAAG3E,eAAe,EAA+B;EAE9D,IAAA4E,kBAAA,GAAgD1E,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAA7D2E,gBAAgB,GAAAD,kBAAA,CAAhBC,gBAAgB;IAAEC,iBAAA,GAAAF,kBAAA,CAAAE,iBAAA;EAE1B,IAAMpB,UAAiD,GAAGvE,KAAK,CAAC4F,OAAO,CACrE;IAAA,OAAAhE,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACKiE,MAAM,CAACC,IAAI,CAAC/F,aAAa,CAAC,CAACgG,MAAM,CAAM,UAACC,GAAG,EAAEC,IAAI,EAAK;MACvDD,GAAG,CAACC,IAAI,CAAC,GAAG;QAAA,QAEV/B,QAAQ,CAACnE,aAAa,CAACkG,IAAI,CAAC,CAAAC,KAAA,CAAnBnG,aAAa,EAAOoG,SAAO,CAAC;QAAA;MAAC;MACxC,OAAOH,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,GACHR,OAAO,CAACY,MAAM,CAAC,MAAM,CAAC;MACzBlC,QAAQ,EAARA,QAAQ;MACRU,SAAS,EAATA,SAAS;MACTyB,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQ,IAAI;MAAA;MACrB7B,SAAS,EAATA,SAAS;MACT8B,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQhF,SAAS;MAAA;MAC1B2B,QAAQ,EAAE,SAAVA,QAAQA,CAAA;QAAA,OAAQsD,QAAQ,CAAC7C,OAAO;MAAA;MAChC0B,YAAY,EAAZA,YAAY;MACZG,eAAe,EAAfA,eAAe;MACfI,iBAAiB,EAAjBA,iBAAiB;MACjBa,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQ3C,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;MAAA;MACzCqC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;QAChB,MAAM,IAAI5D,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IAAA;EAAA,CACA,EACF,CACE2B,SAAS,EACTN,QAAQ,EACRsB,OAAO,EACPG,iBAAiB,EACjBJ,eAAe,EACfH,YAAY,EACZvB,SAAS,CAACO,KAAK,EACfQ,SAAS,CACV,CACF;EAED5E,KAAK,CAAC0G,mBAAmB,CAACtE,GAAG,EAAE;IAAA,OAAMmC,UAAU;EAAA,GAAE,CAACA,UAAU,CAAC,CAAC;EAE9D,IAAMoC,gBAAgB,GAAG3G,KAAK,CAACyD,WAAW,CACxC,UAACU,MAAwB,EAAEyC,IAAa,EAAK;IAC3CpB,OAAO,CAACqB,IAAI,CAAC;MACXC,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE;QAAE5C,MAAM,EAANA,MAAM;QAAEyC,IAAI,EAAJA,IAAI;QAAEI,KAAK,EAAEC,QAAQ,CAACvD;MAAQ;IAChD,CAAC,CAAC;EACJ,CAAC,EACD,CAAC8B,OAAO,CAAC,CACV;EAED,IAAM0B,qBAAqB,GAAGlH,KAAK,CAACsD,MAAM,EAAsB;EAEhE,IAAM6D,eAAe,GAAGnH,KAAK,CAACyD,WAAW,CACtC,UAAA2D,OAAe,EAAK;IACnB,IAAIF,qBAAqB,CAACxD,OAAO,KAAK0D,OAAO,EAAE;MAC7C;IACF;IAEAF,qBAAqB,CAACxD,OAAO,GAAG0D,OAAO;IAEvC5B,OAAO,CAACqB,IAAI,CAAC;MACXC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE;QAAEK,OAAA,EAAAA;MAAQ;IAClB,CAAC,CAAC;EACJ,CAAC,EACD,CAAC5B,OAAO,CAAC,CACV;EAED,IAAMyB,QAAQ,GAAGjH,KAAK,CAACsD,MAAM,EAAsB;EAEnD,IAAM+D,cAAc,GAAGrH,KAAK,CAAC4F,OAAO,CAClC;IAAA,OAAO;MACL9B,WAAW,EAAXA,WAAW;MACXG,gBAAgB,EAAhBA,gBAAgB;MAChB0C,gBAAgB,EAAhBA,gBAAgB;MAChBQ,eAAe,EAAfA,eAAe;MACfF,QAAA,EAAAA;IACF,CAAC;EAAA,CAAC,EACF,CAACnD,WAAW,EAAEG,gBAAgB,EAAE0C,gBAAgB,EAAEQ,eAAe,CAAC,CACnE;EAED,IAAMG,eAAe,GAAGtH,KAAK,CAAC4F,OAAO,CACnC;IAAA,OAAO;MAAEzC,cAAc,EAAdA,cAAc;MAAEC,YAAA,EAAAA;IAAa,CAAC;EAAA,CAAC,EACxC,CAACD,cAAc,EAAEC,YAAY,CAAC,CAC/B;EAED,IAAMmE,YAAY,GAAGvH,KAAK,CAACsD,MAAM,CAAC,IAAI,CAAC;EAEvC,IAAMkE,YAAY,GAAGxH,KAAK,CAACyD,WAAW,CAAC;IAAA,OAAM8D,YAAY,CAAC7D,OAAO;EAAA,GAAE,EAAE,CAAC;EAEtE,IAAM+D,OAAO,GAAGzH,KAAK,CAAC4F,OAAO,CAC3B;IAAA,OAAO;MACLvE,KAAK,EAALA,KAAK;MACL4B,QAAQ,EAARA,QAAQ;MACRC,QAAQ,EAARA,QAAQ;MACRM,MAAM,EAANA,MAAM;MACNG,MAAM,EAANA,MAAM;MACN6D,YAAY,EAAZA,YAAY;MACZ9B,gBAAA,EAAAA;IACF,CAAC;EAAA,CAAC,EACF,CACErE,KAAK,EACL4B,QAAQ,EACRC,QAAQ,EACRM,MAAM,EACNG,MAAM,EACN6D,YAAY,EACZ9B,gBAAgB,CACjB,CACF;EAED,IAAMgC,gBAAgB,GAAG1H,KAAK,CAACsD,MAAM,CAAChB,aAAa,CAAC;EACpD,IAAMiE,QAAQ,GAAGvG,KAAK,CAACsD,MAAM,CAACjC,KAAK,CAAC;EAEpCrB,KAAK,CAAC2H,SAAS,CAAC,YAAM;IACpBJ,YAAY,CAAC7D,OAAO,GAAG,KAAK;IAC5BgE,gBAAgB,CAAChE,OAAO,GAAGpB,aAAa;IACxCiE,QAAQ,CAAC7C,OAAO,GAAGrC,KAAK;EAC1B,CAAC,CAAC;EAEFrB,KAAK,CAAC2H,SAAS,CAAC,YAAM;IACpB,IAAMC,aAAa,GAAGxC,YAAY,EAAE;IAEpC,IAAIyC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIH,aAAa,KAAKtG,SAAS,EAAE;QAC/B,IAAM0G,kBAAkB,GAAG9H,iBAAiB,CAAC0H,aAAa,CAAC;QAE3D,IAAI,CAACI,kBAAkB,CAACC,YAAY,EAAE;UACpC,IAAQC,QAAQ,GAAaF,kBAAkB,CAAvCE,QAAQ;YAAEC,MAAA,GAAWH,kBAAkB,CAA7BG,MAAA;UAElB,IAAIC,IAAI,GAAG,EAAE;UACb,IAAIC,OAAyB,GAAGT,aAAa;UAC7C,IAAIU,MAAM,GAAG,KAAK;UAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;YACxC,IAAME,IAAI,GAAGP,QAAQ,CAACK,CAAC,CAAC;YACxB,IAAMG,IAAI,GAAGR,QAAQ,CAACK,CAAC,GAAG,CAAC,CAAC;YAE5BF,OAAO,GAAGA,OAAO,CAACI,IAAI,CAAC;YAEvB,IAAI,CAACH,MAAM,IAAIG,IAAI,KAAK,OAAO,EAAE;cAC/B;YACF,CAAC,MAAM,IAAI,CAACH,MAAM,IAAIG,IAAI,KAAK,QAAQ,EAAE;cACvC,IAAIL,IAAI,EAAE;gBACRA,IAAI,IAAI,KAAK;cACf;YACF,CAAC,MAAM,IACL,CAACE,MAAM,IACP,OAAOG,IAAI,KAAK,QAAQ,IACxBC,IAAI,KAAK,QAAQ,EACjB;cAAA,IAAAC,QAAA;cACAP,IAAI,KAAAO,QAAA,GAAIN,OAAO,cAAAM,QAAA,uBAAPA,QAAA,CAAS1C,IAAI;YACvB,CAAC,MAAM,IAAI,CAACqC,MAAM,EAAE;cAClBF,IAAI,IAAK,MAAKK,IAAK,EAAC;cACpBH,MAAM,GAAG,IAAI;YACf,CAAC,MAAM;cACL,IAAI,OAAOG,IAAI,KAAK,QAAQ,IAAI,UAAU,CAACG,IAAI,CAACH,IAAI,CAAC,EAAE;gBACrDL,IAAI,IAAK,IAAGK,IAAK,GAAE;cACrB,CAAC,MAAM,IAAI,aAAa,CAACG,IAAI,CAACH,IAAI,CAAC,EAAE;gBACnCL,IAAI,IAAK,IAAGK,IAAK,EAAC;cACpB,CAAC,MAAM;gBACLL,IAAI,IAAK,IAAGS,IAAI,CAACC,SAAS,CAACL,IAAI,CAAE,GAAE;cACrC;YACF;UACF;UAEA,IAAMM,OAAO,GAAI,yEAAwEX,IAAK,KAAID,MAAO,4aAA2a;UAEphB,IAAI,CAACjH,oBAAoB,CAAC8H,QAAQ,CAACD,OAAO,CAAC,EAAE;YAC3C7H,oBAAoB,CAAC+H,IAAI,CAACF,OAAO,CAAC;YAClC1E,OAAO,CAAC6E,IAAI,CAACH,OAAO,CAAC;UACvB;QACF;QAEA,IAAMI,yBAAyB,GAC7BlJ,wBAAwB,CAAC2H,aAAa,CAAC;QAEzC,IAAIuB,yBAAyB,CAACX,MAAM,EAAE;UACpC,IAAMO,QAAO,GAAI,uEAAsEI,yBAAyB,CAACpH,GAAG,CACjH,UAAAqH,SAAS;YAAA,OAAM,KAAIA,SAAS,CAACC,IAAI,CAAC,IAAI,CAAE,EAAC;UAAA,EAC1C,+GAA8G;UAEhH,IAAI,CAAClI,qBAAqB,CAAC6H,QAAQ,CAACD,QAAO,CAAC,EAAE;YAC5C5H,qBAAqB,CAAC8H,IAAI,CAACF,QAAO,CAAC;YACnC1E,OAAO,CAAC6E,IAAI,CAACH,QAAO,CAAC;UACvB;QACF;MACF;IACF;IAEAvD,OAAO,CAACqB,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;QAAE1F,KAAA,EAAAA;MAAM;IAAE,CAAC,CAAC;IAEhD,IAAI,CAACgC,eAAe,CAACK,OAAO,IAAIgE,gBAAgB,CAAChE,OAAO,EAAE;MACxDgE,gBAAgB,CAAChE,OAAO,CAACkE,aAAa,CAAC;IACzC;IAEAvE,eAAe,CAACK,OAAO,GAAG,KAAK;EACjC,CAAC,EAAE,CAAC0B,YAAY,EAAEI,OAAO,EAAEnE,KAAK,CAAC,CAAC;EAElC,IAAMiI,wBAAwB,GAAGtJ,KAAK,CAACyD,WAAW,CAC/C,UAAAU,MAAwB,EAAK;IAC5B,IAAI0D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;IACF;IAEA,IAAMwB,OAAwC,GAAGpF,MAAM,CAACoF,OAAO;IAE/D,IAAIR,OAAO,GAAI,eAAc5E,MAAM,CAAC2C,IAAK,IACvCyC,OAAO,GAAI,iBAAgBV,IAAI,CAACC,SAAS,CAAC3E,MAAM,CAACoF,OAAO,CAAE,EAAC,GAAG,EAC/D,oCAAmC;IAEpC,QAAQpF,MAAM,CAAC2C,IAAI;MACjB,KAAK,UAAU;MACf,KAAK,MAAM;MACX,KAAK,SAAS;MACd,KAAK,SAAS;QACZ,IAAIyC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEtD,IAAI,EAAE;UACjB8C,OAAO,IAAK,mCAAkCQ,OAAO,CAACtD,IAAK,6KAA4K;QACzO,CAAC,MAAM;UACL8C,OAAO,IAAK,mIAAkI;QAChJ;QAEA;MACF,KAAK,SAAS;MACd,KAAK,KAAK;MACV,KAAK,YAAY;QACfA,OAAO,IAAK,wCAAuC;QACnD;MACF,KAAK,aAAa;MAClB,KAAK,cAAc;MACnB,KAAK,eAAe;QAClBA,OAAO,IAAK,+CAA8C;QAC1D;IAAM;IAGVA,OAAO,IAAK,0EAAyE;IAErF1E,OAAO,CAACC,KAAK,CAACyE,OAAO,CAAC;EACxB,CAAC,EACD,EAAE,CACH;EAED,IAAIS,OAAO,GACTxJ,KAAA,CAAAyJ,aAAA,CAAClJ,6BAA6B,CAACmJ,QAAQ;IAACC,KAAK,EAAEpF;EAAW,GACxDvE,KAAA,CAAAyJ,aAAA,CAACzI,qBAAqB,CAAC0I,QAAQ;IAACC,KAAK,EAAErC;EAAgB,GACrDtH,KAAA,CAAAyJ,aAAA,CAACnJ,wBAAwB,CAACoJ,QAAQ;IAACC,KAAK,EAAEtC;EAAe,GACvDrH,KAAA,CAAAyJ,aAAA,CAAC/I,sBAAsB,CAACgJ,QAAQ;IAACC,KAAK,EAAElC;EAAQ,GAC9CzH,KAAA,CAAAyJ,aAAA,CAAC9I,sBAAsB,CAAC+I,QAAQ;IAC9BC,KAAK,EAAEpH,iBAAiB,WAAjBA,iBAAiB,GAAI+G;EAAyB,GAErDtJ,KAAA,CAAAyJ,aAAA,CAACrJ,qBAAqB,QAAEqC,QAAQ,CAAyB,CACzB,CACF,CACA,CACL,CAEpC;EAED,IAAID,WAAW,EAAE;IAEfgH,OAAO,GACLxJ,KAAA,CAAAyJ,aAAA,CAAChJ,sBAAsB,CAACiJ,QAAQ;MAACC,KAAK,EAAErI;IAAU,GAChDtB,KAAA,CAAAyJ,aAAA,CAACjJ,iBAAiB,CAACkJ,QAAQ;MAACC,KAAK,EAAErI;IAAU,GAC1CkI,OAAO,CACmB,CAEhC;EACH;EAEA,OAAOA,OAAO;AAChB,CAAC,CACF;AAED,eAAevH,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}