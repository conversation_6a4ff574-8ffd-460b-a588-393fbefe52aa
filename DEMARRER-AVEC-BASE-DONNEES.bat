@echo off
title Demarrage AquaTrack avec Base de Donnees
color 0A

echo.
echo ========================================
echo    🗄️ AQUATRACK AVEC BASE DE DONNEES
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🗄️ 3. VERIFICATION DE POSTGRESQL...
echo.
echo Verification que PostgreSQL est demarre...
echo Base de donnees: Facturation
echo Utilisateur: postgres
echo Port: 5432
echo.

echo 🖥️ 4. DEMARRAGE BACKEND AVEC BASE DE DONNEES...
echo.
echo Utilisation du serveur minimal avec PostgreSQL...
echo.
start "🖥️ Backend PostgreSQL" cmd /k "title Backend AquaTrack PostgreSQL && color 0B && echo ========================================== && echo    🖥️ BACKEND AVEC BASE DE DONNEES && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Base: PostgreSQL Facturation && echo ✅ API: http://localhost:4000 && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo    - <EMAIL> / Admin123 && echo. && echo 📡 Connexion a la base de donnees... && echo. && node backend/server-minimal.js"

echo.
echo ⏳ 5. ATTENTE BACKEND (10 secondes)...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 6. TEST DE L'API CLIENTS...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/clients' -TimeoutSec 10; Write-Host '✅ API CLIENTS OK:' $response.total 'clients trouves' } catch { Write-Host '❌ API CLIENTS ERREUR:' $_.Exception.Message }"

echo.
echo 📱 7. DEMARRAGE FRONTEND MOBILE...
cd mobile
start "📱 Frontend Mobile" cmd /k "title Frontend AquaTrack Mobile && color 0D && echo ========================================== && echo    📱 APPLICATION MOBILE AQUATRACK && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ URL: http://localhost:19006 && echo ✅ Backend: http://localhost:4000 && echo. && echo 📡 Demarrage de l application mobile... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 8. ATTENTE FRONTEND (12 secondes)...
timeout /t 12 /nobreak >nul

echo.
echo 🌐 9. OUVERTURE DES NAVIGATEURS...
start http://localhost:4000
timeout /t 3 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ AQUATRACK AVEC BASE DE DONNEES !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 📱 Frontend: http://localhost:19006
echo 🗄️ Base de donnees: PostgreSQL Facturation
echo.
echo 🔑 Comptes de test:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 📋 Instructions:
echo    1. Verifiez que le backend fonctionne: http://localhost:4000
echo    2. Testez l'API clients: http://localhost:4000/api/clients
echo    3. Allez sur l'application mobile: http://localhost:19006
echo    4. La liste des clients devrait maintenant s'afficher !
echo.
echo ⚠️ IMPORTANT:
echo    - PostgreSQL doit etre demarre
echo    - La base 'Facturation' doit exister
echo    - La table 'client' doit contenir des donnees
echo.
echo 🆘 Si aucun client ne s'affiche:
echo    1. Verifiez que PostgreSQL est demarre
echo    2. Verifiez que la base 'Facturation' existe
echo    3. Verifiez que la table 'client' contient des donnees
echo    4. Consultez les logs dans la fenetre du backend
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
