import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import GoogleMapsEmbed from '../components/GoogleMapsEmbed';

const { width, height } = Dimensions.get('window');

const ClientsMapScreen = ({ navigation, route }) => {
  const { user } = route.params || {};
  const [loading, setLoading] = useState(false);
  const [secteurs, setSecteurs] = useState([]);
  const [selectedSecteur, setSelectedSecteur] = useState('');
  const [selectedSecteurInfo, setSelectedSecteurInfo] = useState(null);
  const [clients, setClients] = useState([]);
  const [loadingClients, setLoadingClients] = useState(false);

  const API_BASE_URL = 'http://localhost:4000';

  useEffect(() => {
    fetchSecteurs();
  }, []);

  const fetchSecteurs = async () => {
    setLoading(true);
    try {
      console.log('🔍 Récupération des secteurs...');
      const response = await fetch(`${API_BASE_URL}/api/secteurs`);
      const data = await response.json();
      
      if (data.success) {
        setSecteurs(data.data);
        console.log(`✅ ${data.data.length} secteurs récupérés`);
      } else {
        Alert.alert('Erreur', 'Impossible de récupérer les secteurs');
      }
    } catch (error) {
      console.error('Erreur récupération secteurs:', error);
      Alert.alert('Erreur', 'Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const fetchClientsBySecteur = async (secteurId) => {
    if (!secteurId) return;
    
    setLoadingClients(true);
    try {
      console.log(`🔍 Récupération des clients du secteur ${secteurId}...`);
      const response = await fetch(`${API_BASE_URL}/api/secteurs/${secteurId}/clients`);
      const data = await response.json();
      
      if (data.success) {
        setClients(data.data);
        console.log(`✅ ${data.data.length} clients récupérés pour le secteur ${secteurId}`);
        
        if (data.data.length === 0) {
          Alert.alert('Information', `Aucun client trouvé dans le secteur ${data.secteur?.nom || secteurId}`);
        }
      } else {
        Alert.alert('Erreur', 'Impossible de récupérer les clients du secteur');
      }
    } catch (error) {
      console.error('Erreur récupération clients:', error);
      Alert.alert('Erreur', 'Erreur de connexion au serveur');
    } finally {
      setLoadingClients(false);
    }
  };

  const handleSecteurChange = (secteurId) => {
    setSelectedSecteur(secteurId);
    setClients([]);
    
    // Trouver les informations du secteur sélectionné
    const secteurInfo = secteurs.find(s => s.ids.toString() === secteurId);
    setSelectedSecteurInfo(secteurInfo);
    
    if (secteurId) {
      fetchClientsBySecteur(secteurId);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* En-tête */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>← Retour</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>🗺️ Carte des Clients</Text>
          <Text style={styles.headerSubtitle}>
            Visualisez les clients par secteur sur Google Maps
          </Text>
        </View>

        {/* Sélection du secteur */}
        <View style={styles.sectionCard}>
          <Text style={styles.sectionTitle}>📍 Choisir un Secteur</Text>
          
          {loading ? (
            <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
          ) : (
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={selectedSecteur}
                onValueChange={handleSecteurChange}
                style={styles.picker}
              >
                <Picker.Item label="-- Sélectionnez un secteur --" value="" />
                {secteurs.map((secteur) => (
                  <Picker.Item
                    key={secteur.ids}
                    label={secteur.nom}
                    value={secteur.ids.toString()}
                  />
                ))}
              </Picker>
            </View>
          )}
        </View>

        {/* Informations du secteur sélectionné */}
        {selectedSecteurInfo && (
          <View style={styles.sectionCard}>
            <Text style={styles.sectionTitle}>ℹ️ Informations du Secteur</Text>
            <View style={styles.secteurInfo}>
              <Text style={styles.secteurName}>{selectedSecteurInfo.nom}</Text>
              <Text style={styles.secteurCoords}>
                📍 Coordonnées: {selectedSecteurInfo.latitude.toFixed(4)}, {selectedSecteurInfo.longitude.toFixed(4)}
              </Text>
              <Text style={styles.secteurClients}>
                👥 {clients.length} client(s) dans ce secteur
              </Text>
            </View>
          </View>
        )}

        {/* Chargement des clients */}
        {loadingClients && (
          <View style={styles.sectionCard}>
            <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
            <Text style={styles.loadingText}>Chargement des clients...</Text>
          </View>
        )}

        {/* Carte Google Maps intégrée */}
        {selectedSecteur && !loadingClients && (
          <View style={styles.mapSection}>
            <GoogleMapsEmbed 
              secteur={selectedSecteurInfo} 
              clients={clients} 
            />
          </View>
        )}

        {/* Message si aucun secteur sélectionné */}
        {!selectedSecteur && !loading && (
          <View style={styles.sectionCard}>
            <Text style={styles.instructionText}>
              👆 Sélectionnez un secteur ci-dessus pour voir la carte avec tous les clients de ce secteur
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 20,
    paddingTop: 10,
  },
  backButton: {
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 15,
  },
  backButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    textAlign: 'center',
  },
  sectionCard: {
    backgroundColor: '#fff',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#f8f9fa',
  },
  picker: {
    height: 50,
  },
  loader: {
    marginVertical: 20,
  },
  loadingText: {
    textAlign: 'center',
    color: '#666',
    marginTop: 10,
  },
  secteurInfo: {
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
  },
  secteurName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 5,
  },
  secteurCoords: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  secteurClients: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: 'bold',
  },
  mapSection: {
    margin: 15,
  },
  instructionText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    padding: 20,
    fontSize: 16,
    lineHeight: 24,
  },
});

export default ClientsMapScreen;
