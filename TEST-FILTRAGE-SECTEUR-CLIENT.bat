@echo off
title Test Filtrage Secteur-Client
color 0A

echo.
echo ========================================
echo    🔍 TEST FILTRAGE SECTEUR → CLIENT
echo ========================================
echo.

echo 🔍 1. VERIFICATION DU SERVEUR PORT 4002...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 5; Write-Host '✅ Serveur OK:' $response.message } catch { Write-Host '❌ Serveur non accessible - Demarrez le serveur d''abord' }"

echo.
echo 🔍 2. TEST DE LA TABLE SECTEUR...
echo.
echo Verification de tous les secteurs disponibles:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '✅ API Secteurs:' $response.count 'secteurs disponibles'; $response.data | ForEach-Object { Write-Host '   📍' $_.ids ':' $_.nom '(' $_.latitude ',' $_.longitude ')' } } catch { Write-Host '❌ API Secteurs erreur' }"

echo.
echo 🔍 3. TEST DU FILTRAGE CLIENT PAR SECTEUR...
echo.
echo Test Secteur 1 (Centre-Ville):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/1/clients' -TimeoutSec 5; Write-Host '✅ Secteur 1 - Centre-Ville:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   👤' $_.idclient ':' $_.nom $_.prenom '(Secteur ID:' $_.ids ')' } } catch { Write-Host '❌ API Secteur 1 erreur' }"

echo.
echo Test Secteur 2 (Quartier Industriel):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/2/clients' -TimeoutSec 5; Write-Host '✅ Secteur 2 - Quartier Industriel:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   👤' $_.idclient ':' $_.nom $_.prenom '(Secteur ID:' $_.ids ')' } } catch { Write-Host '❌ API Secteur 2 erreur' }"

echo.
echo Test Secteur 3 (Zone Nord):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/3/clients' -TimeoutSec 5; Write-Host '✅ Secteur 3 - Zone Nord:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   👤' $_.idclient ':' $_.nom $_.prenom '(Secteur ID:' $_.ids ')' } } catch { Write-Host '❌ API Secteur 3 erreur' }"

echo.
echo Test Secteur 4 (Zone Sud):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/4/clients' -TimeoutSec 5; Write-Host '✅ Secteur 4 - Zone Sud:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   👤' $_.idclient ':' $_.nom $_.prenom '(Secteur ID:' $_.ids ')' } } catch { Write-Host '❌ API Secteur 4 erreur' }"

echo.
echo Test Secteur 5 (Quartier Commercial):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/5/clients' -TimeoutSec 5; Write-Host '✅ Secteur 5 - Quartier Commercial:' $response.count 'clients'; if ($response.count -eq 0) { Write-Host '   ℹ️ Aucun client dans ce secteur (normal pour les tests)' } else { $response.data | ForEach-Object { Write-Host '   👤' $_.idclient ':' $_.nom $_.prenom '(Secteur ID:' $_.ids ')' } } } catch { Write-Host '❌ API Secteur 5 erreur' }"

echo.
echo 🚀 4. DEMARRAGE DE L'APPLICATION...
echo.
echo Arret des processus existants...
taskkill /f /im node.exe 2>nul

echo.
echo Demarrage du serveur backend (port 4002)...
start "🎯 Backend Port 4002" cmd /k "title BACKEND AQUATRACK PORT 4002 && color 0B && echo ========================================== && echo    🎯 BACKEND AQUATRACK PORT 4002 && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Table secteur: 5 secteurs && echo ✅ Table client: Filtrage par secteur && echo ✅ Relation: client.ids = secteur.ids && echo. && echo 📡 Demarrage... && echo. && node serveur-port-4002.js"

echo.
echo Attente du backend (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo Demarrage de l'application React...
cd mobile
start "📱 Application React" cmd /k "title APPLICATION REACT AQUATRACK && color 0D && echo ========================================== && echo    📱 APPLICATION REACT AQUATRACK && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4002 && echo ✅ Filtrage: Secteur → Client automatique && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo Attente de l'application (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 5. OUVERTURE DES PAGES...
start http://localhost:4002/consommation
timeout /t 3 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ FILTRAGE SECTEUR → CLIENT PRET !
echo ========================================
echo.
echo 📋 LOGIQUE IMPLEMENTEE:
echo.
echo 1️⃣ CHAMP SECTEUR:
echo    - Affiche TOUS les secteurs de la table 'secteur'
echo    - 5 secteurs disponibles (Centre-Ville, Industriel, Nord, Sud, Commercial)
echo    - Selection obligatoire pour debloquer le champ Client
echo.
echo 2️⃣ CHAMP CLIENT:
echo    - Affiche SEULEMENT les clients du secteur selectionne
echo    - Filtrage base sur: client.ids = secteur.ids
echo    - Messages d'aide contextuels selon l'etat
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous avec:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🧪 TESTEZ LE FILTRAGE:
echo.
echo    a) 1er test - Centre-Ville:
echo       - Selectionnez "Centre-Ville" dans Secteur
echo       - Le champ Client affiche: "2 clients dans ce secteur"
echo       - Clients: Benali Fatima, Alami Mohammed
echo.
echo    b) 2eme test - Quartier Industriel:
echo       - Selectionnez "Quartier Industriel" dans Secteur
echo       - Le champ Client affiche: "1 client dans ce secteur"
echo       - Client: Tazi Aicha
echo.
echo    c) 3eme test - Zone Nord:
echo       - Selectionnez "Zone Residentielle Nord" dans Secteur
echo       - Le champ Client affiche: "2 clients dans ce secteur"
echo       - Clients: Benjelloun Youssef, Lahlou Khadija
echo.
echo    d) 4eme test - Zone Sud:
echo       - Selectionnez "Zone Residentielle Sud" dans Secteur
echo       - Le champ Client affiche: "1 client dans ce secteur"
echo       - Client: Fassi Omar
echo.
echo    e) 5eme test - Quartier Commercial:
echo       - Selectionnez "Quartier Commercial" dans Secteur
echo       - Le champ Client affiche: "Aucun client dans ce secteur"
echo       - Message d'aide: "Essayez un autre secteur"
echo.
echo 📊 VERIFICATION DU FILTRAGE:
echo.
echo ✅ Secteur 1 (Centre-Ville) → 2 clients (IDs: 1, 2)
echo ✅ Secteur 2 (Quartier Industriel) → 1 client (ID: 3)
echo ✅ Secteur 3 (Zone Nord) → 2 clients (IDs: 4, 5)
echo ✅ Secteur 4 (Zone Sud) → 1 client (ID: 6)
echo ✅ Secteur 5 (Quartier Commercial) → 0 client
echo.
echo 🎯 RELATION BASE DE DONNEES:
echo    Table secteur: ids (PRIMARY KEY)
echo    Table client: ids (FOREIGN KEY vers secteur.ids)
echo    Filtrage: WHERE client.ids = secteur.ids
echo.
echo 🌐 URLs de test:
echo    - Application: http://localhost:19006
echo    - API Secteurs: http://localhost:4002/api/secteurs
echo    - API Clients Secteur 1: http://localhost:4002/api/secteurs/1/clients
echo    - API Clients Secteur 2: http://localhost:4002/api/secteurs/2/clients
echo.
echo 🎯 RESULTAT ATTENDU:
echo    Le champ Client affiche uniquement les clients
echo    du secteur selectionne, base sur la relation
echo    client.ids = secteur.ids !
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
