@echo off
title Test Validation Période
color 0A

echo.
echo ========================================
echo    📅 TEST VALIDATION PÉRIODE
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR...
echo.
start "🧪 Serveur Test" cmd /k "title SERVEUR TEST VALIDATION && color 0B && echo ========================================== && echo    🧪 SERVEUR TEST VALIDATION && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Validation: Periode implementee && echo ✅ Test: Mois precedents uniquement && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 5. DEMARRAGE DE L'APPLICATION...
echo.
cd mobile
start "📱 App Validation" cmd /k "title APPLICATION VALIDATION PERIODE && color 0D && echo ========================================== && echo    📱 APPLICATION VALIDATION PERIODE && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Validation: Periode active && echo ✅ Test: Mois actuel et futurs bloques && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 6. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 7. OUVERTURE POUR TEST...
start http://localhost:19006

echo.
echo ========================================
echo    📅 TEST VALIDATION PÉRIODE
echo ========================================
echo.
echo 🎯 VALIDATION IMPLEMENTÉE:
echo.
echo ✅ RÈGLES DE VALIDATION:
echo    - Seulement les mois PRÉCÉDENTS sont autorisés
echo    - Le mois ACTUEL est INTERDIT
echo    - Les mois FUTURS sont INTERDITS
echo    - Format requis: YYYY-MM (ex: 2024-12)
echo.
echo ❌ EXEMPLES INTERDITS (selon la date actuelle):
powershell -Command "$now = Get-Date; $currentMonth = $now.ToString('yyyy-MM'); $nextMonth = $now.AddMonths(1).ToString('yyyy-MM'); Write-Host '   - Mois actuel:' $currentMonth '(INTERDIT)'; Write-Host '   - Mois suivant:' $nextMonth '(INTERDIT)'; Write-Host '   - 2025-12 (INTERDIT si futur)'"

echo.
echo ✅ EXEMPLES AUTORISÉS:
powershell -Command "$now = Get-Date; $lastMonth = $now.AddMonths(-1).ToString('yyyy-MM'); $twoMonthsAgo = $now.AddMonths(-2).ToString('yyyy-MM'); Write-Host '   - Mois dernier:' $lastMonth '(AUTORISÉ)'; Write-Host '   - Il y a 2 mois:' $twoMonthsAgo '(AUTORISÉ)'; Write-Host '   - 2024-01 (AUTORISÉ si passé)'"

echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 Sélectionnez un secteur et un client
echo.
echo 5. 📅 TESTEZ LE CHAMP PÉRIODE:
echo.
echo    a) 🟢 TEST VALIDE:
powershell -Command "$lastMonth = (Get-Date).AddMonths(-1).ToString('yyyy-MM'); Write-Host '       - Saisissez:' $lastMonth; Write-Host '       - Résultat attendu: Pas d''erreur, champ vert'"

echo.
echo    b) 🔴 TEST MOIS ACTUEL:
powershell -Command "$currentMonth = (Get-Date).ToString('yyyy-MM'); Write-Host '       - Saisissez:' $currentMonth; Write-Host '       - Résultat attendu: Bouton rouge avec message d''erreur'"

echo.
echo    c) 🔴 TEST MOIS FUTUR:
powershell -Command "$nextMonth = (Get-Date).AddMonths(1).ToString('yyyy-MM'); Write-Host '       - Saisissez:' $nextMonth; Write-Host '       - Résultat attendu: Bouton rouge avec message d''erreur'"

echo.
echo    d) 🔴 TEST FORMAT INVALIDE:
echo       - Saisissez: 2024/12 ou 24-12
echo       - Résultat attendu: Bouton rouge avec message d'erreur
echo.
echo 📊 COMPORTEMENT ATTENDU:
echo.
echo ✅ PÉRIODE VALIDE (mois précédent):
echo    📅 Champ: [2024-12] (bordure normale)
echo    ✅ Pas de message d'erreur
echo    ✅ Soumission autorisée
echo.
echo ❌ PÉRIODE INVALIDE (mois actuel/futur):
echo    📅 Champ: [2025-01] (bordure rouge)
echo    🔴 Bouton rouge: "❌ Impossible de saisir..."
echo    ❌ Soumission bloquée avec alerte
echo.
echo 🔍 MESSAGES D'ERREUR ATTENDUS:
echo.
echo 📅 Mois actuel:
echo    "❌ Impossible de saisir le mois actuel (Janvier 2025).
echo     Seulement les mois précédents sont autorisés."
echo.
echo 📅 Mois futur:
echo    "❌ Impossible de saisir un mois futur (Février 2025).
echo     Seulement les mois précédents sont autorisés."
echo.
echo 📅 Format invalide:
echo    "Format invalide. Utilisez YYYY-MM (ex: 2024-12)"
echo.
echo 🔧 FONCTIONNALITÉS TESTÉES:
echo.
echo ✅ Validation en temps réel lors de la saisie
echo ✅ Message d'erreur dans un bouton rouge cliquable
echo ✅ Bordure rouge sur le champ en erreur
echo ✅ Blocage de la soumission si période invalide
echo ✅ Alerte explicative lors de la soumission
echo ✅ Noms des mois en français dans les messages
echo.
echo 🌐 URLS DE TEST:
echo    - Application: http://localhost:19006
echo    - Console logs: F12 → Console (pour voir les validations)
echo.
echo ✅ VALIDATION PÉRIODE IMPLÉMENTÉE !
echo    Testez maintenant la saisie de différentes périodes
echo    et vérifiez que seuls les mois précédents sont acceptés.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
