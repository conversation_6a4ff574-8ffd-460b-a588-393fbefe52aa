{"version": 3, "names": ["_createPlugin", "require", "_default", "exports", "default", "createPlugin", "name", "development"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @babel/development/plugin-name */\n\nimport createPlugin from \"./create-plugin.ts\";\n\nexport default createPlugin({\n  name: \"transform-react-jsx\",\n  development: false,\n});\n\nexport type { Options } from \"./create-plugin.ts\";\n"], "mappings": ";;;;;;AAEA,IAAAA,aAAA,GAAAC,OAAA;AAA8C,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE/B,IAAAC,qBAAY,EAAC;EAC1BC,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EAAE;AACf,CAAC,CAAC", "ignoreList": []}