{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport ActivityIndicator from \"react-native-web/dist/exports/ActivityIndicator\";\nimport SafeAreaView from \"react-native-web/dist/exports/SafeAreaView\";\nimport KeyboardAvoidingView from \"react-native-web/dist/exports/KeyboardAvoidingView\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport { Picker } from '@react-native-picker/picker';\nimport SimpleGoogleMap from \"../components/SimpleGoogleMap\";\nimport { jsxs as _jsxs, jsx as _jsx } from \"react/jsx-runtime\";\nvar ConsommationScreen = function ConsommationScreen(_ref) {\n  var _selectedClientInfo$l, _selectedClientInfo$l2;\n  var navigation = _ref.navigation,\n    route = _ref.route;\n  var _route$params = route.params,\n    client = _route$params.client,\n    user = _route$params.user;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    loading = _useState2[0],\n    setLoading = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    contract = _useState4[0],\n    setContract = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    lastConsommation = _useState6[0],\n    setLastConsommation = _useState6[1];\n  var _useState7 = useState([]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    secteurs = _useState8[0],\n    setSecteurs = _useState8[1];\n  var _useState9 = useState(''),\n    _useState0 = _slicedToArray(_useState9, 2),\n    selectedSecteur = _useState0[0],\n    setSelectedSecteur = _useState0[1];\n  var _useState1 = useState(null),\n    _useState10 = _slicedToArray(_useState1, 2),\n    selectedSecteurInfo = _useState10[0],\n    setSelectedSecteurInfo = _useState10[1];\n  var _useState11 = useState([]),\n    _useState12 = _slicedToArray(_useState11, 2),\n    clientsBySecteur = _useState12[0],\n    setClientsBySecteur = _useState12[1];\n  var _useState13 = useState(''),\n    _useState14 = _slicedToArray(_useState13, 2),\n    selectedClient = _useState14[0],\n    setSelectedClient = _useState14[1];\n  var _useState15 = useState(null),\n    _useState16 = _slicedToArray(_useState15, 2),\n    selectedClientInfo = _useState16[0],\n    setSelectedClientInfo = _useState16[1];\n  var _useState17 = useState(false),\n    _useState18 = _slicedToArray(_useState17, 2),\n    showMap = _useState18[0],\n    setShowMap = _useState18[1];\n  var _useState19 = useState({\n      periode: '',\n      consommationPre: '',\n      consommationActuelle: '',\n      jours: '',\n      idtranch: 1,\n      status: 'En cours'\n    }),\n    _useState20 = _slicedToArray(_useState19, 2),\n    formData = _useState20[0],\n    setFormData = _useState20[1];\n  var API_BASE_URL = 'http://localhost:4002';\n  useEffect(function () {\n    fetchClientContract();\n    fetchLastConsommation();\n    generateCurrentPeriod();\n    fetchSecteurs();\n  }, []);\n  var generateCurrentPeriod = function generateCurrentPeriod() {\n    var now = new Date();\n    var periode = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n    setFormData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        periode: periode\n      });\n    });\n  };\n  var fetchClientContract = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      var clientId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n      try {\n        var targetClientId = clientId || (client == null ? void 0 : client.idclient);\n        if (!targetClientId) return;\n        var response = yield fetch(`${API_BASE_URL}/api/client-contract/${targetClientId}`);\n        var data = yield response.json();\n        if (data.success && data.contract) {\n          setContract(data.contract);\n          console.log(`✅ Contrat récupéré pour le client ${targetClientId}`);\n        }\n      } catch (error) {\n        console.error('Erreur récupération contrat:', error);\n      }\n    });\n    return function fetchClientContract() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var fetchLastConsommation = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      var clientId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n      try {\n        var targetClientId = clientId || (client == null ? void 0 : client.idclient);\n        if (!targetClientId) return;\n        var response = yield fetch(`${API_BASE_URL}/api/clients/${targetClientId}/last-consommation`);\n        var data = yield response.json();\n        if (data.success && data.data) {\n          setLastConsommation(data.data);\n          setFormData(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              consommationPre: data.data.consommationactuelle.toString()\n            });\n          });\n          console.log(`✅ Dernière consommation récupérée pour le client ${targetClientId}`);\n        }\n      } catch (error) {\n        console.error('Erreur récupération dernière consommation:', error);\n      }\n    });\n    return function fetchLastConsommation() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var fetchSecteurs = function () {\n    var _ref4 = _asyncToGenerator(function* () {\n      try {\n        console.log('🔍 Récupération des secteurs...');\n        var response = yield fetch(`${API_BASE_URL}/api/secteurs`);\n        var data = yield response.json();\n        if (data.success) {\n          setSecteurs(data.data);\n          console.log(`✅ ${data.data.length} secteurs récupérés`);\n        }\n      } catch (error) {\n        console.error('Erreur récupération secteurs:', error);\n      }\n    });\n    return function fetchSecteurs() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var fetchClientsBySecteur = function () {\n    var _ref5 = _asyncToGenerator(function* (secteurId) {\n      if (!secteurId) return;\n      try {\n        console.log(`🔍 Récupération des clients du secteur ${secteurId}...`);\n        var response = yield fetch(`${API_BASE_URL}/api/secteurs/${secteurId}/clients`);\n        var data = yield response.json();\n        if (data.success) {\n          setClientsBySecteur(data.data);\n          console.log(`✅ ${data.data.length} clients récupérés pour le secteur ${secteurId}`);\n          if (data.data.length > 0) {\n            setShowMap(true);\n          }\n        }\n      } catch (error) {\n        console.error('Erreur récupération clients:', error);\n      }\n    });\n    return function fetchClientsBySecteur(_x) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  var handleSecteurChange = function handleSecteurChange(secteurId) {\n    setSelectedSecteur(secteurId);\n    setClientsBySecteur([]);\n    setSelectedClient('');\n    setSelectedClientInfo(null);\n    setShowMap(false);\n    var secteurInfo = secteurs.find(function (s) {\n      return s.ids.toString() === secteurId;\n    });\n    setSelectedSecteurInfo(secteurInfo);\n    if (secteurId) {\n      fetchClientsBySecteur(secteurId);\n    }\n  };\n  var handleClientChange = function handleClientChange(clientId) {\n    setSelectedClient(clientId);\n    var clientInfo = clientsBySecteur.find(function (c) {\n      return c.idclient.toString() === clientId;\n    });\n    setSelectedClientInfo(clientInfo);\n    if (clientInfo) {\n      console.log(`✅ Client sélectionné: ${clientInfo.nom} ${clientInfo.prenom}`);\n      fetchClientContract(clientInfo.idclient);\n      fetchLastConsommation(clientInfo.idclient);\n    }\n  };\n  var openGoogleMaps = function openGoogleMaps() {\n    if (clientsBySecteur.length === 0) {\n      Alert.alert('Information', 'Sélectionnez un secteur avec des clients pour voir la carte');\n      return;\n    }\n    var secteurInfo = secteurs.find(function (s) {\n      return s.ids.toString() === selectedSecteur;\n    });\n    if (!secteurInfo) return;\n    var baseUrl = 'https://www.google.com/maps/dir/';\n    var centerLat = secteurInfo.latitude;\n    var centerLng = secteurInfo.longitude;\n    var markers = clientsBySecteur.map(function (client) {\n      return `${client.latitude},${client.longitude}`;\n    }).join('/');\n    var mapsUrl = `${baseUrl}${centerLat},${centerLng}/${markers}`;\n    if (typeof window !== 'undefined') {\n      window.open(mapsUrl, '_blank');\n    } else {\n      import('react-native').then(function (_ref6) {\n        var Linking = _ref6.Linking;\n        Linking.openURL(mapsUrl);\n      });\n    }\n  };\n  var calculateJours = function calculateJours() {\n    if (lastConsommation && formData.periode) {\n      try {\n        var currentPeriod = new Date(formData.periode + '-01');\n        var lastPeriod = new Date(lastConsommation.periode + '-01');\n        var diffTime = Math.abs(currentPeriod - lastPeriod);\n        var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        setFormData(function (prev) {\n          return _objectSpread(_objectSpread({}, prev), {}, {\n            jours: diffDays.toString()\n          });\n        });\n      } catch (error) {\n        console.error('Erreur calcul jours:', error);\n      }\n    }\n  };\n  useEffect(function () {\n    calculateJours();\n  }, [formData.periode, lastConsommation]);\n  var validateForm = function validateForm() {\n    if (!formData.periode) {\n      Alert.alert('Erreur', 'Veuillez saisir la période');\n      return false;\n    }\n    if (!formData.consommationActuelle) {\n      Alert.alert('Erreur', 'Veuillez saisir la consommation actuelle');\n      return false;\n    }\n    if (parseInt(formData.consommationActuelle) <= parseInt(formData.consommationPre || 0)) {\n      Alert.alert('Erreur', 'La consommation actuelle doit être supérieure à la consommation précédente');\n      return false;\n    }\n    if (!contract) {\n      Alert.alert('Erreur', 'Aucun contrat trouvé pour ce client');\n      return false;\n    }\n    return true;\n  };\n  var handleSubmit = function () {\n    var _ref7 = _asyncToGenerator(function* () {\n      if (!validateForm()) return;\n      setLoading(true);\n      try {\n        var consommationData = {\n          consommationpre: parseInt(formData.consommationPre || 0),\n          consommationactuelle: parseInt(formData.consommationActuelle),\n          idcont: contract.idcontract,\n          idtech: (user == null ? void 0 : user.idtech) || 1,\n          idtranch: formData.idtranch,\n          jours: parseInt(formData.jours || 30),\n          periode: formData.periode,\n          status: formData.status\n        };\n        var response = yield fetch(`${API_BASE_URL}/api/consommations`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(consommationData)\n        });\n        var result = yield response.json();\n        if (result.success) {\n          Alert.alert('Succès', 'Consommation enregistrée avec succès !', [{\n            text: 'OK',\n            onPress: function onPress() {\n              return navigation.goBack();\n            }\n          }]);\n        } else {\n          Alert.alert('Erreur', result.message || 'Erreur lors de l\\'enregistrement');\n        }\n      } catch (error) {\n        console.error('Erreur soumission:', error);\n        Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);\n      } finally {\n        setLoading(false);\n      }\n    });\n    return function handleSubmit() {\n      return _ref7.apply(this, arguments);\n    };\n  }();\n  return _jsx(SafeAreaView, {\n    style: styles.container,\n    children: _jsx(KeyboardAvoidingView, {\n      behavior: Platform.OS === 'ios' ? 'padding' : 'height',\n      style: styles.keyboardView,\n      children: _jsxs(ScrollView, {\n        style: styles.scrollView,\n        showsVerticalScrollIndicator: false,\n        children: [_jsxs(View, {\n          style: styles.clientHeader,\n          children: [_jsxs(Text, {\n            style: styles.clientName,\n            children: [client.nom, \" \", client.prenom]\n          }), _jsxs(Text, {\n            style: styles.clientDetail,\n            children: [\"\\uD83D\\uDCCD \", client.adresse]\n          }), _jsxs(Text, {\n            style: styles.clientDetail,\n            children: [\"\\uD83C\\uDFD9\\uFE0F \", client.ville]\n          }), contract && _jsxs(Text, {\n            style: styles.clientDetail,\n            children: [\"\\uD83D\\uDD27 Compteur: \", contract.marquecompteur]\n          })]\n        }), _jsxs(View, {\n          style: styles.form,\n          children: [_jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"\\uD83D\\uDDFA\\uFE0F Secteur *\"\n            }), _jsx(View, {\n              style: styles.pickerContainer,\n              children: _jsxs(Picker, {\n                selectedValue: selectedSecteur,\n                onValueChange: handleSecteurChange,\n                style: styles.picker,\n                children: [_jsx(Picker.Item, {\n                  label: \"-- S\\xE9lectionnez un secteur --\",\n                  value: \"\"\n                }), secteurs.map(function (secteur) {\n                  return _jsx(Picker.Item, {\n                    label: secteur.nom,\n                    value: secteur.ids.toString()\n                  }, secteur.ids);\n                })]\n              })\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"\\uD83D\\uDC64 Client *\"\n            }), selectedSecteur ? clientsBySecteur.length > 0 ? _jsx(View, {\n              style: styles.pickerContainer,\n              children: _jsxs(Picker, {\n                selectedValue: selectedClient,\n                onValueChange: handleClientChange,\n                style: styles.picker,\n                children: [_jsx(Picker.Item, {\n                  label: \"-- S\\xE9lectionnez un client --\",\n                  value: \"\"\n                }), clientsBySecteur.map(function (client, index) {\n                  return _jsx(Picker.Item, {\n                    label: `${index + 1}. ${client.nom} ${client.prenom} - ${client.ville} (${client.tel || 'N/A'})`,\n                    value: client.idclient.toString()\n                  }, client.idclient);\n                })]\n              })\n            }) : _jsx(View, {\n              style: styles.noClientsContainer,\n              children: _jsx(Text, {\n                style: styles.noClientsText,\n                children: \"Aucun client dans ce secteur\"\n              })\n            }) : _jsx(View, {\n              style: styles.noSecteurContainer,\n              children: _jsx(Text, {\n                style: styles.noSecteurText,\n                children: \"S\\xE9lectionnez d'abord un secteur\"\n              })\n            })]\n          }), selectedClientInfo && _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"\\u2139\\uFE0F Informations du Client\"\n            }), _jsxs(View, {\n              style: styles.clientInfoContainer,\n              children: [_jsxs(Text, {\n                style: styles.clientInfoName,\n                children: [\"\\uD83D\\uDC64 \", selectedClientInfo.nom, \" \", selectedClientInfo.prenom]\n              }), _jsxs(Text, {\n                style: styles.clientInfoAddress,\n                children: [\"\\uD83C\\uDFE0 \", selectedClientInfo.adresse, \", \", selectedClientInfo.ville]\n              }), _jsxs(Text, {\n                style: styles.clientInfoContact,\n                children: [\"\\uD83D\\uDCDE \", selectedClientInfo.tel, \" | \\uD83D\\uDCE7 \", selectedClientInfo.email]\n              }), _jsxs(Text, {\n                style: styles.clientInfoSector,\n                children: [\"\\uD83D\\uDDFA\\uFE0F Secteur: \", selectedClientInfo.secteur_nom]\n              }), _jsxs(Text, {\n                style: styles.clientInfoCoords,\n                children: [\"\\uD83D\\uDCCD GPS: \", (_selectedClientInfo$l = selectedClientInfo.latitude) == null ? void 0 : _selectedClientInfo$l.toFixed(6), \", \", (_selectedClientInfo$l2 = selectedClientInfo.longitude) == null ? void 0 : _selectedClientInfo$l2.toFixed(6)]\n              })]\n            })]\n          }), selectedSecteur && _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"\\uD83D\\uDDFA\\uFE0F Carte du Secteur\"\n            }), _jsx(GoogleMapsEmbed, {\n              secteur: selectedSecteurInfo,\n              clients: clientsBySecteur\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"P\\xE9riode (YYYY-MM) *\"\n            }), _jsx(TextInput, {\n              style: styles.input,\n              value: formData.periode,\n              onChangeText: function onChangeText(text) {\n                return setFormData(function (prev) {\n                  return _objectSpread(_objectSpread({}, prev), {}, {\n                    periode: text\n                  });\n                });\n              },\n              placeholder: \"2024-01\",\n              maxLength: 7\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Consommation Pr\\xE9c\\xE9dente (m\\xB3)\"\n            }), _jsx(TextInput, {\n              style: [styles.input, styles.readOnlyInput],\n              value: formData.consommationPre,\n              editable: false,\n              placeholder: \"Automatique\"\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Consommation Actuelle (m\\xB3) *\"\n            }), _jsx(TextInput, {\n              style: styles.input,\n              value: formData.consommationActuelle,\n              onChangeText: function onChangeText(text) {\n                return setFormData(function (prev) {\n                  return _objectSpread(_objectSpread({}, prev), {}, {\n                    consommationActuelle: text\n                  });\n                });\n              },\n              placeholder: \"Saisir la consommation actuelle\",\n              keyboardType: \"numeric\"\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Nombre de jours\"\n            }), _jsx(TextInput, {\n              style: [styles.input, styles.readOnlyInput],\n              value: formData.jours,\n              editable: false,\n              placeholder: \"Calcul\\xE9 automatiquement\"\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Statut\"\n            }), _jsx(TextInput, {\n              style: styles.input,\n              value: formData.status,\n              onChangeText: function onChangeText(text) {\n                return setFormData(function (prev) {\n                  return _objectSpread(_objectSpread({}, prev), {}, {\n                    status: text\n                  });\n                });\n              },\n              placeholder: \"En cours\"\n            })]\n          })]\n        }), _jsxs(View, {\n          style: styles.buttonContainer,\n          children: [_jsx(TouchableOpacity, {\n            style: styles.cancelButton,\n            onPress: function onPress() {\n              return navigation.goBack();\n            },\n            children: _jsx(Text, {\n              style: styles.cancelButtonText,\n              children: \"Annuler\"\n            })\n          }), _jsx(TouchableOpacity, {\n            style: [styles.submitButton, loading && styles.disabledButton],\n            onPress: handleSubmit,\n            disabled: loading,\n            children: loading ? _jsx(ActivityIndicator, {\n              color: \"#fff\"\n            }) : _jsx(Text, {\n              style: styles.submitButtonText,\n              children: \"Enregistrer\"\n            })\n          })]\n        })]\n      })\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  keyboardView: {\n    flex: 1\n  },\n  scrollView: {\n    flex: 1\n  },\n  clientHeader: {\n    backgroundColor: '#007AFF',\n    padding: 20,\n    alignItems: 'center'\n  },\n  clientName: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginBottom: 5\n  },\n  clientDetail: {\n    fontSize: 14,\n    color: '#fff',\n    opacity: 0.9,\n    marginBottom: 2\n  },\n  form: {\n    padding: 20\n  },\n  formGroup: {\n    marginBottom: 20\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 8\n  },\n  input: {\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    fontSize: 16,\n    backgroundColor: '#fff'\n  },\n  readOnlyInput: {\n    backgroundColor: '#f8f9fa',\n    color: '#666'\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    padding: 20,\n    paddingTop: 0\n  },\n  cancelButton: {\n    flex: 1,\n    backgroundColor: '#f8f9fa',\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginRight: 10\n  },\n  cancelButtonText: {\n    color: '#666',\n    fontWeight: 'bold',\n    fontSize: 16\n  },\n  submitButton: {\n    flex: 1,\n    backgroundColor: '#007AFF',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginLeft: 10\n  },\n  submitButtonText: {\n    color: '#fff',\n    fontWeight: 'bold',\n    fontSize: 16\n  },\n  disabledButton: {\n    backgroundColor: '#ccc'\n  },\n  pickerContainer: {\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    backgroundColor: '#fff'\n  },\n  picker: {\n    height: 50\n  },\n  noClientsContainer: {\n    backgroundColor: '#fff3cd',\n    padding: 15,\n    borderRadius: 10,\n    borderWidth: 1,\n    borderColor: '#ffeaa7'\n  },\n  noClientsText: {\n    color: '#856404',\n    textAlign: 'center',\n    fontStyle: 'italic'\n  },\n  noSecteurContainer: {\n    backgroundColor: '#f8d7da',\n    padding: 15,\n    borderRadius: 10,\n    borderWidth: 1,\n    borderColor: '#f5c6cb'\n  },\n  noSecteurText: {\n    color: '#721c24',\n    textAlign: 'center',\n    fontStyle: 'italic'\n  },\n  clientInfoContainer: {\n    backgroundColor: '#f8f9fa',\n    padding: 15,\n    borderRadius: 10,\n    borderLeftWidth: 4,\n    borderLeftColor: '#007AFF'\n  },\n  clientInfoName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 8\n  },\n  clientInfoAddress: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 6\n  },\n  clientInfoContact: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 6\n  },\n  clientInfoSector: {\n    fontSize: 14,\n    color: '#28a745',\n    fontWeight: 'bold',\n    marginBottom: 6\n  },\n  clientInfoCoords: {\n    fontSize: 12,\n    color: '#999',\n    fontFamily: 'monospace'\n  }\n});\nexport default ConsommationScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "TextInput", "TouchableOpacity", "StyleSheet", "<PERSON><PERSON>", "ScrollView", "ActivityIndicator", "SafeAreaView", "KeyboardAvoidingView", "Platform", "Dimensions", "Picker", "SimpleGoogleMap", "jsxs", "_jsxs", "jsx", "_jsx", "ConsommationScreen", "_ref", "_selectedClientInfo$l", "_selectedClientInfo$l2", "navigation", "route", "_route$params", "params", "client", "user", "_useState", "_useState2", "_slicedToArray", "loading", "setLoading", "_useState3", "_useState4", "contract", "setContract", "_useState5", "_useState6", "lastConsommation", "setLastConsommation", "_useState7", "_useState8", "secteurs", "setSecteurs", "_useState9", "_useState0", "selectedSec<PERSON>ur", "setSelectedSecteur", "_useState1", "_useState10", "selectedSecteurInfo", "setSelectedSecteurInfo", "_useState11", "_useState12", "clientsBySecteur", "setClientsBySecteur", "_useState13", "_useState14", "selectedClient", "setSelectedClient", "_useState15", "_useState16", "selectedClientInfo", "setSelectedClientInfo", "_useState17", "_useState18", "showMap", "setShowMap", "_useState19", "periode", "consommationPre", "consommationActuelle", "jours", "idtranch", "status", "_useState20", "formData", "setFormData", "API_BASE_URL", "fetchClientContract", "fetchLastConsommation", "generateCurrentPeriod", "fetchSecteurs", "now", "Date", "getFullYear", "String", "getMonth", "padStart", "prev", "_objectSpread", "_ref2", "_asyncToGenerator", "clientId", "arguments", "length", "undefined", "targetClientId", "idclient", "response", "fetch", "data", "json", "success", "console", "log", "error", "apply", "_ref3", "consommationactuelle", "toString", "_ref4", "fetchClientsBySecteur", "_ref5", "secteurId", "_x", "handleSecteurChange", "secteurInfo", "find", "s", "ids", "handleClientChange", "clientInfo", "c", "nom", "prenom", "openGoogleMaps", "alert", "baseUrl", "centerLat", "latitude", "centerLng", "longitude", "markers", "map", "join", "mapsUrl", "window", "open", "then", "_ref6", "Linking", "openURL", "calculateJours", "currentPeriod", "last<PERSON><PERSON><PERSON>", "diffTime", "Math", "abs", "diffDays", "ceil", "validateForm", "parseInt", "handleSubmit", "_ref7", "consommationData", "consommationpre", "idcont", "idcontract", "idtech", "method", "headers", "body", "JSON", "stringify", "result", "text", "onPress", "goBack", "message", "style", "styles", "container", "children", "behavior", "OS", "keyboard<PERSON>iew", "scrollView", "showsVerticalScrollIndicator", "clientHeader", "clientName", "clientDetail", "adresse", "ville", "marquecompteur", "form", "formGroup", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON>", "onValueChange", "picker", "<PERSON><PERSON>", "value", "secteur", "index", "tel", "noClientsContainer", "noClientsText", "noSecteurContainer", "noSecteurText", "clientInfoContainer", "clientInfoName", "clientInfoAddress", "clientInfoContact", "email", "clientInfoSector", "secteur_nom", "clientInfoCoords", "toFixed", "GoogleMapsEmbed", "clients", "input", "onChangeText", "placeholder", "max<PERSON><PERSON><PERSON>", "readOnlyInput", "editable", "keyboardType", "buttonContainer", "cancelButton", "cancelButtonText", "submitButton", "disabled<PERSON><PERSON>on", "disabled", "color", "submitButtonText", "create", "flex", "backgroundColor", "padding", "alignItems", "fontSize", "fontWeight", "marginBottom", "opacity", "borderWidth", "borderColor", "borderRadius", "flexDirection", "justifyContent", "paddingTop", "marginRight", "marginLeft", "height", "textAlign", "fontStyle", "borderLeftWidth", "borderLeftColor", "fontFamily"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/mobile/src/screens/ConsommationScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  View,\n  Text,\n  TextInput,\n  TouchableOpacity,\n  StyleSheet,\n  Alert,\n  ScrollView,\n  ActivityIndicator,\n  SafeAreaView,\n  KeyboardAvoidingView,\n  Platform,\n  Dimensions,\n} from 'react-native';\nimport { Picker } from '@react-native-picker/picker';\nimport SimpleGoogleMap from '../components/SimpleGoogleMap';\n\nconst ConsommationScreen = ({ navigation, route }) => {\n  const { client, user } = route.params;\n  const [loading, setLoading] = useState(false);\n  const [contract, setContract] = useState(null);\n  const [lastConsommation, setLastConsommation] = useState(null);\n  const [secteurs, setSecteurs] = useState([]);\n  const [selectedSecteur, setSelectedSecteur] = useState('');\n  const [selectedSecteurInfo, setSelectedSecteurInfo] = useState(null);\n  const [clientsBySecteur, setClientsBySecteur] = useState([]);\n  const [selectedClient, setSelectedClient] = useState('');\n  const [selectedClientInfo, setSelectedClientInfo] = useState(null);\n  const [showMap, setShowMap] = useState(false);\n  const [formData, setFormData] = useState({\n    periode: '',\n    consommationPre: '',\n    consommationActuelle: '',\n    jours: '',\n    idtranch: 1,\n    status: 'En cours'\n  });\n\n  const API_BASE_URL = 'http://localhost:4002'; // Serveur sur port 4002\n\n  useEffect(() => {\n    fetchClientContract();\n    fetchLastConsommation();\n    generateCurrentPeriod();\n    fetchSecteurs();\n  }, []);\n\n  const generateCurrentPeriod = () => {\n    const now = new Date();\n    const periode = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n    setFormData(prev => ({ ...prev, periode }));\n  };\n\n  const fetchClientContract = async (clientId = null) => {\n    try {\n      const targetClientId = clientId || client?.idclient;\n      if (!targetClientId) return;\n\n      const response = await fetch(`${API_BASE_URL}/api/client-contract/${targetClientId}`);\n      const data = await response.json();\n      if (data.success && data.contract) {\n        setContract(data.contract);\n        console.log(`✅ Contrat récupéré pour le client ${targetClientId}`);\n      }\n    } catch (error) {\n      console.error('Erreur récupération contrat:', error);\n    }\n  };\n\n  const fetchLastConsommation = async (clientId = null) => {\n    try {\n      const targetClientId = clientId || client?.idclient;\n      if (!targetClientId) return;\n\n      const response = await fetch(`${API_BASE_URL}/api/clients/${targetClientId}/last-consommation`);\n      const data = await response.json();\n      if (data.success && data.data) {\n        setLastConsommation(data.data);\n        setFormData(prev => ({\n          ...prev,\n          consommationPre: data.data.consommationactuelle.toString()\n        }));\n        console.log(`✅ Dernière consommation récupérée pour le client ${targetClientId}`);\n      }\n    } catch (error) {\n      console.error('Erreur récupération dernière consommation:', error);\n    }\n  };\n\n  const fetchSecteurs = async () => {\n    try {\n      console.log('🔍 Récupération des secteurs...');\n      const response = await fetch(`${API_BASE_URL}/api/secteurs`);\n      const data = await response.json();\n\n      if (data.success) {\n        setSecteurs(data.data);\n        console.log(`✅ ${data.data.length} secteurs récupérés`);\n      }\n    } catch (error) {\n      console.error('Erreur récupération secteurs:', error);\n    }\n  };\n\n  const fetchClientsBySecteur = async (secteurId) => {\n    if (!secteurId) return;\n\n    try {\n      console.log(`🔍 Récupération des clients du secteur ${secteurId}...`);\n      const response = await fetch(`${API_BASE_URL}/api/secteurs/${secteurId}/clients`);\n      const data = await response.json();\n\n      if (data.success) {\n        setClientsBySecteur(data.data);\n        console.log(`✅ ${data.data.length} clients récupérés pour le secteur ${secteurId}`);\n\n        if (data.data.length > 0) {\n          setShowMap(true);\n        }\n      }\n    } catch (error) {\n      console.error('Erreur récupération clients:', error);\n    }\n  };\n\n  const handleSecteurChange = (secteurId) => {\n    setSelectedSecteur(secteurId);\n    setClientsBySecteur([]);\n    setSelectedClient('');\n    setSelectedClientInfo(null);\n    setShowMap(false);\n\n    // Trouver les informations du secteur sélectionné\n    const secteurInfo = secteurs.find(s => s.ids.toString() === secteurId);\n    setSelectedSecteurInfo(secteurInfo);\n\n    if (secteurId) {\n      fetchClientsBySecteur(secteurId);\n    }\n  };\n\n  const handleClientChange = (clientId) => {\n    setSelectedClient(clientId);\n\n    // Trouver les informations du client sélectionné\n    const clientInfo = clientsBySecteur.find(c => c.idclient.toString() === clientId);\n    setSelectedClientInfo(clientInfo);\n\n    if (clientInfo) {\n      console.log(`✅ Client sélectionné: ${clientInfo.nom} ${clientInfo.prenom}`);\n\n      // Récupérer le contrat et la dernière consommation du client\n      fetchClientContract(clientInfo.idclient);\n      fetchLastConsommation(clientInfo.idclient);\n    }\n  };\n\n  const openGoogleMaps = () => {\n    if (clientsBySecteur.length === 0) {\n      Alert.alert('Information', 'Sélectionnez un secteur avec des clients pour voir la carte');\n      return;\n    }\n\n    const secteurInfo = secteurs.find(s => s.ids.toString() === selectedSecteur);\n    if (!secteurInfo) return;\n\n    // Construire l'URL Google Maps avec les marqueurs des clients\n    const baseUrl = 'https://www.google.com/maps/dir/';\n\n    // Centre de la carte sur le secteur\n    const centerLat = secteurInfo.latitude;\n    const centerLng = secteurInfo.longitude;\n\n    // Créer les marqueurs pour chaque client\n    const markers = clientsBySecteur.map(client =>\n      `${client.latitude},${client.longitude}`\n    ).join('/');\n\n    // URL complète avec tous les points\n    const mapsUrl = `${baseUrl}${centerLat},${centerLng}/${markers}`;\n\n    // Pour React Native Web, ouvrir dans une nouvelle fenêtre\n    if (typeof window !== 'undefined') {\n      window.open(mapsUrl, '_blank');\n    } else {\n      // Pour React Native mobile, utiliser Linking\n      import('react-native').then(({ Linking }) => {\n        Linking.openURL(mapsUrl);\n      });\n    }\n  };\n\n  const calculateJours = () => {\n    if (lastConsommation && formData.periode) {\n      try {\n        const currentPeriod = new Date(formData.periode + '-01');\n        const lastPeriod = new Date(lastConsommation.periode + '-01');\n        const diffTime = Math.abs(currentPeriod - lastPeriod);\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        setFormData(prev => ({ ...prev, jours: diffDays.toString() }));\n      } catch (error) {\n        console.error('Erreur calcul jours:', error);\n      }\n    }\n  };\n\n  useEffect(() => {\n    calculateJours();\n  }, [formData.periode, lastConsommation]);\n\n  const validateForm = () => {\n    if (!formData.periode) {\n      Alert.alert('Erreur', 'Veuillez saisir la période');\n      return false;\n    }\n    if (!formData.consommationActuelle) {\n      Alert.alert('Erreur', 'Veuillez saisir la consommation actuelle');\n      return false;\n    }\n    if (parseInt(formData.consommationActuelle) <= parseInt(formData.consommationPre || 0)) {\n      Alert.alert('Erreur', 'La consommation actuelle doit être supérieure à la consommation précédente');\n      return false;\n    }\n    if (!contract) {\n      Alert.alert('Erreur', 'Aucun contrat trouvé pour ce client');\n      return false;\n    }\n    return true;\n  };\n\n  const handleSubmit = async () => {\n    if (!validateForm()) return;\n\n    setLoading(true);\n    try {\n      const consommationData = {\n        consommationpre: parseInt(formData.consommationPre || 0),\n        consommationactuelle: parseInt(formData.consommationActuelle),\n        idcont: contract.idcontract,\n        idtech: user?.idtech || 1,\n        idtranch: formData.idtranch,\n        jours: parseInt(formData.jours || 30),\n        periode: formData.periode,\n        status: formData.status\n      };\n\n      const response = await fetch(`${API_BASE_URL}/api/consommations`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(consommationData),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        Alert.alert(\n          'Succès',\n          'Consommation enregistrée avec succès !',\n          [\n            {\n              text: 'OK',\n              onPress: () => navigation.goBack()\n            }\n          ]\n        );\n      } else {\n        Alert.alert('Erreur', result.message || 'Erreur lors de l\\'enregistrement');\n      }\n    } catch (error) {\n      console.error('Erreur soumission:', error);\n      Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <KeyboardAvoidingView \n        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}\n        style={styles.keyboardView}\n      >\n        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>\n          {/* En-tête Client */}\n          <View style={styles.clientHeader}>\n            <Text style={styles.clientName}>{client.nom} {client.prenom}</Text>\n            <Text style={styles.clientDetail}>📍 {client.adresse}</Text>\n            <Text style={styles.clientDetail}>🏙️ {client.ville}</Text>\n            {contract && (\n              <Text style={styles.clientDetail}>🔧 Compteur: {contract.marquecompteur}</Text>\n            )}\n          </View>\n\n          {/* Sélection de Secteur */}\n          <View style={styles.form}>\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>🗺️ Secteur *</Text>\n              <View style={styles.pickerContainer}>\n                <Picker\n                  selectedValue={selectedSecteur}\n                  onValueChange={handleSecteurChange}\n                  style={styles.picker}\n                >\n                  <Picker.Item label=\"-- Sélectionnez un secteur --\" value=\"\" />\n                  {secteurs.map((secteur) => (\n                    <Picker.Item\n                      key={secteur.ids}\n                      label={secteur.nom}\n                      value={secteur.ids.toString()}\n                    />\n                  ))}\n                </Picker>\n              </View>\n            </View>\n\n            {/* Champ Client - Deuxième champ */}\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>👤 Client *</Text>\n              {selectedSecteur ? (\n                clientsBySecteur.length > 0 ? (\n                  <View style={styles.pickerContainer}>\n                    <Picker\n                      selectedValue={selectedClient}\n                      onValueChange={handleClientChange}\n                      style={styles.picker}\n                    >\n                      <Picker.Item label=\"-- Sélectionnez un client --\" value=\"\" />\n                      {clientsBySecteur.map((client, index) => (\n                        <Picker.Item\n                          key={client.idclient}\n                          label={`${index + 1}. ${client.nom} ${client.prenom} - ${client.ville} (${client.tel || 'N/A'})`}\n                          value={client.idclient.toString()}\n                        />\n                      ))}\n                    </Picker>\n                  </View>\n                ) : (\n                  <View style={styles.noClientsContainer}>\n                    <Text style={styles.noClientsText}>\n                      Aucun client dans ce secteur\n                    </Text>\n                  </View>\n                )\n              ) : (\n                <View style={styles.noSecteurContainer}>\n                  <Text style={styles.noSecteurText}>\n                    Sélectionnez d'abord un secteur\n                  </Text>\n                </View>\n              )}\n            </View>\n\n            {/* Informations du client sélectionné */}\n            {selectedClientInfo && (\n              <View style={styles.formGroup}>\n                <Text style={styles.label}>ℹ️ Informations du Client</Text>\n                <View style={styles.clientInfoContainer}>\n                  <Text style={styles.clientInfoName}>\n                    👤 {selectedClientInfo.nom} {selectedClientInfo.prenom}\n                  </Text>\n                  <Text style={styles.clientInfoAddress}>\n                    🏠 {selectedClientInfo.adresse}, {selectedClientInfo.ville}\n                  </Text>\n                  <Text style={styles.clientInfoContact}>\n                    📞 {selectedClientInfo.tel} | 📧 {selectedClientInfo.email}\n                  </Text>\n                  <Text style={styles.clientInfoSector}>\n                    🗺️ Secteur: {selectedClientInfo.secteur_nom}\n                  </Text>\n                  <Text style={styles.clientInfoCoords}>\n                    📍 GPS: {selectedClientInfo.latitude?.toFixed(6)}, {selectedClientInfo.longitude?.toFixed(6)}\n                  </Text>\n                </View>\n              </View>\n            )}\n\n            {/* Carte Google Maps Intégrée */}\n            {selectedSecteur && (\n              <View style={styles.formGroup}>\n                <Text style={styles.label}>🗺️ Carte du Secteur</Text>\n                <GoogleMapsEmbed\n                  secteur={selectedSecteurInfo}\n                  clients={clientsBySecteur}\n                />\n              </View>\n            )}\n\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Période (YYYY-MM) *</Text>\n              <TextInput\n                style={styles.input}\n                value={formData.periode}\n                onChangeText={(text) => setFormData(prev => ({ ...prev, periode: text }))}\n                placeholder=\"2024-01\"\n                maxLength={7}\n              />\n            </View>\n\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Consommation Précédente (m³)</Text>\n              <TextInput\n                style={[styles.input, styles.readOnlyInput]}\n                value={formData.consommationPre}\n                editable={false}\n                placeholder=\"Automatique\"\n              />\n            </View>\n\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Consommation Actuelle (m³) *</Text>\n              <TextInput\n                style={styles.input}\n                value={formData.consommationActuelle}\n                onChangeText={(text) => setFormData(prev => ({ ...prev, consommationActuelle: text }))}\n                placeholder=\"Saisir la consommation actuelle\"\n                keyboardType=\"numeric\"\n              />\n            </View>\n\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Nombre de jours</Text>\n              <TextInput\n                style={[styles.input, styles.readOnlyInput]}\n                value={formData.jours}\n                editable={false}\n                placeholder=\"Calculé automatiquement\"\n              />\n            </View>\n\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Statut</Text>\n              <TextInput\n                style={styles.input}\n                value={formData.status}\n                onChangeText={(text) => setFormData(prev => ({ ...prev, status: text }))}\n                placeholder=\"En cours\"\n              />\n            </View>\n          </View>\n\n          {/* Boutons */}\n          <View style={styles.buttonContainer}>\n            <TouchableOpacity\n              style={styles.cancelButton}\n              onPress={() => navigation.goBack()}\n            >\n              <Text style={styles.cancelButtonText}>Annuler</Text>\n            </TouchableOpacity>\n\n            <TouchableOpacity\n              style={[styles.submitButton, loading && styles.disabledButton]}\n              onPress={handleSubmit}\n              disabled={loading}\n            >\n              {loading ? (\n                <ActivityIndicator color=\"#fff\" />\n              ) : (\n                <Text style={styles.submitButtonText}>Enregistrer</Text>\n              )}\n            </TouchableOpacity>\n          </View>\n        </ScrollView>\n      </KeyboardAvoidingView>\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  keyboardView: {\n    flex: 1,\n  },\n  scrollView: {\n    flex: 1,\n  },\n  clientHeader: {\n    backgroundColor: '#007AFF',\n    padding: 20,\n    alignItems: 'center',\n  },\n  clientName: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginBottom: 5,\n  },\n  clientDetail: {\n    fontSize: 14,\n    color: '#fff',\n    opacity: 0.9,\n    marginBottom: 2,\n  },\n  form: {\n    padding: 20,\n  },\n  formGroup: {\n    marginBottom: 20,\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 8,\n  },\n  input: {\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    fontSize: 16,\n    backgroundColor: '#fff',\n  },\n  readOnlyInput: {\n    backgroundColor: '#f8f9fa',\n    color: '#666',\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    padding: 20,\n    paddingTop: 0,\n  },\n  cancelButton: {\n    flex: 1,\n    backgroundColor: '#f8f9fa',\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginRight: 10,\n  },\n  cancelButtonText: {\n    color: '#666',\n    fontWeight: 'bold',\n    fontSize: 16,\n  },\n  submitButton: {\n    flex: 1,\n    backgroundColor: '#007AFF',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginLeft: 10,\n  },\n  submitButtonText: {\n    color: '#fff',\n    fontWeight: 'bold',\n    fontSize: 16,\n  },\n  disabledButton: {\n    backgroundColor: '#ccc',\n  },\n  // Styles pour la sélection de secteur et client\n  pickerContainer: {\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    backgroundColor: '#fff',\n  },\n  picker: {\n    height: 50,\n  },\n  // Styles pour les messages d'état\n  noClientsContainer: {\n    backgroundColor: '#fff3cd',\n    padding: 15,\n    borderRadius: 10,\n    borderWidth: 1,\n    borderColor: '#ffeaa7',\n  },\n  noClientsText: {\n    color: '#856404',\n    textAlign: 'center',\n    fontStyle: 'italic',\n  },\n  noSecteurContainer: {\n    backgroundColor: '#f8d7da',\n    padding: 15,\n    borderRadius: 10,\n    borderWidth: 1,\n    borderColor: '#f5c6cb',\n  },\n  noSecteurText: {\n    color: '#721c24',\n    textAlign: 'center',\n    fontStyle: 'italic',\n  },\n  // Styles pour les informations du client\n  clientInfoContainer: {\n    backgroundColor: '#f8f9fa',\n    padding: 15,\n    borderRadius: 10,\n    borderLeftWidth: 4,\n    borderLeftColor: '#007AFF',\n  },\n  clientInfoName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 8,\n  },\n  clientInfoAddress: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 6,\n  },\n  clientInfoContact: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 6,\n  },\n  clientInfoSector: {\n    fontSize: 14,\n    color: '#28a745',\n    fontWeight: 'bold',\n    marginBottom: 6,\n  },\n  clientInfoCoords: {\n    fontSize: 12,\n    color: '#999',\n    fontFamily: 'monospace',\n  },\n});\n\nexport default ConsommationScreen;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,iBAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,oBAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAenD,SAASC,MAAM,QAAQ,6BAA6B;AACpD,OAAOC,eAAe;AAAsC,SAAAC,IAAA,IAAAC,KAAA,EAAAC,GAAA,IAAAC,IAAA;AAE5D,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,IAAA,EAA8B;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAAA,IAAxBC,UAAU,GAAAH,IAAA,CAAVG,UAAU;IAAEC,KAAK,GAAAJ,IAAA,CAALI,KAAK;EAC7C,IAAAC,aAAA,GAAyBD,KAAK,CAACE,MAAM;IAA7BC,MAAM,GAAAF,aAAA,CAANE,MAAM;IAAEC,IAAI,GAAAH,aAAA,CAAJG,IAAI;EACpB,IAAAC,SAAA,GAA8B9B,QAAQ,CAAC,KAAK,CAAC;IAAA+B,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAtCG,OAAO,GAAAF,UAAA;IAAEG,UAAU,GAAAH,UAAA;EAC1B,IAAAI,UAAA,GAAgCnC,QAAQ,CAAC,IAAI,CAAC;IAAAoC,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAvCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAAgDvC,QAAQ,CAAC,IAAI,CAAC;IAAAwC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAvDE,gBAAgB,GAAAD,UAAA;IAAEE,mBAAmB,GAAAF,UAAA;EAC5C,IAAAG,UAAA,GAAgC3C,QAAQ,CAAC,EAAE,CAAC;IAAA4C,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAArCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAA8C/C,QAAQ,CAAC,EAAE,CAAC;IAAAgD,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAAnDE,eAAe,GAAAD,UAAA;IAAEE,kBAAkB,GAAAF,UAAA;EAC1C,IAAAG,UAAA,GAAsDnD,QAAQ,CAAC,IAAI,CAAC;IAAAoD,WAAA,GAAApB,cAAA,CAAAmB,UAAA;IAA7DE,mBAAmB,GAAAD,WAAA;IAAEE,sBAAsB,GAAAF,WAAA;EAClD,IAAAG,WAAA,GAAgDvD,QAAQ,CAAC,EAAE,CAAC;IAAAwD,WAAA,GAAAxB,cAAA,CAAAuB,WAAA;IAArDE,gBAAgB,GAAAD,WAAA;IAAEE,mBAAmB,GAAAF,WAAA;EAC5C,IAAAG,WAAA,GAA4C3D,QAAQ,CAAC,EAAE,CAAC;IAAA4D,WAAA,GAAA5B,cAAA,CAAA2B,WAAA;IAAjDE,cAAc,GAAAD,WAAA;IAAEE,iBAAiB,GAAAF,WAAA;EACxC,IAAAG,WAAA,GAAoD/D,QAAQ,CAAC,IAAI,CAAC;IAAAgE,WAAA,GAAAhC,cAAA,CAAA+B,WAAA;IAA3DE,kBAAkB,GAAAD,WAAA;IAAEE,qBAAqB,GAAAF,WAAA;EAChD,IAAAG,WAAA,GAA8BnE,QAAQ,CAAC,KAAK,CAAC;IAAAoE,WAAA,GAAApC,cAAA,CAAAmC,WAAA;IAAtCE,OAAO,GAAAD,WAAA;IAAEE,UAAU,GAAAF,WAAA;EAC1B,IAAAG,WAAA,GAAgCvE,QAAQ,CAAC;MACvCwE,OAAO,EAAE,EAAE;MACXC,eAAe,EAAE,EAAE;MACnBC,oBAAoB,EAAE,EAAE;MACxBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACV,CAAC,CAAC;IAAAC,WAAA,GAAA9C,cAAA,CAAAuC,WAAA;IAPKQ,QAAQ,GAAAD,WAAA;IAAEE,WAAW,GAAAF,WAAA;EAS5B,IAAMG,YAAY,GAAG,uBAAuB;EAE5ChF,SAAS,CAAC,YAAM;IACdiF,mBAAmB,CAAC,CAAC;IACrBC,qBAAqB,CAAC,CAAC;IACvBC,qBAAqB,CAAC,CAAC;IACvBC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMD,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAClC,IAAME,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,IAAMf,OAAO,GAAG,GAAGc,GAAG,CAACE,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IACrFX,WAAW,CAAC,UAAAY,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;QAAEpB,OAAO,EAAPA;MAAO;IAAA,CAAG,CAAC;EAC7C,CAAC;EAED,IAAMU,mBAAmB;IAAA,IAAAY,KAAA,GAAAC,iBAAA,CAAG,aAA2B;MAAA,IAApBC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAChD,IAAI;QACF,IAAMG,cAAc,GAAGJ,QAAQ,KAAIpE,MAAM,oBAANA,MAAM,CAAEyE,QAAQ;QACnD,IAAI,CAACD,cAAc,EAAE;QAErB,IAAME,QAAQ,SAASC,KAAK,CAAC,GAAGtB,YAAY,wBAAwBmB,cAAc,EAAE,CAAC;QACrF,IAAMI,IAAI,SAASF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACnE,QAAQ,EAAE;UACjCC,WAAW,CAACkE,IAAI,CAACnE,QAAQ,CAAC;UAC1BsE,OAAO,CAACC,GAAG,CAAC,qCAAqCR,cAAc,EAAE,CAAC;QACpE;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAAA,gBAdK3B,mBAAmBA,CAAA;MAAA,OAAAY,KAAA,CAAAgB,KAAA,OAAAb,SAAA;IAAA;EAAA,GAcxB;EAED,IAAMd,qBAAqB;IAAA,IAAA4B,KAAA,GAAAhB,iBAAA,CAAG,aAA2B;MAAA,IAApBC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAClD,IAAI;QACF,IAAMG,cAAc,GAAGJ,QAAQ,KAAIpE,MAAM,oBAANA,MAAM,CAAEyE,QAAQ;QACnD,IAAI,CAACD,cAAc,EAAE;QAErB,IAAME,QAAQ,SAASC,KAAK,CAAC,GAAGtB,YAAY,gBAAgBmB,cAAc,oBAAoB,CAAC;QAC/F,IAAMI,IAAI,SAASF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;UAC7B9D,mBAAmB,CAAC8D,IAAI,CAACA,IAAI,CAAC;UAC9BxB,WAAW,CAAC,UAAAY,IAAI;YAAA,OAAAC,aAAA,CAAAA,aAAA,KACXD,IAAI;cACPnB,eAAe,EAAE+B,IAAI,CAACA,IAAI,CAACQ,oBAAoB,CAACC,QAAQ,CAAC;YAAC;UAAA,CAC1D,CAAC;UACHN,OAAO,CAACC,GAAG,CAAC,oDAAoDR,cAAc,EAAE,CAAC;QACnF;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MACpE;IACF,CAAC;IAAA,gBAlBK1B,qBAAqBA,CAAA;MAAA,OAAA4B,KAAA,CAAAD,KAAA,OAAAb,SAAA;IAAA;EAAA,GAkB1B;EAED,IAAMZ,aAAa;IAAA,IAAA6B,KAAA,GAAAnB,iBAAA,CAAG,aAAY;MAChC,IAAI;QACFY,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAMN,QAAQ,SAASC,KAAK,CAAC,GAAGtB,YAAY,eAAe,CAAC;QAC5D,IAAMuB,IAAI,SAASF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB5D,WAAW,CAAC0D,IAAI,CAACA,IAAI,CAAC;UACtBG,OAAO,CAACC,GAAG,CAAC,KAAKJ,IAAI,CAACA,IAAI,CAACN,MAAM,qBAAqB,CAAC;QACzD;MACF,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC;IAAA,gBAbKxB,aAAaA,CAAA;MAAA,OAAA6B,KAAA,CAAAJ,KAAA,OAAAb,SAAA;IAAA;EAAA,GAalB;EAED,IAAMkB,qBAAqB;IAAA,IAAAC,KAAA,GAAArB,iBAAA,CAAG,WAAOsB,SAAS,EAAK;MACjD,IAAI,CAACA,SAAS,EAAE;MAEhB,IAAI;QACFV,OAAO,CAACC,GAAG,CAAC,0CAA0CS,SAAS,KAAK,CAAC;QACrE,IAAMf,QAAQ,SAASC,KAAK,CAAC,GAAGtB,YAAY,iBAAiBoC,SAAS,UAAU,CAAC;QACjF,IAAMb,IAAI,SAASF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChBhD,mBAAmB,CAAC8C,IAAI,CAACA,IAAI,CAAC;UAC9BG,OAAO,CAACC,GAAG,CAAC,KAAKJ,IAAI,CAACA,IAAI,CAACN,MAAM,sCAAsCmB,SAAS,EAAE,CAAC;UAEnF,IAAIb,IAAI,CAACA,IAAI,CAACN,MAAM,GAAG,CAAC,EAAE;YACxB5B,UAAU,CAAC,IAAI,CAAC;UAClB;QACF;MACF,CAAC,CAAC,OAAOuC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAAA,gBAnBKM,qBAAqBA,CAAAG,EAAA;MAAA,OAAAF,KAAA,CAAAN,KAAA,OAAAb,SAAA;IAAA;EAAA,GAmB1B;EAED,IAAMsB,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIF,SAAS,EAAK;IACzCnE,kBAAkB,CAACmE,SAAS,CAAC;IAC7B3D,mBAAmB,CAAC,EAAE,CAAC;IACvBI,iBAAiB,CAAC,EAAE,CAAC;IACrBI,qBAAqB,CAAC,IAAI,CAAC;IAC3BI,UAAU,CAAC,KAAK,CAAC;IAGjB,IAAMkD,WAAW,GAAG3E,QAAQ,CAAC4E,IAAI,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACC,GAAG,CAACV,QAAQ,CAAC,CAAC,KAAKI,SAAS;IAAA,EAAC;IACtE/D,sBAAsB,CAACkE,WAAW,CAAC;IAEnC,IAAIH,SAAS,EAAE;MACbF,qBAAqB,CAACE,SAAS,CAAC;IAClC;EACF,CAAC;EAED,IAAMO,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAI5B,QAAQ,EAAK;IACvClC,iBAAiB,CAACkC,QAAQ,CAAC;IAG3B,IAAM6B,UAAU,GAAGpE,gBAAgB,CAACgE,IAAI,CAAC,UAAAK,CAAC;MAAA,OAAIA,CAAC,CAACzB,QAAQ,CAACY,QAAQ,CAAC,CAAC,KAAKjB,QAAQ;IAAA,EAAC;IACjF9B,qBAAqB,CAAC2D,UAAU,CAAC;IAEjC,IAAIA,UAAU,EAAE;MACdlB,OAAO,CAACC,GAAG,CAAC,yBAAyBiB,UAAU,CAACE,GAAG,IAAIF,UAAU,CAACG,MAAM,EAAE,CAAC;MAG3E9C,mBAAmB,CAAC2C,UAAU,CAACxB,QAAQ,CAAC;MACxClB,qBAAqB,CAAC0C,UAAU,CAACxB,QAAQ,CAAC;IAC5C;EACF,CAAC;EAED,IAAM4B,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAIxE,gBAAgB,CAACyC,MAAM,KAAK,CAAC,EAAE;MACjC3F,KAAK,CAAC2H,KAAK,CAAC,aAAa,EAAE,6DAA6D,CAAC;MACzF;IACF;IAEA,IAAMV,WAAW,GAAG3E,QAAQ,CAAC4E,IAAI,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACC,GAAG,CAACV,QAAQ,CAAC,CAAC,KAAKhE,eAAe;IAAA,EAAC;IAC5E,IAAI,CAACuE,WAAW,EAAE;IAGlB,IAAMW,OAAO,GAAG,kCAAkC;IAGlD,IAAMC,SAAS,GAAGZ,WAAW,CAACa,QAAQ;IACtC,IAAMC,SAAS,GAAGd,WAAW,CAACe,SAAS;IAGvC,IAAMC,OAAO,GAAG/E,gBAAgB,CAACgF,GAAG,CAAC,UAAA7G,MAAM;MAAA,OACzC,GAAGA,MAAM,CAACyG,QAAQ,IAAIzG,MAAM,CAAC2G,SAAS,EAAE;IAAA,CAC1C,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;IAGX,IAAMC,OAAO,GAAG,GAAGR,OAAO,GAAGC,SAAS,IAAIE,SAAS,IAAIE,OAAO,EAAE;IAGhE,IAAI,OAAOI,MAAM,KAAK,WAAW,EAAE;MACjCA,MAAM,CAACC,IAAI,CAACF,OAAO,EAAE,QAAQ,CAAC;IAChC,CAAC,MAAM;MAEL,MAAM,CAAC,cAAc,CAAC,CAACG,IAAI,CAAC,UAAAC,KAAA,EAAiB;QAAA,IAAdC,OAAO,GAAAD,KAAA,CAAPC,OAAO;QACpCA,OAAO,CAACC,OAAO,CAACN,OAAO,CAAC;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC;EAED,IAAMO,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAIzG,gBAAgB,IAAIsC,QAAQ,CAACP,OAAO,EAAE;MACxC,IAAI;QACF,IAAM2E,aAAa,GAAG,IAAI5D,IAAI,CAACR,QAAQ,CAACP,OAAO,GAAG,KAAK,CAAC;QACxD,IAAM4E,UAAU,GAAG,IAAI7D,IAAI,CAAC9C,gBAAgB,CAAC+B,OAAO,GAAG,KAAK,CAAC;QAC7D,IAAM6E,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,aAAa,GAAGC,UAAU,CAAC;QACrD,IAAMI,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC5DrE,WAAW,CAAC,UAAAY,IAAI;UAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;YAAEjB,KAAK,EAAE6E,QAAQ,CAACvC,QAAQ,CAAC;UAAC;QAAA,CAAG,CAAC;MAChE,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF;EACF,CAAC;EAED5G,SAAS,CAAC,YAAM;IACdiJ,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACnE,QAAQ,CAACP,OAAO,EAAE/B,gBAAgB,CAAC,CAAC;EAExC,IAAMiH,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,IAAI,CAAC3E,QAAQ,CAACP,OAAO,EAAE;MACrBjE,KAAK,CAAC2H,KAAK,CAAC,QAAQ,EAAE,4BAA4B,CAAC;MACnD,OAAO,KAAK;IACd;IACA,IAAI,CAACnD,QAAQ,CAACL,oBAAoB,EAAE;MAClCnE,KAAK,CAAC2H,KAAK,CAAC,QAAQ,EAAE,0CAA0C,CAAC;MACjE,OAAO,KAAK;IACd;IACA,IAAIyB,QAAQ,CAAC5E,QAAQ,CAACL,oBAAoB,CAAC,IAAIiF,QAAQ,CAAC5E,QAAQ,CAACN,eAAe,IAAI,CAAC,CAAC,EAAE;MACtFlE,KAAK,CAAC2H,KAAK,CAAC,QAAQ,EAAE,4EAA4E,CAAC;MACnG,OAAO,KAAK;IACd;IACA,IAAI,CAAC7F,QAAQ,EAAE;MACb9B,KAAK,CAAC2H,KAAK,CAAC,QAAQ,EAAE,qCAAqC,CAAC;MAC5D,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,IAAM0B,YAAY;IAAA,IAAAC,KAAA,GAAA9D,iBAAA,CAAG,aAAY;MAC/B,IAAI,CAAC2D,YAAY,CAAC,CAAC,EAAE;MAErBxH,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,IAAM4H,gBAAgB,GAAG;UACvBC,eAAe,EAAEJ,QAAQ,CAAC5E,QAAQ,CAACN,eAAe,IAAI,CAAC,CAAC;UACxDuC,oBAAoB,EAAE2C,QAAQ,CAAC5E,QAAQ,CAACL,oBAAoB,CAAC;UAC7DsF,MAAM,EAAE3H,QAAQ,CAAC4H,UAAU;UAC3BC,MAAM,EAAE,CAAArI,IAAI,oBAAJA,IAAI,CAAEqI,MAAM,KAAI,CAAC;UACzBtF,QAAQ,EAAEG,QAAQ,CAACH,QAAQ;UAC3BD,KAAK,EAAEgF,QAAQ,CAAC5E,QAAQ,CAACJ,KAAK,IAAI,EAAE,CAAC;UACrCH,OAAO,EAAEO,QAAQ,CAACP,OAAO;UACzBK,MAAM,EAAEE,QAAQ,CAACF;QACnB,CAAC;QAED,IAAMyB,QAAQ,SAASC,KAAK,CAAC,GAAGtB,YAAY,oBAAoB,EAAE;UAChEkF,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACT,gBAAgB;QACvC,CAAC,CAAC;QAEF,IAAMU,MAAM,SAASlE,QAAQ,CAACG,IAAI,CAAC,CAAC;QAEpC,IAAI+D,MAAM,CAAC9D,OAAO,EAAE;UAClBnG,KAAK,CAAC2H,KAAK,CACT,QAAQ,EACR,wCAAwC,EACxC,CACE;YACEuC,IAAI,EAAE,IAAI;YACVC,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQlJ,UAAU,CAACmJ,MAAM,CAAC,CAAC;YAAA;UACpC,CAAC,CAEL,CAAC;QACH,CAAC,MAAM;UACLpK,KAAK,CAAC2H,KAAK,CAAC,QAAQ,EAAEsC,MAAM,CAACI,OAAO,IAAI,kCAAkC,CAAC;QAC7E;MACF,CAAC,CAAC,OAAO/D,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1CtG,KAAK,CAAC2H,KAAK,CAAC,QAAQ,EAAE,wBAAwBrB,KAAK,CAAC+D,OAAO,EAAE,CAAC;MAChE,CAAC,SAAS;QACR1I,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBA9CK0H,YAAYA,CAAA;MAAA,OAAAC,KAAA,CAAA/C,KAAA,OAAAb,SAAA;IAAA;EAAA,GA8CjB;EAED,OACE9E,IAAA,CAACT,YAAY;IAACmK,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,EACpC7J,IAAA,CAACR,oBAAoB;MACnBsK,QAAQ,EAAErK,QAAQ,CAACsK,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG,QAAS;MACvDL,KAAK,EAAEC,MAAM,CAACK,YAAa;MAAAH,QAAA,EAE3B/J,KAAA,CAACT,UAAU;QAACqK,KAAK,EAAEC,MAAM,CAACM,UAAW;QAACC,4BAA4B,EAAE,KAAM;QAAAL,QAAA,GAExE/J,KAAA,CAACf,IAAI;UAAC2K,KAAK,EAAEC,MAAM,CAACQ,YAAa;UAAAN,QAAA,GAC/B/J,KAAA,CAACd,IAAI;YAAC0K,KAAK,EAAEC,MAAM,CAACS,UAAW;YAAAP,QAAA,GAAEpJ,MAAM,CAACmG,GAAG,EAAC,GAAC,EAACnG,MAAM,CAACoG,MAAM;UAAA,CAAO,CAAC,EACnE/G,KAAA,CAACd,IAAI;YAAC0K,KAAK,EAAEC,MAAM,CAACU,YAAa;YAAAR,QAAA,GAAC,eAAG,EAACpJ,MAAM,CAAC6J,OAAO;UAAA,CAAO,CAAC,EAC5DxK,KAAA,CAACd,IAAI;YAAC0K,KAAK,EAAEC,MAAM,CAACU,YAAa;YAAAR,QAAA,GAAC,qBAAI,EAACpJ,MAAM,CAAC8J,KAAK;UAAA,CAAO,CAAC,EAC1DrJ,QAAQ,IACPpB,KAAA,CAACd,IAAI;YAAC0K,KAAK,EAAEC,MAAM,CAACU,YAAa;YAAAR,QAAA,GAAC,yBAAa,EAAC3I,QAAQ,CAACsJ,cAAc;UAAA,CAAO,CAC/E;QAAA,CACG,CAAC,EAGP1K,KAAA,CAACf,IAAI;UAAC2K,KAAK,EAAEC,MAAM,CAACc,IAAK;UAAAZ,QAAA,GACvB/J,KAAA,CAACf,IAAI;YAAC2K,KAAK,EAAEC,MAAM,CAACe,SAAU;YAAAb,QAAA,GAC5B7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACgB,KAAM;cAAAd,QAAA,EAAC;YAAa,CAAM,CAAC,EAC/C7J,IAAA,CAACjB,IAAI;cAAC2K,KAAK,EAAEC,MAAM,CAACiB,eAAgB;cAAAf,QAAA,EAClC/J,KAAA,CAACH,MAAM;gBACLkL,aAAa,EAAE/I,eAAgB;gBAC/BgJ,aAAa,EAAE1E,mBAAoB;gBACnCsD,KAAK,EAAEC,MAAM,CAACoB,MAAO;gBAAAlB,QAAA,GAErB7J,IAAA,CAACL,MAAM,CAACqL,IAAI;kBAACL,KAAK,EAAC,kCAA+B;kBAACM,KAAK,EAAC;gBAAE,CAAE,CAAC,EAC7DvJ,QAAQ,CAAC4F,GAAG,CAAC,UAAC4D,OAAO;kBAAA,OACpBlL,IAAA,CAACL,MAAM,CAACqL,IAAI;oBAEVL,KAAK,EAAEO,OAAO,CAACtE,GAAI;oBACnBqE,KAAK,EAAEC,OAAO,CAAC1E,GAAG,CAACV,QAAQ,CAAC;kBAAE,GAFzBoF,OAAO,CAAC1E,GAGd,CAAC;gBAAA,CACH,CAAC;cAAA,CACI;YAAC,CACL,CAAC;UAAA,CACH,CAAC,EAGP1G,KAAA,CAACf,IAAI;YAAC2K,KAAK,EAAEC,MAAM,CAACe,SAAU;YAAAb,QAAA,GAC5B7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACgB,KAAM;cAAAd,QAAA,EAAC;YAAW,CAAM,CAAC,EAC5C/H,eAAe,GACdQ,gBAAgB,CAACyC,MAAM,GAAG,CAAC,GACzB/E,IAAA,CAACjB,IAAI;cAAC2K,KAAK,EAAEC,MAAM,CAACiB,eAAgB;cAAAf,QAAA,EAClC/J,KAAA,CAACH,MAAM;gBACLkL,aAAa,EAAEnI,cAAe;gBAC9BoI,aAAa,EAAErE,kBAAmB;gBAClCiD,KAAK,EAAEC,MAAM,CAACoB,MAAO;gBAAAlB,QAAA,GAErB7J,IAAA,CAACL,MAAM,CAACqL,IAAI;kBAACL,KAAK,EAAC,iCAA8B;kBAACM,KAAK,EAAC;gBAAE,CAAE,CAAC,EAC5D3I,gBAAgB,CAACgF,GAAG,CAAC,UAAC7G,MAAM,EAAE0K,KAAK;kBAAA,OAClCnL,IAAA,CAACL,MAAM,CAACqL,IAAI;oBAEVL,KAAK,EAAE,GAAGQ,KAAK,GAAG,CAAC,KAAK1K,MAAM,CAACmG,GAAG,IAAInG,MAAM,CAACoG,MAAM,MAAMpG,MAAM,CAAC8J,KAAK,KAAK9J,MAAM,CAAC2K,GAAG,IAAI,KAAK,GAAI;oBACjGH,KAAK,EAAExK,MAAM,CAACyE,QAAQ,CAACY,QAAQ,CAAC;kBAAE,GAF7BrF,MAAM,CAACyE,QAGb,CAAC;gBAAA,CACH,CAAC;cAAA,CACI;YAAC,CACL,CAAC,GAEPlF,IAAA,CAACjB,IAAI;cAAC2K,KAAK,EAAEC,MAAM,CAAC0B,kBAAmB;cAAAxB,QAAA,EACrC7J,IAAA,CAAChB,IAAI;gBAAC0K,KAAK,EAAEC,MAAM,CAAC2B,aAAc;gBAAAzB,QAAA,EAAC;cAEnC,CAAM;YAAC,CACH,CACP,GAED7J,IAAA,CAACjB,IAAI;cAAC2K,KAAK,EAAEC,MAAM,CAAC4B,kBAAmB;cAAA1B,QAAA,EACrC7J,IAAA,CAAChB,IAAI;gBAAC0K,KAAK,EAAEC,MAAM,CAAC6B,aAAc;gBAAA3B,QAAA,EAAC;cAEnC,CAAM;YAAC,CACH,CACP;UAAA,CACG,CAAC,EAGN/G,kBAAkB,IACjBhD,KAAA,CAACf,IAAI;YAAC2K,KAAK,EAAEC,MAAM,CAACe,SAAU;YAAAb,QAAA,GAC5B7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACgB,KAAM;cAAAd,QAAA,EAAC;YAAyB,CAAM,CAAC,EAC3D/J,KAAA,CAACf,IAAI;cAAC2K,KAAK,EAAEC,MAAM,CAAC8B,mBAAoB;cAAA5B,QAAA,GACtC/J,KAAA,CAACd,IAAI;gBAAC0K,KAAK,EAAEC,MAAM,CAAC+B,cAAe;gBAAA7B,QAAA,GAAC,eAC/B,EAAC/G,kBAAkB,CAAC8D,GAAG,EAAC,GAAC,EAAC9D,kBAAkB,CAAC+D,MAAM;cAAA,CAClD,CAAC,EACP/G,KAAA,CAACd,IAAI;gBAAC0K,KAAK,EAAEC,MAAM,CAACgC,iBAAkB;gBAAA9B,QAAA,GAAC,eAClC,EAAC/G,kBAAkB,CAACwH,OAAO,EAAC,IAAE,EAACxH,kBAAkB,CAACyH,KAAK;cAAA,CACtD,CAAC,EACPzK,KAAA,CAACd,IAAI;gBAAC0K,KAAK,EAAEC,MAAM,CAACiC,iBAAkB;gBAAA/B,QAAA,GAAC,eAClC,EAAC/G,kBAAkB,CAACsI,GAAG,EAAC,kBAAM,EAACtI,kBAAkB,CAAC+I,KAAK;cAAA,CACtD,CAAC,EACP/L,KAAA,CAACd,IAAI;gBAAC0K,KAAK,EAAEC,MAAM,CAACmC,gBAAiB;gBAAAjC,QAAA,GAAC,8BACvB,EAAC/G,kBAAkB,CAACiJ,WAAW;cAAA,CACxC,CAAC,EACPjM,KAAA,CAACd,IAAI;gBAAC0K,KAAK,EAAEC,MAAM,CAACqC,gBAAiB;gBAAAnC,QAAA,GAAC,oBAC5B,GAAA1J,qBAAA,GAAC2C,kBAAkB,CAACoE,QAAQ,qBAA3B/G,qBAAA,CAA6B8L,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,GAAA7L,sBAAA,GAAC0C,kBAAkB,CAACsE,SAAS,qBAA5BhH,sBAAA,CAA8B6L,OAAO,CAAC,CAAC,CAAC;cAAA,CACxF,CAAC;YAAA,CACH,CAAC;UAAA,CACH,CACP,EAGAnK,eAAe,IACdhC,KAAA,CAACf,IAAI;YAAC2K,KAAK,EAAEC,MAAM,CAACe,SAAU;YAAAb,QAAA,GAC5B7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACgB,KAAM;cAAAd,QAAA,EAAC;YAAoB,CAAM,CAAC,EACtD7J,IAAA,CAACkM,eAAe;cACdhB,OAAO,EAAEhJ,mBAAoB;cAC7BiK,OAAO,EAAE7J;YAAiB,CAC3B,CAAC;UAAA,CACE,CACP,EAEDxC,KAAA,CAACf,IAAI;YAAC2K,KAAK,EAAEC,MAAM,CAACe,SAAU;YAAAb,QAAA,GAC5B7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACgB,KAAM;cAAAd,QAAA,EAAC;YAAmB,CAAM,CAAC,EACrD7J,IAAA,CAACf,SAAS;cACRyK,KAAK,EAAEC,MAAM,CAACyC,KAAM;cACpBnB,KAAK,EAAErH,QAAQ,CAACP,OAAQ;cACxBgJ,YAAY,EAAE,SAAdA,YAAYA,CAAG/C,IAAI;gBAAA,OAAKzF,WAAW,CAAC,UAAAY,IAAI;kBAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;oBAAEpB,OAAO,EAAEiG;kBAAI;gBAAA,CAAG,CAAC;cAAA,CAAC;cAC1EgD,WAAW,EAAC,SAAS;cACrBC,SAAS,EAAE;YAAE,CACd,CAAC;UAAA,CACE,CAAC,EAEPzM,KAAA,CAACf,IAAI;YAAC2K,KAAK,EAAEC,MAAM,CAACe,SAAU;YAAAb,QAAA,GAC5B7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACgB,KAAM;cAAAd,QAAA,EAAC;YAA4B,CAAM,CAAC,EAC9D7J,IAAA,CAACf,SAAS;cACRyK,KAAK,EAAE,CAACC,MAAM,CAACyC,KAAK,EAAEzC,MAAM,CAAC6C,aAAa,CAAE;cAC5CvB,KAAK,EAAErH,QAAQ,CAACN,eAAgB;cAChCmJ,QAAQ,EAAE,KAAM;cAChBH,WAAW,EAAC;YAAa,CAC1B,CAAC;UAAA,CACE,CAAC,EAEPxM,KAAA,CAACf,IAAI;YAAC2K,KAAK,EAAEC,MAAM,CAACe,SAAU;YAAAb,QAAA,GAC5B7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACgB,KAAM;cAAAd,QAAA,EAAC;YAA4B,CAAM,CAAC,EAC9D7J,IAAA,CAACf,SAAS;cACRyK,KAAK,EAAEC,MAAM,CAACyC,KAAM;cACpBnB,KAAK,EAAErH,QAAQ,CAACL,oBAAqB;cACrC8I,YAAY,EAAE,SAAdA,YAAYA,CAAG/C,IAAI;gBAAA,OAAKzF,WAAW,CAAC,UAAAY,IAAI;kBAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;oBAAElB,oBAAoB,EAAE+F;kBAAI;gBAAA,CAAG,CAAC;cAAA,CAAC;cACvFgD,WAAW,EAAC,iCAAiC;cAC7CI,YAAY,EAAC;YAAS,CACvB,CAAC;UAAA,CACE,CAAC,EAEP5M,KAAA,CAACf,IAAI;YAAC2K,KAAK,EAAEC,MAAM,CAACe,SAAU;YAAAb,QAAA,GAC5B7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACgB,KAAM;cAAAd,QAAA,EAAC;YAAe,CAAM,CAAC,EACjD7J,IAAA,CAACf,SAAS;cACRyK,KAAK,EAAE,CAACC,MAAM,CAACyC,KAAK,EAAEzC,MAAM,CAAC6C,aAAa,CAAE;cAC5CvB,KAAK,EAAErH,QAAQ,CAACJ,KAAM;cACtBiJ,QAAQ,EAAE,KAAM;cAChBH,WAAW,EAAC;YAAyB,CACtC,CAAC;UAAA,CACE,CAAC,EAEPxM,KAAA,CAACf,IAAI;YAAC2K,KAAK,EAAEC,MAAM,CAACe,SAAU;YAAAb,QAAA,GAC5B7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACgB,KAAM;cAAAd,QAAA,EAAC;YAAM,CAAM,CAAC,EACxC7J,IAAA,CAACf,SAAS;cACRyK,KAAK,EAAEC,MAAM,CAACyC,KAAM;cACpBnB,KAAK,EAAErH,QAAQ,CAACF,MAAO;cACvB2I,YAAY,EAAE,SAAdA,YAAYA,CAAG/C,IAAI;gBAAA,OAAKzF,WAAW,CAAC,UAAAY,IAAI;kBAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;oBAAEf,MAAM,EAAE4F;kBAAI;gBAAA,CAAG,CAAC;cAAA,CAAC;cACzEgD,WAAW,EAAC;YAAU,CACvB,CAAC;UAAA,CACE,CAAC;QAAA,CACH,CAAC,EAGPxM,KAAA,CAACf,IAAI;UAAC2K,KAAK,EAAEC,MAAM,CAACgD,eAAgB;UAAA9C,QAAA,GAClC7J,IAAA,CAACd,gBAAgB;YACfwK,KAAK,EAAEC,MAAM,CAACiD,YAAa;YAC3BrD,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQlJ,UAAU,CAACmJ,MAAM,CAAC,CAAC;YAAA,CAAC;YAAAK,QAAA,EAEnC7J,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACkD,gBAAiB;cAAAhD,QAAA,EAAC;YAAO,CAAM;UAAC,CACpC,CAAC,EAEnB7J,IAAA,CAACd,gBAAgB;YACfwK,KAAK,EAAE,CAACC,MAAM,CAACmD,YAAY,EAAEhM,OAAO,IAAI6I,MAAM,CAACoD,cAAc,CAAE;YAC/DxD,OAAO,EAAEd,YAAa;YACtBuE,QAAQ,EAAElM,OAAQ;YAAA+I,QAAA,EAEjB/I,OAAO,GACNd,IAAA,CAACV,iBAAiB;cAAC2N,KAAK,EAAC;YAAM,CAAE,CAAC,GAElCjN,IAAA,CAAChB,IAAI;cAAC0K,KAAK,EAAEC,MAAM,CAACuD,gBAAiB;cAAArD,QAAA,EAAC;YAAW,CAAM;UACxD,CACe,CAAC;QAAA,CACf,CAAC;MAAA,CACG;IAAC,CACO;EAAC,CACX,CAAC;AAEnB,CAAC;AAED,IAAMF,MAAM,GAAGxK,UAAU,CAACgO,MAAM,CAAC;EAC/BvD,SAAS,EAAE;IACTwD,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACDrD,YAAY,EAAE;IACZoD,IAAI,EAAE;EACR,CAAC;EACDnD,UAAU,EAAE;IACVmD,IAAI,EAAE;EACR,CAAC;EACDjD,YAAY,EAAE;IACZkD,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC;EACDnD,UAAU,EAAE;IACVoD,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBR,KAAK,EAAE,MAAM;IACbS,YAAY,EAAE;EAChB,CAAC;EACDrD,YAAY,EAAE;IACZmD,QAAQ,EAAE,EAAE;IACZP,KAAK,EAAE,MAAM;IACbU,OAAO,EAAE,GAAG;IACZD,YAAY,EAAE;EAChB,CAAC;EACDjD,IAAI,EAAE;IACJ6C,OAAO,EAAE;EACX,CAAC;EACD5C,SAAS,EAAE;IACTgD,YAAY,EAAE;EAChB,CAAC;EACD/C,KAAK,EAAE;IACL6C,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBR,KAAK,EAAE,MAAM;IACbS,YAAY,EAAE;EAChB,CAAC;EACDtB,KAAK,EAAE;IACLwB,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,EAAE;IAChBR,OAAO,EAAE,EAAE;IACXE,QAAQ,EAAE,EAAE;IACZH,eAAe,EAAE;EACnB,CAAC;EACDb,aAAa,EAAE;IACba,eAAe,EAAE,SAAS;IAC1BJ,KAAK,EAAE;EACT,CAAC;EACDN,eAAe,EAAE;IACfoB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BV,OAAO,EAAE,EAAE;IACXW,UAAU,EAAE;EACd,CAAC;EACDrB,YAAY,EAAE;IACZQ,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,SAAS;IAC1BO,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,EAAE;IAChBR,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,QAAQ;IACpBW,WAAW,EAAE;EACf,CAAC;EACDrB,gBAAgB,EAAE;IAChBI,KAAK,EAAE,MAAM;IACbQ,UAAU,EAAE,MAAM;IAClBD,QAAQ,EAAE;EACZ,CAAC;EACDV,YAAY,EAAE;IACZM,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,SAAS;IAC1BS,YAAY,EAAE,EAAE;IAChBR,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,QAAQ;IACpBY,UAAU,EAAE;EACd,CAAC;EACDjB,gBAAgB,EAAE;IAChBD,KAAK,EAAE,MAAM;IACbQ,UAAU,EAAE,MAAM;IAClBD,QAAQ,EAAE;EACZ,CAAC;EACDT,cAAc,EAAE;IACdM,eAAe,EAAE;EACnB,CAAC;EAEDzC,eAAe,EAAE;IACfgD,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,EAAE;IAChBT,eAAe,EAAE;EACnB,CAAC;EACDtC,MAAM,EAAE;IACNqD,MAAM,EAAE;EACV,CAAC;EAED/C,kBAAkB,EAAE;IAClBgC,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE,EAAE;IACXQ,YAAY,EAAE,EAAE;IAChBF,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDvC,aAAa,EAAE;IACb2B,KAAK,EAAE,SAAS;IAChBoB,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE;EACb,CAAC;EACD/C,kBAAkB,EAAE;IAClB8B,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE,EAAE;IACXQ,YAAY,EAAE,EAAE;IAChBF,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDrC,aAAa,EAAE;IACbyB,KAAK,EAAE,SAAS;IAChBoB,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE;EACb,CAAC;EAED7C,mBAAmB,EAAE;IACnB4B,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE,EAAE;IACXQ,YAAY,EAAE,EAAE;IAChBS,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE;EACnB,CAAC;EACD9C,cAAc,EAAE;IACd8B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBR,KAAK,EAAE,MAAM;IACbS,YAAY,EAAE;EAChB,CAAC;EACD/B,iBAAiB,EAAE;IACjB6B,QAAQ,EAAE,EAAE;IACZP,KAAK,EAAE,MAAM;IACbS,YAAY,EAAE;EAChB,CAAC;EACD9B,iBAAiB,EAAE;IACjB4B,QAAQ,EAAE,EAAE;IACZP,KAAK,EAAE,MAAM;IACbS,YAAY,EAAE;EAChB,CAAC;EACD5B,gBAAgB,EAAE;IAChB0B,QAAQ,EAAE,EAAE;IACZP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE;EAChB,CAAC;EACD1B,gBAAgB,EAAE;IAChBwB,QAAQ,EAAE,EAAE;IACZP,KAAK,EAAE,MAAM;IACbwB,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAexO,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}