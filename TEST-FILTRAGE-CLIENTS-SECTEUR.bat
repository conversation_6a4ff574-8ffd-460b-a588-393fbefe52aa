@echo off
title Test Filtrage Clients par Secteur
color 0A

echo.
echo ========================================
echo    🔍 TEST FILTRAGE CLIENTS PAR SECTEUR
echo ========================================
echo.

echo 🔍 1. VERIFICATION DU SERVEUR PORT 4002...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 5; Write-Host '✅ Serveur OK:' $response.message } catch { Write-Host '❌ Serveur non accessible - Demarrez le serveur d''abord' }"

echo.
echo 🔍 2. TEST DES APIs DE FILTRAGE...
echo.
echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '✅ API Secteurs:' $response.count 'secteurs disponibles'; $response.data | ForEach-Object { Write-Host '   -' $_.ids ':' $_.nom } } catch { Write-Host '❌ API Secteurs erreur' }"

echo.
echo 🔍 3. TEST DU FILTRAGE PAR SECTEUR...
echo.
echo Test Secteur 1 (Centre-Ville):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/1/clients' -TimeoutSec 5; Write-Host '✅ Secteur 1 - Centre-Ville:' $response.count 'clients'; Write-Host '📊 Secteur info:' $response.secteur.nom '(Lat:' $response.secteur.latitude ', Lng:' $response.secteur.longitude ')'; $response.data | ForEach-Object { Write-Host '   👤' $_.nom $_.prenom '- Secteur ID:' $_.ids '(' $_.secteur_nom ')' } } catch { Write-Host '❌ Erreur secteur 1' }"

echo.
echo Test Secteur 2 (Quartier Industriel):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/2/clients' -TimeoutSec 5; Write-Host '✅ Secteur 2 - Quartier Industriel:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   👤' $_.nom $_.prenom '- Secteur ID:' $_.ids '(' $_.secteur_nom ')' } } catch { Write-Host '❌ Erreur secteur 2' }"

echo.
echo Test Secteur 3 (Zone Nord):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/3/clients' -TimeoutSec 5; Write-Host '✅ Secteur 3 - Zone Nord:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   👤' $_.nom $_.prenom '- Secteur ID:' $_.ids '(' $_.secteur_nom ')' } } catch { Write-Host '❌ Erreur secteur 3' }"

echo.
echo Test Secteur 4 (Zone Sud):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/4/clients' -TimeoutSec 5; Write-Host '✅ Secteur 4 - Zone Sud:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   👤' $_.nom $_.prenom '- Secteur ID:' $_.ids '(' $_.secteur_nom ')' } } catch { Write-Host '❌ Erreur secteur 4' }"

echo.
echo Test Secteur 5 (Quartier Commercial):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/5/clients' -TimeoutSec 5; Write-Host '✅ Secteur 5 - Quartier Commercial:' $response.count 'clients'; if ($response.count -eq 0) { Write-Host '   ℹ️ Aucun client dans ce secteur (normal)' } else { $response.data | ForEach-Object { Write-Host '   👤' $_.nom $_.prenom '- Secteur ID:' $_.ids '(' $_.secteur_nom ')' } } } catch { Write-Host '❌ Erreur secteur 5' }"

echo.
echo 🚀 4. DEMARRAGE DE L'APPLICATION POUR TEST...
echo.
echo Arret des processus existants...
taskkill /f /im node.exe 2>nul

echo.
echo Demarrage du serveur backend (port 4002)...
start "🎯 Backend Port 4002" cmd /k "title BACKEND FILTRAGE CLIENTS && color 0B && echo ========================================== && echo    🎯 BACKEND FILTRAGE CLIENTS && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ API Secteurs: /api/secteurs && echo ✅ API Clients par Secteur: /api/secteurs/:id/clients && echo ✅ Filtrage: Utilise INNER JOIN sur table secteur && echo. && echo 📡 Demarrage... && echo. && node serveur-port-4002.js"

echo.
echo Attente du backend (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo Demarrage de l'application React...
cd mobile
start "📱 Application React" cmd /k "title APPLICATION REACT FILTRAGE && color 0D && echo ========================================== && echo    📱 APPLICATION REACT FILTRAGE && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4002 && echo ✅ Filtrage: Clients par secteur && echo ✅ Champ Client: Filtre selon secteur selectionne && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo Attente de l'application (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 5. OUVERTURE DE L'APPLICATION...
start http://localhost:19006

echo.
echo ========================================
echo    ✅ FILTRAGE CLIENTS PAR SECTEUR PRET !
echo ========================================
echo.
echo 🧪 INSTRUCTIONS DE TEST DETAILLEES:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous avec:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🧪 TESTEZ LE FILTRAGE:
echo.
echo    a) 📍 ETAPE 1 - Secteur:
echo       - Selectionnez "Centre-Ville" dans le 1er champ
echo       - Observez que le champ Client se prepare
echo.
echo    b) 👤 ETAPE 2 - Client filtre:
echo       - Le 2eme champ Client affiche SEULEMENT:
echo         * 1. Benali Fatima - Setrou (Centre-Ville)
echo         * 2. Alami Mohammed - Setrou (Centre-Ville)
echo       - PAS d'autres clients d'autres secteurs !
echo.
echo    c) 🔄 ETAPE 3 - Changement de secteur:
echo       - Changez pour "Quartier Industriel"
echo       - Le champ Client affiche SEULEMENT:
echo         * 1. Tazi Aicha - Setrou (Quartier Industriel)
echo.
echo    d) 🔄 ETAPE 4 - Secteur avec plusieurs clients:
echo       - Changez pour "Zone Residentielle Nord"
echo       - Le champ Client affiche SEULEMENT:
echo         * 1. Benjelloun Youssef - Setrou (Zone Nord)
echo         * 2. Lahlou Khadija - Setrou (Zone Nord)
echo.
echo    e) 🔄 ETAPE 5 - Secteur vide:
echo       - Changez pour "Quartier Commercial"
echo       - Le champ Client affiche:
echo         * "❌ Aucun client dans ce secteur"
echo         * "Essayez un autre secteur"
echo.
echo 📊 VERIFICATION DU FILTRAGE:
echo.
echo    ✅ Secteur 1 (Centre-Ville) → 2 clients
echo    ✅ Secteur 2 (Quartier Industriel) → 1 client
echo    ✅ Secteur 3 (Zone Nord) → 2 clients
echo    ✅ Secteur 4 (Zone Sud) → 1 client
echo    ✅ Secteur 5 (Quartier Commercial) → 0 client
echo.
echo 🔍 VERIFICATION TECHNIQUE:
echo.
echo    ✅ API utilise INNER JOIN entre tables client et secteur
echo    ✅ Filtrage sur c.ids = s.ids (cle etrangere)
echo    ✅ Requete SQL: WHERE c.ids = $1
echo    ✅ Champ Client se vide lors du changement de secteur
echo    ✅ Contrats se reinitialise lors du changement
echo.
echo 🎯 RESULTAT ATTENDU:
echo    Le champ Client affiche UNIQUEMENT les clients
echo    du secteur selectionne, en utilisant la relation
echo    entre les tables client.ids et secteur.ids !
echo.
echo 🌐 URLs de verification:
echo    - Application: http://localhost:19006
echo    - API Secteurs: http://localhost:4002/api/secteurs
echo    - API Secteur 1: http://localhost:4002/api/secteurs/1/clients
echo    - API Secteur 3: http://localhost:4002/api/secteurs/3/clients
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
