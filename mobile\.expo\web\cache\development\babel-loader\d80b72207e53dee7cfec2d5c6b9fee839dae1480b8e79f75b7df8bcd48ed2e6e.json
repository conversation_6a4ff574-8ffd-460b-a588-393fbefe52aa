{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nvar formatToList = function formatToList(items) {\n  return items.map(function (key) {\n    return `- ${key}`;\n  }).join('\\n');\n};\nexport default function validatePathConfig(config) {\n  var root = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  var validKeys = ['initialRouteName', 'screens'];\n  if (!root) {\n    validKeys.push('path', 'exact', 'stringify', 'parse');\n  }\n  var invalidKeys = Object.keys(config).filter(function (key) {\n    return !validKeys.includes(key);\n  });\n  if (invalidKeys.length) {\n    throw new Error(`Found invalid properties in the configuration:\\n${formatToList(invalidKeys)}\\n\\nDid you forget to specify them under a 'screens' property?\\n\\nYou can only specify the following properties:\\n${formatToList(validKeys)}\\n\\nSee https://reactnavigation.org/docs/configuring-links for more details on how to specify a linking configuration.`);\n  }\n  if (config.screens) {\n    Object.entries(config.screens).forEach(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 2),\n        _ = _ref2[0],\n        value = _ref2[1];\n      if (typeof value !== 'string') {\n        validatePathConfig(value, false);\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["formatToList", "items", "map", "key", "join", "validatePathConfig", "config", "root", "arguments", "length", "undefined", "validKeys", "push", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "filter", "includes", "Error", "screens", "entries", "for<PERSON>ach", "_ref", "_ref2", "_slicedToArray", "_", "value"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\core\\src\\validatePathConfig.tsx"], "sourcesContent": ["const formatToList = (items: string[]) =>\n  items.map((key) => `- ${key}`).join('\\n');\n\nexport default function validatePathConfig(config: any, root = true) {\n  const validKeys = ['initialRouteName', 'screens'];\n\n  if (!root) {\n    validKeys.push('path', 'exact', 'stringify', 'parse');\n  }\n\n  const invalidKeys = Object.keys(config).filter(\n    (key) => !validKeys.includes(key)\n  );\n\n  if (invalidKeys.length) {\n    throw new Error(\n      `Found invalid properties in the configuration:\\n${formatToList(\n        invalidKeys\n      )}\\n\\nDid you forget to specify them under a 'screens' property?\\n\\nYou can only specify the following properties:\\n${formatToList(\n        validKeys\n      )}\\n\\nSee https://reactnavigation.org/docs/configuring-links for more details on how to specify a linking configuration.`\n    );\n  }\n\n  if (config.screens) {\n    Object.entries(config.screens).forEach(([_, value]) => {\n      if (typeof value !== 'string') {\n        validatePathConfig(value, false);\n      }\n    });\n  }\n}\n"], "mappings": ";AAAA,IAAMA,YAAY,GAAI,SAAhBA,YAAYA,CAAIC,KAAe;EAAA,OACnCA,KAAK,CAACC,GAAG,CAAE,UAAAC,GAAG;IAAA,OAAM,KAAIA,GAAI,EAAC;EAAA,EAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AAAA;AAE3C,eAAe,SAASC,kBAAkBA,CAACC,MAAW,EAAe;EAAA,IAAbC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACjE,IAAMG,SAAS,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC;EAEjD,IAAI,CAACJ,IAAI,EAAE;IACTI,SAAS,CAACC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC;EACvD;EAEA,IAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,CAC3C,UAAAb,GAAG;IAAA,OAAK,CAACQ,SAAS,CAACM,QAAQ,CAACd,GAAG,CAAC;EAAA,EAClC;EAED,IAAIU,WAAW,CAACJ,MAAM,EAAE;IACtB,MAAM,IAAIS,KAAK,CACZ,mDAAkDlB,YAAY,CAC7Da,WAAW,CACX,qHAAoHb,YAAY,CAChIW,SAAS,CACT,wHAAuH,CAC1H;EACH;EAEA,IAAIL,MAAM,CAACa,OAAO,EAAE;IAClBL,MAAM,CAACM,OAAO,CAACd,MAAM,CAACa,OAAO,CAAC,CAACE,OAAO,CAAC,UAAAC,IAAA,EAAgB;MAAA,IAAAC,KAAA,GAAAC,cAAA,CAALF,IAAA;QAATG,CAAC,GAAAF,KAAA;QAAEG,KAAK,GAAAH,KAAA;MAC/C,IAAI,OAAOG,KAAK,KAAK,QAAQ,EAAE;QAC7BrB,kBAAkB,CAACqB,KAAK,EAAE,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}