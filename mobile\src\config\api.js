// Configuration API pour AquaTrack
// Modifiez le port ici pour changer l'URL de base

// Configuration des ports
const PORTS = {
  DEVELOPMENT: 4000,    // Port de développement par défaut
  PRODUCTION: 4002,     // Port de production
  ALTERNATIVE: 3000,    // Port alternatif
  MOBILE: 19006        // Port de l'application mobile
};

// Port actuel (modifiez cette valeur pour changer le port)
const CURRENT_PORT = PORTS.PRODUCTION; // Utilise le port 4002

// Configuration de l'environnement
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
};

// Environnement actuel
const CURRENT_ENV = ENV.PRODUCTION;

// URLs de base selon l'environnement
const API_URLS = {
  [ENV.DEVELOPMENT]: `http://localhost:${PORTS.DEVELOPMENT}`,
  [ENV.PRODUCTION]: `http://localhost:${PORTS.PRODUCTION}`,
  [ENV.TEST]: `http://localhost:${PORTS.ALTERNATIVE}`
};

// URL de base actuelle
export const API_BASE_URL = API_URLS[CURRENT_ENV];

// Configuration complète
export const API_CONFIG = {
  baseURL: API_BASE_URL,
  port: CURRENT_PORT,
  environment: CURRENT_ENV,
  timeout: 10000, // 10 secondes
  retries: 3,
  
  // URLs complètes des endpoints
  endpoints: {
    // Authentification
    login: `${API_BASE_URL}/api/auth/login`,
    logout: `${API_BASE_URL}/api/auth/logout`,
    
    // Secteurs
    secteurs: `${API_BASE_URL}/api/secteurs`,
    secteurClients: (id) => `${API_BASE_URL}/api/secteurs/${id}/clients`,
    
    // Clients
    clients: `${API_BASE_URL}/api/clients`,
    clientContract: (id) => `${API_BASE_URL}/api/client-contract/${id}`,
    clientLastConsommation: (id) => `${API_BASE_URL}/api/clients/${id}/last-consommation`,
    
    // Consommations
    consommations: `${API_BASE_URL}/api/consommations`,
    consommationsByTech: (id) => `${API_BASE_URL}/api/consommation/technicien/${id}`,
    lastConsommationGlobal: `${API_BASE_URL}/api/last-consommation-global`,
    
    // Contrats
    contracts: `${API_BASE_URL}/api/contracts`,
    contractLastConsommation: (id) => `${API_BASE_URL}/api/contracts/${id}/last-consommation`,
    
    // Factures
    factures: `${API_BASE_URL}/api/factures`,
    facturesByClient: (id) => `${API_BASE_URL}/api/factures/client/${id}`,
    
    // Pages web
    consommationPage: `${API_BASE_URL}/consommation`,
    homePage: `${API_BASE_URL}/`,
    
    // Application mobile
    mobileApp: `http://localhost:${PORTS.MOBILE}`
  }
};

// Fonction pour changer le port dynamiquement
export const changePort = (newPort) => {
  const newBaseURL = `http://localhost:${newPort}`;
  
  // Mettre à jour la configuration
  API_CONFIG.baseURL = newBaseURL;
  API_CONFIG.port = newPort;
  
  // Mettre à jour tous les endpoints
  Object.keys(API_CONFIG.endpoints).forEach(key => {
    if (typeof API_CONFIG.endpoints[key] === 'string') {
      API_CONFIG.endpoints[key] = API_CONFIG.endpoints[key].replace(API_BASE_URL, newBaseURL);
    }
  });
  
  console.log(`✅ Configuration API mise à jour pour le port ${newPort}`);
  console.log(`📡 Nouvelle URL de base: ${newBaseURL}`);
  
  return API_CONFIG;
};

// Fonction pour tester la connexion
export const testConnection = async (port = CURRENT_PORT) => {
  const testURL = `http://localhost:${port}`;
  
  try {
    const response = await fetch(testURL, {
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Connexion réussie au port ${port}`);
      console.log(`📡 Serveur: ${data.message || 'OK'}`);
      return { success: true, data, port };
    } else {
      console.log(`❌ Erreur HTTP ${response.status} sur le port ${port}`);
      return { success: false, error: `HTTP ${response.status}`, port };
    }
  } catch (error) {
    console.log(`❌ Impossible de se connecter au port ${port}: ${error.message}`);
    return { success: false, error: error.message, port };
  }
};

// Fonction pour détecter automatiquement le port disponible
export const autoDetectPort = async () => {
  const portsToTest = [PORTS.PRODUCTION, PORTS.DEVELOPMENT, PORTS.ALTERNATIVE];
  
  console.log('🔍 Détection automatique du port...');
  
  for (const port of portsToTest) {
    const result = await testConnection(port);
    if (result.success) {
      console.log(`✅ Port ${port} détecté et fonctionnel`);
      changePort(port);
      return port;
    }
  }
  
  console.log('❌ Aucun serveur détecté sur les ports testés');
  return null;
};

// Informations de debug
export const getDebugInfo = () => {
  return {
    currentPort: CURRENT_PORT,
    currentEnv: CURRENT_ENV,
    baseURL: API_BASE_URL,
    availablePorts: PORTS,
    endpoints: Object.keys(API_CONFIG.endpoints).length,
    timestamp: new Date().toISOString()
  };
};

// Log de la configuration au démarrage
console.log('🔧 Configuration API AquaTrack');
console.log('================================');
console.log(`📡 URL de base: ${API_BASE_URL}`);
console.log(`🎯 Port: ${CURRENT_PORT}`);
console.log(`🌍 Environnement: ${CURRENT_ENV}`);
console.log(`📋 Page consommation: ${API_CONFIG.endpoints.consommationPage}`);
console.log(`📱 Application mobile: ${API_CONFIG.endpoints.mobileApp}`);
console.log('================================');

// Export par défaut
export default API_CONFIG;
