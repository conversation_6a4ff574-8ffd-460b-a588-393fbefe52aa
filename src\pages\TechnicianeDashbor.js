import React from 'react';
import { useNavigate } from 'react-router-dom';

const TechnicianeDashbor = ({ user, onLogout }) => {
  const navigate = useNavigate();

  // Fonctions de navigation
  const navigateToClients = () => {
    navigate('/clients');
  };

  const navigateToConsommation = () => {
    navigate('/consommation');
  };

  const navigateToFactures = () => {
    navigate('/factures');
  };

  const navigateToQRScanner = () => {
    navigate('/qr-scanner');
  };

  const navigateToMap = () => {
    navigate('/carte-clients');
  };

  const navigateToHistorique = () => {
    navigate('/historique');
  };

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1>🔧 AquaTrack - Dashboard Technicien</h1>
        <div className="user-info">
          <span>Bonjour {user ? `${user.prenom} ${user.nom}` : 'loukil '} (Technicien)</span>
          <button onClick={onLogout || (() => window.location.reload())} className="logout-btn">
            Déconnexion
          </button>
        </div>
      </header>

      <main className="dashboard-content">
        <div className="welcome-card">
          <h2>Bienvenue Technicien</h2>
          <p>Panneau de travail pour les techniciens AquaTrack</p>
        </div>

        <div className="dashboard-grid">
          <div className="dashboard-card" onClick={navigateToClients} style={{cursor: 'pointer'}}>
            <div className="card-icon">👥</div>
            <h3>Les Clients</h3>
            
          </div>

          <div className="dashboard-card" onClick={navigateToConsommation} style={{cursor: 'pointer'}}>
            <div className="card-icon">💧</div>
            <h3>Saisie Consommation</h3>
            
          </div>

          <div className="dashboard-card" onClick={navigateToFactures} style={{cursor: 'pointer'}}>
            <div className="card-icon">🧾</div>
            <h3>Les Factures</h3>
           
          </div>

          <div className="dashboard-card" onClick={navigateToQRScanner} style={{cursor: 'pointer'}}>
            <div className="card-icon">📱</div>
            <h3>Scanner QR</h3>
           
          </div>

          <div className="dashboard-card" onClick={navigateToMap} style={{cursor: 'pointer'}}>
            <div className="card-icon">🗺️</div>
            <h3>Carte Clients</h3>
          
          </div>

          <div className="dashboard-card" onClick={navigateToHistorique} style={{cursor: 'pointer'}}>
            <div className="card-icon">📋</div>
            <h3>Historique Actions</h3>
           
          </div>
        </div>

      </main>
    </div>
  );
};

export default TechnicianeDashbor;
