@echo off
title Test Carte Google Maps Integree
color 0A

echo.
echo ========================================
echo    🗺️ TEST CARTE GOOGLE MAPS INTEGREE
echo ========================================
echo.

echo 🔍 1. VERIFICATION DU BACKEND...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs' -TimeoutSec 5; Write-Host '✅ Backend OK:' $response.count 'secteurs disponibles' } catch { Write-Host '❌ Backend non accessible' }"

echo.
echo 🔍 2. TEST API CLIENTS PAR SECTEUR...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs/1/clients' -TimeoutSec 5; Write-Host '✅ API Clients OK:' $response.count 'clients dans le secteur 1' } catch { Write-Host '❌ API Clients erreur' }"

echo.
echo 📱 3. DEMARRAGE DE L'APPLICATION MOBILE...
cd mobile
start "Application Mobile Maps" cmd /k "title APPLICATION MOBILE AVEC CARTES && color 0D && echo ========================================== && echo    📱 APPLICATION AVEC GOOGLE MAPS && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4000 && echo ✅ Google Maps: Integration directe && echo ✅ Marqueurs: Secteur + Clients && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 4. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 5. OUVERTURE DES TESTS...
start http://localhost:4000/api/secteurs
timeout /t 2 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ TEST CARTE GOOGLE MAPS INTEGREE
echo ========================================
echo.
echo 📋 INSTRUCTIONS DE TEST DETAILLEES:
echo.
echo 1. 🔍 Verifiez le backend:
echo    - http://localhost:4000/api/secteurs doit afficher 5 secteurs
echo    - http://localhost:4000/api/secteurs/1/clients doit afficher 2 clients
echo.
echo 2. 📱 Testez l'application mobile:
echo    - Allez sur http://localhost:19006
echo    - Connectez-<NAME_EMAIL> / Tech123
echo    - Allez dans "Consommation"
echo.
echo 3. 🗺️ Testez la carte integree:
echo    - Selectionnez "Centre-Ville" dans le menu secteur
echo    - La carte Google Maps doit s'afficher DANS LA MEME PAGE
echo    - Vous devriez voir:
echo      * Un marqueur BLEU pour le centre du secteur
echo      * Des marqueurs ROUGES pour chaque client
echo      * Les noms des clients sur les marqueurs
echo.
echo 4. 🔍 Testez les interactions:
echo    - Cliquez sur le marqueur bleu (secteur)
echo    - Cliquez sur les marqueurs rouges (clients)
echo    - Les info-bulles doivent s'afficher avec les details
echo.
echo 5. 🌐 Testez l'ouverture externe:
echo    - Cliquez sur "Ouvrir dans Google Maps"
echo    - Google Maps s'ouvre dans un nouvel onglet
echo    - Tous les clients sont affiches avec un itineraire
echo.
echo 📊 SECTEURS A TESTER:
echo    1. Centre-Ville → 2 clients (Benali Fatima, Alami Mohammed)
echo    2. Quartier Industriel → 1 client (Tazi Aicha)
echo    3. Zone Residentielle Nord → 2 clients (Benjelloun, Lahlou)
echo    4. Zone Residentielle Sud → 1 client (Fassi Omar)
echo    5. Quartier Commercial → 0 client
echo.
echo ✅ FONCTIONNALITES IMPLEMENTEES:
echo    - Carte Google Maps integree dans le formulaire
echo    - Marqueurs automatiques pour secteur et clients
echo    - Info-bulles avec details complets
echo    - Zoom automatique pour inclure tous les points
echo    - Bouton pour ouvrir Google Maps externe
echo    - Compatible React Native Web
echo.
echo ⚠️ POINTS IMPORTANTS:
echo    - La carte s'affiche dans la MEME PAGE (pas de nouvelle fenetre)
echo    - Les marqueurs sont interactifs (cliquables)
echo    - Les coordonnees GPS sont generees automatiquement
echo    - Fallback si Google Maps ne charge pas
echo.
echo 🆘 SI LA CARTE NE S'AFFICHE PAS:
echo    1. Verifiez votre connexion internet
echo    2. Actualisez la page (F5)
echo    3. Verifiez que le backend fonctionne
echo    4. Essayez un autre secteur
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
