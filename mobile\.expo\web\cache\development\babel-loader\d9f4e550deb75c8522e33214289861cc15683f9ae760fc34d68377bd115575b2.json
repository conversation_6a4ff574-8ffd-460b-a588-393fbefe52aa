{"ast": null, "code": "import * as React from 'react';\nimport UnimplementedView from \"./UnimplementedView\";\nfunction PickerIOS() {\n  return React.createElement(UnimplementedView, null);\n}\nPickerIOS.Item = UnimplementedView;\nexport default PickerIOS;", "map": {"version": 3, "names": ["React", "UnimplementedView", "PickerIOS", "createElement", "<PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-native-picker\\picker\\js\\PickerIOS.web.js"], "sourcesContent": ["/**\n * @flow\n */\n\nimport * as React from 'react';\nimport UnimplementedView from './UnimplementedView';\n\nfunction PickerIOS(): React.Node {\n  return <UnimplementedView />;\n}\n\n/**\n * Fallback for other platforms\n */\nPickerIOS.Item = UnimplementedView;\n\nexport default PickerIOS;\n"], "mappings": "AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB;AAExB,SAASC,SAASA,CAAA,EAAe;EAC/B,OAAOF,KAAA,CAAAG,aAAA,CAACF,iBAAiB,MAAE,CAAC;AAC9B;AAKAC,SAAS,CAACE,IAAI,GAAGH,iBAAiB;AAElC,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}