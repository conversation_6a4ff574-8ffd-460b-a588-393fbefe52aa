@echo off
title AquaTrack - Port 4002
color 0A

echo.
echo ========================================
echo    🎯 AQUATRACK SUR PORT 4002
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🖥️ 3. DEMARRAGE DU SERVEUR SUR PORT 4002...
start "🎯 Serveur Port 4002" cmd /k "title SERVEUR AQUATRACK PORT 4002 && color 0B && echo ========================================== && echo    🎯 SERVEUR AQUATRACK PORT 4002 && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ URL: http://localhost:4002 && echo ✅ Consommation: http://localhost:4002/consommation && echo ✅ APIs: Secteurs, Clients, Auth && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo    - <EMAIL> / Admin123 && echo. && echo 📡 Demarrage du serveur... && echo. && node serveur-port-4002.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (10 secondes)...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 5. TEST DE CONNEXION...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 10; Write-Host '✅ SERVEUR OK:' $response.message } catch { Write-Host '❌ SERVEUR NON ACCESSIBLE' }"

echo.
echo 🌐 6. TEST DE LA PAGE CONSOMMATION...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:4002/consommation' -TimeoutSec 10; Write-Host '✅ PAGE CONSOMMATION OK: Status' $response.StatusCode } catch { Write-Host '❌ PAGE CONSOMMATION ERREUR' }"

echo.
echo 🌐 7. OUVERTURE DES PAGES...
start http://localhost:4002
timeout /t 3 /nobreak >nul
start http://localhost:4002/consommation

echo.
echo 📱 8. DEMARRAGE DE L'APPLICATION MOBILE (Port 19006)...
cd mobile
start "📱 Frontend Mobile" cmd /k "title APPLICATION MOBILE AQUATRACK && color 0D && echo ========================================== && echo    📱 APPLICATION MOBILE AQUATRACK && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ URL: http://localhost:19006 && echo ✅ Backend: http://localhost:4002 && echo ✅ Page Consommation: http://localhost:4002/consommation && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 9. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 10. OUVERTURE DE L'APPLICATION MOBILE...
start http://localhost:19006

echo.
echo ========================================
echo    ✅ AQUATRACK PORT 4002 DEMARRE !
echo ========================================
echo.
echo 📡 Serveur Backend: http://localhost:4002
echo 📋 Page Consommation: http://localhost:4002/consommation
echo 📱 Application Mobile: http://localhost:19006
echo.
echo 🔧 URLs Disponibles:
echo    - Accueil: http://localhost:4002/
echo    - Consommation: http://localhost:4002/consommation
echo    - API Secteurs: http://localhost:4002/api/secteurs
echo    - API Clients: http://localhost:4002/api/clients
echo    - Application Mobile: http://localhost:19006
echo.
echo 🔑 Comptes de test:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 📋 Instructions:
echo    1. Le serveur fonctionne sur le port 4002
echo    2. La page /consommation est accessible
echo    3. L'application mobile fonctionne sur le port 19006
echo    4. Toutes les APIs sont disponibles
echo    5. Utilisez les comptes de test pour vous connecter
echo.
echo ⚠️ IMPORTANT:
echo    - Gardez les deux fenetres ouvertes (serveur + mobile)
echo    - Testez d'abord http://localhost:4002/consommation
echo    - Puis testez l'application mobile sur http://localhost:19006
echo.
echo 🎯 VOTRE URL DEMANDEE EST PRETE:
echo    http://localhost:4002/consommation
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
