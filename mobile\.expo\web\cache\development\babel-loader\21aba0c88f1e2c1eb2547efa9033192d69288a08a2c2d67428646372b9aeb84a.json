{"ast": null, "code": "'use client';\n\nimport { createRoot as domCreateRoot, hydrateRoot as domHydrateRoot } from 'react-dom/client';\nimport { createSheet } from \"../StyleSheet/dom\";\nexport function hydrate(element, root) {\n  createSheet(root);\n  return domHydrateRoot(root, element);\n}\nexport default function render(element, root) {\n  createSheet(root);\n  var reactRoot = domCreateRoot(root);\n  reactRoot.render(element);\n  return reactRoot;\n}", "map": {"version": 3, "names": ["createRoot", "domCreateRoot", "hydrateRoot", "domHydrateRoot", "createSheet", "hydrate", "element", "root", "render", "reactRoot"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/render/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport { createRoot as domCreateRoot, hydrateRoot as domHydrateRoot } from 'react-dom/client';\nimport { createSheet } from '../StyleSheet/dom';\nexport function hydrate(element, root) {\n  createSheet(root);\n  return domHydrateRoot(root, element);\n}\nexport default function render(element, root) {\n  createSheet(root);\n  var reactRoot = domCreateRoot(root);\n  reactRoot.render(element);\n  return reactRoot;\n}"], "mappings": "AASA,YAAY;;AAEZ,SAASA,UAAU,IAAIC,aAAa,EAAEC,WAAW,IAAIC,cAAc,QAAQ,kBAAkB;AAC7F,SAASC,WAAW;AACpB,OAAO,SAASC,OAAOA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACrCH,WAAW,CAACG,IAAI,CAAC;EACjB,OAAOJ,cAAc,CAACI,IAAI,EAAED,OAAO,CAAC;AACtC;AACA,eAAe,SAASE,MAAMA,CAACF,OAAO,EAAEC,IAAI,EAAE;EAC5CH,WAAW,CAACG,IAAI,CAAC;EACjB,IAAIE,SAAS,GAAGR,aAAa,CAACM,IAAI,CAAC;EACnCE,SAAS,CAACD,MAAM,CAACF,OAAO,CAAC;EACzB,OAAOG,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}