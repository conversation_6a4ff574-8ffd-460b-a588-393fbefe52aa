{"version": 3, "names": ["InvalidNameError", "CLIError", "constructor", "name"], "sources": ["../../../../src/commands/init/errors/InvalidNameError.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\n\nexport default class InvalidNameError extends CLIError {\n  constructor(name: string) {\n    super(\n      `\"${name}\" is not a valid name for a project. Please use a valid identifier name (alphanumeric).`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEe,MAAMA,gBAAgB,SAASC,oBAAQ,CAAC;EACrDC,WAAW,CAACC,IAAY,EAAE;IACxB,KAAK,CACF,IAAGA,IAAK,yFAAwF,CAClG;EACH;AACF;AAAC"}