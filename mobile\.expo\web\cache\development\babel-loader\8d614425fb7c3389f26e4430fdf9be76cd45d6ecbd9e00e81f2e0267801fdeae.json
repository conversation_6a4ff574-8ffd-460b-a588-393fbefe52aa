{"ast": null, "code": "import React from 'react';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createStackNavigator } from '@react-navigation/stack';\nimport { StatusBar } from 'expo-status-bar';\nimport LoginScreen from \"./src/screens/LoginScreen\";\nimport TechnicianDashboard from \"./src/screens/TechnicianDashboard\";\nimport ClientsScreen from \"./src/screens/ClientsScreen\";\nimport ConsommationScreen from \"./src/screens/ConsommationScreen\";\nimport FacturesScreen from \"./src/screens/FacturesScreen\";\nimport QRScannerScreen from \"./src/screens/QRScannerScreen\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Stack = createStackNavigator();\nexport default function App() {\n  return _jsxs(NavigationContainer, {\n    children: [_jsx(StatusBar, {\n      style: \"auto\"\n    }), _jsxs(Stack.Navigator, {\n      initialRouteName: \"Login\",\n      screenOptions: {\n        headerStyle: {\n          backgroundColor: '#007AFF'\n        },\n        headerTintColor: '#fff',\n        headerTitleStyle: {\n          fontWeight: 'bold'\n        }\n      },\n      children: [_jsx(Stack.Screen, {\n        name: \"Login\",\n        component: LoginScreen,\n        options: {\n          headerShown: false\n        }\n      }), _jsx(Stack.Screen, {\n        name: \"TechnicianDashboard\",\n        component: TechnicianDashboard,\n        options: {\n          title: 'Dashboard Technicien',\n          headerLeft: null\n        }\n      }), _jsx(Stack.Screen, {\n        name: \"Clients\",\n        component: ClientsScreen,\n        options: {\n          title: 'Mes Clients'\n        }\n      }), _jsx(Stack.Screen, {\n        name: \"Consommation\",\n        component: ConsommationScreen,\n        options: {\n          title: 'Consommation'\n        }\n      }), _jsx(Stack.Screen, {\n        name: \"Factures\",\n        component: FacturesScreen,\n        options: {\n          title: 'Factures'\n        }\n      }), _jsx(Stack.Screen, {\n        name: \"QRScanner\",\n        component: QRScannerScreen,\n        options: {\n          title: 'Scanner QR'\n        }\n      })]\n    })]\n  });\n}", "map": {"version": 3, "names": ["React", "NavigationContainer", "createStackNavigator", "StatusBar", "LoginScreen", "TechnicianDashboard", "ClientsScreen", "ConsommationScreen", "FacturesScreen", "QRScannerScreen", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "App", "children", "style", "Navigator", "initialRouteName", "screenOptions", "headerStyle", "backgroundColor", "headerTintColor", "headerTitleStyle", "fontWeight", "Screen", "name", "component", "options", "headerShown", "title", "headerLeft"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/mobile/App.js"], "sourcesContent": ["import React from 'react';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createStackNavigator } from '@react-navigation/stack';\nimport { StatusBar } from 'expo-status-bar';\n\n// Import des écrans\nimport LoginScreen from './src/screens/LoginScreen';\nimport TechnicianDashboard from './src/screens/TechnicianDashboard';\nimport ClientsScreen from './src/screens/ClientsScreen';\nimport ConsommationScreen from './src/screens/ConsommationScreen';\nimport FacturesScreen from './src/screens/FacturesScreen';\nimport QRScannerScreen from './src/screens/QRScannerScreen';\n\nconst Stack = createStackNavigator();\n\nexport default function App() {\n  return (\n    <NavigationContainer>\n      <StatusBar style=\"auto\" />\n      <Stack.Navigator \n        initialRouteName=\"Login\"\n        screenOptions={{\n          headerStyle: {\n            backgroundColor: '#007AFF',\n          },\n          headerTintColor: '#fff',\n          headerTitleStyle: {\n            fontWeight: 'bold',\n          },\n        }}\n      >\n        <Stack.Screen \n          name=\"Login\" \n          component={LoginScreen}\n          options={{ headerShown: false }}\n        />\n        <Stack.Screen \n          name=\"TechnicianDashboard\" \n          component={TechnicianDashboard}\n          options={{ \n            title: 'Dashboard Technicien',\n            headerLeft: null, // Empêche le retour\n          }}\n        />\n        <Stack.Screen \n          name=\"Clients\" \n          component={ClientsScreen}\n          options={{ title: 'Mes Clients' }}\n        />\n        <Stack.Screen \n          name=\"Consommation\" \n          component={ConsommationScreen}\n          options={{ title: 'Consommation' }}\n        />\n        <Stack.Screen \n          name=\"Factures\" \n          component={FacturesScreen}\n          options={{ title: 'Factures' }}\n        />\n        <Stack.Screen \n          name=\"QRScanner\" \n          component={QRScannerScreen}\n          options={{ title: 'Scanner QR' }}\n        />\n      </Stack.Navigator>\n    </NavigationContainer>\n  );\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,SAAS,QAAQ,iBAAiB;AAG3C,OAAOC,WAAW;AAClB,OAAOC,mBAAmB;AAC1B,OAAOC,aAAa;AACpB,OAAOC,kBAAkB;AACzB,OAAOC,cAAc;AACrB,OAAOC,eAAe;AAAsC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE5D,IAAMC,KAAK,GAAGZ,oBAAoB,CAAC,CAAC;AAEpC,eAAe,SAASa,GAAGA,CAAA,EAAG;EAC5B,OACEF,KAAA,CAACZ,mBAAmB;IAAAe,QAAA,GAClBL,IAAA,CAACR,SAAS;MAACc,KAAK,EAAC;IAAM,CAAE,CAAC,EAC1BJ,KAAA,CAACC,KAAK,CAACI,SAAS;MACdC,gBAAgB,EAAC,OAAO;MACxBC,aAAa,EAAE;QACbC,WAAW,EAAE;UACXC,eAAe,EAAE;QACnB,CAAC;QACDC,eAAe,EAAE,MAAM;QACvBC,gBAAgB,EAAE;UAChBC,UAAU,EAAE;QACd;MACF,CAAE;MAAAT,QAAA,GAEFL,IAAA,CAACG,KAAK,CAACY,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,SAAS,EAAExB,WAAY;QACvByB,OAAO,EAAE;UAAEC,WAAW,EAAE;QAAM;MAAE,CACjC,CAAC,EACFnB,IAAA,CAACG,KAAK,CAACY,MAAM;QACXC,IAAI,EAAC,qBAAqB;QAC1BC,SAAS,EAAEvB,mBAAoB;QAC/BwB,OAAO,EAAE;UACPE,KAAK,EAAE,sBAAsB;UAC7BC,UAAU,EAAE;QACd;MAAE,CACH,CAAC,EACFrB,IAAA,CAACG,KAAK,CAACY,MAAM;QACXC,IAAI,EAAC,SAAS;QACdC,SAAS,EAAEtB,aAAc;QACzBuB,OAAO,EAAE;UAAEE,KAAK,EAAE;QAAc;MAAE,CACnC,CAAC,EACFpB,IAAA,CAACG,KAAK,CAACY,MAAM;QACXC,IAAI,EAAC,cAAc;QACnBC,SAAS,EAAErB,kBAAmB;QAC9BsB,OAAO,EAAE;UAAEE,KAAK,EAAE;QAAe;MAAE,CACpC,CAAC,EACFpB,IAAA,CAACG,KAAK,CAACY,MAAM;QACXC,IAAI,EAAC,UAAU;QACfC,SAAS,EAAEpB,cAAe;QAC1BqB,OAAO,EAAE;UAAEE,KAAK,EAAE;QAAW;MAAE,CAChC,CAAC,EACFpB,IAAA,CAACG,KAAK,CAACY,MAAM;QACXC,IAAI,EAAC,WAAW;QAChBC,SAAS,EAAEnB,eAAgB;QAC3BoB,OAAO,EAAE;UAAEE,KAAK,EAAE;QAAa;MAAE,CAClC,CAAC;IAAA,CACa,CAAC;EAAA,CACC,CAAC;AAE1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}