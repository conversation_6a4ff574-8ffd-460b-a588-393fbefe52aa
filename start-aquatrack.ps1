# Script PowerShell pour démarrer AquaTrack Mobile
Write-Host "========================================" -ForegroundColor Green
Write-Host "    DEMARRAGE AQUATRACK MOBILE" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Fonction pour vérifier si un port est utilisé
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Arrêter les processus existants
Write-Host "1. Arrêt des processus existants..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# Démarrer le backend
Write-Host "2. Démarrage du backend..." -ForegroundColor Yellow
$backendPath = Get-Location
Start-Process -FilePath "cmd" -ArgumentList "/k", "title Backend AquaTrack && node simple-server.js" -WorkingDirectory $backendPath

# Attendre que le backend démarre
Write-Host "3. Attente du démarrage du backend..." -ForegroundColor Yellow
$timeout = 30
$elapsed = 0
do {
    Start-Sleep -Seconds 2
    $elapsed += 2
    Write-Host "   Vérification du backend... ($elapsed/$timeout secondes)" -ForegroundColor Gray
} while (-not (Test-Port 4000) -and $elapsed -lt $timeout)

if (Test-Port 4000) {
    Write-Host "   ✅ Backend démarré avec succès !" -ForegroundColor Green
} else {
    Write-Host "   ❌ Échec du démarrage du backend" -ForegroundColor Red
    exit 1
}

# Démarrer l'application mobile
Write-Host "4. Démarrage de l'application mobile..." -ForegroundColor Yellow
$mobilePath = Join-Path $backendPath "mobile"
if (Test-Path $mobilePath) {
    Start-Process -FilePath "cmd" -ArgumentList "/k", "title Mobile AquaTrack && npx expo start --web" -WorkingDirectory $mobilePath
    
    # Attendre que l'application mobile démarre
    Write-Host "5. Attente du démarrage de l'application mobile..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    
    # Ouvrir les navigateurs
    Write-Host "6. Ouverture des navigateurs..." -ForegroundColor Yellow
    Start-Process "http://localhost:4000"
    Start-Sleep -Seconds 3
    Start-Process "http://localhost:19006"
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "    ✅ AQUATRACK DEMARRE !" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "📡 Backend: http://localhost:4000" -ForegroundColor Cyan
    Write-Host "📱 Mobile: http://localhost:19006" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🔑 Comptes de test:" -ForegroundColor Yellow
    Write-Host "   - Tech: <EMAIL> / Tech123" -ForegroundColor White
    Write-Host "   - Admin: <EMAIL> / Admin123" -ForegroundColor White
    Write-Host ""
    Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
} else {
    Write-Host "   ❌ Dossier mobile introuvable : $mobilePath" -ForegroundColor Red
    exit 1
}
