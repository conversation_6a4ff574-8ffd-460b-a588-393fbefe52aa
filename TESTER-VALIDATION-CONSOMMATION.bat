@echo off
title Test Validation Consommation Actuelle
color 0A

echo.
echo ========================================
echo    ⚡ TEST VALIDATION CONSOMMATION
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR...
echo.
start "🧪 Serveur Validation" cmd /k "title SERVEUR VALIDATION && color 0B && echo ========================================== && echo    🧪 SERVEUR VALIDATION && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Validation: Temps réel && echo ✅ Bouton erreur: Actif && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 5. DEMARRAGE DE L'APPLICATION...
echo.
cd mobile
start "📱 App Validation" cmd /k "title APPLICATION VALIDATION && color 0D && echo ========================================== && echo    📱 APPLICATION VALIDATION && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Validation: En temps réel && echo ✅ Erreur: Bouton rouge && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 6. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 7. OUVERTURE POUR TEST...
start http://localhost:19006

echo.
echo ========================================
echo    ⚡ VALIDATION CONSOMMATION ACTUELLE
echo ========================================
echo.
echo 🎯 FONCTIONNALITÉ AJOUTÉE:
echo.
echo ✅ VALIDATION EN TEMPS RÉEL:
echo    - Le champ "⚡ Consommation Actuelle (m³)" valide automatiquement
echo    - Vérifie que la consommation actuelle > consommation précédente
echo    - Validation déclenchée 500ms après la saisie
echo.
echo ✅ BOUTON D'ERREUR:
echo    - Apparaît si la consommation actuelle ≤ consommation précédente
echo    - Bouton rouge avec message d'erreur
echo    - Clic sur le bouton → popup avec détails
echo.
echo ✅ INDICATION VISUELLE:
echo    - Champ avec bordure rouge et fond rose en cas d'erreur
echo    - Placeholder dynamique: "Doit être > X m³"
echo    - Message d'erreur détaillé dans le bouton
echo.
echo 📊 EXEMPLES DE VALIDATION:
echo.
echo 👤 Benali Fatima (Consommation précédente: 45 m³):
echo    ✅ Consommation actuelle: 50 m³ → VALIDE
echo    ❌ Consommation actuelle: 40 m³ → INVALIDE (bouton d'erreur)
echo    ❌ Consommation actuelle: 45 m³ → INVALIDE (égale)
echo.
echo 👤 Alami Mohammed (Consommation précédente: 38 m³):
echo    ✅ Consommation actuelle: 42 m³ → VALIDE
echo    ❌ Consommation actuelle: 35 m³ → INVALIDE (bouton d'erreur)
echo    ❌ Consommation actuelle: 38 m³ → INVALIDE (égale)
echo.
echo 👤 Benjelloun Youssef (Consommation précédente: 67 m³):
echo    ✅ Consommation actuelle: 70 m³ → VALIDE
echo    ❌ Consommation actuelle: 60 m³ → INVALIDE (bouton d'erreur)
echo    ❌ Consommation actuelle: 67 m³ → INVALIDE (égale)
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 TESTEZ LA VALIDATION:
echo.
echo    a) 📍 ÉTAPE 1 - Préparation:
echo       - Sélectionnez "Centre-Ville" (secteur)
echo       - Sélectionnez "Benali Fatima" (client)
echo       - Le champ "📊 Consommation Précédente" se remplit: "45"
echo       - Le placeholder du champ "⚡ Consommation Actuelle" devient:
echo         "Doit être > 45 m³"
echo.
echo    b) ✅ ÉTAPE 2 - Test valeur valide:
echo       - Saisissez "50" dans "⚡ Consommation Actuelle"
echo       - Attendez 500ms
echo       - Aucun bouton d'erreur ne doit apparaître
echo       - Le champ reste normal (bordure grise)
echo.
echo    c) ❌ ÉTAPE 3 - Test valeur invalide:
echo       - Saisissez "40" dans "⚡ Consommation Actuelle"
echo       - Attendez 500ms
echo       - Un bouton rouge doit apparaître:
echo         "❌ Consommation Invalide - Cliquez pour plus d'infos"
echo       - Le champ devient rouge (bordure rouge, fond rose)
echo.
echo    d) 📱 ÉTAPE 4 - Test du bouton d'erreur:
echo       - Cliquez sur le bouton rouge
echo       - Une popup doit s'afficher:
echo         Titre: "❌ Consommation Invalide"
echo         Message: "Consommation invalide ! La consommation actuelle (40 m³)
echo                   doit être supérieure à la consommation précédente (45 m³)"
echo         Bouton: "Compris"
echo.
echo    e) 🔄 ÉTAPE 5 - Test correction:
echo       - Fermez la popup
echo       - Modifiez la valeur pour "48"
echo       - Attendez 500ms
echo       - Le bouton d'erreur doit disparaître
echo       - Le champ redevient normal
echo.
echo    f) 🧪 ÉTAPE 6 - Test autres clients:
echo       - Changez pour "Alami Mohammed" (précédente: 38 m³)
echo       - Testez avec "35" → Erreur attendue
echo       - Testez avec "42" → Valide
echo       - Changez pour "Benjelloun Youssef" (précédente: 67 m³)
echo       - Testez avec "65" → Erreur attendue
echo       - Testez avec "70" → Valide
echo.
echo 📊 COMPORTEMENTS ATTENDUS:
echo.
echo ✅ CHAMP VIDE:
echo    ⚡ Consommation Actuelle: []
echo    🎨 Apparence: Normale
echo    ❌ Bouton erreur: Pas visible
echo    ℹ️ Validation: Pas de validation
echo.
echo ✅ VALEUR VALIDE (Actuelle > Précédente):
echo    ⚡ Consommation Actuelle: [50] (si précédente = 45)
echo    🎨 Apparence: Normale (bordure grise)
echo    ❌ Bouton erreur: Pas visible
echo    ✅ Validation: Réussie
echo.
echo ✅ VALEUR INVALIDE (Actuelle ≤ Précédente):
echo    ⚡ Consommation Actuelle: [40] (si précédente = 45)
echo    🎨 Apparence: Erreur (bordure rouge, fond rose)
echo    ❌ Bouton erreur: Visible (rouge)
echo    ❌ Validation: Échouée
echo.
echo ✅ VALEUR ÉGALE (Actuelle = Précédente):
echo    ⚡ Consommation Actuelle: [45] (si précédente = 45)
echo    🎨 Apparence: Erreur (bordure rouge, fond rose)
echo    ❌ Bouton erreur: Visible (rouge)
echo    ❌ Validation: Échouée
echo.
echo 🔧 FONCTIONNALITÉS TESTÉES:
echo.
echo ✅ Validation en temps réel (délai 500ms)
echo ✅ Comparaison numérique précise
echo ✅ Affichage conditionnel du bouton d'erreur
echo ✅ Indication visuelle sur le champ (bordure/fond)
echo ✅ Popup d'erreur détaillée
echo ✅ Placeholder dynamique avec valeur de référence
echo ✅ Gestion des valeurs non numériques
echo ✅ Logs détaillés pour debug
echo.
echo 🎨 APPARENCE DU BOUTON D'ERREUR:
echo.
echo ✅ Couleur: Rouge (#dc3545)
echo ✅ Texte: Blanc, gras, centré
echo ✅ Message: "❌ Consommation Invalide - Cliquez pour plus d'infos"
echo ✅ Effet: Ombre portée, élévation
echo ✅ Position: Sous le champ de saisie
echo ✅ Action: Clic → Popup avec détails
echo.
echo 🔍 DEBUG ET LOGS:
echo.
echo Pour voir les logs détaillés:
echo 1. Ouvrez la console du navigateur (F12)
echo 2. Onglet "Console"
echo 3. Recherchez les messages:
echo    - "🔄 Changement consommation actuelle"
echo    - "🔍 VALIDATION CONSOMMATION ACTUELLE"
echo    - "📊 Consommation actuelle saisie"
echo    - "📊 Consommation précédente"
echo    - "🔢 Valeurs numériques"
echo    - "❌ ERREUR DE VALIDATION" ou "✅ VALIDATION RÉUSSIE"
echo.
echo 🌐 URLS DE TEST:
echo    - Application: http://localhost:19006
echo    - Console logs: F12 → Console
echo.
echo ✅ VALIDATION CONSOMMATION ACTUELLE IMPLÉMENTÉE !
echo    Le champ valide maintenant en temps réel et affiche
echo    un bouton d'erreur si la consommation est invalide.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
