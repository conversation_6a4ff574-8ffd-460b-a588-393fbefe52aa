{"version": 3, "file": "ExpoHtmlWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ExpoHtmlWebpackPlugin.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAC1B,8EAA4D;AAE5D,gCAAsD;AAEtD,oCAAwD;AAExD,MAAM,cAAc,GAAG;IACrB,cAAc,EAAE,IAAI;IACpB,kBAAkB,EAAE,IAAI;IACxB,yBAAyB,EAAE,IAAI;IAC/B,eAAe,EAAE,IAAI;IACrB,qBAAqB,EAAE,IAAI;IAC3B,6BAA6B,EAAE,IAAI;IACnC,gBAAgB,EAAE,IAAI;IACtB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,IAAI;CACjB,CAAC;AAEF;;;;GAIG;AACH,MAAqB,iBAAkB,SAAQ,6BAAyB;IACtE,YAAY,GAAgB,EAAE,gBAAsB;;QAClD,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,IAAA,cAAQ,EAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;QAClE,MAAM,MAAM,GAAG,IAAA,eAAS,EAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,YAAY,GAAG,IAAA,aAAO,EAAC,GAAG,CAAC,KAAK,YAAY,CAAC;QAEnD;;;WAGG;QACH,MAAM,MAAM,GAAG,IAAA,oCAA4B,EACzC,YAAY,CAAC,CAAC,CAAC,MAAA,MAAA,MAAM,CAAC,GAAG,0CAAE,KAAK,0CAAE,UAAU,CAAC,CAAC,CAAC,KAAK,EACpD,cAAc,CACf,CAAC;QAEF,MAAM,IAAI,GAAwB,EAAE,CAAC;QAErC,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACzD,aAAa;YACb,MAAM,YAAY,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAE/D,wEAAwE;YACxE,uEAAuE;YACvE,8EAA8E;YAC9E,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,UAAU,CAAC,EAAE;gBAC/E,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,QAAQ,CAAC,KAAK,CAClB,6JAA6J,CAC9J;oBACC,eAAK,CAAC,OAAO,CACX,8HAA8H,CAC/H,CACJ,CAAC;gBACF,IAAI,CAAC,QAAQ;oBACX,iGAAiG,CAAC;aACrG;YACD,2IAA2I;YAC3I,sEAAsE;YACtE,IACE,CAAA,MAAA,MAAM,CAAC,GAAG,0CAAE,UAAU;gBACtB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,aAAa,CAAC,EAC9E;gBACA,IAAI,CAAC,aAAa,CAAC,GAAG,MAAA,MAAM,CAAC,GAAG,0CAAE,UAAU,CAAC;aAC9C;YAED,IACE,CAAA,MAAA,MAAM,CAAC,GAAG,0CAAE,WAAW;gBACvB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,aAAa,CAAC,EAC9E;gBACA,IAAI,CAAC,aAAa,CAAC,GAAG,MAAA,MAAM,CAAC,GAAG,0CAAE,WAAW,CAAC;aAC/C;SACF;QAED,KAAK,CAAC;YACJ,iCAAiC;YACjC,QAAQ,EAAE,SAAS,CAAC,UAAU,CAAC,SAAS;YACxC,oDAAoD;YACpD,KAAK,EAAE,MAAA,MAAM,CAAC,GAAG,0CAAE,IAAI;YACvB,4DAA4D;YAC5D,kEAAkE;YAClE,MAAM;YACN,8CAA8C;YAC9C,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS;YACtC,IAAI;SACL,CAAC,CAAC;IACL,CAAC;CACF;AAlED,oCAkEC", "sourcesContent": ["import chalk from 'chalk';\nimport OriginalHtmlWebpackPlugin from 'html-webpack-plugin';\n\nimport { getConfig, getMode, getPaths } from '../env';\nimport { Environment } from '../types';\nimport { overrideWithPropertyOrConfig } from '../utils';\n\nconst DEFAULT_MINIFY = {\n  removeComments: true,\n  collapseWhitespace: true,\n  removeRedundantAttributes: true,\n  useShortDoctype: true,\n  removeEmptyAttributes: true,\n  removeStyleLinkTypeAttributes: true,\n  keepClosingSlash: true,\n  minifyJS: true,\n  minifyCSS: true,\n  minifyURLs: true,\n};\n\n/**\n * Generates an `index.html` file with the <script> injected.\n *\n * @category plugins\n */\nexport default class HtmlWebpackPlugin extends OriginalHtmlWebpackPlugin {\n  constructor(env: Environment, templateHtmlData?: any) {\n    const locations = env.locations || getPaths(env.projectRoot, env);\n    const config = getConfig(env);\n    const isProduction = getMode(env) === 'production';\n\n    /**\n     * The user can disable minify with\n     * `web.minifyHTML = false || {}`\n     */\n    const minify = overrideWithPropertyOrConfig(\n      isProduction ? config.web?.build?.minifyHTML : false,\n      DEFAULT_MINIFY\n    );\n\n    const meta: Record<string, any> = {};\n\n    if (templateHtmlData && templateHtmlData.querySelectorAll) {\n      // @ts-ignore\n      const templateMeta = templateHtmlData.querySelectorAll('meta');\n\n      // Ensure there is no viewport meta tag in the default `web/index.html`.\n      // Because the viewport tag has been moved into the template, this will\n      // ensure that legacy `web/index.html`s get a viewport meta tag added to them.\n      if (!templateMeta.some((node: any) => node.getAttribute('name') === 'viewport')) {\n        console.warn(\n          chalk.bgYellow.black(\n            'Warning: No viewport meta tag is defined in the <head /> of `web/index.html`. Please update your `web/index.html` to include one. The default value is:\\n\\n'\n          ) +\n            chalk.magenta(\n              '<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1.00001,viewport-fit=cover\">'\n            )\n        );\n        meta.viewport =\n          'width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1.00001, viewport-fit=cover';\n      }\n      // Meta tag to define a suggested color that browsers should use to customize the display of the page or of the surrounding user interface.\n      // The meta tag overrides any theme-color set in the web app manifest.\n      if (\n        config.web?.themeColor &&\n        !templateMeta.some((node: any) => node.getAttribute('name') === 'theme-color')\n      ) {\n        meta['theme-color'] = config.web?.themeColor;\n      }\n\n      if (\n        config.web?.description &&\n        !templateMeta.some((node: any) => node.getAttribute('name') === 'description')\n      ) {\n        meta['description'] = config.web?.description;\n      }\n    }\n\n    super({\n      // The file to write the HTML to.\n      filename: locations.production.indexHtml,\n      // The title to use for the generated HTML document.\n      title: config.web?.name,\n      // Pass a html-minifier options object to minify the output.\n      // https://github.com/kangax/html-minifier#options-quick-reference\n      minify,\n      // The `webpack` require path to the template.\n      template: locations.template.indexHtml,\n      meta,\n    });\n  }\n}\n"]}