{"name": "@expo/webpack-config", "version": "19.0.1", "author": "Expo <<EMAIL>>", "description": "A Webpack configuration used to bundle Expo websites with Expo CLI.", "license": "MIT", "main": "webpack.config.js", "types": "webpack.config.d.ts", "scripts": {"build": "expo-module tsc", "clean": "expo-module clean", "lint": "expo-module lint", "prepare": "expo-module clean && expo-module tsc", "test": "expo-module test", "test:e2e:start": "EXPO_E2E_COMMAND='start' jest --config e2e/jest.config.js --runInBand", "test:e2e:build": "EXPO_E2E_COMMAND='build' jest --config e2e/jest.config.js --runInBand", "typecheck": "expo-module typecheck", "watch": "expo-module-tsc --watch --preserveWatchOutput"}, "files": ["addons", "env", "loaders", "plugins", "template", "utils", "web-default", "webpack", "getWebExtensions.js", "webpack.config.d.ts", "webpack.config.js"], "homepage": "https://github.com/expo/expo-webpack-integrations/tree/main/packages/webpack-config#readme", "bugs": {"url": "https://github.com/expo/expo-webpack-integrations/issues"}, "repository": {"type": "git", "url": "https://github.com/expo/expo-webpack-integrations.git", "directory": "packages/webpack-config"}, "dependencies": {"@babel/core": "^7.20.2", "babel-loader": "^8.3.0", "chalk": "^4.0.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^10.2.0", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.4.1", "expo-pwa": "0.0.127", "find-up": "^5.0.0", "find-yarn-workspace-root": "~2.0.0", "fs-extra": "^11.2.0", "getenv": "^1.0.0", "html-webpack-plugin": "^5.5.0", "is-wsl": "^2.0.0", "mini-css-extract-plugin": "^2.5.2", "node-html-parser": "^5.2.0", "semver": "~7.5.4", "source-map-loader": "^3.0.1", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.0", "webpack": "^5.64.4", "webpack-dev-server": "^4.11.1", "webpack-manifest-plugin": "^4.1.1"}, "devDependencies": {"@expo/spawn-async": "^1.7.2", "@types/babel__code-frame": "^7.0.6", "@types/copy-webpack-plugin": "^10.1.0", "@types/css-minimizer-webpack-plugin": "3.2.1", "@types/fs-extra": "^11.0.4", "@types/getenv": "^1.0.3", "@types/html-webpack-plugin": "^3.2.6", "@types/image-size": "^0.8.0", "@types/loader-utils": "^2.0.3", "@types/terser-webpack-plugin": "^5.2.0", "@types/webpack-dev-server": "^4.7.2", "@types/webpack-manifest-plugin": "^3.0.5", "arg": "^5.0.2", "expo": "^49.0.7", "expo-module-scripts": "^3.4.0", "jest": "^29.7.0", "jest-puppeteer": "^9.0.2", "puppeteer": "^21.6.1", "react-native": "0.72.3", "resize-observer-polyfill": "^1.5.1", "serve": "^11.1.0"}, "peerDependencies": {"expo": "^49.0.7 || ^50.0.0-0"}, "engines": {"node": ">=12"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}