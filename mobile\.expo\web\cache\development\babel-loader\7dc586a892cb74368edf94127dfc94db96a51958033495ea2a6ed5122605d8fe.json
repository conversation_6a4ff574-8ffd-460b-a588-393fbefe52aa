{"ast": null, "code": "'use strict';\nexport function elementsThatOverlapOffsets(offsets, props, getFrameMetrics, zoomScale) {\n  if (zoomScale === void 0) {\n    zoomScale = 1;\n  }\n  var itemCount = props.getItemCount(props.data);\n  var result = [];\n  for (var offsetIndex = 0; offsetIndex < offsets.length; offsetIndex++) {\n    var currentOffset = offsets[offsetIndex];\n    var left = 0;\n    var right = itemCount - 1;\n    while (left <= right) {\n      var mid = left + (right - left >>> 1);\n      var frame = getFrameMetrics(mid, props);\n      var scaledOffsetStart = frame.offset * zoomScale;\n      var scaledOffsetEnd = (frame.offset + frame.length) * zoomScale;\n      if (mid === 0 && currentOffset < scaledOffsetStart || mid !== 0 && currentOffset <= scaledOffsetStart) {\n        right = mid - 1;\n      } else if (currentOffset > scaledOffsetEnd) {\n        left = mid + 1;\n      } else {\n        result[offsetIndex] = mid;\n        break;\n      }\n    }\n  }\n  return result;\n}\nexport function newRangeCount(prev, next) {\n  return next.last - next.first + 1 - Math.max(0, 1 + Math.min(next.last, prev.last) - Math.max(next.first, prev.first));\n}\nexport function computeWindowedRenderLimits(props, maxToRenderPerBatch, windowSize, prev, getFrameMetricsApprox, scrollMetrics) {\n  var itemCount = props.getItemCount(props.data);\n  if (itemCount === 0) {\n    return {\n      first: 0,\n      last: -1\n    };\n  }\n  var offset = scrollMetrics.offset,\n    velocity = scrollMetrics.velocity,\n    visibleLength = scrollMetrics.visibleLength,\n    _scrollMetrics$zoomSc = scrollMetrics.zoomScale,\n    zoomScale = _scrollMetrics$zoomSc === void 0 ? 1 : _scrollMetrics$zoomSc;\n  var visibleBegin = Math.max(0, offset);\n  var visibleEnd = visibleBegin + visibleLength;\n  var overscanLength = (windowSize - 1) * visibleLength;\n  var leadFactor = 0.5;\n  var fillPreference = velocity > 1 ? 'after' : velocity < -1 ? 'before' : 'none';\n  var overscanBegin = Math.max(0, visibleBegin - (1 - leadFactor) * overscanLength);\n  var overscanEnd = Math.max(0, visibleEnd + leadFactor * overscanLength);\n  var lastItemOffset = getFrameMetricsApprox(itemCount - 1, props).offset * zoomScale;\n  if (lastItemOffset < overscanBegin) {\n    return {\n      first: Math.max(0, itemCount - 1 - maxToRenderPerBatch),\n      last: itemCount - 1\n    };\n  }\n  var _elementsThatOverlapO = elementsThatOverlapOffsets([overscanBegin, visibleBegin, visibleEnd, overscanEnd], props, getFrameMetricsApprox, zoomScale),\n    overscanFirst = _elementsThatOverlapO[0],\n    first = _elementsThatOverlapO[1],\n    last = _elementsThatOverlapO[2],\n    overscanLast = _elementsThatOverlapO[3];\n  overscanFirst = overscanFirst == null ? 0 : overscanFirst;\n  first = first == null ? Math.max(0, overscanFirst) : first;\n  overscanLast = overscanLast == null ? itemCount - 1 : overscanLast;\n  last = last == null ? Math.min(overscanLast, first + maxToRenderPerBatch - 1) : last;\n  var visible = {\n    first: first,\n    last: last\n  };\n  var newCellCount = newRangeCount(prev, visible);\n  while (true) {\n    if (first <= overscanFirst && last >= overscanLast) {\n      break;\n    }\n    var maxNewCells = newCellCount >= maxToRenderPerBatch;\n    var firstWillAddMore = first <= prev.first || first > prev.last;\n    var firstShouldIncrement = first > overscanFirst && (!maxNewCells || !firstWillAddMore);\n    var lastWillAddMore = last >= prev.last || last < prev.first;\n    var lastShouldIncrement = last < overscanLast && (!maxNewCells || !lastWillAddMore);\n    if (maxNewCells && !firstShouldIncrement && !lastShouldIncrement) {\n      break;\n    }\n    if (firstShouldIncrement && !(fillPreference === 'after' && lastShouldIncrement && lastWillAddMore)) {\n      if (firstWillAddMore) {\n        newCellCount++;\n      }\n      first--;\n    }\n    if (lastShouldIncrement && !(fillPreference === 'before' && firstShouldIncrement && firstWillAddMore)) {\n      if (lastWillAddMore) {\n        newCellCount++;\n      }\n      last++;\n    }\n  }\n  if (!(last >= first && first >= 0 && last < itemCount && first >= overscanFirst && last <= overscanLast && first <= visible.first && last >= visible.last)) {\n    throw new Error('Bad window calculation ' + JSON.stringify({\n      first: first,\n      last: last,\n      itemCount: itemCount,\n      overscanFirst: overscanFirst,\n      overscanLast: overscanLast,\n      visible: visible\n    }));\n  }\n  return {\n    first: first,\n    last: last\n  };\n}\nexport function keyExtractor(item, index) {\n  if (typeof item === 'object' && (item == null ? void 0 : item.key) != null) {\n    return item.key;\n  }\n  if (typeof item === 'object' && (item == null ? void 0 : item.id) != null) {\n    return item.id;\n  }\n  return String(index);\n}", "map": {"version": 3, "names": ["elementsThatOverlapOffsets", "offsets", "props", "getFrameMetrics", "zoomScale", "itemCount", "getItemCount", "data", "result", "offsetIndex", "length", "currentOffset", "left", "right", "mid", "frame", "scaledOffsetStart", "offset", "scaledOffsetEnd", "newRangeCount", "prev", "next", "last", "first", "Math", "max", "min", "computeWindowedRenderLimits", "maxToRenderPerBatch", "windowSize", "getFrameMetricsApprox", "scrollMetrics", "velocity", "<PERSON><PERSON><PERSON><PERSON>", "_scrollMetrics$zoomSc", "visibleBegin", "visibleEnd", "overscanLength", "leadFactor", "fillPreference", "overscanBegin", "overscanEnd", "lastItemOffset", "_elementsThatOverlapO", "overscanFirst", "overscanLast", "visible", "newCellCount", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "firstWillAddMore", "firstShouldIncrement", "lastWillAddMore", "lastShouldIncrement", "Error", "JSON", "stringify", "keyExtractor", "item", "index", "key", "id", "String"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/vendor/react-native/VirtualizeUtils/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\n/**\n * Used to find the indices of the frames that overlap the given offsets. Useful for finding the\n * items that bound different windows of content, such as the visible area or the buffered overscan\n * area.\n */\nexport function elementsThatOverlapOffsets(offsets, props, getFrameMetrics, zoomScale) {\n  if (zoomScale === void 0) {\n    zoomScale = 1;\n  }\n  var itemCount = props.getItemCount(props.data);\n  var result = [];\n  for (var offsetIndex = 0; offsetIndex < offsets.length; offsetIndex++) {\n    var currentOffset = offsets[offsetIndex];\n    var left = 0;\n    var right = itemCount - 1;\n    while (left <= right) {\n      // eslint-disable-next-line no-bitwise\n      var mid = left + (right - left >>> 1);\n      var frame = getFrameMetrics(mid, props);\n      var scaledOffsetStart = frame.offset * zoomScale;\n      var scaledOffsetEnd = (frame.offset + frame.length) * zoomScale;\n\n      // We want the first frame that contains the offset, with inclusive bounds. Thus, for the\n      // first frame the scaledOffsetStart is inclusive, while for other frames it is exclusive.\n      if (mid === 0 && currentOffset < scaledOffsetStart || mid !== 0 && currentOffset <= scaledOffsetStart) {\n        right = mid - 1;\n      } else if (currentOffset > scaledOffsetEnd) {\n        left = mid + 1;\n      } else {\n        result[offsetIndex] = mid;\n        break;\n      }\n    }\n  }\n  return result;\n}\n\n/**\n * Computes the number of elements in the `next` range that are new compared to the `prev` range.\n * Handy for calculating how many new items will be rendered when the render window changes so we\n * can restrict the number of new items render at once so that content can appear on the screen\n * faster.\n */\nexport function newRangeCount(prev, next) {\n  return next.last - next.first + 1 - Math.max(0, 1 + Math.min(next.last, prev.last) - Math.max(next.first, prev.first));\n}\n\n/**\n * Custom logic for determining which items should be rendered given the current frame and scroll\n * metrics, as well as the previous render state. The algorithm may evolve over time, but generally\n * prioritizes the visible area first, then expands that with overscan regions ahead and behind,\n * biased in the direction of scroll.\n */\nexport function computeWindowedRenderLimits(props, maxToRenderPerBatch, windowSize, prev, getFrameMetricsApprox, scrollMetrics) {\n  var itemCount = props.getItemCount(props.data);\n  if (itemCount === 0) {\n    return {\n      first: 0,\n      last: -1\n    };\n  }\n  var offset = scrollMetrics.offset,\n    velocity = scrollMetrics.velocity,\n    visibleLength = scrollMetrics.visibleLength,\n    _scrollMetrics$zoomSc = scrollMetrics.zoomScale,\n    zoomScale = _scrollMetrics$zoomSc === void 0 ? 1 : _scrollMetrics$zoomSc;\n\n  // Start with visible area, then compute maximum overscan region by expanding from there, biased\n  // in the direction of scroll. Total overscan area is capped, which should cap memory consumption\n  // too.\n  var visibleBegin = Math.max(0, offset);\n  var visibleEnd = visibleBegin + visibleLength;\n  var overscanLength = (windowSize - 1) * visibleLength;\n\n  // Considering velocity seems to introduce more churn than it's worth.\n  var leadFactor = 0.5; // Math.max(0, Math.min(1, velocity / 25 + 0.5));\n\n  var fillPreference = velocity > 1 ? 'after' : velocity < -1 ? 'before' : 'none';\n  var overscanBegin = Math.max(0, visibleBegin - (1 - leadFactor) * overscanLength);\n  var overscanEnd = Math.max(0, visibleEnd + leadFactor * overscanLength);\n  var lastItemOffset = getFrameMetricsApprox(itemCount - 1, props).offset * zoomScale;\n  if (lastItemOffset < overscanBegin) {\n    // Entire list is before our overscan window\n    return {\n      first: Math.max(0, itemCount - 1 - maxToRenderPerBatch),\n      last: itemCount - 1\n    };\n  }\n\n  // Find the indices that correspond to the items at the render boundaries we're targeting.\n  var _elementsThatOverlapO = elementsThatOverlapOffsets([overscanBegin, visibleBegin, visibleEnd, overscanEnd], props, getFrameMetricsApprox, zoomScale),\n    overscanFirst = _elementsThatOverlapO[0],\n    first = _elementsThatOverlapO[1],\n    last = _elementsThatOverlapO[2],\n    overscanLast = _elementsThatOverlapO[3];\n  overscanFirst = overscanFirst == null ? 0 : overscanFirst;\n  first = first == null ? Math.max(0, overscanFirst) : first;\n  overscanLast = overscanLast == null ? itemCount - 1 : overscanLast;\n  last = last == null ? Math.min(overscanLast, first + maxToRenderPerBatch - 1) : last;\n  var visible = {\n    first,\n    last\n  };\n\n  // We want to limit the number of new cells we're rendering per batch so that we can fill the\n  // content on the screen quickly. If we rendered the entire overscan window at once, the user\n  // could be staring at white space for a long time waiting for a bunch of offscreen content to\n  // render.\n  var newCellCount = newRangeCount(prev, visible);\n  while (true) {\n    if (first <= overscanFirst && last >= overscanLast) {\n      // If we fill the entire overscan range, we're done.\n      break;\n    }\n    var maxNewCells = newCellCount >= maxToRenderPerBatch;\n    var firstWillAddMore = first <= prev.first || first > prev.last;\n    var firstShouldIncrement = first > overscanFirst && (!maxNewCells || !firstWillAddMore);\n    var lastWillAddMore = last >= prev.last || last < prev.first;\n    var lastShouldIncrement = last < overscanLast && (!maxNewCells || !lastWillAddMore);\n    if (maxNewCells && !firstShouldIncrement && !lastShouldIncrement) {\n      // We only want to stop if we've hit maxNewCells AND we cannot increment first or last\n      // without rendering new items. This let's us preserve as many already rendered items as\n      // possible, reducing render churn and keeping the rendered overscan range as large as\n      // possible.\n      break;\n    }\n    if (firstShouldIncrement && !(fillPreference === 'after' && lastShouldIncrement && lastWillAddMore)) {\n      if (firstWillAddMore) {\n        newCellCount++;\n      }\n      first--;\n    }\n    if (lastShouldIncrement && !(fillPreference === 'before' && firstShouldIncrement && firstWillAddMore)) {\n      if (lastWillAddMore) {\n        newCellCount++;\n      }\n      last++;\n    }\n  }\n  if (!(last >= first && first >= 0 && last < itemCount && first >= overscanFirst && last <= overscanLast && first <= visible.first && last >= visible.last)) {\n    throw new Error('Bad window calculation ' + JSON.stringify({\n      first,\n      last,\n      itemCount,\n      overscanFirst,\n      overscanLast,\n      visible\n    }));\n  }\n  return {\n    first,\n    last\n  };\n}\nexport function keyExtractor(item, index) {\n  if (typeof item === 'object' && (item == null ? void 0 : item.key) != null) {\n    return item.key;\n  }\n  if (typeof item === 'object' && (item == null ? void 0 : item.id) != null) {\n    return item.id;\n  }\n  return String(index);\n}"], "mappings": "AAUA,YAAY;AAOZ,OAAO,SAASA,0BAA0BA,CAACC,OAAO,EAAEC,KAAK,EAAEC,eAAe,EAAEC,SAAS,EAAE;EACrF,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,CAAC;EACf;EACA,IAAIC,SAAS,GAAGH,KAAK,CAACI,YAAY,CAACJ,KAAK,CAACK,IAAI,CAAC;EAC9C,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGR,OAAO,CAACS,MAAM,EAAED,WAAW,EAAE,EAAE;IACrE,IAAIE,aAAa,GAAGV,OAAO,CAACQ,WAAW,CAAC;IACxC,IAAIG,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAGR,SAAS,GAAG,CAAC;IACzB,OAAOO,IAAI,IAAIC,KAAK,EAAE;MAEpB,IAAIC,GAAG,GAAGF,IAAI,IAAIC,KAAK,GAAGD,IAAI,KAAK,CAAC,CAAC;MACrC,IAAIG,KAAK,GAAGZ,eAAe,CAACW,GAAG,EAAEZ,KAAK,CAAC;MACvC,IAAIc,iBAAiB,GAAGD,KAAK,CAACE,MAAM,GAAGb,SAAS;MAChD,IAAIc,eAAe,GAAG,CAACH,KAAK,CAACE,MAAM,GAAGF,KAAK,CAACL,MAAM,IAAIN,SAAS;MAI/D,IAAIU,GAAG,KAAK,CAAC,IAAIH,aAAa,GAAGK,iBAAiB,IAAIF,GAAG,KAAK,CAAC,IAAIH,aAAa,IAAIK,iBAAiB,EAAE;QACrGH,KAAK,GAAGC,GAAG,GAAG,CAAC;MACjB,CAAC,MAAM,IAAIH,aAAa,GAAGO,eAAe,EAAE;QAC1CN,IAAI,GAAGE,GAAG,GAAG,CAAC;MAChB,CAAC,MAAM;QACLN,MAAM,CAACC,WAAW,CAAC,GAAGK,GAAG;QACzB;MACF;IACF;EACF;EACA,OAAON,MAAM;AACf;AAQA,OAAO,SAASW,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACxC,OAAOA,IAAI,CAACC,IAAI,GAAGD,IAAI,CAACE,KAAK,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGD,IAAI,CAACE,GAAG,CAACL,IAAI,CAACC,IAAI,EAAEF,IAAI,CAACE,IAAI,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACE,KAAK,EAAEH,IAAI,CAACG,KAAK,CAAC,CAAC;AACxH;AAQA,OAAO,SAASI,2BAA2BA,CAACzB,KAAK,EAAE0B,mBAAmB,EAAEC,UAAU,EAAET,IAAI,EAAEU,qBAAqB,EAAEC,aAAa,EAAE;EAC9H,IAAI1B,SAAS,GAAGH,KAAK,CAACI,YAAY,CAACJ,KAAK,CAACK,IAAI,CAAC;EAC9C,IAAIF,SAAS,KAAK,CAAC,EAAE;IACnB,OAAO;MACLkB,KAAK,EAAE,CAAC;MACRD,IAAI,EAAE,CAAC;IACT,CAAC;EACH;EACA,IAAIL,MAAM,GAAGc,aAAa,CAACd,MAAM;IAC/Be,QAAQ,GAAGD,aAAa,CAACC,QAAQ;IACjCC,aAAa,GAAGF,aAAa,CAACE,aAAa;IAC3CC,qBAAqB,GAAGH,aAAa,CAAC3B,SAAS;IAC/CA,SAAS,GAAG8B,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;EAK1E,IAAIC,YAAY,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,MAAM,CAAC;EACtC,IAAImB,UAAU,GAAGD,YAAY,GAAGF,aAAa;EAC7C,IAAII,cAAc,GAAG,CAACR,UAAU,GAAG,CAAC,IAAII,aAAa;EAGrD,IAAIK,UAAU,GAAG,GAAG;EAEpB,IAAIC,cAAc,GAAGP,QAAQ,GAAG,CAAC,GAAG,OAAO,GAAGA,QAAQ,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,MAAM;EAC/E,IAAIQ,aAAa,GAAGhB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEU,YAAY,GAAG,CAAC,CAAC,GAAGG,UAAU,IAAID,cAAc,CAAC;EACjF,IAAII,WAAW,GAAGjB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEW,UAAU,GAAGE,UAAU,GAAGD,cAAc,CAAC;EACvE,IAAIK,cAAc,GAAGZ,qBAAqB,CAACzB,SAAS,GAAG,CAAC,EAAEH,KAAK,CAAC,CAACe,MAAM,GAAGb,SAAS;EACnF,IAAIsC,cAAc,GAAGF,aAAa,EAAE;IAElC,OAAO;MACLjB,KAAK,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpB,SAAS,GAAG,CAAC,GAAGuB,mBAAmB,CAAC;MACvDN,IAAI,EAAEjB,SAAS,GAAG;IACpB,CAAC;EACH;EAGA,IAAIsC,qBAAqB,GAAG3C,0BAA0B,CAAC,CAACwC,aAAa,EAAEL,YAAY,EAAEC,UAAU,EAAEK,WAAW,CAAC,EAAEvC,KAAK,EAAE4B,qBAAqB,EAAE1B,SAAS,CAAC;IACrJwC,aAAa,GAAGD,qBAAqB,CAAC,CAAC,CAAC;IACxCpB,KAAK,GAAGoB,qBAAqB,CAAC,CAAC,CAAC;IAChCrB,IAAI,GAAGqB,qBAAqB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,qBAAqB,CAAC,CAAC,CAAC;EACzCC,aAAa,GAAGA,aAAa,IAAI,IAAI,GAAG,CAAC,GAAGA,aAAa;EACzDrB,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEmB,aAAa,CAAC,GAAGrB,KAAK;EAC1DsB,YAAY,GAAGA,YAAY,IAAI,IAAI,GAAGxC,SAAS,GAAG,CAAC,GAAGwC,YAAY;EAClEvB,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAGE,IAAI,CAACE,GAAG,CAACmB,YAAY,EAAEtB,KAAK,GAAGK,mBAAmB,GAAG,CAAC,CAAC,GAAGN,IAAI;EACpF,IAAIwB,OAAO,GAAG;IACZvB,KAAK,EAALA,KAAK;IACLD,IAAI,EAAJA;EACF,CAAC;EAMD,IAAIyB,YAAY,GAAG5B,aAAa,CAACC,IAAI,EAAE0B,OAAO,CAAC;EAC/C,OAAO,IAAI,EAAE;IACX,IAAIvB,KAAK,IAAIqB,aAAa,IAAItB,IAAI,IAAIuB,YAAY,EAAE;MAElD;IACF;IACA,IAAIG,WAAW,GAAGD,YAAY,IAAInB,mBAAmB;IACrD,IAAIqB,gBAAgB,GAAG1B,KAAK,IAAIH,IAAI,CAACG,KAAK,IAAIA,KAAK,GAAGH,IAAI,CAACE,IAAI;IAC/D,IAAI4B,oBAAoB,GAAG3B,KAAK,GAAGqB,aAAa,KAAK,CAACI,WAAW,IAAI,CAACC,gBAAgB,CAAC;IACvF,IAAIE,eAAe,GAAG7B,IAAI,IAAIF,IAAI,CAACE,IAAI,IAAIA,IAAI,GAAGF,IAAI,CAACG,KAAK;IAC5D,IAAI6B,mBAAmB,GAAG9B,IAAI,GAAGuB,YAAY,KAAK,CAACG,WAAW,IAAI,CAACG,eAAe,CAAC;IACnF,IAAIH,WAAW,IAAI,CAACE,oBAAoB,IAAI,CAACE,mBAAmB,EAAE;MAKhE;IACF;IACA,IAAIF,oBAAoB,IAAI,EAAEX,cAAc,KAAK,OAAO,IAAIa,mBAAmB,IAAID,eAAe,CAAC,EAAE;MACnG,IAAIF,gBAAgB,EAAE;QACpBF,YAAY,EAAE;MAChB;MACAxB,KAAK,EAAE;IACT;IACA,IAAI6B,mBAAmB,IAAI,EAAEb,cAAc,KAAK,QAAQ,IAAIW,oBAAoB,IAAID,gBAAgB,CAAC,EAAE;MACrG,IAAIE,eAAe,EAAE;QACnBJ,YAAY,EAAE;MAChB;MACAzB,IAAI,EAAE;IACR;EACF;EACA,IAAI,EAAEA,IAAI,IAAIC,KAAK,IAAIA,KAAK,IAAI,CAAC,IAAID,IAAI,GAAGjB,SAAS,IAAIkB,KAAK,IAAIqB,aAAa,IAAItB,IAAI,IAAIuB,YAAY,IAAItB,KAAK,IAAIuB,OAAO,CAACvB,KAAK,IAAID,IAAI,IAAIwB,OAAO,CAACxB,IAAI,CAAC,EAAE;IAC1J,MAAM,IAAI+B,KAAK,CAAC,yBAAyB,GAAGC,IAAI,CAACC,SAAS,CAAC;MACzDhC,KAAK,EAALA,KAAK;MACLD,IAAI,EAAJA,IAAI;MACJjB,SAAS,EAATA,SAAS;MACTuC,aAAa,EAAbA,aAAa;MACbC,YAAY,EAAZA,YAAY;MACZC,OAAO,EAAPA;IACF,CAAC,CAAC,CAAC;EACL;EACA,OAAO;IACLvB,KAAK,EAALA,KAAK;IACLD,IAAI,EAAJA;EACF,CAAC;AACH;AACA,OAAO,SAASkC,YAAYA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACxC,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACE,GAAG,KAAK,IAAI,EAAE;IAC1E,OAAOF,IAAI,CAACE,GAAG;EACjB;EACA,IAAI,OAAOF,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACG,EAAE,KAAK,IAAI,EAAE;IACzE,OAAOH,IAAI,CAACG,EAAE;EAChB;EACA,OAAOC,MAAM,CAACH,KAAK,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}