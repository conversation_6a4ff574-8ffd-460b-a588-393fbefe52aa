# 🚀 Guide de Démarrage Manuel - AquaTrack Mobile

## 📋 Étapes de Démarrage

### 1. **Démarrage du Backend** 
Ouvrez un premier terminal (CMD ou PowerShell) et exécutez :

```bash
# Naviguez vers le dossier du projet
cd "c:\Users\<USER>\OneDrive\Desktop\Facturation stage\samle-react-app"

# Démarrez le serveur backend
node simple-server.js
```

**Résultat attendu :**
- Le serveur démarre sur le port 4000
- Vous devriez voir des messages de confirmation
- URL : http://localhost:4000

### 2. **Démarrage de l'Application Mobile**
Ouvrez un deuxième terminal et exécutez :

```bash
# Naviguez vers le dossier mobile
cd "c:\Users\<USER>\OneDrive\Desktop\Facturation stage\samle-react-app\mobile"

# Démarrez l'application mobile en mode web
npx expo start --web
```

**Résultat attendu :**
- L'application démarre sur le port 19006
- Un navigateur s'ouvre automatiquement
- URL : http://localhost:19006

## 🔧 Vérification des Services

### Test du Backend
Ouvrez votre navigateur et allez sur :
- **Test serveur :** http://localhost:4000
- **Test API :** http://localhost:4000/api/clients

### Test de l'Application Mobile
- **Application :** http://localhost:19006
- Utilisez les comptes de test pour vous connecter

## 🔑 Comptes de Test

```
Technicien :
- Email : <EMAIL>
- Mot de passe : Tech123

Administrateur :
- Email : <EMAIL>
- Mot de passe : Admin123
```

## 🆘 Résolution de Problèmes

### Si le Backend ne démarre pas :
1. Vérifiez que Node.js est installé : `node --version`
2. Vérifiez que le port 4000 est libre
3. Installez les dépendances : `npm install`

### Si l'Application Mobile ne démarre pas :
1. Vérifiez que vous êtes dans le dossier `mobile/`
2. Installez Expo CLI : `npm install -g @expo/cli`
3. Installez les dépendances : `npm install`

### Commandes de Nettoyage :
```bash
# Arrêter tous les processus Node.js
taskkill /f /im node.exe

# Réinstaller les dépendances
npm install
cd mobile && npm install
```

## 📱 Options d'Affichage

1. **Mode Web (Recommandé pour PC) :** `npx expo start --web`
2. **Mode Développement :** `npx expo start`
3. **Mode Android :** `npx expo start --android` (nécessite émulateur)

## ✅ Ordre de Démarrage Recommandé

1. **Backend** → Démarrez en premier
2. **Test Backend** → Vérifiez http://localhost:4000
3. **Mobile** → Démarrez ensuite
4. **Test Mobile** → Vérifiez http://localhost:19006
5. **Connexion** → Utilisez les comptes de test

---

**Note :** Gardez les deux terminaux ouverts pendant que vous utilisez l'application.
