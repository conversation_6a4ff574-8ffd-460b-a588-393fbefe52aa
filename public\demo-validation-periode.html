<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Validation Période</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #dc3545;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .periode-info {
            font-size: 12px;
            color: #6c757d;
            font-style: italic;
            margin-bottom: 8px;
            padding: 0 4px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .form-input.error {
            border-color: #dc3545;
            background-color: #fff5f5;
        }
        
        .error-button {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
            width: 100%;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .error-button:hover {
            background: #c82333;
        }
        
        .validation-rules {
            background: #fff3cd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }
        
        .validation-rules h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .test-cases {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .test-case {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .test-case:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .test-case.valid {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .test-case.invalid {
            border-color: #dc3545;
            background: #fff8f8;
        }
        
        .test-case h4 {
            margin-bottom: 10px;
        }
        
        .test-case.valid h4 {
            color: #28a745;
        }
        
        .test-case.invalid h4 {
            color: #dc3545;
        }
        
        .test-input {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 8px;
            font-family: monospace;
            margin: 5px 0;
            display: block;
            width: 100%;
        }
        
        .test-result {
            margin-top: 10px;
            padding: 8px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        
        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 Validation Période</h1>
            <p>Démonstration de la validation des périodes - Seulement les mois précédents</p>
        </div>
        
        <div class="content">
            <div class="demo-form">
                <h3 style="color: #dc3545; margin-bottom: 20px;">📱 Simulation du Champ Période</h3>
                
                <div class="form-group">
                    <label class="form-label">📅 Période (YYYY-MM) *</label>
                    <div class="periode-info">
                        ⚠️ Seulement les mois précédents sont autorisés (pas le mois actuel ni les mois futurs)
                    </div>
                    <input 
                        type="text" 
                        id="periodeInput" 
                        class="form-input" 
                        placeholder="2024-12" 
                        maxlength="7"
                    >
                    <div id="errorMessage" style="display: none;">
                        <button class="error-button" onclick="showErrorDetails()">
                            <span id="errorText">❌ Message d'erreur</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="validation-rules">
                <h3>📋 Règles de Validation</h3>
                <ul style="color: #856404; line-height: 1.6;">
                    <li>✅ <strong>Format requis :</strong> YYYY-MM (ex: 2024-12)</li>
                    <li>✅ <strong>Mois autorisés :</strong> Seulement les mois précédents</li>
                    <li>❌ <strong>Mois interdits :</strong> Mois actuel et mois futurs</li>
                    <li>🔴 <strong>Erreur :</strong> Message affiché dans un bouton rouge</li>
                    <li>🚫 <strong>Soumission :</strong> Bloquée si période invalide</li>
                </ul>
            </div>
            
            <div class="test-cases">
                <div class="test-case valid">
                    <h4>✅ Cas Valides</h4>
                    <div class="test-input" id="validExample1">Chargement...</div>
                    <div class="test-input" id="validExample2">Chargement...</div>
                    <div class="test-result success">
                        ✅ Pas d'erreur, bordure normale, soumission autorisée
                    </div>
                </div>
                
                <div class="test-case invalid">
                    <h4>❌ Mois Actuel (Interdit)</h4>
                    <div class="test-input" id="currentMonth">Chargement...</div>
                    <div class="test-result error">
                        ❌ "Impossible de saisir le mois actuel. Seulement les mois précédents sont autorisés."
                    </div>
                </div>
                
                <div class="test-case invalid">
                    <h4>❌ Mois Futur (Interdit)</h4>
                    <div class="test-input" id="futureMonth">Chargement...</div>
                    <div class="test-result error">
                        ❌ "Impossible de saisir un mois futur. Seulement les mois précédents sont autorisés."
                    </div>
                </div>
                
                <div class="test-case invalid">
                    <h4>❌ Format Invalide</h4>
                    <div class="test-input">2024/12, 24-12, 2024-13</div>
                    <div class="test-result error">
                        ❌ "Format invalide. Utilisez YYYY-MM (ex: 2024-12)"
                    </div>
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-primary" target="_blank">
                    📱 Tester l'Application Réelle
                </a>
                <button class="btn btn-danger" onclick="testAllCases()">
                    🧪 Tester Tous les Cas
                </button>
            </div>
            
            <div style="background: #d4edda; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #28a745;">
                <h3 style="color: #155724; margin-bottom: 15px;">✅ Validation Implémentée</h3>
                <ul style="color: #155724; line-height: 1.6;">
                    <li>✅ <strong>Validation en temps réel</strong> lors de la saisie</li>
                    <li>✅ <strong>Message d'erreur</strong> dans un bouton rouge cliquable</li>
                    <li>✅ <strong>Bordure rouge</strong> sur le champ en erreur</li>
                    <li>✅ <strong>Blocage soumission</strong> si période invalide</li>
                    <li>✅ <strong>Noms des mois</strong> en français dans les messages</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // Obtenir les dates pour les exemples
        const now = new Date();
        const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastMonthStr = lastMonth.getFullYear() + '-' + String(lastMonth.getMonth() + 1).padStart(2, '0');
        const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, 1);
        const twoMonthsAgoStr = twoMonthsAgo.getFullYear() + '-' + String(twoMonthsAgo.getMonth() + 1).padStart(2, '0');
        const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        const nextMonthStr = nextMonth.getFullYear() + '-' + String(nextMonth.getMonth() + 1).padStart(2, '0');
        
        // Remplir les exemples
        document.getElementById('validExample1').textContent = lastMonthStr + ' (mois dernier)';
        document.getElementById('validExample2').textContent = twoMonthsAgoStr + ' (il y a 2 mois)';
        document.getElementById('currentMonth').textContent = currentMonth + ' (mois actuel)';
        document.getElementById('futureMonth').textContent = nextMonthStr + ' (mois suivant)';
        
        // Noms des mois en français
        const nomsMois = [
            'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
            'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
        ];
        
        // Fonction de validation
        function validerPeriode(periode) {
            if (!periode) return { valide: true, message: '' };
            
            // Vérifier le format
            const formatRegex = /^\d{4}-\d{2}$/;
            if (!formatRegex.test(periode)) {
                return { 
                    valide: false, 
                    message: 'Format invalide. Utilisez YYYY-MM (ex: 2024-12)' 
                };
            }
            
            // Vérifier si c'est antérieur au mois actuel
            if (periode >= currentMonth) {
                const [annee, mois] = periode.split('-');
                const nomMois = nomsMois[parseInt(mois) - 1] || mois;
                
                if (periode === currentMonth) {
                    return { 
                        valide: false, 
                        message: `❌ Impossible de saisir le mois actuel (${nomMois} ${annee}). Seulement les mois précédents sont autorisés.` 
                    };
                } else {
                    return { 
                        valide: false, 
                        message: `❌ Impossible de saisir un mois futur (${nomMois} ${annee}). Seulement les mois précédents sont autorisés.` 
                    };
                }
            }
            
            return { valide: true, message: '' };
        }
        
        // Gestionnaire de saisie
        document.getElementById('periodeInput').addEventListener('input', function() {
            const periode = this.value;
            const validation = validerPeriode(periode);
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            if (validation.valide) {
                this.classList.remove('error');
                errorDiv.style.display = 'none';
            } else {
                this.classList.add('error');
                errorText.textContent = validation.message;
                errorDiv.style.display = 'block';
            }
        });
        
        // Fonction pour afficher les détails de l'erreur
        function showErrorDetails() {
            const errorText = document.getElementById('errorText').textContent;
            alert('Période Invalide\n\n' + errorText + '\n\nVeuillez saisir une période antérieure au mois actuel.');
        }
        
        // Tester tous les cas
        function testAllCases() {
            const testCases = [
                { value: lastMonthStr, expected: 'VALIDE' },
                { value: currentMonth, expected: 'INVALIDE (mois actuel)' },
                { value: nextMonthStr, expected: 'INVALIDE (mois futur)' },
                { value: '2024/12', expected: 'INVALIDE (format)' },
                { value: '24-12', expected: 'INVALIDE (format)' }
            ];
            
            let results = 'Résultats des tests :\n\n';
            
            testCases.forEach(test => {
                const validation = validerPeriode(test.value);
                const result = validation.valide ? 'VALIDE' : 'INVALIDE';
                results += `${test.value} → ${result} (attendu: ${test.expected})\n`;
            });
            
            alert(results);
        }
    </script>
</body>
</html>
