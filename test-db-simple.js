// Test simple de la base de données
const { Pool } = require('pg');

console.log('🔍 TEST SIMPLE DE LA BASE DE DONNEES');
console.log('====================================');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function testDatabase() {
  try {
    console.log('\n1. Test de connexion...');
    const result = await pool.query('SELECT NOW()');
    console.log('✅ Connexion réussie');
    
    console.log('\n2. Test de la table client...');
    const clientsResult = await pool.query('SELECT COUNT(*) as count FROM client');
    console.log(`✅ ${clientsResult.rows[0].count} client(s) dans la table`);
    
    if (clientsResult.rows[0].count > 0) {
      console.log('\n3. Récupération des clients...');
      const clients = await pool.query('SELECT nom, prenom, ville FROM client LIMIT 5');
      clients.rows.forEach((client, index) => {
        console.log(`   ${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`);
      });
    } else {
      console.log('\n⚠️  Aucun client dans la table !');
    }
    
  } catch (error) {
    console.error('\n❌ Erreur:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 PostgreSQL n\'est pas démarré');
    } else if (error.code === '3D000') {
      console.log('💡 La base de données "Facturation" n\'existe pas');
    } else if (error.code === '42P01') {
      console.log('💡 La table "client" n\'existe pas');
    }
  } finally {
    await pool.end();
  }
}

testDatabase();
