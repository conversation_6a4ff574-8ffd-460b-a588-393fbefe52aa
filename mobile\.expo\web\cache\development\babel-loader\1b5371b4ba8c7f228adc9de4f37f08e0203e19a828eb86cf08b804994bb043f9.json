{"ast": null, "code": "import normalizeColor from \"./compiler/normalizeColor\";\nimport normalizeValueWithProperty from \"./compiler/normalizeValueWithProperty\";\nimport { warnOnce } from \"../../modules/warnOnce\";\nvar emptyObject = {};\nvar defaultOffset = {\n  height: 0,\n  width: 0\n};\nexport var createBoxShadowValue = function createBoxShadowValue(style) {\n  var shadowColor = style.shadowColor,\n    shadowOffset = style.shadowOffset,\n    shadowOpacity = style.shadowOpacity,\n    shadowRadius = style.shadowRadius;\n  var _ref = shadowOffset || defaultOffset,\n    height = _ref.height,\n    width = _ref.width;\n  var offsetX = normalizeValueWithProperty(width);\n  var offsetY = normalizeValueWithProperty(height);\n  var blurRadius = normalizeValueWithProperty(shadowRadius || 0);\n  var color = normalizeColor(shadowColor || 'black', shadowOpacity);\n  if (color != null && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\nexport var createTextShadowValue = function createTextShadowValue(style) {\n  var textShadowColor = style.textShadowColor,\n    textShadowOffset = style.textShadowOffset,\n    textShadowRadius = style.textShadowRadius;\n  var _ref2 = textShadowOffset || defaultOffset,\n    height = _ref2.height,\n    width = _ref2.width;\n  var radius = textShadowRadius || 0;\n  var offsetX = normalizeValueWithProperty(width);\n  var offsetY = normalizeValueWithProperty(height);\n  var blurRadius = normalizeValueWithProperty(radius);\n  var color = normalizeValueWithProperty(textShadowColor, 'textShadowColor');\n  if (color && (height !== 0 || width !== 0 || radius !== 0) && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\nvar mapBoxShadow = function mapBoxShadow(boxShadow) {\n  if (typeof boxShadow === 'string') {\n    return boxShadow;\n  }\n  var offsetX = normalizeValueWithProperty(boxShadow.offsetX) || 0;\n  var offsetY = normalizeValueWithProperty(boxShadow.offsetY) || 0;\n  var blurRadius = normalizeValueWithProperty(boxShadow.blurRadius) || 0;\n  var spreadDistance = normalizeValueWithProperty(boxShadow.spreadDistance) || 0;\n  var color = normalizeColor(boxShadow.color) || 'black';\n  var position = boxShadow.inset ? 'inset ' : '';\n  return \"\" + position + offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + spreadDistance + \" \" + color;\n};\nexport var createBoxShadowArrayValue = function createBoxShadowArrayValue(value) {\n  return value.map(mapBoxShadow).join(', ');\n};\nvar mapTransform = function mapTransform(transform) {\n  var type = Object.keys(transform)[0];\n  var value = transform[type];\n  if (type === 'matrix' || type === 'matrix3d') {\n    return type + \"(\" + value.join(',') + \")\";\n  } else {\n    var normalizedValue = normalizeValueWithProperty(value, type);\n    return type + \"(\" + normalizedValue + \")\";\n  }\n};\nexport var createTransformValue = function createTransformValue(value) {\n  return value.map(mapTransform).join(' ');\n};\nexport var createTransformOriginValue = function createTransformOriginValue(value) {\n  return value.map(function (v) {\n    return normalizeValueWithProperty(v);\n  }).join(' ');\n};\nvar PROPERTIES_STANDARD = {\n  borderBottomEndRadius: 'borderEndEndRadius',\n  borderBottomStartRadius: 'borderEndStartRadius',\n  borderTopEndRadius: 'borderStartEndRadius',\n  borderTopStartRadius: 'borderStartStartRadius',\n  borderEndColor: 'borderInlineEndColor',\n  borderEndStyle: 'borderInlineEndStyle',\n  borderEndWidth: 'borderInlineEndWidth',\n  borderStartColor: 'borderInlineStartColor',\n  borderStartStyle: 'borderInlineStartStyle',\n  borderStartWidth: 'borderInlineStartWidth',\n  end: 'insetInlineEnd',\n  marginEnd: 'marginInlineEnd',\n  marginHorizontal: 'marginInline',\n  marginStart: 'marginInlineStart',\n  marginVertical: 'marginBlock',\n  paddingEnd: 'paddingInlineEnd',\n  paddingHorizontal: 'paddingInline',\n  paddingStart: 'paddingInlineStart',\n  paddingVertical: 'paddingBlock',\n  start: 'insetInlineStart'\n};\nvar ignoredProps = {\n  elevation: true,\n  overlayColor: true,\n  resizeMode: true,\n  tintColor: true\n};\nexport var preprocess = function preprocess(originalStyle, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var style = originalStyle || emptyObject;\n  var nextStyle = {};\n  if (options.shadow === true, style.shadowColor != null || style.shadowOffset != null || style.shadowOpacity != null || style.shadowRadius != null) {\n    warnOnce('shadowStyles', \"\\\"shadow*\\\" style props are deprecated. Use \\\"boxShadow\\\".\");\n    var boxShadowValue = createBoxShadowValue(style);\n    if (boxShadowValue != null) {\n      nextStyle.boxShadow = boxShadowValue;\n    }\n  }\n  if (options.textShadow === true, style.textShadowColor != null || style.textShadowOffset != null || style.textShadowRadius != null) {\n    warnOnce('textShadowStyles', \"\\\"textShadow*\\\" style props are deprecated. Use \\\"textShadow\\\".\");\n    var textShadowValue = createTextShadowValue(style);\n    if (textShadowValue != null && nextStyle.textShadow == null) {\n      var textShadow = style.textShadow;\n      var value = textShadow ? textShadow + \", \" + textShadowValue : textShadowValue;\n      nextStyle.textShadow = value;\n    }\n  }\n  for (var originalProp in style) {\n    if (ignoredProps[originalProp] != null || originalProp === 'shadowColor' || originalProp === 'shadowOffset' || originalProp === 'shadowOpacity' || originalProp === 'shadowRadius' || originalProp === 'textShadowColor' || originalProp === 'textShadowOffset' || originalProp === 'textShadowRadius') {\n      continue;\n    }\n    var originalValue = style[originalProp];\n    var prop = PROPERTIES_STANDARD[originalProp] || originalProp;\n    var _value = originalValue;\n    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || prop !== originalProp && style[prop] != null) {\n      continue;\n    }\n    if (prop === 'aspectRatio' && typeof _value === 'number') {\n      nextStyle[prop] = _value.toString();\n    } else if (prop === 'boxShadow') {\n      if (Array.isArray(_value)) {\n        _value = createBoxShadowArrayValue(_value);\n      }\n      var boxShadow = nextStyle.boxShadow;\n      nextStyle.boxShadow = boxShadow ? _value + \", \" + boxShadow : _value;\n    } else if (prop === 'fontVariant') {\n      if (Array.isArray(_value) && _value.length > 0) {\n        _value = _value.join(' ');\n      }\n      nextStyle[prop] = _value;\n    } else if (prop === 'textAlignVertical') {\n      if (style.verticalAlign == null) {\n        nextStyle.verticalAlign = _value === 'center' ? 'middle' : _value;\n      }\n    } else if (prop === 'transform') {\n      if (Array.isArray(_value)) {\n        _value = createTransformValue(_value);\n      }\n      nextStyle.transform = _value;\n    } else if (prop === 'transformOrigin') {\n      if (Array.isArray(_value)) {\n        _value = createTransformOriginValue(_value);\n      }\n      nextStyle.transformOrigin = _value;\n    } else {\n      nextStyle[prop] = _value;\n    }\n  }\n  return nextStyle;\n};\nexport default preprocess;", "map": {"version": 3, "names": ["normalizeColor", "normalizeValueWithProperty", "warnOnce", "emptyObject", "defaultOffset", "height", "width", "createBoxShadowValue", "style", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "_ref", "offsetX", "offsetY", "blurRadius", "color", "createTextShadowValue", "textShadowColor", "textShadowOffset", "textShadowRadius", "_ref2", "radius", "mapBoxShadow", "boxShadow", "spreadDistance", "position", "inset", "createBoxShadowArrayValue", "value", "map", "join", "mapTransform", "transform", "type", "Object", "keys", "normalizedValue", "createTransformValue", "createTransformOriginValue", "v", "PROPERTIES_STANDARD", "borderBottomEndRadius", "borderBottomStartRadius", "borderTopEndRadius", "borderTopStartRadius", "borderEndColor", "borderEndStyle", "borderEndWidth", "borderStartColor", "borderStartStyle", "borderStartWidth", "end", "marginEnd", "marginHorizontal", "marginStart", "marginVertical", "paddingEnd", "paddingHorizontal", "paddingStart", "paddingVertical", "start", "ignoredProps", "elevation", "overlayColor", "resizeMode", "tintColor", "preprocess", "originalStyle", "options", "nextStyle", "shadow", "boxShadowValue", "textShadow", "textShadowValue", "originalProp", "originalValue", "prop", "_value", "prototype", "hasOwnProperty", "call", "toString", "Array", "isArray", "length", "verticalAlign", "transform<PERSON><PERSON>in"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/StyleSheet/preprocess.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport normalizeColor from './compiler/normalizeColor';\nimport normalizeValueWithProperty from './compiler/normalizeValueWithProperty';\nimport { warnOnce } from '../../modules/warnOnce';\nvar emptyObject = {};\n\n/**\n * Shadows\n */\n\nvar defaultOffset = {\n  height: 0,\n  width: 0\n};\nexport var createBoxShadowValue = style => {\n  var shadowColor = style.shadowColor,\n    shadowOffset = style.shadowOffset,\n    shadowOpacity = style.shadowOpacity,\n    shadowRadius = style.shadowRadius;\n  var _ref = shadowOffset || defaultOffset,\n    height = _ref.height,\n    width = _ref.width;\n  var offsetX = normalizeValueWithProperty(width);\n  var offsetY = normalizeValueWithProperty(height);\n  var blurRadius = normalizeValueWithProperty(shadowRadius || 0);\n  var color = normalizeColor(shadowColor || 'black', shadowOpacity);\n  if (color != null && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\nexport var createTextShadowValue = style => {\n  var textShadowColor = style.textShadowColor,\n    textShadowOffset = style.textShadowOffset,\n    textShadowRadius = style.textShadowRadius;\n  var _ref2 = textShadowOffset || defaultOffset,\n    height = _ref2.height,\n    width = _ref2.width;\n  var radius = textShadowRadius || 0;\n  var offsetX = normalizeValueWithProperty(width);\n  var offsetY = normalizeValueWithProperty(height);\n  var blurRadius = normalizeValueWithProperty(radius);\n  var color = normalizeValueWithProperty(textShadowColor, 'textShadowColor');\n  if (color && (height !== 0 || width !== 0 || radius !== 0) && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\n\n// { offsetX: 1, offsetY: 2, blurRadius: 3, spreadDistance: 4, color: 'rgba(255, 0, 0)', inset: true }\n// => 'rgba(255, 0, 0) 1px 2px 3px 4px inset'\nvar mapBoxShadow = boxShadow => {\n  if (typeof boxShadow === 'string') {\n    return boxShadow;\n  }\n  var offsetX = normalizeValueWithProperty(boxShadow.offsetX) || 0;\n  var offsetY = normalizeValueWithProperty(boxShadow.offsetY) || 0;\n  var blurRadius = normalizeValueWithProperty(boxShadow.blurRadius) || 0;\n  var spreadDistance = normalizeValueWithProperty(boxShadow.spreadDistance) || 0;\n  var color = normalizeColor(boxShadow.color) || 'black';\n  var position = boxShadow.inset ? 'inset ' : '';\n  return \"\" + position + offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + spreadDistance + \" \" + color;\n};\nexport var createBoxShadowArrayValue = value => {\n  return value.map(mapBoxShadow).join(', ');\n};\n\n// { scale: 2 } => 'scale(2)'\n// { translateX: 20 } => 'translateX(20px)'\n// { matrix: [1,2,3,4,5,6] } => 'matrix(1,2,3,4,5,6)'\nvar mapTransform = transform => {\n  var type = Object.keys(transform)[0];\n  var value = transform[type];\n  if (type === 'matrix' || type === 'matrix3d') {\n    return type + \"(\" + value.join(',') + \")\";\n  } else {\n    var normalizedValue = normalizeValueWithProperty(value, type);\n    return type + \"(\" + normalizedValue + \")\";\n  }\n};\nexport var createTransformValue = value => {\n  return value.map(mapTransform).join(' ');\n};\n\n// [2, '30%', 10] => '2px 30% 10px'\nexport var createTransformOriginValue = value => {\n  return value.map(v => normalizeValueWithProperty(v)).join(' ');\n};\nvar PROPERTIES_STANDARD = {\n  borderBottomEndRadius: 'borderEndEndRadius',\n  borderBottomStartRadius: 'borderEndStartRadius',\n  borderTopEndRadius: 'borderStartEndRadius',\n  borderTopStartRadius: 'borderStartStartRadius',\n  borderEndColor: 'borderInlineEndColor',\n  borderEndStyle: 'borderInlineEndStyle',\n  borderEndWidth: 'borderInlineEndWidth',\n  borderStartColor: 'borderInlineStartColor',\n  borderStartStyle: 'borderInlineStartStyle',\n  borderStartWidth: 'borderInlineStartWidth',\n  end: 'insetInlineEnd',\n  marginEnd: 'marginInlineEnd',\n  marginHorizontal: 'marginInline',\n  marginStart: 'marginInlineStart',\n  marginVertical: 'marginBlock',\n  paddingEnd: 'paddingInlineEnd',\n  paddingHorizontal: 'paddingInline',\n  paddingStart: 'paddingInlineStart',\n  paddingVertical: 'paddingBlock',\n  start: 'insetInlineStart'\n};\nvar ignoredProps = {\n  elevation: true,\n  overlayColor: true,\n  resizeMode: true,\n  tintColor: true\n};\n\n/**\n * Preprocess styles\n */\nexport var preprocess = function preprocess(originalStyle, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var style = originalStyle || emptyObject;\n  var nextStyle = {};\n\n  // Convert shadow styles\n  if (options.shadow === true, style.shadowColor != null || style.shadowOffset != null || style.shadowOpacity != null || style.shadowRadius != null) {\n    warnOnce('shadowStyles', \"\\\"shadow*\\\" style props are deprecated. Use \\\"boxShadow\\\".\");\n    var boxShadowValue = createBoxShadowValue(style);\n    if (boxShadowValue != null) {\n      nextStyle.boxShadow = boxShadowValue;\n    }\n  }\n\n  // Convert text shadow styles\n  if (options.textShadow === true, style.textShadowColor != null || style.textShadowOffset != null || style.textShadowRadius != null) {\n    warnOnce('textShadowStyles', \"\\\"textShadow*\\\" style props are deprecated. Use \\\"textShadow\\\".\");\n    var textShadowValue = createTextShadowValue(style);\n    if (textShadowValue != null && nextStyle.textShadow == null) {\n      var textShadow = style.textShadow;\n      var value = textShadow ? textShadow + \", \" + textShadowValue : textShadowValue;\n      nextStyle.textShadow = value;\n    }\n  }\n  for (var originalProp in style) {\n    if (\n    // Ignore some React Native styles\n    ignoredProps[originalProp] != null || originalProp === 'shadowColor' || originalProp === 'shadowOffset' || originalProp === 'shadowOpacity' || originalProp === 'shadowRadius' || originalProp === 'textShadowColor' || originalProp === 'textShadowOffset' || originalProp === 'textShadowRadius') {\n      continue;\n    }\n    var originalValue = style[originalProp];\n    var prop = PROPERTIES_STANDARD[originalProp] || originalProp;\n    var _value = originalValue;\n    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || prop !== originalProp && style[prop] != null) {\n      continue;\n    }\n    if (prop === 'aspectRatio' && typeof _value === 'number') {\n      nextStyle[prop] = _value.toString();\n    } else if (prop === 'boxShadow') {\n      if (Array.isArray(_value)) {\n        _value = createBoxShadowArrayValue(_value);\n      }\n      var boxShadow = nextStyle.boxShadow;\n      nextStyle.boxShadow = boxShadow ? _value + \", \" + boxShadow : _value;\n    } else if (prop === 'fontVariant') {\n      if (Array.isArray(_value) && _value.length > 0) {\n        /*\n        warnOnce(\n          'fontVariant',\n          '\"fontVariant\" style array value is deprecated. Use space-separated values.'\n        );\n        */\n        _value = _value.join(' ');\n      }\n      nextStyle[prop] = _value;\n    } else if (prop === 'textAlignVertical') {\n      /*\n      warnOnce(\n        'textAlignVertical',\n        '\"textAlignVertical\" style is deprecated. Use \"verticalAlign\".'\n      );\n      */\n      if (style.verticalAlign == null) {\n        nextStyle.verticalAlign = _value === 'center' ? 'middle' : _value;\n      }\n    } else if (prop === 'transform') {\n      if (Array.isArray(_value)) {\n        _value = createTransformValue(_value);\n      }\n      nextStyle.transform = _value;\n    } else if (prop === 'transformOrigin') {\n      if (Array.isArray(_value)) {\n        _value = createTransformOriginValue(_value);\n      }\n      nextStyle.transformOrigin = _value;\n    } else {\n      nextStyle[prop] = _value;\n    }\n  }\n\n  // $FlowIgnore\n  return nextStyle;\n};\nexport default preprocess;"], "mappings": "AASA,OAAOA,cAAc;AACrB,OAAOC,0BAA0B;AACjC,SAASC,QAAQ;AACjB,IAAIC,WAAW,GAAG,CAAC,CAAC;AAMpB,IAAIC,aAAa,GAAG;EAClBC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE;AACT,CAAC;AACD,OAAO,IAAIC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAGC,KAAK,EAAI;EACzC,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;IACjCC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,aAAa,GAAGH,KAAK,CAACG,aAAa;IACnCC,YAAY,GAAGJ,KAAK,CAACI,YAAY;EACnC,IAAIC,IAAI,GAAGH,YAAY,IAAIN,aAAa;IACtCC,MAAM,GAAGQ,IAAI,CAACR,MAAM;IACpBC,KAAK,GAAGO,IAAI,CAACP,KAAK;EACpB,IAAIQ,OAAO,GAAGb,0BAA0B,CAACK,KAAK,CAAC;EAC/C,IAAIS,OAAO,GAAGd,0BAA0B,CAACI,MAAM,CAAC;EAChD,IAAIW,UAAU,GAAGf,0BAA0B,CAACW,YAAY,IAAI,CAAC,CAAC;EAC9D,IAAIK,KAAK,GAAGjB,cAAc,CAACS,WAAW,IAAI,OAAO,EAAEE,aAAa,CAAC;EACjE,IAAIM,KAAK,IAAI,IAAI,IAAIH,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;IAC7E,OAAOF,OAAO,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG,GAAGC,KAAK;EACjE;AACF,CAAC;AACD,OAAO,IAAIC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAGV,KAAK,EAAI;EAC1C,IAAIW,eAAe,GAAGX,KAAK,CAACW,eAAe;IACzCC,gBAAgB,GAAGZ,KAAK,CAACY,gBAAgB;IACzCC,gBAAgB,GAAGb,KAAK,CAACa,gBAAgB;EAC3C,IAAIC,KAAK,GAAGF,gBAAgB,IAAIhB,aAAa;IAC3CC,MAAM,GAAGiB,KAAK,CAACjB,MAAM;IACrBC,KAAK,GAAGgB,KAAK,CAAChB,KAAK;EACrB,IAAIiB,MAAM,GAAGF,gBAAgB,IAAI,CAAC;EAClC,IAAIP,OAAO,GAAGb,0BAA0B,CAACK,KAAK,CAAC;EAC/C,IAAIS,OAAO,GAAGd,0BAA0B,CAACI,MAAM,CAAC;EAChD,IAAIW,UAAU,GAAGf,0BAA0B,CAACsB,MAAM,CAAC;EACnD,IAAIN,KAAK,GAAGhB,0BAA0B,CAACkB,eAAe,EAAE,iBAAiB,CAAC;EAC1E,IAAIF,KAAK,KAAKZ,MAAM,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,IAAIiB,MAAM,KAAK,CAAC,CAAC,IAAIT,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;IACtH,OAAOF,OAAO,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG,GAAGC,KAAK;EACjE;AACF,CAAC;AAID,IAAIO,YAAY,GAAG,SAAfA,YAAYA,CAAGC,SAAS,EAAI;EAC9B,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,OAAOA,SAAS;EAClB;EACA,IAAIX,OAAO,GAAGb,0BAA0B,CAACwB,SAAS,CAACX,OAAO,CAAC,IAAI,CAAC;EAChE,IAAIC,OAAO,GAAGd,0BAA0B,CAACwB,SAAS,CAACV,OAAO,CAAC,IAAI,CAAC;EAChE,IAAIC,UAAU,GAAGf,0BAA0B,CAACwB,SAAS,CAACT,UAAU,CAAC,IAAI,CAAC;EACtE,IAAIU,cAAc,GAAGzB,0BAA0B,CAACwB,SAAS,CAACC,cAAc,CAAC,IAAI,CAAC;EAC9E,IAAIT,KAAK,GAAGjB,cAAc,CAACyB,SAAS,CAACR,KAAK,CAAC,IAAI,OAAO;EACtD,IAAIU,QAAQ,GAAGF,SAAS,CAACG,KAAK,GAAG,QAAQ,GAAG,EAAE;EAC9C,OAAO,EAAE,GAAGD,QAAQ,GAAGb,OAAO,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG,GAAGU,cAAc,GAAG,GAAG,GAAGT,KAAK;AACxG,CAAC;AACD,OAAO,IAAIY,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAGC,KAAK,EAAI;EAC9C,OAAOA,KAAK,CAACC,GAAG,CAACP,YAAY,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAC;AAC3C,CAAC;AAKD,IAAIC,YAAY,GAAG,SAAfA,YAAYA,CAAGC,SAAS,EAAI;EAC9B,IAAIC,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;EACpC,IAAIJ,KAAK,GAAGI,SAAS,CAACC,IAAI,CAAC;EAC3B,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU,EAAE;IAC5C,OAAOA,IAAI,GAAG,GAAG,GAAGL,KAAK,CAACE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC3C,CAAC,MAAM;IACL,IAAIM,eAAe,GAAGrC,0BAA0B,CAAC6B,KAAK,EAAEK,IAAI,CAAC;IAC7D,OAAOA,IAAI,GAAG,GAAG,GAAGG,eAAe,GAAG,GAAG;EAC3C;AACF,CAAC;AACD,OAAO,IAAIC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAGT,KAAK,EAAI;EACzC,OAAOA,KAAK,CAACC,GAAG,CAACE,YAAY,CAAC,CAACD,IAAI,CAAC,GAAG,CAAC;AAC1C,CAAC;AAGD,OAAO,IAAIQ,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAGV,KAAK,EAAI;EAC/C,OAAOA,KAAK,CAACC,GAAG,CAAC,UAAAU,CAAC;IAAA,OAAIxC,0BAA0B,CAACwC,CAAC,CAAC;EAAA,EAAC,CAACT,IAAI,CAAC,GAAG,CAAC;AAChE,CAAC;AACD,IAAIU,mBAAmB,GAAG;EACxBC,qBAAqB,EAAE,oBAAoB;EAC3CC,uBAAuB,EAAE,sBAAsB;EAC/CC,kBAAkB,EAAE,sBAAsB;EAC1CC,oBAAoB,EAAE,wBAAwB;EAC9CC,cAAc,EAAE,sBAAsB;EACtCC,cAAc,EAAE,sBAAsB;EACtCC,cAAc,EAAE,sBAAsB;EACtCC,gBAAgB,EAAE,wBAAwB;EAC1CC,gBAAgB,EAAE,wBAAwB;EAC1CC,gBAAgB,EAAE,wBAAwB;EAC1CC,GAAG,EAAE,gBAAgB;EACrBC,SAAS,EAAE,iBAAiB;EAC5BC,gBAAgB,EAAE,cAAc;EAChCC,WAAW,EAAE,mBAAmB;EAChCC,cAAc,EAAE,aAAa;EAC7BC,UAAU,EAAE,kBAAkB;EAC9BC,iBAAiB,EAAE,eAAe;EAClCC,YAAY,EAAE,oBAAoB;EAClCC,eAAe,EAAE,cAAc;EAC/BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE;AACb,CAAC;AAKD,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,aAAa,EAAEC,OAAO,EAAE;EAClE,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAI9D,KAAK,GAAG6D,aAAa,IAAIlE,WAAW;EACxC,IAAIoE,SAAS,GAAG,CAAC,CAAC;EAGlB,IAAID,OAAO,CAACE,MAAM,KAAK,IAAI,EAAEhE,KAAK,CAACC,WAAW,IAAI,IAAI,IAAID,KAAK,CAACE,YAAY,IAAI,IAAI,IAAIF,KAAK,CAACG,aAAa,IAAI,IAAI,IAAIH,KAAK,CAACI,YAAY,IAAI,IAAI,EAAE;IACjJV,QAAQ,CAAC,cAAc,EAAE,4DAA4D,CAAC;IACtF,IAAIuE,cAAc,GAAGlE,oBAAoB,CAACC,KAAK,CAAC;IAChD,IAAIiE,cAAc,IAAI,IAAI,EAAE;MAC1BF,SAAS,CAAC9C,SAAS,GAAGgD,cAAc;IACtC;EACF;EAGA,IAAIH,OAAO,CAACI,UAAU,KAAK,IAAI,EAAElE,KAAK,CAACW,eAAe,IAAI,IAAI,IAAIX,KAAK,CAACY,gBAAgB,IAAI,IAAI,IAAIZ,KAAK,CAACa,gBAAgB,IAAI,IAAI,EAAE;IAClInB,QAAQ,CAAC,kBAAkB,EAAE,iEAAiE,CAAC;IAC/F,IAAIyE,eAAe,GAAGzD,qBAAqB,CAACV,KAAK,CAAC;IAClD,IAAImE,eAAe,IAAI,IAAI,IAAIJ,SAAS,CAACG,UAAU,IAAI,IAAI,EAAE;MAC3D,IAAIA,UAAU,GAAGlE,KAAK,CAACkE,UAAU;MACjC,IAAI5C,KAAK,GAAG4C,UAAU,GAAGA,UAAU,GAAG,IAAI,GAAGC,eAAe,GAAGA,eAAe;MAC9EJ,SAAS,CAACG,UAAU,GAAG5C,KAAK;IAC9B;EACF;EACA,KAAK,IAAI8C,YAAY,IAAIpE,KAAK,EAAE;IAC9B,IAEAuD,YAAY,CAACa,YAAY,CAAC,IAAI,IAAI,IAAIA,YAAY,KAAK,aAAa,IAAIA,YAAY,KAAK,cAAc,IAAIA,YAAY,KAAK,eAAe,IAAIA,YAAY,KAAK,cAAc,IAAIA,YAAY,KAAK,iBAAiB,IAAIA,YAAY,KAAK,kBAAkB,IAAIA,YAAY,KAAK,kBAAkB,EAAE;MAClS;IACF;IACA,IAAIC,aAAa,GAAGrE,KAAK,CAACoE,YAAY,CAAC;IACvC,IAAIE,IAAI,GAAGpC,mBAAmB,CAACkC,YAAY,CAAC,IAAIA,YAAY;IAC5D,IAAIG,MAAM,GAAGF,aAAa;IAC1B,IAAI,CAACzC,MAAM,CAAC4C,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC1E,KAAK,EAAEoE,YAAY,CAAC,IAAIE,IAAI,KAAKF,YAAY,IAAIpE,KAAK,CAACsE,IAAI,CAAC,IAAI,IAAI,EAAE;MAC9G;IACF;IACA,IAAIA,IAAI,KAAK,aAAa,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;MACxDR,SAAS,CAACO,IAAI,CAAC,GAAGC,MAAM,CAACI,QAAQ,CAAC,CAAC;IACrC,CAAC,MAAM,IAAIL,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAIM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;QACzBA,MAAM,GAAGlD,yBAAyB,CAACkD,MAAM,CAAC;MAC5C;MACA,IAAItD,SAAS,GAAG8C,SAAS,CAAC9C,SAAS;MACnC8C,SAAS,CAAC9C,SAAS,GAAGA,SAAS,GAAGsD,MAAM,GAAG,IAAI,GAAGtD,SAAS,GAAGsD,MAAM;IACtE,CAAC,MAAM,IAAID,IAAI,KAAK,aAAa,EAAE;MACjC,IAAIM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,IAAIA,MAAM,CAACO,MAAM,GAAG,CAAC,EAAE;QAO9CP,MAAM,GAAGA,MAAM,CAAC/C,IAAI,CAAC,GAAG,CAAC;MAC3B;MACAuC,SAAS,CAACO,IAAI,CAAC,GAAGC,MAAM;IAC1B,CAAC,MAAM,IAAID,IAAI,KAAK,mBAAmB,EAAE;MAOvC,IAAItE,KAAK,CAAC+E,aAAa,IAAI,IAAI,EAAE;QAC/BhB,SAAS,CAACgB,aAAa,GAAGR,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAAGA,MAAM;MACnE;IACF,CAAC,MAAM,IAAID,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAIM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;QACzBA,MAAM,GAAGxC,oBAAoB,CAACwC,MAAM,CAAC;MACvC;MACAR,SAAS,CAACrC,SAAS,GAAG6C,MAAM;IAC9B,CAAC,MAAM,IAAID,IAAI,KAAK,iBAAiB,EAAE;MACrC,IAAIM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;QACzBA,MAAM,GAAGvC,0BAA0B,CAACuC,MAAM,CAAC;MAC7C;MACAR,SAAS,CAACiB,eAAe,GAAGT,MAAM;IACpC,CAAC,MAAM;MACLR,SAAS,CAACO,IAAI,CAAC,GAAGC,MAAM;IAC1B;EACF;EAGA,OAAOR,SAAS;AAClB,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}