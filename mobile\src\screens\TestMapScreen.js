import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import GoogleMapsEmbed from '../components/GoogleMapsEmbed';

const TestMapScreen = ({ navigation }) => {
  const [secteurs, setSecteurs] = useState([]);
  const [selectedSecteur, setSelectedSecteur] = useState('');
  const [selectedSecteurInfo, setSelectedSecteurInfo] = useState(null);
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(false);

  const API_BASE_URL = 'http://localhost:4000';

  useEffect(() => {
    fetchSecteurs();
  }, []);

  const fetchSecteurs = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/secteurs`);
      const data = await response.json();
      if (data.success) {
        setSecteurs(data.data);
      }
    } catch (error) {
      console.error('Erreur récupération secteurs:', error);
      Alert.alert('Erreur', 'Impossible de récupérer les secteurs');
    }
  };

  const fetchClientsBySecteur = async (secteurId) => {
    if (!secteurId) return;
    
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/secteurs/${secteurId}/clients`);
      const data = await response.json();
      
      if (data.success) {
        setClients(data.data);
        console.log(`✅ ${data.data.length} clients récupérés pour le secteur ${secteurId}`);
      }
    } catch (error) {
      console.error('Erreur récupération clients:', error);
      Alert.alert('Erreur', 'Impossible de récupérer les clients du secteur');
    } finally {
      setLoading(false);
    }
  };

  const handleSecteurChange = (secteurId) => {
    setSelectedSecteur(secteurId);
    setClients([]);
    
    const secteurInfo = secteurs.find(s => s.ids.toString() === secteurId);
    setSelectedSecteurInfo(secteurInfo);
    
    if (secteurId) {
      fetchClientsBySecteur(secteurId);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* En-tête */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>← Retour</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>🧪 Test Carte Google Maps</Text>
          <Text style={styles.headerSubtitle}>
            Démonstration de la carte intégrée avec clients
          </Text>
        </View>

        {/* Instructions */}
        <View style={styles.instructionsCard}>
          <Text style={styles.instructionsTitle}>📋 Instructions de Test</Text>
          <Text style={styles.instructionsText}>
            1. Sélectionnez un secteur dans le menu ci-dessous{'\n'}
            2. La carte Google Maps s'affiche automatiquement{'\n'}
            3. Les clients apparaissent avec des marqueurs rouges{'\n'}
            4. Le centre du secteur avec un marqueur bleu{'\n'}
            5. Cliquez sur les marqueurs pour voir les détails
          </Text>
        </View>

        {/* Sélection du secteur */}
        <View style={styles.sectionCard}>
          <Text style={styles.sectionTitle}>🗺️ Choisir un Secteur</Text>
          
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={selectedSecteur}
              onValueChange={handleSecteurChange}
              style={styles.picker}
            >
              <Picker.Item label="-- Sélectionnez un secteur pour voir la carte --" value="" />
              {secteurs.map((secteur) => (
                <Picker.Item
                  key={secteur.ids}
                  label={`${secteur.nom} (${secteur.latitude.toFixed(4)}, ${secteur.longitude.toFixed(4)})`}
                  value={secteur.ids.toString()}
                />
              ))}
            </Picker>
          </View>
        </View>

        {/* Informations du secteur */}
        {selectedSecteurInfo && (
          <View style={styles.sectionCard}>
            <Text style={styles.sectionTitle}>ℹ️ Secteur Sélectionné</Text>
            <View style={styles.secteurInfo}>
              <Text style={styles.secteurName}>{selectedSecteurInfo.nom}</Text>
              <Text style={styles.secteurCoords}>
                📍 Centre: {selectedSecteurInfo.latitude.toFixed(6)}, {selectedSecteurInfo.longitude.toFixed(6)}
              </Text>
              <Text style={styles.secteurClients}>
                👥 {clients.length} client(s) trouvé(s)
              </Text>
            </View>
          </View>
        )}

        {/* Carte Google Maps Intégrée */}
        {selectedSecteur && (
          <View style={styles.mapSection}>
            <Text style={styles.mapSectionTitle}>
              🗺️ Carte Interactive du Secteur
            </Text>
            <GoogleMapsEmbed 
              secteur={selectedSecteurInfo} 
              clients={clients} 
            />
          </View>
        )}

        {/* Statistiques */}
        {selectedSecteur && (
          <View style={styles.statsCard}>
            <Text style={styles.statsTitle}>📊 Statistiques</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{clients.length}</Text>
                <Text style={styles.statLabel}>Clients</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>1</Text>
                <Text style={styles.statLabel}>Secteur</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{clients.length + 1}</Text>
                <Text style={styles.statLabel}>Marqueurs</Text>
              </View>
            </View>
          </View>
        )}

        {/* Message si aucun secteur sélectionné */}
        {!selectedSecteur && (
          <View style={styles.noSelectionCard}>
            <Text style={styles.noSelectionText}>
              👆 Sélectionnez un secteur ci-dessus pour voir la carte Google Maps intégrée avec tous les clients !
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 20,
    paddingTop: 10,
  },
  backButton: {
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 15,
  },
  backButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    textAlign: 'center',
  },
  instructionsCard: {
    backgroundColor: '#fff3cd',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    borderLeftWidth: 5,
    borderLeftColor: '#ffc107',
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#856404',
    marginBottom: 10,
  },
  instructionsText: {
    fontSize: 14,
    color: '#856404',
    lineHeight: 20,
  },
  sectionCard: {
    backgroundColor: '#fff',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#f8f9fa',
  },
  picker: {
    height: 50,
  },
  secteurInfo: {
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
  },
  secteurName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 5,
  },
  secteurCoords: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
    fontFamily: 'monospace',
  },
  secteurClients: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: 'bold',
  },
  mapSection: {
    margin: 15,
  },
  mapSectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  statsCard: {
    backgroundColor: '#fff',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  noSelectionCard: {
    backgroundColor: '#e3f2fd',
    margin: 15,
    padding: 30,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
  },
  noSelectionText: {
    textAlign: 'center',
    color: '#1976d2',
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
  },
});

export default TestMapScreen;
