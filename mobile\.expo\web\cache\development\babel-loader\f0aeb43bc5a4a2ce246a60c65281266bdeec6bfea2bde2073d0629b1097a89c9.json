{"ast": null, "code": "'use client';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport invariant from 'fbjs/lib/invariant';\nimport unmountComponentAtNode from \"../unmountComponentAtNode\";\nimport renderApplication, { getApplication as _getApplication } from \"./renderApplication\";\nvar emptyObject = {};\nvar runnables = {};\nvar componentProviderInstrumentationHook = function componentProviderInstrumentationHook(component) {\n  return component();\n};\nvar wrapperComponentProvider;\nvar AppRegistry = function () {\n  function AppRegistry() {\n    _classCallCheck(this, AppRegistry);\n  }\n  return _createClass(AppRegistry, null, [{\n    key: \"getAppKeys\",\n    value: function getAppKeys() {\n      return Object.keys(runnables);\n    }\n  }, {\n    key: \"getApplication\",\n    value: function getApplication(appKey, appParameters) {\n      invariant(runnables[appKey] && runnables[appKey].getApplication, \"Application \" + appKey + \" has not been registered. \" + 'This is either due to an import error during initialization or failure to call AppRegistry.registerComponent.');\n      return runnables[appKey].getApplication(appParameters);\n    }\n  }, {\n    key: \"registerComponent\",\n    value: function registerComponent(appKey, componentProvider) {\n      runnables[appKey] = {\n        getApplication: function getApplication(appParameters) {\n          return _getApplication(componentProviderInstrumentationHook(componentProvider), appParameters ? appParameters.initialProps : emptyObject, wrapperComponentProvider && wrapperComponentProvider(appParameters));\n        },\n        run: function run(appParameters) {\n          return renderApplication(componentProviderInstrumentationHook(componentProvider), wrapperComponentProvider && wrapperComponentProvider(appParameters), appParameters.callback, {\n            hydrate: appParameters.hydrate || false,\n            initialProps: appParameters.initialProps || emptyObject,\n            mode: appParameters.mode || 'concurrent',\n            rootTag: appParameters.rootTag\n          });\n        }\n      };\n      return appKey;\n    }\n  }, {\n    key: \"registerConfig\",\n    value: function registerConfig(config) {\n      config.forEach(function (_ref) {\n        var appKey = _ref.appKey,\n          component = _ref.component,\n          run = _ref.run;\n        if (run) {\n          AppRegistry.registerRunnable(appKey, run);\n        } else {\n          invariant(component, 'No component provider passed in');\n          AppRegistry.registerComponent(appKey, component);\n        }\n      });\n    }\n  }, {\n    key: \"registerRunnable\",\n    value: function registerRunnable(appKey, run) {\n      runnables[appKey] = {\n        run: run\n      };\n      return appKey;\n    }\n  }, {\n    key: \"runApplication\",\n    value: function runApplication(appKey, appParameters) {\n      var isDevelopment = process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'test';\n      if (isDevelopment) {\n        var params = _objectSpread({}, appParameters);\n        params.rootTag = \"#\" + params.rootTag.id;\n        console.log(\"Running application \\\"\" + appKey + \"\\\" with appParams:\\n\", params, \"\\nDevelopment-level warnings: \" + (isDevelopment ? 'ON' : 'OFF') + \".\" + (\"\\nPerformance optimizations: \" + (isDevelopment ? 'OFF' : 'ON') + \".\"));\n      }\n      invariant(runnables[appKey] && runnables[appKey].run, \"Application \\\"\" + appKey + \"\\\" has not been registered. \" + 'This is either due to an import error during initialization or failure to call AppRegistry.registerComponent.');\n      return runnables[appKey].run(appParameters);\n    }\n  }, {\n    key: \"setComponentProviderInstrumentationHook\",\n    value: function setComponentProviderInstrumentationHook(hook) {\n      componentProviderInstrumentationHook = hook;\n    }\n  }, {\n    key: \"setWrapperComponentProvider\",\n    value: function setWrapperComponentProvider(provider) {\n      wrapperComponentProvider = provider;\n    }\n  }, {\n    key: \"unmountApplicationComponentAtRootTag\",\n    value: function unmountApplicationComponentAtRootTag(rootTag) {\n      unmountComponentAtNode(rootTag);\n    }\n  }]);\n}();\nexport { AppRegistry as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_objectSpread", "invariant", "unmountComponentAtNode", "renderApplication", "getApplication", "_getApplication", "emptyObject", "runnables", "componentProviderInstrumentationHook", "component", "wrapperComponentProvider", "AppRegistry", "key", "value", "getApp<PERSON><PERSON>s", "Object", "keys", "appKey", "appParameters", "registerComponent", "componentProvider", "initialProps", "run", "callback", "hydrate", "mode", "rootTag", "registerConfig", "config", "for<PERSON>ach", "_ref", "registerRunnable", "runApplication", "isDevelopment", "process", "env", "NODE_ENV", "params", "id", "console", "log", "setComponentProviderInstrumentationHook", "hook", "setWrapperComponentProvider", "provider", "unmountApplicationComponentAtRootTag", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/AppRegistry/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport invariant from 'fbjs/lib/invariant';\nimport unmountComponentAtNode from '../unmountComponentAtNode';\nimport renderApplication, { getApplication as _getApplication } from './renderApplication';\nvar emptyObject = {};\nvar runnables = {};\nvar componentProviderInstrumentationHook = component => component();\nvar wrapperComponentProvider;\n\n/**\n * `AppRegistry` is the JS entry point to running all React Native apps.\n */\nexport default class AppRegistry {\n  static getAppKeys() {\n    return Object.keys(runnables);\n  }\n  static getApplication(appKey, appParameters) {\n    invariant(runnables[appKey] && runnables[appKey].getApplication, \"Application \" + appKey + \" has not been registered. \" + 'This is either due to an import error during initialization or failure to call AppRegistry.registerComponent.');\n    return runnables[appKey].getApplication(appParameters);\n  }\n  static registerComponent(appKey, componentProvider) {\n    runnables[appKey] = {\n      getApplication: appParameters => _getApplication(componentProviderInstrumentationHook(componentProvider), appParameters ? appParameters.initialProps : emptyObject, wrapperComponentProvider && wrapperComponentProvider(appParameters)),\n      run: appParameters => renderApplication(componentProviderInstrumentationHook(componentProvider), wrapperComponentProvider && wrapperComponentProvider(appParameters), appParameters.callback, {\n        hydrate: appParameters.hydrate || false,\n        initialProps: appParameters.initialProps || emptyObject,\n        mode: appParameters.mode || 'concurrent',\n        rootTag: appParameters.rootTag\n      })\n    };\n    return appKey;\n  }\n  static registerConfig(config) {\n    config.forEach(_ref => {\n      var appKey = _ref.appKey,\n        component = _ref.component,\n        run = _ref.run;\n      if (run) {\n        AppRegistry.registerRunnable(appKey, run);\n      } else {\n        invariant(component, 'No component provider passed in');\n        AppRegistry.registerComponent(appKey, component);\n      }\n    });\n  }\n\n  // TODO: fix style sheet creation when using this method\n  static registerRunnable(appKey, run) {\n    runnables[appKey] = {\n      run\n    };\n    return appKey;\n  }\n  static runApplication(appKey, appParameters) {\n    var isDevelopment = process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'test';\n    if (isDevelopment) {\n      var params = _objectSpread({}, appParameters);\n      params.rootTag = \"#\" + params.rootTag.id;\n      console.log(\"Running application \\\"\" + appKey + \"\\\" with appParams:\\n\", params, \"\\nDevelopment-level warnings: \" + (isDevelopment ? 'ON' : 'OFF') + \".\" + (\"\\nPerformance optimizations: \" + (isDevelopment ? 'OFF' : 'ON') + \".\"));\n    }\n    invariant(runnables[appKey] && runnables[appKey].run, \"Application \\\"\" + appKey + \"\\\" has not been registered. \" + 'This is either due to an import error during initialization or failure to call AppRegistry.registerComponent.');\n    return runnables[appKey].run(appParameters);\n  }\n  static setComponentProviderInstrumentationHook(hook) {\n    componentProviderInstrumentationHook = hook;\n  }\n  static setWrapperComponentProvider(provider) {\n    wrapperComponentProvider = provider;\n  }\n  static unmountApplicationComponentAtRootTag(rootTag) {\n    unmountComponentAtNode(rootTag);\n  }\n}"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAEb,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,sBAAsB;AAC7B,OAAOC,iBAAiB,IAAIC,cAAc,IAAIC,eAAe;AAC7D,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,oCAAoC,GAAG,SAAvCA,oCAAoCA,CAAGC,SAAS;EAAA,OAAIA,SAAS,CAAC,CAAC;AAAA;AACnE,IAAIC,wBAAwB;AAAC,IAKRC,WAAW;EAAA,SAAAA,YAAA;IAAAb,eAAA,OAAAa,WAAA;EAAA;EAAA,OAAAZ,YAAA,CAAAY,WAAA;IAAAC,GAAA;IAAAC,KAAA,EAC9B,SAAOC,UAAUA,CAAA,EAAG;MAClB,OAAOC,MAAM,CAACC,IAAI,CAACT,SAAS,CAAC;IAC/B;EAAC;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAOT,cAAcA,CAACa,MAAM,EAAEC,aAAa,EAAE;MAC3CjB,SAAS,CAACM,SAAS,CAACU,MAAM,CAAC,IAAIV,SAAS,CAACU,MAAM,CAAC,CAACb,cAAc,EAAE,cAAc,GAAGa,MAAM,GAAG,4BAA4B,GAAG,+GAA+G,CAAC;MAC1O,OAAOV,SAAS,CAACU,MAAM,CAAC,CAACb,cAAc,CAACc,aAAa,CAAC;IACxD;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAOM,iBAAiBA,CAACF,MAAM,EAAEG,iBAAiB,EAAE;MAClDb,SAAS,CAACU,MAAM,CAAC,GAAG;QAClBb,cAAc,EAAE,SAAhBA,cAAcA,CAAEc,aAAa;UAAA,OAAIb,eAAe,CAACG,oCAAoC,CAACY,iBAAiB,CAAC,EAAEF,aAAa,GAAGA,aAAa,CAACG,YAAY,GAAGf,WAAW,EAAEI,wBAAwB,IAAIA,wBAAwB,CAACQ,aAAa,CAAC,CAAC;QAAA;QACxOI,GAAG,EAAE,SAALA,GAAGA,CAAEJ,aAAa;UAAA,OAAIf,iBAAiB,CAACK,oCAAoC,CAACY,iBAAiB,CAAC,EAAEV,wBAAwB,IAAIA,wBAAwB,CAACQ,aAAa,CAAC,EAAEA,aAAa,CAACK,QAAQ,EAAE;YAC5LC,OAAO,EAAEN,aAAa,CAACM,OAAO,IAAI,KAAK;YACvCH,YAAY,EAAEH,aAAa,CAACG,YAAY,IAAIf,WAAW;YACvDmB,IAAI,EAAEP,aAAa,CAACO,IAAI,IAAI,YAAY;YACxCC,OAAO,EAAER,aAAa,CAACQ;UACzB,CAAC,CAAC;QAAA;MACJ,CAAC;MACD,OAAOT,MAAM;IACf;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAOc,cAAcA,CAACC,MAAM,EAAE;MAC5BA,MAAM,CAACC,OAAO,CAAC,UAAAC,IAAI,EAAI;QACrB,IAAIb,MAAM,GAAGa,IAAI,CAACb,MAAM;UACtBR,SAAS,GAAGqB,IAAI,CAACrB,SAAS;UAC1Ba,GAAG,GAAGQ,IAAI,CAACR,GAAG;QAChB,IAAIA,GAAG,EAAE;UACPX,WAAW,CAACoB,gBAAgB,CAACd,MAAM,EAAEK,GAAG,CAAC;QAC3C,CAAC,MAAM;UACLrB,SAAS,CAACQ,SAAS,EAAE,iCAAiC,CAAC;UACvDE,WAAW,CAACQ,iBAAiB,CAACF,MAAM,EAAER,SAAS,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAGD,SAAOkB,gBAAgBA,CAACd,MAAM,EAAEK,GAAG,EAAE;MACnCf,SAAS,CAACU,MAAM,CAAC,GAAG;QAClBK,GAAG,EAAHA;MACF,CAAC;MACD,OAAOL,MAAM;IACf;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAOmB,cAAcA,CAACf,MAAM,EAAEC,aAAa,EAAE;MAC3C,IAAIe,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM;MAC5F,IAAIH,aAAa,EAAE;QACjB,IAAII,MAAM,GAAGrC,aAAa,CAAC,CAAC,CAAC,EAAEkB,aAAa,CAAC;QAC7CmB,MAAM,CAACX,OAAO,GAAG,GAAG,GAAGW,MAAM,CAACX,OAAO,CAACY,EAAE;QACxCC,OAAO,CAACC,GAAG,CAAC,wBAAwB,GAAGvB,MAAM,GAAG,sBAAsB,EAAEoB,MAAM,EAAE,gCAAgC,IAAIJ,aAAa,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,+BAA+B,IAAIA,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;MACrO;MACAhC,SAAS,CAACM,SAAS,CAACU,MAAM,CAAC,IAAIV,SAAS,CAACU,MAAM,CAAC,CAACK,GAAG,EAAE,gBAAgB,GAAGL,MAAM,GAAG,8BAA8B,GAAG,+GAA+G,CAAC;MACnO,OAAOV,SAAS,CAACU,MAAM,CAAC,CAACK,GAAG,CAACJ,aAAa,CAAC;IAC7C;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAO4B,uCAAuCA,CAACC,IAAI,EAAE;MACnDlC,oCAAoC,GAAGkC,IAAI;IAC7C;EAAC;IAAA9B,GAAA;IAAAC,KAAA,EACD,SAAO8B,2BAA2BA,CAACC,QAAQ,EAAE;MAC3ClC,wBAAwB,GAAGkC,QAAQ;IACrC;EAAC;IAAAhC,GAAA;IAAAC,KAAA,EACD,SAAOgC,oCAAoCA,CAACnB,OAAO,EAAE;MACnDxB,sBAAsB,CAACwB,OAAO,CAAC;IACjC;EAAC;AAAA;AAAA,SA3DkBf,WAAW,IAAAmC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}