{"version": 3, "file": "ChromeIconsWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ChromeIconsWebpackPlugin.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAC1B,uCAAgF;AAChF,qCAAyD;AAGzD,wFAAgE;AAShE,MAAqB,wBAAyB,SAAQ,iCAAuB;IAC3E,uEAAuE;IACvE,YAAoB,OAAuB,EAAU,IAAwB;QAC3E,0BAA0B;QAC1B,KAAK,EAAE,CAAC;QAFU,YAAO,GAAP,OAAO,CAAgB;QAAU,SAAI,GAAJ,IAAI,CAAoB;IAG7E,CAAC;IAED,KAAK,CAAC,WAAW,CACf,QAAkB,EAClB,WAAwB,EACxB,IAAuB;QAEvB,MAAM,MAAM,GAAG,QAAQ,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC;QAEvE,SAAS,SAAS,CAAC,IAAY,EAAE,OAAe;YAC9C,MAAM,CAAC,GAAG,CAAC,eAAK,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,SAAS,UAAU,CAAC,IAAY,EAAE,OAAe;YAC/C,MAAM,CAAC,IAAI,CAAC,eAAK,CAAC,MAAM,CAAC,UAAU,IAAI,KAAK,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,oEAAoE;QACpE,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAClC,SAAS,CAAC,cAAc,EAAE,0CAA0C,CAAC,CAAC;YACtE,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,UAAU,CAAC,cAAc,EAAE,sDAAsD,CAAC,CAAC;YACnF,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAErB,MAAM,UAAU,GAAG,MAAM,IAAA,kCAAuB,EAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAE9E,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE;YAC9B,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,iBAAO,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YACnF,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;SACtC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA3CD,2CA2CC", "sourcesContent": ["import chalk from 'chalk';\nimport { generateChromeIconAsync, IconOptions, ProjectOptions } from 'expo-pwa';\nimport { Compilation, Compiler, sources } from 'webpack';\n\nimport { BeforeEmitOptions } from './JsonWebpackPlugin';\nimport ModifyJsonWebpackPlugin from './ModifyJsonWebpackPlugin';\n\nexport type Options = {\n  source: string;\n  outputPath?: string;\n  backgroundColor?: string;\n  resizeMode?: 'contain' | 'cover';\n};\n\nexport default class ChromeIconsWebpackPlugin extends ModifyJsonWebpackPlugin {\n  // Maybe we should support the ability to create all icons individually\n  constructor(private options: ProjectOptions, private icon: IconOptions | null) {\n    // TODO(Bacon): Validation\n    super();\n  }\n\n  async modifyAsync(\n    compiler: Compiler,\n    compilation: Compilation,\n    data: BeforeEmitOptions\n  ): Promise<BeforeEmitOptions> {\n    const logger = compiler.getInfrastructureLogger('chrome-icons-plugin');\n\n    function logNotice(type: string, message: string) {\n      logger.log(chalk.magenta(`\\u203A ${type}: ${chalk.gray(message)}`));\n    }\n\n    function logWarning(type: string, message: string) {\n      logger.warn(chalk.yellow(`\\u203A ${type}: ${chalk.gray(message)}`));\n    }\n\n    // If the icons array is already defined, then skip icon generation.\n    if (Array.isArray(data.json.icons)) {\n      logNotice('Chrome Icons', `Using custom \\`icons\\` from PWA manifest`);\n      return data;\n    }\n    if (!this.icon) {\n      logWarning('Chrome Icons', `No template image found, skipping auto generation...`);\n      return data;\n    }\n\n    data.json.icons = [];\n\n    const iconAssets = await generateChromeIconAsync(this.options, this.icon, {});\n\n    for (const asset of iconAssets) {\n      compilation.emitAsset(asset.asset.path, new sources.RawSource(asset.asset.source));\n      data.json.icons.push(asset.manifest);\n    }\n\n    return data;\n  }\n}\n"]}