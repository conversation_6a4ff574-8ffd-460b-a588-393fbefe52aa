@echo off
title Solution Clients AquaTrack
color 0A

echo.
echo ========================================
echo    📋 SOLUTION POUR AFFICHER LES CLIENTS
echo ========================================
echo.

echo 🛑 1. ARRET DE TOUS LES PROCESSUS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. ATTENTE (5 secondes)...
timeout /t 5 /nobreak >nul

echo.
echo 🖥️ 3. DEMARRAGE DU SERVEUR AVEC CLIENTS...
start "Backend Clients" cmd /k "title SERVEUR CLIENTS AQUATRACK && color 0B && echo ========================================== && echo    📋 SERVEUR AVEC LISTE DE CLIENTS && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Clients: 6 clients de test disponibles && echo ✅ API: http://localhost:4000/api/clients && echo. && echo 📡 Demarrage... && echo. && node serveur-urgence.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (10 secondes)...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 5. OUVERTURE DE L'API CLIENTS...
start http://localhost:4000/api/clients

echo.
echo 📱 6. DEMARRAGE DE L'APPLICATION MOBILE...
cd mobile
start "Frontend Mobile" cmd /k "title APPLICATION MOBILE AQUATRACK && color 0D && echo ========================================== && echo    📱 APPLICATION MOBILE && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4000 && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 7. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 8. OUVERTURE DE L'APPLICATION...
start http://localhost:19006

echo.
echo ========================================
echo    ✅ SOLUTION CLIENTS DEPLOYEE !
echo ========================================
echo.
echo 📡 API Clients: http://localhost:4000/api/clients
echo 📱 Application: http://localhost:19006
echo.
echo 📋 INSTRUCTIONS POUR VOIR LES CLIENTS:
echo.
echo 1. Verifiez que l'API fonctionne:
echo    - Allez sur http://localhost:4000/api/clients
echo    - Vous devriez voir 6 clients en JSON
echo.
echo 2. Testez l'application mobile:
echo    - Allez sur http://localhost:19006
echo    - Connectez-<NAME_EMAIL> / Tech123
echo    - Allez dans "Mes Clients"
echo    - La liste des 6 clients devrait s'afficher !
echo.
echo 📋 Liste des clients disponibles:
echo    1. Benali Fatima - Setrou
echo    2. Alami Mohammed - Setrou  
echo    3. Tazi Aicha - Setrou
echo    4. Benjelloun Youssef - Setrou
echo    5. Lahlou Khadija - Setrou
echo    6. Fassi Omar - Setrou
echo.
echo ⚠️ IMPORTANT:
echo    - Gardez les deux fenetres ouvertes
echo    - Si les clients ne s'affichent pas, actualisez (F5)
echo    - Verifiez d'abord l'API sur http://localhost:4000/api/clients
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
