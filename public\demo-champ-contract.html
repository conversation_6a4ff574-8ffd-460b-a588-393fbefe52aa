<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Champ Contract</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #ff9800;
        }
        
        .form-group {
            margin-bottom: 20px;
            padding: 12px;
            border-radius: 8px;
            border-width: 1px;
            border-style: solid;
        }
        
        .form-group.secteur {
            background: #e3f2fd;
            border-color: #007bff;
        }
        
        .form-group.client {
            background: #f0fff0;
            border-color: #28a745;
        }
        
        .form-group.contract {
            background: #fff8e1;
            border-color: #ff9800;
        }
        
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .form-label.secteur {
            color: #007bff;
        }
        
        .form-label.client {
            color: #28a745;
        }
        
        .form-label.contract {
            color: #ff9800;
        }
        
        .form-select {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            background: white;
            transition: border-color 0.3s;
        }
        
        .form-select:focus {
            outline: none;
            border-color: #ff9800;
        }
        
        .form-select:disabled {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            background: #f9fafb;
            color: #6b7280;
        }
        
        .contract-info {
            font-size: 12px;
            color: #28a745;
            font-style: italic;
            margin-top: 5px;
            text-align: center;
        }
        
        .message-box {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin-top: 8px;
        }
        
        .message-box.info {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            color: #6b7280;
        }
        
        .message-box.error {
            background: #fff5f5;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        
        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .step {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .step.active {
            border-color: #ff9800;
            background: #fff8e1;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: #ff9800;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 15px;
        }
        
        .contracts-data {
            background: #fff3cd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }
        
        .contracts-data h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .client-contracts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .client-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid #ff9800;
        }
        
        .client-name {
            font-weight: bold;
            color: #ff9800;
            margin-bottom: 10px;
        }
        
        .contract-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        
        .contract-item:last-child {
            border-bottom: none;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ff9800;
            color: white;
        }
        
        .btn-warning:hover {
            background: #f57c00;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 Champ Contract</h1>
            <p>Démonstration du champ Contract comme troisième champ du formulaire</p>
        </div>
        
        <div class="content">
            <div class="demo-form">
                <h3 style="color: #ff9800; margin-bottom: 20px;">📱 Simulation du Formulaire avec Champ Contract</h3>
                
                <div class="form-group secteur">
                    <label class="form-label secteur">📍 Secteur *</label>
                    <select id="secteurSelect" class="form-select">
                        <option value="">Sélectionner un secteur</option>
                        <option value="1">Centre-Ville</option>
                        <option value="2">Quartier Industriel</option>
                        <option value="3">Zone Résidentielle Nord</option>
                    </select>
                </div>
                
                <div class="form-group client">
                    <label class="form-label client">👤 Client *</label>
                    <select id="clientSelect" class="form-select" disabled>
                        <option value="">⬆️ Sélectionnez d'abord un secteur ci-dessus</option>
                    </select>
                </div>
                
                <div class="form-group contract">
                    <label class="form-label contract">📄 Contrat *</label>
                    <select id="contractSelect" class="form-select" disabled>
                        <option value="">⬆️ Sélectionnez d'abord un client ci-dessus</option>
                    </select>
                    <div id="contractMessage" class="message-box info" style="display: none;">
                        Message du contrat
                    </div>
                </div>
            </div>
            
            <div class="flow-steps">
                <div class="step" id="step1">
                    <div class="step-number">1</div>
                    <h4>Sélection Secteur</h4>
                    <p>L'utilisateur choisit un secteur</p>
                </div>
                
                <div class="step" id="step2">
                    <div class="step-number">2</div>
                    <h4>Filtrage Client</h4>
                    <p>Les clients du secteur apparaissent</p>
                </div>
                
                <div class="step" id="step3">
                    <div class="step-number">3</div>
                    <h4>Chargement Contrats</h4>
                    <p>Les contrats du client sont récupérés</p>
                </div>
                
                <div class="step" id="step4">
                    <div class="step-number">4</div>
                    <h4>Affichage Contract</h4>
                    <p>Le champ Contract se met à jour</p>
                </div>
            </div>
            
            <div class="contracts-data">
                <h3>📊 Données des Contrats par Client</h3>
                <div class="client-contracts">
                    <div class="client-card">
                        <div class="client-name">👤 Benali Fatima</div>
                        <div class="contract-item">📄 Contrat 1 - QR: QR001 - Sensus</div>
                        <div style="color: #28a745; font-size: 0.8em; margin-top: 5px;">✅ Sélection automatique</div>
                    </div>
                    
                    <div class="client-card">
                        <div class="client-name">👤 Alami Mohammed</div>
                        <div class="contract-item">📄 Contrat 2 - QR: QR002 - Itron</div>
                        <div style="color: #28a745; font-size: 0.8em; margin-top: 5px;">✅ Sélection automatique</div>
                    </div>
                    
                    <div class="client-card">
                        <div class="client-name">👤 Benjelloun Youssef</div>
                        <div class="contract-item">📄 Contrat 4 - QR: QR004 - Elster</div>
                        <div class="contract-item">📄 Contrat 5 - QR: QR005 - Itron</div>
                        <div style="color: #ffc107; font-size: 0.8em; margin-top: 5px;">⚠️ Choix manuel requis</div>
                    </div>
                    
                    <div class="client-card">
                        <div class="client-name">👤 Lahlou Khadija</div>
                        <div class="contract-item">📄 Contrat 6 - QR: QR006 - Sensus</div>
                        <div style="color: #28a745; font-size: 0.8em; margin-top: 5px;">✅ Sélection automatique</div>
                    </div>
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-primary" target="_blank">
                    📱 Tester l'Application Réelle
                </a>
                <button class="btn btn-warning" onclick="simulateFlow()">
                    🧪 Simuler le Flux Complet
                </button>
            </div>
            
            <div style="background: #d4edda; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #28a745;">
                <h3 style="color: #155724; margin-bottom: 15px;">✅ Champ Contract Implémenté</h3>
                <ul style="color: #155724; line-height: 1.6;">
                    <li>✅ <strong>Position :</strong> Troisième champ du formulaire</li>
                    <li>✅ <strong>Dépendance :</strong> Activé après sélection du client</li>
                    <li>✅ <strong>Sélection automatique :</strong> Si un seul contrat disponible</li>
                    <li>✅ <strong>Menu déroulant :</strong> Si plusieurs contrats disponibles</li>
                    <li>✅ <strong>Messages informatifs :</strong> Guide l'utilisateur</li>
                    <li>✅ <strong>Données de test :</strong> Contrats avec QR codes et marques</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // Données des clients par secteur
        const clientsParSecteur = {
            '1': [
                { id: 1, nom: 'Benali Fatima', contracts: [{ id: 1, qr: 'QR001', marque: 'Sensus' }] },
                { id: 2, nom: 'Alami Mohammed', contracts: [{ id: 2, qr: 'QR002', marque: 'Itron' }] },
                { id: 3, nom: 'Tazi Aicha', contracts: [{ id: 3, qr: 'QR003', marque: 'Sensus' }] }
            ],
            '2': [
                { id: 4, nom: 'Benjelloun Youssef', contracts: [{ id: 4, qr: 'QR004', marque: 'Elster' }, { id: 5, qr: 'QR005', marque: 'Itron' }] },
                { id: 5, nom: 'Lahlou Khadija', contracts: [{ id: 6, qr: 'QR006', marque: 'Sensus' }] }
            ],
            '3': [
                { id: 6, nom: 'Fassi Omar', contracts: [{ id: 7, qr: 'QR007', marque: 'Elster' }] },
                { id: 7, nom: 'Chraibi Salma', contracts: [{ id: 8, qr: 'QR008', marque: 'Itron' }] }
            ]
        };
        
        // Gestion du changement de secteur
        document.getElementById('secteurSelect').addEventListener('change', function() {
            const secteurId = this.value;
            const clientSelect = document.getElementById('clientSelect');
            const contractSelect = document.getElementById('contractSelect');
            const contractMessage = document.getElementById('contractMessage');
            
            // Réinitialiser les étapes
            document.querySelectorAll('.step').forEach(step => step.classList.remove('active'));
            
            if (secteurId) {
                // Étape 1 : Secteur sélectionné
                document.getElementById('step1').classList.add('active');
                
                // Remplir les clients
                const clients = clientsParSecteur[secteurId] || [];
                clientSelect.innerHTML = `<option value="">-- Sélectionner un client (${clients.length} dans ce secteur) --</option>`;
                
                clients.forEach((client, index) => {
                    const option = document.createElement('option');
                    option.value = client.id;
                    option.textContent = `${index + 1}. ${client.nom}`;
                    clientSelect.appendChild(option);
                });
                
                clientSelect.disabled = false;
                
                // Étape 2 : Clients chargés
                setTimeout(() => {
                    document.getElementById('step2').classList.add('active');
                }, 500);
                
                // Réinitialiser le contrat
                contractSelect.innerHTML = '<option value="">⬆️ Sélectionnez d\'abord un client ci-dessus</option>';
                contractSelect.disabled = true;
                contractMessage.style.display = 'none';
                
            } else {
                // Réinitialiser tout
                clientSelect.innerHTML = '<option value="">⬆️ Sélectionnez d\'abord un secteur ci-dessus</option>';
                clientSelect.disabled = true;
                contractSelect.innerHTML = '<option value="">⬆️ Sélectionnez d\'abord un client ci-dessus</option>';
                contractSelect.disabled = true;
                contractMessage.style.display = 'none';
            }
        });
        
        // Gestion du changement de client
        document.getElementById('clientSelect').addEventListener('change', function() {
            const clientId = this.value;
            const contractSelect = document.getElementById('contractSelect');
            const contractMessage = document.getElementById('contractMessage');
            
            if (clientId) {
                // Étape 3 : Client sélectionné
                document.getElementById('step3').classList.add('active');
                
                // Trouver le client et ses contrats
                let clientTrouve = null;
                Object.values(clientsParSecteur).forEach(clients => {
                    const client = clients.find(c => c.id.toString() === clientId);
                    if (client) clientTrouve = client;
                });
                
                if (clientTrouve && clientTrouve.contracts) {
                    const contracts = clientTrouve.contracts;
                    
                    if (contracts.length === 1) {
                        // Un seul contrat : sélection automatique
                        const contract = contracts[0];
                        contractSelect.innerHTML = `<option value="${contract.id}">Contrat ${contract.id} - QR: ${contract.qr}</option>`;
                        contractSelect.disabled = true;
                        contractMessage.textContent = '✅ Contrat unique sélectionné automatiquement';
                        contractMessage.className = 'message-box info';
                        contractMessage.style.display = 'block';
                        
                    } else {
                        // Plusieurs contrats : menu déroulant
                        contractSelect.innerHTML = `<option value="">-- Sélectionner un contrat (${contracts.length} disponibles) --</option>`;
                        contracts.forEach((contract, index) => {
                            const option = document.createElement('option');
                            option.value = contract.id;
                            option.textContent = `${index + 1}. Contrat ${contract.id} - QR: ${contract.qr}`;
                            contractSelect.appendChild(option);
                        });
                        contractSelect.disabled = false;
                        contractMessage.style.display = 'none';
                    }
                } else {
                    // Aucun contrat
                    contractSelect.innerHTML = '<option value="">❌ Aucun contrat pour ce client</option>';
                    contractSelect.disabled = true;
                    contractMessage.textContent = 'Contactez l\'administrateur';
                    contractMessage.className = 'message-box error';
                    contractMessage.style.display = 'block';
                }
                
                // Étape 4 : Contrats chargés
                setTimeout(() => {
                    document.getElementById('step4').classList.add('active');
                }, 500);
                
            } else {
                contractSelect.innerHTML = '<option value="">⬆️ Sélectionnez d\'abord un client ci-dessus</option>';
                contractSelect.disabled = true;
                contractMessage.style.display = 'none';
                document.getElementById('step3').classList.remove('active');
                document.getElementById('step4').classList.remove('active');
            }
        });
        
        // Simulation du flux complet
        function simulateFlow() {
            // Réinitialiser
            document.getElementById('secteurSelect').value = '';
            document.getElementById('clientSelect').value = '';
            document.getElementById('contractSelect').value = '';
            
            setTimeout(() => {
                document.getElementById('secteurSelect').value = '1';
                document.getElementById('secteurSelect').dispatchEvent(new Event('change'));
                
                setTimeout(() => {
                    document.getElementById('clientSelect').value = '4';
                    document.getElementById('clientSelect').dispatchEvent(new Event('change'));
                }, 1000);
            }, 500);
        }
    </script>
</body>
</html>
