@echo off
title Demarrage AquaTrack - Solution Immediate
color 0A

echo.
echo ========================================
echo    🚀 DEMARRAGE AQUATRACK IMMEDIAT
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🖥️  3. DEMARRAGE BACKEND (Port 4000)...
echo.
echo Utilisation du serveur d'urgence (sans base de donnees)
echo pour resoudre rapidement l'erreur de connexion...
echo.
start "🖥️ Backend AquaTrack" cmd /k "title Backend AquaTrack && color 0B && echo ========================================== && echo    🖥️ SERVEUR BACKEND AQUATRACK && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Status: ACTIF && echo ✅ Mode: Serveur d urgence && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo    - <EMAIL> / Admin123 && echo. && echo 📡 Demarrage du serveur... && echo. && node serveur-urgence.js"

echo.
echo ⏳ 4. ATTENTE BACKEND (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 5. DEMARRAGE FRONTEND MOBILE (Port 19006)...
cd mobile
start "📱 Frontend Mobile" cmd /k "title Frontend AquaTrack Mobile && color 0D && echo ========================================== && echo    📱 APPLICATION MOBILE AQUATRACK && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ URL: http://localhost:19006 && echo ✅ Backend: http://localhost:4000 && echo. && echo 📡 Demarrage de l application mobile... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 6. ATTENTE FRONTEND (12 secondes)...
timeout /t 12 /nobreak >nul

echo.
echo 🌐 7. TEST DE CONNEXION BACKEND...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000' -TimeoutSec 5; Write-Host '✅ BACKEND OK:' $response.message } catch { Write-Host '❌ BACKEND NON ACCESSIBLE' }"

echo.
echo 🌐 8. OUVERTURE DES NAVIGATEURS...
start http://localhost:4000
timeout /t 3 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ AQUATRACK DEMARRE !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 📱 Frontend: http://localhost:19006
echo.
echo 🔑 Comptes de test:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 📋 Instructions pour resoudre l'erreur:
echo    1. Verifiez que le backend fonctionne sur http://localhost:4000
echo    2. Allez sur votre application mobile http://localhost:19006
echo    3. Actualisez la page (F5) si necessaire
echo    4. Essayez de vous connecter avec les comptes de test
echo    5. L'erreur "Impossible de se connecter au serveur" devrait disparaitre
echo.
echo ⚠️  IMPORTANT:
echo    - Gardez les deux fenetres ouvertes
echo    - Ne fermez pas les terminaux
echo    - Si l'erreur persiste, redemarrez ce script
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
