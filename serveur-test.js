// Serveur de test simple pour AquaTrack
const express = require('express');
const cors = require('cors');

console.log('🔄 Initialisation du serveur de test...');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors({
  origin: '*', // Autoriser toutes les origines pour le test
  credentials: true
}));

app.use(express.json());

console.log('✅ Middleware configuré');

// Route de test principale
app.get('/', (req, res) => {
  console.log('📥 Requête reçue sur /');
  res.json({
    message: 'Serveur AquaTrack de test fonctionnel',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString(),
    version: '1.0.0-test'
  });
});

// Route d'authentification
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Tentative de connexion:', req.body);
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'Tech123') {
    console.log('✅ Connexion ré<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      },
      token: 'test-token-123'
    });
  } else if (email === '<EMAIL>' && password === 'Admin123') {
    console.log('✅ Connexion ré<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 2,
        nom: 'Admin',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Admin'
      },
      token: 'test-token-456'
    });
  } else {
    console.log('❌ Échec de connexion pour:', email);
    res.status(401).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// Route pour les clients
app.get('/api/clients', (req, res) => {
  console.log('📋 Requête clients reçue');
  res.json({
    success: true,
    data: [
      {
        idclient: 1,
        nom: 'Benali',
        prenom: 'Fatima',
        adresse: '45 Avenue Hassan II',
        ville: 'Setrou',
        tel: '0612345678',
        email: '<EMAIL>',
        statut: 'Actif'
      }
    ],
    count: 1
  });
});

// Route pour les secteurs
app.get('/api/secteurs', (req, res) => {
  console.log('🗺️ Requête secteurs reçue');
  res.json({
    success: true,
    data: [
      {
        ids: 1,
        nom: 'Centre Ville',
        latitude: 33.5731,
        longitude: -7.5898
      }
    ],
    count: 1
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur'
  });
});

// Démarrage du serveur
console.log('🚀 Tentative de démarrage du serveur...');

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log('');
  console.log('========================================');
  console.log('🚀 SERVEUR AQUATRACK TEST DÉMARRÉ !');
  console.log('========================================');
  console.log(`📡 URL: http://localhost:${PORT}`);
  console.log('🌐 Accessible sur toutes les interfaces');
  console.log('');
  console.log('🔧 Routes disponibles:');
  console.log('   - GET  /                - Test serveur');
  console.log('   - POST /api/auth/login  - Authentification');
  console.log('   - GET  /api/clients     - Liste clients');
  console.log('   - GET  /api/secteurs    - Liste secteurs');
  console.log('');
  console.log('🔑 Comptes de test:');
  console.log('   - <EMAIL> / Tech123');
  console.log('   - <EMAIL> / Admin123');
  console.log('');
  console.log('✅ SERVEUR PRÊT À RECEVOIR DES CONNEXIONS');
  console.log('⚠️  Appuyez sur Ctrl+C pour arrêter');
  console.log('========================================');
});

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.log('');
    console.log('❌ ERREUR: Port 4000 déjà utilisé');
    console.log('💡 Solutions:');
    console.log('   1. Arrêtez les autres serveurs: taskkill /f /im node.exe');
    console.log('   2. Ou changez de port dans le code');
    console.log('   3. Ou redémarrez votre PC');
  } else {
    console.log('❌ Erreur serveur:', error.message);
  }
});

console.log('📝 Serveur configuré, en attente de démarrage...');
