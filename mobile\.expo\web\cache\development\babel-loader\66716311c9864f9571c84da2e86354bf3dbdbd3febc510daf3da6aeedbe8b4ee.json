{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport Constants, { ExecutionEnvironment } from 'expo-constants';\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport DevLoadingView from \"../environment/DevLoadingView\";\nexport function withDevTools(AppRootComponent) {\n  var useOptionalKeepAwake = function () {\n    if (Platform.OS !== 'web') {\n      try {\n        var _require = require('expo-keep-awake'),\n          useKeepAwake = _require.useKeepAwake,\n          ExpoKeepAwakeTag = _require.ExpoKeepAwakeTag;\n        return function () {\n          return useKeepAwake(ExpoKeepAwakeTag, {\n            suppressDeactivateWarnings: true\n          });\n        };\n      } catch (_unused) {}\n    }\n    return function () {};\n  }();\n  var shouldUseExpoFastRefreshView = Platform.select({\n    web: true,\n    ios: Constants.executionEnvironment !== ExecutionEnvironment.Bare,\n    default: false\n  });\n  function WithDevTools(props) {\n    useOptionalKeepAwake();\n    if (shouldUseExpoFastRefreshView) {\n      return React.createElement(React.Fragment, null, React.createElement(AppRootComponent, _objectSpread({}, props)), React.createElement(DevLoadingView, null));\n    }\n    return React.createElement(AppRootComponent, _objectSpread({}, props));\n  }\n  var name = AppRootComponent.displayName || AppRootComponent.name || 'Anonymous';\n  WithDevTools.displayName = `withDevTools(${name})`;\n  return WithDevTools;\n}", "map": {"version": 3, "names": ["Constants", "ExecutionEnvironment", "React", "Platform", "DevLoadingView", "withDevTools", "AppRootComponent", "useOptionalKeepAwake", "OS", "_require", "require", "useKeepAwake", "ExpoKeepAwakeTag", "suppressDeactivateWarnings", "_unused", "shouldUseExpoFastRefreshView", "select", "web", "ios", "executionEnvironment", "<PERSON><PERSON>", "default", "WithDevTools", "props", "createElement", "Fragment", "_objectSpread", "name", "displayName"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\expo\\src\\launch\\withDevTools.tsx"], "sourcesContent": ["import Constants, { ExecutionEnvironment } from 'expo-constants';\nimport * as React from 'react';\nimport { Platform } from 'react-native';\n\nimport DevLoadingView from '../environment/DevLoadingView';\n\n/**\n * Append the Expo Fast Refresh view and optionally\n * keep the screen awake if `expo-keep-awake` is installed.\n */\nexport function withDevTools<TComponent extends React.ComponentType<any>>(\n  AppRootComponent: TComponent\n): React.ComponentType<React.ComponentProps<TComponent>> {\n  // This hook can be optionally imported because __DEV__ never changes during runtime.\n  // Using __DEV__ like this enables tree shaking to remove the hook in production.\n  const useOptionalKeepAwake: (tag?: string) => void = (() => {\n    if (Platform.OS !== 'web') {\n      try {\n        // Optionally import expo-keep-awake\n        const { useKeepAwake, ExpoKeepAwakeTag } = require('expo-keep-awake');\n        return () => useKeepAwake(ExpoKeepAwakeTag, { suppressDeactivateWarnings: true });\n      } catch {}\n    }\n\n    return () => {};\n  })();\n\n  const shouldUseExpoFastRefreshView = Platform.select({\n    web: true,\n    ios: Constants.executionEnvironment !== ExecutionEnvironment.Bare,\n    default: false,\n  });\n\n  function WithDevTools(props: React.ComponentProps<TComponent>) {\n    useOptionalKeepAwake();\n\n    if (shouldUseExpoFastRefreshView) {\n      return (\n        <>\n          <AppRootComponent {...props} />\n          <DevLoadingView />\n        </>\n      );\n    }\n\n    return <AppRootComponent {...props} />;\n  }\n\n  const name = AppRootComponent.displayName || AppRootComponent.name || 'Anonymous';\n  WithDevTools.displayName = `withDevTools(${name})`;\n\n  return WithDevTools;\n}\n"], "mappings": ";;;AAAA,OAAOA,SAAS,IAAIC,oBAAoB,QAAQ,gBAAgB;AAChE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAC,OAAAC,QAAA;AAG/B,OAAOC,cAAc;AAMrB,OAAM,SAAUC,YAAYA,CAC1BC,gBAA4B;EAI5B,IAAMC,oBAAoB,GAA4B,YAAK;IACzD,IAAIJ,QAAQ,CAACK,EAAE,KAAK,KAAK,EAAE;MACzB,IAAI;QAEF,IAAAC,QAAA,GAA2CC,OAAO,CAAC,iBAAiB,CAAC;UAA7DC,YAAY,GAAAF,QAAA,CAAZE,YAAY;UAAEC,gBAAgB,GAAAH,QAAA,CAAhBG,gBAAgB;QACtC,OAAO;UAAA,OAAMD,YAAY,CAACC,gBAAgB,EAAE;YAAEC,0BAA0B,EAAE;UAAI,CAAE,CAAC;QAAA;OAClF,CAAC,OAAAC,OAAA,EAAM;;IAGV,OAAO,YAAK,CAAE,CAAC;EACjB,CAAC,CAAC,CAAE;EAEJ,IAAMC,4BAA4B,GAAGZ,QAAQ,CAACa,MAAM,CAAC;IACnDC,GAAG,EAAE,IAAI;IACTC,GAAG,EAAElB,SAAS,CAACmB,oBAAoB,KAAKlB,oBAAoB,CAACmB,IAAI;IACjEC,OAAO,EAAE;GACV,CAAC;EAEF,SAASC,YAAYA,CAACC,KAAuC;IAC3DhB,oBAAoB,EAAE;IAEtB,IAAIQ,4BAA4B,EAAE;MAChC,OACEb,KAAA,CAAAsB,aAAA,CAAAtB,KAAA,CAAAuB,QAAA,QACEvB,KAAA,CAAAsB,aAAA,CAAClB,gBAAgB,EAAAoB,aAAA,KAAKH,KAAK,EAAI,EAC/BrB,KAAA,CAAAsB,aAAA,CAACpB,cAAc,OAAG,CACjB;;IAIP,OAAOF,KAAA,CAAAsB,aAAA,CAAClB,gBAAgB,EAAAoB,aAAA,KAAKH,KAAK,EAAI;EACxC;EAEA,IAAMI,IAAI,GAAGrB,gBAAgB,CAACsB,WAAW,IAAItB,gBAAgB,CAACqB,IAAI,IAAI,WAAW;EACjFL,YAAY,CAACM,WAAW,GAAG,gBAAgBD,IAAI,GAAG;EAElD,OAAOL,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}