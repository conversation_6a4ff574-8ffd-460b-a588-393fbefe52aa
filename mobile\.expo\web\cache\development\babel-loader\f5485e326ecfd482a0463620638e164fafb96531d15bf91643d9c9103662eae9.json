{"ast": null, "code": "var _vibrate = function vibrate(pattern) {\n  if ('vibrate' in window.navigator) {\n    window.navigator.vibrate(pattern);\n  }\n};\nvar Vibration = {\n  cancel: function cancel() {\n    _vibrate(0);\n  },\n  vibrate: function vibrate(pattern) {\n    if (pattern === void 0) {\n      pattern = 400;\n    }\n    _vibrate(pattern);\n  }\n};\nexport default Vibration;", "map": {"version": 3, "names": ["vibrate", "pattern", "window", "navigator", "Vibration", "cancel"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/Vibration/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar vibrate = pattern => {\n  if ('vibrate' in window.navigator) {\n    window.navigator.vibrate(pattern);\n  }\n};\nvar Vibration = {\n  cancel() {\n    vibrate(0);\n  },\n  vibrate(pattern) {\n    if (pattern === void 0) {\n      pattern = 400;\n    }\n    vibrate(pattern);\n  }\n};\nexport default Vibration;"], "mappings": "AAUA,IAAIA,QAAO,GAAG,SAAVA,OAAOA,CAAGC,OAAO,EAAI;EACvB,IAAI,SAAS,IAAIC,MAAM,CAACC,SAAS,EAAE;IACjCD,MAAM,CAACC,SAAS,CAACH,OAAO,CAACC,OAAO,CAAC;EACnC;AACF,CAAC;AACD,IAAIG,SAAS,GAAG;EACdC,MAAM,WAANA,MAAMA,CAAA,EAAG;IACPL,QAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EACDA,OAAO,WAAPA,OAAOA,CAACC,OAAO,EAAE;IACf,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MACtBA,OAAO,GAAG,GAAG;IACf;IACAD,QAAO,CAACC,OAAO,CAAC;EAClB;AACF,CAAC;AACD,eAAeG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}