import React from 'react';

const TechnicianDashboard = ({ user, onLogout }) => {
  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1>🔧 AquaTrack - Dashboard Technicien</h1>
        <div className="user-info">
          <span>Bonjour {user ? `${user.prenom} ${user.nom}` : 'Technicien'} (Technicien)</span>
          <button onClick={onLogout || (() => window.location.reload())} className="logout-btn">
            Déconnexion
          </button>
        </div>
      </header>
      
      <main className="dashboard-content">
        <div className="welcome-card">
          <h2>Bienvenue Technicien</h2>
          <p>Panneau de travail pour les techniciens AquaTrack</p>
        </div>
        
        <div className="dashboard-grid">
          <div className="dashboard-card">
            <div className="card-icon">👥</div>
            <h3>Les Clients</h3>
            
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">💧</div>
            <h3>Saisie Consommation</h3>
           
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">🧾</div>
            <h3>les Factures</h3>
           
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">📱</div>
            <h3>Scanner QR</h3>
           
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">🗺️</div>
            <h3>Carte Clients</h3>
           
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">📋</div>
            <h3>les Tâches</h3>
            
          </div>
        </div>
        
        <div className="stats-container">
          <h3>Mes Statistiques</h3>
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-number">0</div>
              <div className="stat-label">Clients Assignés</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">0</div>
              <div className="stat-label">Relevés ce mois</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">0</div>
              <div className="stat-label">Factures générées</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">0</div>
              <div className="stat-label">Tâches en cours</div>
            </div>
          </div>
        </div>
        
        <div className="recent-activity">
          <h3>Activité Récente</h3>
          <div className="activity-list">
            <div className="activity-item">
              <div className="activity-icon">💧</div>
              <div className="activity-content">
                <div className="activity-title">Aucune activité récente</div>
                <div className="activity-time">Commencez par saisir des relevés</div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default TechnicianDashboard;
