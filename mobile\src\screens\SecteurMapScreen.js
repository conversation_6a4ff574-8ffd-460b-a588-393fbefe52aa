import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';

const { width, height } = Dimensions.get('window');

const SecteurMapScreen = ({ navigation, route }) => {
  const { user } = route.params || {};
  const [loading, setLoading] = useState(false);
  const [secteurs, setSecteurs] = useState([]);
  const [selectedSecteur, setSelectedSecteur] = useState('');
  const [clients, setClients] = useState([]);
  const [secteurInfo, setSecteurInfo] = useState(null);
  const [loadingClients, setLoadingClients] = useState(false);

  const API_BASE_URL = 'http://localhost:4000';

  useEffect(() => {
    fetchSecteurs();
  }, []);

  const fetchSecteurs = async () => {
    setLoading(true);
    try {
      console.log('🔍 Récupération des secteurs...');
      const response = await fetch(`${API_BASE_URL}/api/secteurs`);
      const data = await response.json();
      
      if (data.success) {
        setSecteurs(data.data);
        console.log(`✅ ${data.data.length} secteurs récupérés`);
      } else {
        Alert.alert('Erreur', 'Impossible de récupérer les secteurs');
      }
    } catch (error) {
      console.error('Erreur récupération secteurs:', error);
      Alert.alert('Erreur', 'Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const fetchClientsBySecteur = async (secteurId) => {
    if (!secteurId) return;
    
    setLoadingClients(true);
    try {
      console.log(`🔍 Récupération des clients du secteur ${secteurId}...`);
      const response = await fetch(`${API_BASE_URL}/api/secteurs/${secteurId}/clients`);
      const data = await response.json();
      
      if (data.success) {
        setClients(data.data);
        setSecteurInfo(data.secteur);
        console.log(`✅ ${data.data.length} clients récupérés pour le secteur ${secteurId}`);
        
        if (data.data.length === 0) {
          Alert.alert('Information', `Aucun client trouvé dans le secteur ${data.secteur?.nom || secteurId}`);
        }
      } else {
        Alert.alert('Erreur', 'Impossible de récupérer les clients du secteur');
      }
    } catch (error) {
      console.error('Erreur récupération clients:', error);
      Alert.alert('Erreur', 'Erreur de connexion au serveur');
    } finally {
      setLoadingClients(false);
    }
  };

  const handleSecteurChange = (secteurId) => {
    setSelectedSecteur(secteurId);
    setClients([]);
    setSecteurInfo(null);
    
    if (secteurId) {
      fetchClientsBySecteur(secteurId);
    }
  };

  const openGoogleMaps = () => {
    if (!secteurInfo || clients.length === 0) {
      Alert.alert('Information', 'Sélectionnez un secteur avec des clients pour voir la carte');
      return;
    }

    // Construire l'URL Google Maps avec les marqueurs des clients
    const baseUrl = 'https://www.google.com/maps/dir/';
    
    // Centre de la carte sur le secteur
    const centerLat = secteurInfo.latitude;
    const centerLng = secteurInfo.longitude;
    
    // Créer les marqueurs pour chaque client
    const markers = clients.map(client => 
      `${client.latitude},${client.longitude}`
    ).join('/');
    
    // URL complète avec tous les points
    const mapsUrl = `${baseUrl}${centerLat},${centerLng}/${markers}`;
    
    // Pour React Native Web, ouvrir dans une nouvelle fenêtre
    if (typeof window !== 'undefined') {
      window.open(mapsUrl, '_blank');
    } else {
      // Pour React Native mobile, utiliser Linking
      import('react-native').then(({ Linking }) => {
        Linking.openURL(mapsUrl);
      });
    }
  };

  const renderGoogleMapsEmbed = () => {
    if (!secteurInfo || clients.length === 0) return null;

    // Créer l'URL pour l'iframe Google Maps
    const centerLat = secteurInfo.latitude;
    const centerLng = secteurInfo.longitude;
    
    // Construire les marqueurs
    const markersQuery = clients.map(client => 
      `markers=color:red%7Clabel:${client.nom.charAt(0)}%7C${client.latitude},${client.longitude}`
    ).join('&');
    
    const embedUrl = `https://www.google.com/maps/embed/v1/view?key=YOUR_API_KEY&center=${centerLat},${centerLng}&zoom=15&${markersQuery}`;
    
    return (
      <View style={styles.mapContainer}>
        <Text style={styles.mapTitle}>📍 Localisation des Clients - {secteurInfo.nom}</Text>
        <View style={styles.mapPlaceholder}>
          <Text style={styles.mapPlaceholderText}>
            🗺️ Carte Google Maps
          </Text>
          <Text style={styles.mapPlaceholderSubtext}>
            {clients.length} client(s) dans ce secteur
          </Text>
          <TouchableOpacity style={styles.openMapsButton} onPress={openGoogleMaps}>
            <Text style={styles.openMapsButtonText}>
              🌐 Ouvrir dans Google Maps
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* En-tête */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>🗺️ Clients par Secteur</Text>
          <Text style={styles.headerSubtitle}>
            Sélectionnez un secteur pour voir les clients sur la carte
          </Text>
        </View>

        {/* Sélection du secteur */}
        <View style={styles.sectionCard}>
          <Text style={styles.sectionTitle}>📍 Choisir un Secteur</Text>
          
          {loading ? (
            <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
          ) : (
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={selectedSecteur}
                onValueChange={handleSecteurChange}
                style={styles.picker}
              >
                <Picker.Item label="-- Sélectionnez un secteur --" value="" />
                {secteurs.map((secteur) => (
                  <Picker.Item
                    key={secteur.ids}
                    label={secteur.nom}
                    value={secteur.ids.toString()}
                  />
                ))}
              </Picker>
            </View>
          )}
        </View>

        {/* Informations du secteur sélectionné */}
        {secteurInfo && (
          <View style={styles.sectionCard}>
            <Text style={styles.sectionTitle}>ℹ️ Informations du Secteur</Text>
            <View style={styles.secteurInfo}>
              <Text style={styles.secteurName}>{secteurInfo.nom}</Text>
              <Text style={styles.secteurCoords}>
                📍 Coordonnées: {secteurInfo.latitude.toFixed(4)}, {secteurInfo.longitude.toFixed(4)}
              </Text>
              <Text style={styles.secteurClients}>
                👥 {clients.length} client(s) dans ce secteur
              </Text>
            </View>
          </View>
        )}

        {/* Liste des clients */}
        {loadingClients ? (
          <View style={styles.sectionCard}>
            <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
            <Text style={styles.loadingText}>Chargement des clients...</Text>
          </View>
        ) : clients.length > 0 ? (
          <View style={styles.sectionCard}>
            <Text style={styles.sectionTitle}>👥 Clients du Secteur</Text>
            {clients.map((client) => (
              <View key={client.idclient} style={styles.clientCard}>
                <Text style={styles.clientName}>{client.nom} {client.prenom}</Text>
                <Text style={styles.clientAddress}>📍 {client.adresse}</Text>
                <Text style={styles.clientCity}>🏙️ {client.ville}</Text>
                <Text style={styles.clientCoords}>
                  🗺️ {client.latitude?.toFixed(4)}, {client.longitude?.toFixed(4)}
                </Text>
              </View>
            ))}
          </View>
        ) : selectedSecteur ? (
          <View style={styles.sectionCard}>
            <Text style={styles.noClientsText}>
              Aucun client trouvé dans ce secteur
            </Text>
          </View>
        ) : null}

        {/* Carte Google Maps */}
        {renderGoogleMapsEmbed()}

        {/* Bouton retour */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>← Retour</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    textAlign: 'center',
  },
  sectionCard: {
    backgroundColor: '#fff',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#f8f9fa',
  },
  picker: {
    height: 50,
  },
  loader: {
    marginVertical: 20,
  },
  loadingText: {
    textAlign: 'center',
    color: '#666',
    marginTop: 10,
  },
  secteurInfo: {
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
  },
  secteurName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 5,
  },
  secteurCoords: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  secteurClients: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: 'bold',
  },
  clientCard: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  clientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  clientAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  clientCity: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  clientCoords: {
    fontSize: 12,
    color: '#999',
  },
  noClientsText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    padding: 20,
  },
  mapContainer: {
    margin: 15,
  },
  mapTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  mapPlaceholder: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 30,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
  },
  mapPlaceholderText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 10,
  },
  mapPlaceholderSubtext: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
  },
  openMapsButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 10,
  },
  openMapsButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonContainer: {
    padding: 20,
  },
  backButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
  },
  backButtonText: {
    color: '#666',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default SecteurMapScreen;
