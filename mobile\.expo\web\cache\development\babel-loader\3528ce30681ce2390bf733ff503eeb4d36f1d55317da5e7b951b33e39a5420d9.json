{"ast": null, "code": "var uppercasePattern = /[A-Z]/g;\nvar msPattern = /^ms-/;\nvar cache = {};\nfunction toHyphenLower(match) {\n  return '-' + match.toLowerCase();\n}\nfunction hyphenateStyleName(name) {\n  if (name in cache) {\n    return cache[name];\n  }\n  var hName = name.replace(uppercasePattern, toHyphenLower);\n  return cache[name] = msPattern.test(hName) ? '-' + hName : hName;\n}\nexport default hyphenateStyleName;", "map": {"version": 3, "names": ["uppercasePattern", "msPattern", "cache", "toHyphenLower", "match", "toLowerCase", "hyphenateStyleName", "name", "hName", "replace", "test"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/StyleSheet/compiler/hyphenateStyleName.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar uppercasePattern = /[A-Z]/g;\nvar msPattern = /^ms-/;\nvar cache = {};\nfunction toHyphenLower(match) {\n  return '-' + match.toLowerCase();\n}\nfunction hyphenateStyleName(name) {\n  if (name in cache) {\n    return cache[name];\n  }\n  var hName = name.replace(uppercasePattern, toHyphenLower);\n  return cache[name] = msPattern.test(hName) ? '-' + hName : hName;\n}\nexport default hyphenateStyleName;"], "mappings": "AASA,IAAIA,gBAAgB,GAAG,QAAQ;AAC/B,IAAIC,SAAS,GAAG,MAAM;AACtB,IAAIC,KAAK,GAAG,CAAC,CAAC;AACd,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,GAAG,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;AAClC;AACA,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EAChC,IAAIA,IAAI,IAAIL,KAAK,EAAE;IACjB,OAAOA,KAAK,CAACK,IAAI,CAAC;EACpB;EACA,IAAIC,KAAK,GAAGD,IAAI,CAACE,OAAO,CAACT,gBAAgB,EAAEG,aAAa,CAAC;EACzD,OAAOD,KAAK,CAACK,IAAI,CAAC,GAAGN,SAAS,CAACS,IAAI,CAACF,KAAK,CAAC,GAAG,GAAG,GAAGA,KAAK,GAAGA,KAAK;AAClE;AACA,eAAeF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}