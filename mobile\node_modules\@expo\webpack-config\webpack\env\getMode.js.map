{"version": 3, "file": "getMode.js", "sourceRoot": "", "sources": ["../../src/env/getMode.ts"], "names": [], "mappings": ";;AAEA;;;;;GAKG;AACH,SAAwB,OAAO,CAAC,EAC9B,UAAU,EACV,WAAW,EACX,IAAI,GAKL;IACC,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,IAAI,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACrE,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAU,CAAC;SACnD;KACF;SAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAC,WAAW,EAAU,CAAC;KACnC;IACD,IAAI,UAAU,EAAE;QACd,OAAO,YAAY,CAAC;KACrB;SAAM,IAAI,WAAW,EAAE;QACtB,OAAO,aAAa,CAAC;KACtB;IACD,OAAO,aAAa,CAAC;AACvB,CAAC;AAtBD,0BAsBC;AAED,SAAS,WAAW,CAAC,SAAkB;IACrC,IAAI,IAAI,GAAG,SAAS,IAAI,EAAE,CAAC;IAC3B,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QACjC,IAAI,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;KAChC;IACD,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxE,CAAC", "sourcesContent": ["import { Mode } from '../types';\n\n/**\n * Resolve the `mode` in a way that accounts for legacy treatment and environment variables.\n *\n * mode -> production -> development -> process.env.NODE_ENV -> 'development'\n * @category env\n */\nexport default function getMode({\n  production,\n  development,\n  mode,\n}: {\n  production?: boolean;\n  development?: boolean;\n  mode?: string;\n}): Mode {\n  if (mode === undefined) {\n    if (process.env.NODE_ENV != null && isValidMode(process.env.NODE_ENV)) {\n      return process.env.NODE_ENV.toLowerCase() as Mode;\n    }\n  } else if (isValidMode(mode)) {\n    return mode.toLowerCase() as Mode;\n  }\n  if (production) {\n    return 'production';\n  } else if (development) {\n    return 'development';\n  }\n  return 'development';\n}\n\nfunction isValidMode(inputMode?: string): boolean {\n  let mode = inputMode || '';\n  if (typeof inputMode === 'string') {\n    mode = inputMode.toLowerCase();\n  }\n  return !!mode && ['none', 'production', 'development'].includes(mode);\n}\n"]}