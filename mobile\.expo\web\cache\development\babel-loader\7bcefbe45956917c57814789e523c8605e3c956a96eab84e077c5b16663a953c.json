{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { CommonActions } from '@react-navigation/routers';\nexport var NOT_INITIALIZED_ERROR = \"The 'navigation' object hasn't been initialized yet. This might happen if you don't have a navigator mounted, or if the navigator hasn't finished mounting. See https://reactnavigation.org/docs/navigating-without-navigation-prop#handling-initialization for more details.\";\nexport default function createNavigationContainerRef() {\n  var methods = [].concat(_toConsumableArray(Object.keys(CommonActions)), ['addListener', 'removeListener', 'resetRoot', 'dispatch', 'isFocused', 'canGoBack', 'getRootState', 'getState', 'getParent', 'getCurrentRoute', 'getCurrentOptions']);\n  var listeners = {};\n  var removeListener = function removeListener(event, callback) {\n    if (listeners[event]) {\n      listeners[event] = listeners[event].filter(function (cb) {\n        return cb !== callback;\n      });\n    }\n  };\n  var current = null;\n  var ref = _objectSpread({\n    get current() {\n      return current;\n    },\n    set current(value) {\n      current = value;\n      if (value != null) {\n        Object.entries(listeners).forEach(function (_ref) {\n          var _ref2 = _slicedToArray(_ref, 2),\n            event = _ref2[0],\n            callbacks = _ref2[1];\n          callbacks.forEach(function (callback) {\n            value.addListener(event, callback);\n          });\n        });\n      }\n    },\n    isReady: function isReady() {\n      if (current == null) {\n        return false;\n      }\n      return current.isReady();\n    }\n  }, methods.reduce(function (acc, name) {\n    acc[name] = function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      if (current == null) {\n        switch (name) {\n          case 'addListener':\n            {\n              var event = args[0],\n                callback = args[1];\n              listeners[event] = listeners[event] || [];\n              listeners[event].push(callback);\n              return function () {\n                return removeListener(event, callback);\n              };\n            }\n          case 'removeListener':\n            {\n              var _event = args[0],\n                _callback = args[1];\n              removeListener(_event, _callback);\n              break;\n            }\n          default:\n            console.error(NOT_INITIALIZED_ERROR);\n        }\n      } else {\n        var _current;\n        return (_current = current)[name].apply(_current, args);\n      }\n    };\n    return acc;\n  }, {}));\n  return ref;\n}", "map": {"version": 3, "names": ["CommonActions", "NOT_INITIALIZED_ERROR", "createNavigationContainerRef", "methods", "concat", "_toConsumableArray", "Object", "keys", "listeners", "removeListener", "event", "callback", "filter", "cb", "current", "ref", "_objectSpread", "value", "entries", "for<PERSON>ach", "_ref", "_ref2", "_slicedToArray", "callbacks", "addListener", "isReady", "reduce", "acc", "name", "_len", "arguments", "length", "args", "Array", "_key", "push", "console", "error", "_current", "apply"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\core\\src\\createNavigationContainerRef.tsx"], "sourcesContent": ["import { CommonActions } from '@react-navigation/routers';\n\nimport type {\n  NavigationContainerEventMap,\n  NavigationContainerRef,\n  NavigationContainerRefWithCurrent,\n} from './types';\n\nexport const NOT_INITIALIZED_ERROR =\n  \"The 'navigation' object hasn't been initialized yet. This might happen if you don't have a navigator mounted, or if the navigator hasn't finished mounting. See https://reactnavigation.org/docs/navigating-without-navigation-prop#handling-initialization for more details.\";\n\nexport default function createNavigationContainerRef<\n  ParamList extends {} = ReactNavigation.RootParamList\n>(): NavigationContainerRefWithCurrent<ParamList> {\n  const methods = [\n    ...Object.keys(CommonActions),\n    'addListener',\n    'removeListener',\n    'resetRoot',\n    'dispatch',\n    'isFocused',\n    'canGoBack',\n    'getRootState',\n    'getState',\n    'getParent',\n    'getCurrentRoute',\n    'getCurrentOptions',\n  ] as const;\n\n  const listeners: Record<string, ((...args: any[]) => void)[]> = {};\n\n  const removeListener = (\n    event: string,\n    callback: (...args: any[]) => void\n  ) => {\n    if (listeners[event]) {\n      listeners[event] = listeners[event].filter((cb) => cb !== callback);\n    }\n  };\n\n  let current: NavigationContainerRef<ParamList> | null = null;\n\n  const ref: NavigationContainerRefWithCurrent<ParamList> = {\n    get current() {\n      return current;\n    },\n    set current(value: NavigationContainerRef<ParamList> | null) {\n      current = value;\n\n      if (value != null) {\n        Object.entries(listeners).forEach(([event, callbacks]) => {\n          callbacks.forEach((callback) => {\n            value.addListener(\n              event as keyof NavigationContainerEventMap,\n              callback\n            );\n          });\n        });\n      }\n    },\n    isReady: () => {\n      if (current == null) {\n        return false;\n      }\n\n      return current.isReady();\n    },\n    ...methods.reduce<any>((acc, name) => {\n      acc[name] = (...args: any[]) => {\n        if (current == null) {\n          switch (name) {\n            case 'addListener': {\n              const [event, callback] = args;\n\n              listeners[event] = listeners[event] || [];\n              listeners[event].push(callback);\n\n              return () => removeListener(event, callback);\n            }\n            case 'removeListener': {\n              const [event, callback] = args;\n\n              removeListener(event, callback);\n              break;\n            }\n            default:\n              console.error(NOT_INITIALIZED_ERROR);\n          }\n        } else {\n          // @ts-expect-error: this is ok\n          return current[name](...args);\n        }\n      };\n      return acc;\n    }, {}),\n  };\n\n  return ref;\n}\n"], "mappings": ";;;;;AAAA,SAASA,aAAa,QAAQ,2BAA2B;AAQzD,OAAO,IAAMC,qBAAqB,GAChC,+QAA+Q;AAEjR,eAAe,SAASC,4BAA4BA,CAAA,EAEF;EAChD,IAAMC,OAAO,MAAAC,MAAA,CAAAC,kBAAA,CACRC,MAAM,CAACC,IAAI,CAACP,aAAa,CAAC,IAC7B,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,WAAW,EACX,WAAW,EACX,cAAc,EACd,UAAU,EACV,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACX;EAEV,IAAMQ,SAAuD,GAAG,CAAC,CAAC;EAElE,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAClBC,KAAa,EACbC,QAAkC,EAC/B;IACH,IAAIH,SAAS,CAACE,KAAK,CAAC,EAAE;MACpBF,SAAS,CAACE,KAAK,CAAC,GAAGF,SAAS,CAACE,KAAK,CAAC,CAACE,MAAM,CAAE,UAAAC,EAAE;QAAA,OAAKA,EAAE,KAAKF,QAAQ;MAAA,EAAC;IACrE;EACF,CAAC;EAED,IAAIG,OAAiD,GAAG,IAAI;EAE5D,IAAMC,GAAiD,GAAAC,aAAA;IACrD,IAAIF,OAAOA,CAAA,EAAG;MACZ,OAAOA,OAAO;IAChB,CAAC;IACD,IAAIA,OAAOA,CAACG,KAA+C,EAAE;MAC3DH,OAAO,GAAGG,KAAK;MAEf,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjBX,MAAM,CAACY,OAAO,CAACV,SAAS,CAAC,CAACW,OAAO,CAAC,UAAAC,IAAA,EAAwB;UAAA,IAAAC,KAAA,GAAAC,cAAA,CAALF,IAAA;YAAjBV,KAAK,GAAAW,KAAA;YAAEE,SAAS,GAAAF,KAAA;UAClDE,SAAS,CAACJ,OAAO,CAAE,UAAAR,QAAQ,EAAK;YAC9BM,KAAK,CAACO,WAAW,CACfd,KAAK,EACLC,QAAQ,CACT;UACH,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;IACDc,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;MACb,IAAIX,OAAO,IAAI,IAAI,EAAE;QACnB,OAAO,KAAK;MACd;MAEA,OAAOA,OAAO,CAACW,OAAO,EAAE;IAC1B;EAAC,GACEtB,OAAO,CAACuB,MAAM,CAAM,UAACC,GAAG,EAAEC,IAAI,EAAK;IACpCD,GAAG,CAACC,IAAI,CAAC,GAAG,YAAoB;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAhBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAClB,IAAIpB,OAAO,IAAI,IAAI,EAAE;QACnB,QAAQc,IAAI;UACV,KAAK,aAAa;YAAE;cAClB,IAAOlB,KAAK,GAAcsB,IAAI;gBAAhBrB,QAAQ,GAAIqB,IAAI;cAE9BxB,SAAS,CAACE,KAAK,CAAC,GAAGF,SAAS,CAACE,KAAK,CAAC,IAAI,EAAE;cACzCF,SAAS,CAACE,KAAK,CAAC,CAACyB,IAAI,CAACxB,QAAQ,CAAC;cAE/B,OAAO;gBAAA,OAAMF,cAAc,CAACC,KAAK,EAAEC,QAAQ,CAAC;cAAA;YAC9C;UACA,KAAK,gBAAgB;YAAE;cACrB,IAAOD,MAAK,GAAcsB,IAAI;gBAAhBrB,SAAQ,GAAIqB,IAAI;cAE9BvB,cAAc,CAACC,MAAK,EAAEC,SAAQ,CAAC;cAC/B;YACF;UACA;YACEyB,OAAO,CAACC,KAAK,CAACpC,qBAAqB,CAAC;QAAC;MAE3C,CAAC,MAAM;QAAA,IAAAqC,QAAA;QAEL,OAAO,CAAAA,QAAA,GAAAxB,OAAO,EAACc,IAAI,CAAC,CAAAW,KAAA,CAAAD,QAAA,EAAIN,IAAI,CAAC;MAC/B;IACF,CAAC;IACD,OAAOL,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,EACN;EAED,OAAOZ,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}