const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 4000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://localhost:3008', 'http://localhost:3009', 'http://localhost:19006'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes de test simples (sans base de données)
// Route d'authentification de test
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Tentative de connexion:', req.body);
  const { email, password } = req.body;

  if (email === '<EMAIL>' && password === 'Tech123') {
    console.log('✅ Connexion ré<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      },
      token: 'test-token-123'
    });
  } else if (email === '<EMAIL>' && password === 'Admin123') {
    console.log('✅ Connexion ré<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 2,
        nom: 'Admin',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Admin'
      },
      token: 'test-token-456'
    });
  } else {
    console.log('❌ Échec de connexion pour:', email);
    res.status(401).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// Route pour les clients de test
app.get('/api/clients', (req, res) => {
  console.log('📋 Route /api/clients appelée');
  const testClients = [
    {
      idclient: 1,
      nom: 'Benali',
      prenom: 'Fatima',
      adresse: '45 Avenue Hassan II, près de l\'école Omar Ibn Al Khattab',
      ville: 'Setrou',
      tel: '0612345678',
      email: '<EMAIL>',
      statut: 'Actif'
    },
    {
      idclient: 2,
      nom: 'Alami',
      prenom: 'Mohammed',
      adresse: '12 Rue des Oliviers',
      ville: 'Setrou',
      tel: '0623456789',
      email: '<EMAIL>',
      statut: 'Actif'
    }
  ];

  res.json({
    success: true,
    data: testClients,
    count: testClients.length
  });
});

// Route pour les contrats d'un client
app.get('/api/clients/:id/contracts', (req, res) => {
  console.log('📋 Route /api/clients/:id/contracts appelée pour ID:', req.params.id);
  const testContracts = [
    {
      idcontract: 1,
      codeqr: 'QR-TEST-RFNAI1',
      datecontract: '2023-07-03',
      marquecompteur: 'SAGEMCOM TEST',
      numseriecompteur: 'TEST-001'
    }
  ];

  res.json({
    success: true,
    count: testContracts.length,
    data: testContracts
  });
});

// Route pour les secteurs
app.get('/api/secteurs', (req, res) => {
  console.log('🗺️ Route /api/secteurs appelée');
  const testSecteurs = [
    {
      ids: 1,
      nom: 'Centre Ville',
      latitude: 33.5731,
      longitude: -7.5898
    },
    {
      ids: 2,
      nom: 'Quartier Industriel',
      latitude: 33.5831,
      longitude: -7.5998
    }
  ];

  res.json({
    success: true,
    data: testSecteurs,
    count: testSecteurs.length
  });
});

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'API AquaTrack - Backend Complet',
    version: '1.0.0',
    endpoints: {
      // Scanner QR
      scan: '/api/scan/:qrCode',
      qrCodes: '/api/qr-codes',
      // Clients
      clients: '/api/clients',
      clientById: '/api/clients/:id',
      clientContracts: '/api/clients/:id/contracts',
      clientFactures: '/api/clients/:id/factures',
      // Secteurs
      secteurs: '/api/secteurs',
      // Consommations
      consommations: '/api/consommations',
      clientConsommations: '/api/clients/:id/consommations',
      lastConsommation: '/api/contracts/:id/last-consommation',
      // Factures
      factures: '/api/factures',
      factureById: '/api/factures/:id',
      updateFactureStatus: '/api/factures/:id/status',
      // Test
      testDb: '/api/test-db'
    }
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err.stack);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Erreur serveur'
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur API AquaTrack démarré sur le port ${PORT}`);
  console.log(`📡 Endpoints disponibles:`);
  console.log(`   📋 CLIENTS:`);
  console.log(`      - GET  /api/clients          - Liste des clients`);
  console.log(`      - GET  /api/clients/:id      - Client par ID`);
  console.log(`      - GET  /api/clients/:id/contracts - Contrats d'un client`);
  console.log(`      - GET  /api/clients/:id/factures - Factures d'un client`);
  console.log(`   💧 CONSOMMATIONS:`);
  console.log(`      - GET  /api/consommations    - Liste des consommations`);
  console.log(`      - POST /api/consommations    - Créer une consommation`);
  console.log(`      - GET  /api/clients/:id/consommations - Consommations d'un client`);
  console.log(`   🧾 FACTURES:`);
  console.log(`      - GET  /api/factures         - Liste des factures`);
  console.log(`      - POST /api/factures         - Créer une facture`);
  console.log(`      - GET  /api/factures/:id     - Facture par ID`);
  console.log(`   📱 SCANNER:`);
  console.log(`      - GET  /api/scan/:qrCode     - Scanner un QR code`);
  console.log(`      - GET  /api/qr-codes         - Lister les QR codes`);
  console.log(`   🔧 AUTRES:`);
  console.log(`      - GET  /api/secteurs         - Liste des secteurs`);
  console.log(`      - GET  /api/test-db          - Tester la connexion DB`);
  console.log(`🌐 CORS autorisé pour: localhost:3000, localhost:3001, localhost:3002`);
  console.log(`🎯 Interface web: http://localhost:3002/technician-dashboard`);
});

