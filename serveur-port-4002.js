// Serveur AquaTrack sur le port 4002
console.log('🚀 SERVEUR AQUATRACK - PORT 4002');
console.log('================================');

const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 4002;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8081', 'http://localhost:19006', 'http://localhost:4002'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toLocaleTimeString()} - ${req.method} ${req.path}`);
  next();
});

// Servir les fichiers statiques du frontend
app.use(express.static(path.join(__dirname, 'mobile', 'web-build')));
app.use(express.static(path.join(__dirname, 'public')));

// Route principale
app.get('/', (req, res) => {
  console.log('✅ Route / appelée à', new Date().toLocaleTimeString());
  res.json({
    message: 'Serveur AquaTrack sur port 4002',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString(),
    routes: {
      consommation: '/consommation',
      api_secteurs: '/api/secteurs',
      api_clients: '/api/clients',
      api_auth: '/api/auth/login'
    }
  });
});

// Route pour la page consommation
app.get('/consommation', (req, res) => {
  console.log('📋 Route /consommation appelée');
  
  // Essayer de servir le fichier HTML de l'application React
  const htmlPath = path.join(__dirname, 'mobile', 'web-build', 'index.html');
  const fs = require('fs');
  
  if (fs.existsSync(htmlPath)) {
    res.sendFile(htmlPath);
  } else {
    // Si pas de build, retourner une page simple
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>AquaTrack - Consommation</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
          .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
          .header { text-align: center; margin-bottom: 30px; }
          .title { color: #007AFF; font-size: 28px; margin-bottom: 10px; }
          .subtitle { color: #666; font-size: 16px; }
          .info { background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0; }
          .button { background: #007AFF; color: white; padding: 15px 30px; border: none; border-radius: 10px; font-size: 16px; cursor: pointer; margin: 10px; }
          .button:hover { background: #0056b3; }
          .status { background: #d4edda; padding: 15px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 class="title">🚀 AquaTrack - Consommation</h1>
            <p class="subtitle">Serveur démarré avec succès sur le port 4002</p>
          </div>
          
          <div class="status">
            <h3>✅ Serveur Actif</h3>
            <p><strong>URL:</strong> http://localhost:4002</p>
            <p><strong>Page:</strong> /consommation</p>
            <p><strong>Status:</strong> Opérationnel</p>
            <p><strong>Heure:</strong> ${new Date().toLocaleString()}</p>
          </div>
          
          <div class="info">
            <h3>📋 Pages Disponibles</h3>
            <p><strong>Accueil:</strong> <a href="/">http://localhost:4002/</a></p>
            <p><strong>Consommation:</strong> <a href="/consommation">http://localhost:4002/consommation</a></p>
            <p><strong>Application Mobile:</strong> <a href="http://localhost:19006">http://localhost:19006</a></p>
          </div>
          
          <div class="info">
            <h3>🔧 APIs Disponibles</h3>
            <p><strong>Secteurs:</strong> <a href="/api/secteurs">/api/secteurs</a></p>
            <p><strong>Clients:</strong> <a href="/api/clients">/api/clients</a></p>
            <p><strong>Authentification:</strong> /api/auth/login</p>
          </div>
          
          <div style="text-align: center; margin-top: 30px;">
            <button class="button" onclick="window.open('http://localhost:19006', '_blank')">
              📱 Ouvrir l'Application Mobile
            </button>
            <button class="button" onclick="window.location.reload()">
              🔄 Actualiser
            </button>
          </div>
        </div>
      </body>
      </html>
    `);
  }
});

// Routes API - Authentification
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Tentative de connexion:', req.body);
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'Tech123') {
    console.log('✅ CONNEXION RÉ<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      },
      token: 'test-token-123'
    });
  } else if (email === '<EMAIL>' && password === 'Admin123') {
    console.log('✅ CONNEXION RÉ<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 2,
        nom: 'Admin',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Admin'
      },
      token: 'test-token-456'
    });
  } else {
    console.log('❌ ÉCHEC DE CONNEXION pour:', email);
    res.status(401).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// Routes API - Secteurs
app.get('/api/secteurs', (req, res) => {
  console.log('🗺️ Requête secteurs reçue');
  
  const testSecteurs = [
    { ids: 1, nom: 'Centre-Ville', latitude: 33.5731, longitude: -7.5898 },
    { ids: 2, nom: 'Quartier Industriel', latitude: 33.5831, longitude: -7.5998 },
    { ids: 3, nom: 'Zone Résidentielle Nord', latitude: 33.5931, longitude: -7.6098 },
    { ids: 4, nom: 'Zone Résidentielle Sud', latitude: 33.5631, longitude: -7.5798 },
    { ids: 5, nom: 'Quartier Commercial', latitude: 33.5531, longitude: -7.5698 }
  ];
  
  console.log(`✅ ${testSecteurs.length} secteurs retournés`);
  res.json({
    success: true,
    data: testSecteurs,
    count: testSecteurs.length,
    total: testSecteurs.length
  });
});

// Routes API - Clients
app.get('/api/clients', (req, res) => {
  console.log('📋 Requête clients reçue');
  
  const testClients = [
    { idclient: 1, nom: 'Benali', prenom: 'Fatima', adresse: '45 Avenue Hassan II', ville: 'Setrou', tel: '0612345678', email: '<EMAIL>', statut: 'Actif', ids: 1, secteur_nom: 'Centre-Ville' },
    { idclient: 2, nom: 'Alami', prenom: 'Mohammed', adresse: '12 Rue des Oliviers', ville: 'Setrou', tel: '0623456789', email: '<EMAIL>', statut: 'Actif', ids: 1, secteur_nom: 'Centre-Ville' },
    { idclient: 3, nom: 'Tazi', prenom: 'Aicha', adresse: '78 Boulevard Zerktouni', ville: 'Setrou', tel: '0634567890', email: '<EMAIL>', statut: 'Actif', ids: 2, secteur_nom: 'Quartier Industriel' },
    { idclient: 4, nom: 'Benjelloun', prenom: 'Youssef', adresse: '23 Rue Ibn Sina', ville: 'Setrou', tel: '0645678901', email: '<EMAIL>', statut: 'Actif', ids: 3, secteur_nom: 'Zone Résidentielle Nord' },
    { idclient: 5, nom: 'Lahlou', prenom: 'Khadija', adresse: '56 Avenue Mohammed V', ville: 'Setrou', tel: '0656789012', email: '<EMAIL>', statut: 'Actif', ids: 3, secteur_nom: 'Zone Résidentielle Nord' },
    { idclient: 6, nom: 'Fassi', prenom: 'Omar', adresse: '89 Rue Al Massira', ville: 'Setrou', tel: '0667890123', email: '<EMAIL>', statut: 'Actif', ids: 4, secteur_nom: 'Zone Résidentielle Sud' }
  ];
  
  console.log(`✅ ${testClients.length} clients retournés`);
  res.json({
    success: true,
    data: testClients,
    clients: testClients,
    total: testClients.length,
    count: testClients.length,
    message: 'Liste des clients récupérée avec succès'
  });
});

// Routes API - Clients par secteur
app.get('/api/secteurs/:id/clients', (req, res) => {
  const { id } = req.params;
  console.log(`🗺️ Requête clients du secteur ${id} reçue`);
  
  const clientsParSecteur = {
    '1': [
      { idclient: 1, nom: 'Benali', prenom: 'Fatima', adresse: '45 Avenue Hassan II', ville: 'Setrou', tel: '0612345678', email: '<EMAIL>', statut: 'Actif', ids: 1, secteur_nom: 'Centre-Ville', latitude: 33.5735, longitude: -7.5895 },
      { idclient: 2, nom: 'Alami', prenom: 'Mohammed', adresse: '12 Rue des Oliviers', ville: 'Setrou', tel: '0623456789', email: '<EMAIL>', statut: 'Actif', ids: 1, secteur_nom: 'Centre-Ville', latitude: 33.5728, longitude: -7.5901 }
    ],
    '2': [
      { idclient: 3, nom: 'Tazi', prenom: 'Aicha', adresse: '78 Boulevard Zerktouni', ville: 'Setrou', tel: '0634567890', email: '<EMAIL>', statut: 'Actif', ids: 2, secteur_nom: 'Quartier Industriel', latitude: 33.5835, longitude: -7.5995 }
    ],
    '3': [
      { idclient: 4, nom: 'Benjelloun', prenom: 'Youssef', adresse: '23 Rue Ibn Sina', ville: 'Setrou', tel: '0645678901', email: '<EMAIL>', statut: 'Actif', ids: 3, secteur_nom: 'Zone Résidentielle Nord', latitude: 33.5935, longitude: -7.6095 },
      { idclient: 5, nom: 'Lahlou', prenom: 'Khadija', adresse: '56 Avenue Mohammed V', ville: 'Setrou', tel: '0656789012', email: '<EMAIL>', statut: 'Actif', ids: 3, secteur_nom: 'Zone Résidentielle Nord', latitude: 33.5928, longitude: -7.6101 }
    ],
    '4': [
      { idclient: 6, nom: 'Fassi', prenom: 'Omar', adresse: '89 Rue Al Massira', ville: 'Setrou', tel: '0667890123', email: '<EMAIL>', statut: 'Actif', ids: 4, secteur_nom: 'Zone Résidentielle Sud', latitude: 33.5635, longitude: -7.5795 }
    ],
    '5': []
  };
  
  const clients = clientsParSecteur[id] || [];
  const secteurInfo = {
    '1': { ids: 1, nom: 'Centre-Ville', latitude: 33.5731, longitude: -7.5898 },
    '2': { ids: 2, nom: 'Quartier Industriel', latitude: 33.5831, longitude: -7.5998 },
    '3': { ids: 3, nom: 'Zone Résidentielle Nord', latitude: 33.5931, longitude: -7.6098 },
    '4': { ids: 4, nom: 'Zone Résidentielle Sud', latitude: 33.5631, longitude: -7.5798 },
    '5': { ids: 5, nom: 'Quartier Commercial', latitude: 33.5531, longitude: -7.5698 }
  };
  
  console.log(`✅ ${clients.length} client(s) trouvé(s) dans le secteur ${id}`);
  
  res.json({
    success: true,
    data: clients,
    count: clients.length,
    total: clients.length,
    secteur: secteurInfo[id] || null,
    message: `${clients.length} client(s) trouvé(s) dans le secteur ${secteurInfo[id]?.nom || id}`
  });
});

// Démarrage du serveur
console.log('🚀 Tentative de démarrage du serveur sur le port 4002...');

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log('');
  console.log('🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯');
  console.log('🎯                                      🎯');
  console.log('🎯    SERVEUR AQUATRACK PORT 4002      🎯');
  console.log('🎯           DÉMARRÉ AVEC SUCCÈS       🎯');
  console.log('🎯                                      🎯');
  console.log('🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯');
  console.log('');
  console.log('📡 URL Principal: http://localhost:' + PORT);
  console.log('📋 Page Consommation: http://localhost:' + PORT + '/consommation');
  console.log('🌐 Accessible depuis toutes les interfaces');
  console.log('');
  console.log('🔧 Routes disponibles:');
  console.log('   - GET  /                     - Page d\'accueil');
  console.log('   - GET  /consommation         - Page consommation');
  console.log('   - POST /api/auth/login       - Authentification');
  console.log('   - GET  /api/clients          - Liste clients');
  console.log('   - GET  /api/secteurs         - Liste secteurs');
  console.log('   - GET  /api/secteurs/:id/clients - Clients par secteur');
  console.log('');
  console.log('🔑 Comptes de test:');
  console.log('   - <EMAIL> / Tech123');
  console.log('   - <EMAIL> / Admin123');
  console.log('');
  console.log('✅ SERVEUR PRÊT À RECEVOIR DES CONNEXIONS SUR LE PORT 4002');
  console.log('⚠️  Appuyez sur Ctrl+C pour arrêter');
  console.log('');
  console.log('🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯');
});

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.log('');
    console.log('❌ ERREUR: Port 4002 déjà utilisé');
    console.log('💡 Solutions:');
    console.log('   1. Arrêtez les autres serveurs: taskkill /f /im node.exe');
    console.log('   2. Ou redémarrez votre PC');
    console.log('   3. Ou changez de port');
  } else {
    console.log('❌ Erreur serveur:', error.message);
  }
});

console.log('📝 Serveur configuré pour le port 4002, en attente de démarrage...');
