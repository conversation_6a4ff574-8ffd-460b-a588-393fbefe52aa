{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { NavigationContext, NavigationRouteContext } from '@react-navigation/native';\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useSafeAreaFrame, useSafeAreaInsets } from 'react-native-safe-area-context';\nimport Background from \"./Background\";\nimport getDefaultHeaderHeight from \"./Header/getDefaultHeaderHeight\";\nimport HeaderHeightContext from \"./Header/HeaderHeightContext\";\nimport HeaderShownContext from \"./Header/HeaderShownContext\";\nexport default function Screen(props) {\n  var dimensions = useSafeAreaFrame();\n  var insets = useSafeAreaInsets();\n  var isParentHeaderShown = React.useContext(HeaderShownContext);\n  var parentHeaderHeight = React.useContext(HeaderHeightContext);\n  var focused = props.focused,\n    _props$modal = props.modal,\n    modal = _props$modal === void 0 ? false : _props$modal,\n    header = props.header,\n    _props$headerShown = props.headerShown,\n    headerShown = _props$headerShown === void 0 ? true : _props$headerShown,\n    headerTransparent = props.headerTransparent,\n    _props$headerStatusBa = props.headerStatusBarHeight,\n    headerStatusBarHeight = _props$headerStatusBa === void 0 ? isParentHeaderShown ? 0 : insets.top : _props$headerStatusBa,\n    navigation = props.navigation,\n    route = props.route,\n    children = props.children,\n    style = props.style;\n  var _React$useState = React.useState(function () {\n      return getDefaultHeaderHeight(dimensions, modal, headerStatusBarHeight);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    headerHeight = _React$useState2[0],\n    setHeaderHeight = _React$useState2[1];\n  return React.createElement(Background, {\n    accessibilityElementsHidden: !focused,\n    importantForAccessibility: focused ? 'auto' : 'no-hide-descendants',\n    style: [styles.container, style]\n  }, React.createElement(View, {\n    style: styles.content\n  }, React.createElement(HeaderShownContext.Provider, {\n    value: isParentHeaderShown || headerShown !== false\n  }, React.createElement(HeaderHeightContext.Provider, {\n    value: headerShown ? headerHeight : parentHeaderHeight != null ? parentHeaderHeight : 0\n  }, children))), headerShown ? React.createElement(NavigationContext.Provider, {\n    value: navigation\n  }, React.createElement(NavigationRouteContext.Provider, {\n    value: route\n  }, React.createElement(View, {\n    onLayout: function onLayout(e) {\n      var height = e.nativeEvent.layout.height;\n      setHeaderHeight(height);\n    },\n    style: headerTransparent ? styles.absolute : null\n  }, header))) : null);\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'column-reverse'\n  },\n  content: {\n    flex: 1\n  },\n  absolute: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0\n  }\n});", "map": {"version": 3, "names": ["NavigationContext", "NavigationRouteContext", "React", "StyleSheet", "View", "useSafeAreaFrame", "useSafeAreaInsets", "Background", "getDefaultHeaderHeight", "HeaderHeightContext", "HeaderShownContext", "Screen", "props", "dimensions", "insets", "isParentHeaderShown", "useContext", "parentHeaderHeight", "focused", "_props$modal", "modal", "header", "_props$headerShown", "headerShown", "headerTransparent", "_props$headerStatusBa", "headerStatusBarHeight", "top", "navigation", "route", "children", "style", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "headerHeight", "setHeaderHeight", "createElement", "accessibilityElementsHidden", "importantForAccessibility", "styles", "container", "content", "Provider", "value", "onLayout", "e", "height", "nativeEvent", "layout", "absolute", "create", "flex", "flexDirection", "position", "left", "right"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\elements\\src\\Screen.tsx"], "sourcesContent": ["import {\n  NavigationContext,\n  NavigationProp,\n  NavigationRouteContext,\n  ParamListBase,\n  RouteProp,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\nimport {\n  useSafeAreaFrame,\n  useSafeAreaInsets,\n} from 'react-native-safe-area-context';\n\nimport Background from './Background';\nimport getDefaultHeaderHeight from './Header/getDefaultHeaderHeight';\nimport HeaderHeightContext from './Header/HeaderHeightContext';\nimport HeaderShownContext from './Header/HeaderShownContext';\n\ntype Props = {\n  focused: boolean;\n  modal?: boolean;\n  navigation: NavigationProp<ParamListBase>;\n  route: RouteProp<ParamListBase>;\n  header: React.ReactNode;\n  headerShown?: boolean;\n  headerStatusBarHeight?: number;\n  headerTransparent?: boolean;\n  style?: StyleProp<ViewStyle>;\n  children: React.ReactNode;\n};\n\nexport default function Screen(props: Props) {\n  const dimensions = useSafeAreaFrame();\n  const insets = useSafeAreaInsets();\n\n  const isParentHeaderShown = React.useContext(HeaderShownContext);\n  const parentHeaderHeight = React.useContext(HeaderHeightContext);\n\n  const {\n    focused,\n    modal = false,\n    header,\n    headerShown = true,\n    headerTransparent,\n    headerStatusBarHeight = isParentHeaderShown ? 0 : insets.top,\n    navigation,\n    route,\n    children,\n    style,\n  } = props;\n\n  const [headerHeight, setHeaderHeight] = React.useState(() =>\n    getDefaultHeaderHeight(dimensions, modal, headerStatusBarHeight)\n  );\n\n  return (\n    <Background\n      accessibilityElementsHidden={!focused}\n      importantForAccessibility={focused ? 'auto' : 'no-hide-descendants'}\n      style={[styles.container, style]}\n    >\n      <View style={styles.content}>\n        <HeaderShownContext.Provider\n          value={isParentHeaderShown || headerShown !== false}\n        >\n          <HeaderHeightContext.Provider\n            value={headerShown ? headerHeight : parentHeaderHeight ?? 0}\n          >\n            {children}\n          </HeaderHeightContext.Provider>\n        </HeaderShownContext.Provider>\n      </View>\n      {headerShown ? (\n        <NavigationContext.Provider value={navigation}>\n          <NavigationRouteContext.Provider value={route}>\n            <View\n              onLayout={(e) => {\n                const { height } = e.nativeEvent.layout;\n\n                setHeaderHeight(height);\n              }}\n              style={headerTransparent ? styles.absolute : null}\n            >\n              {header}\n            </View>\n          </NavigationRouteContext.Provider>\n        </NavigationContext.Provider>\n      ) : null}\n    </Background>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'column-reverse',\n  },\n  // This is necessary to avoid applying 'column-reverse' to screen content\n  content: {\n    flex: 1,\n  },\n  absolute: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n  },\n});\n"], "mappings": ";AAAA,SACEA,iBAAiB,EAEjBC,sBAAsB,QAGjB,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAE9B,SACEC,gBAAgB,EAChBC,iBAAiB,QACZ,gCAAgC;AAEvC,OAAOC,UAAU;AACjB,OAAOC,sBAAsB;AAC7B,OAAOC,mBAAmB;AAC1B,OAAOC,kBAAkB;AAezB,eAAe,SAASC,MAAMA,CAACC,KAAY,EAAE;EAC3C,IAAMC,UAAU,GAAGR,gBAAgB,EAAE;EACrC,IAAMS,MAAM,GAAGR,iBAAiB,EAAE;EAElC,IAAMS,mBAAmB,GAAGb,KAAK,CAACc,UAAU,CAACN,kBAAkB,CAAC;EAChE,IAAMO,kBAAkB,GAAGf,KAAK,CAACc,UAAU,CAACP,mBAAmB,CAAC;EAEhE,IACES,OAAO,GAULN,KAAK,CAVPM,OAAO;IAAAC,YAAA,GAULP,KAAK,CATPQ,KAAK;IAALA,KAAK,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;IACbE,MAAM,GAQJT,KAAK,CARPS,MAAM;IAAAC,kBAAA,GAQJV,KAAK,CAPPW,WAAW;IAAXA,WAAW,GAAAD,kBAAA,cAAG,IAAI,GAAAA,kBAAA;IAClBE,iBAAiB,GAMfZ,KAAK,CANPY,iBAAiB;IAAAC,qBAAA,GAMfb,KAAK,CALPc,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAGV,mBAAmB,GAAG,CAAC,GAAGD,MAAM,CAACa,GAAG,GAAAF,qBAAA;IAC5DG,UAAU,GAIRhB,KAAK,CAJPgB,UAAU;IACVC,KAAK,GAGHjB,KAAK,CAHPiB,KAAK;IACLC,QAAQ,GAENlB,KAAK,CAFPkB,QAAQ;IACRC,KAAA,GACEnB,KAAK,CADPmB,KAAA;EAGF,IAAAC,eAAA,GAAwC9B,KAAK,CAAC+B,QAAQ,CAAC;MAAA,OACrDzB,sBAAsB,CAACK,UAAU,EAAEO,KAAK,EAAEM,qBAAqB,CAAC;IAAA,EACjE;IAAAQ,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAFMI,YAAY,GAAAF,gBAAA;IAAEG,eAAe,GAAAH,gBAAA;EAIpC,OACEhC,KAAA,CAAAoC,aAAA,CAAC/B,UAAU;IACTgC,2BAA2B,EAAE,CAACrB,OAAQ;IACtCsB,yBAAyB,EAAEtB,OAAO,GAAG,MAAM,GAAG,qBAAsB;IACpEa,KAAK,EAAE,CAACU,MAAM,CAACC,SAAS,EAAEX,KAAK;EAAE,GAEjC7B,KAAA,CAAAoC,aAAA,CAAClC,IAAI;IAAC2B,KAAK,EAAEU,MAAM,CAACE;EAAQ,GAC1BzC,KAAA,CAAAoC,aAAA,CAAC5B,kBAAkB,CAACkC,QAAQ;IAC1BC,KAAK,EAAE9B,mBAAmB,IAAIQ,WAAW,KAAK;EAAM,GAEpDrB,KAAA,CAAAoC,aAAA,CAAC7B,mBAAmB,CAACmC,QAAQ;IAC3BC,KAAK,EAAEtB,WAAW,GAAGa,YAAY,GAAGnB,kBAAkB,WAAlBA,kBAAkB,GAAI;EAAE,GAE3Da,QAAQ,CACoB,CACH,CACzB,EACNP,WAAW,GACVrB,KAAA,CAAAoC,aAAA,CAACtC,iBAAiB,CAAC4C,QAAQ;IAACC,KAAK,EAAEjB;EAAW,GAC5C1B,KAAA,CAAAoC,aAAA,CAACrC,sBAAsB,CAAC2C,QAAQ;IAACC,KAAK,EAAEhB;EAAM,GAC5C3B,KAAA,CAAAoC,aAAA,CAAClC,IAAI;IACH0C,QAAQ,EAAG,SAAXA,QAAQA,CAAGC,CAAC,EAAK;MACf,IAAQC,MAAA,GAAWD,CAAC,CAACE,WAAW,CAACC,MAAM,CAA/BF,MAAA;MAERX,eAAe,CAACW,MAAM,CAAC;IACzB,CAAE;IACFjB,KAAK,EAAEP,iBAAiB,GAAGiB,MAAM,CAACU,QAAQ,GAAG;EAAK,GAEjD9B,MAAM,CACF,CACyB,CACP,GAC3B,IAAI,CACG;AAEjB;AAEA,IAAMoB,MAAM,GAAGtC,UAAU,CAACiD,MAAM,CAAC;EAC/BV,SAAS,EAAE;IACTW,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB,CAAC;EAEDX,OAAO,EAAE;IACPU,IAAI,EAAE;EACR,CAAC;EACDF,QAAQ,EAAE;IACRI,QAAQ,EAAE,UAAU;IACpB5B,GAAG,EAAE,CAAC;IACN6B,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}