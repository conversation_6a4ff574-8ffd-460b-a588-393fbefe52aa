{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _excluded = [\"defaultStatus\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { nanoid } from 'nanoid/non-secure';\nimport TabRouter, { TabActions } from \"./TabRouter\";\nexport var DrawerActions = _objectSpread(_objectSpread({}, TabActions), {}, {\n  openDrawer: function openDrawer() {\n    return {\n      type: 'OPEN_DRAWER'\n    };\n  },\n  closeDrawer: function closeDrawer() {\n    return {\n      type: 'CLOSE_DRAWER'\n    };\n  },\n  toggleDrawer: function toggleDrawer() {\n    return {\n      type: 'TOGGLE_DRAWER'\n    };\n  }\n});\nexport default function DrawerRouter(_ref) {\n  var _ref$defaultStatus = _ref.defaultStatus,\n    defaultStatus = _ref$defaultStatus === void 0 ? 'closed' : _ref$defaultStatus,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var router = TabRouter(rest);\n  var isDrawerInHistory = function isDrawerInHistory(state) {\n    var _state$history;\n    return Boolean((_state$history = state.history) === null || _state$history === void 0 ? void 0 : _state$history.some(function (it) {\n      return it.type === 'drawer';\n    }));\n  };\n  var addDrawerToHistory = function addDrawerToHistory(state) {\n    if (isDrawerInHistory(state)) {\n      return state;\n    }\n    return _objectSpread(_objectSpread({}, state), {}, {\n      history: [].concat(_toConsumableArray(state.history), [{\n        type: 'drawer',\n        status: defaultStatus === 'open' ? 'closed' : 'open'\n      }])\n    });\n  };\n  var removeDrawerFromHistory = function removeDrawerFromHistory(state) {\n    if (!isDrawerInHistory(state)) {\n      return state;\n    }\n    return _objectSpread(_objectSpread({}, state), {}, {\n      history: state.history.filter(function (it) {\n        return it.type !== 'drawer';\n      })\n    });\n  };\n  var openDrawer = function openDrawer(state) {\n    if (defaultStatus === 'open') {\n      return removeDrawerFromHistory(state);\n    }\n    return addDrawerToHistory(state);\n  };\n  var closeDrawer = function closeDrawer(state) {\n    if (defaultStatus === 'open') {\n      return addDrawerToHistory(state);\n    }\n    return removeDrawerFromHistory(state);\n  };\n  return _objectSpread(_objectSpread({}, router), {}, {\n    type: 'drawer',\n    getInitialState: function getInitialState(_ref2) {\n      var routeNames = _ref2.routeNames,\n        routeParamList = _ref2.routeParamList,\n        routeGetIdList = _ref2.routeGetIdList;\n      var state = router.getInitialState({\n        routeNames: routeNames,\n        routeParamList: routeParamList,\n        routeGetIdList: routeGetIdList\n      });\n      return _objectSpread(_objectSpread({}, state), {}, {\n        default: defaultStatus,\n        stale: false,\n        type: 'drawer',\n        key: `drawer-${nanoid()}`\n      });\n    },\n    getRehydratedState: function getRehydratedState(partialState, _ref3) {\n      var routeNames = _ref3.routeNames,\n        routeParamList = _ref3.routeParamList,\n        routeGetIdList = _ref3.routeGetIdList;\n      if (partialState.stale === false) {\n        return partialState;\n      }\n      var state = router.getRehydratedState(partialState, {\n        routeNames: routeNames,\n        routeParamList: routeParamList,\n        routeGetIdList: routeGetIdList\n      });\n      if (isDrawerInHistory(partialState)) {\n        state = removeDrawerFromHistory(state);\n        state = addDrawerToHistory(state);\n      }\n      return _objectSpread(_objectSpread({}, state), {}, {\n        default: defaultStatus,\n        type: 'drawer',\n        key: `drawer-${nanoid()}`\n      });\n    },\n    getStateForRouteFocus: function getStateForRouteFocus(state, key) {\n      var result = router.getStateForRouteFocus(state, key);\n      return closeDrawer(result);\n    },\n    getStateForAction: function getStateForAction(state, action, options) {\n      switch (action.type) {\n        case 'OPEN_DRAWER':\n          return openDrawer(state);\n        case 'CLOSE_DRAWER':\n          return closeDrawer(state);\n        case 'TOGGLE_DRAWER':\n          if (isDrawerInHistory(state)) {\n            return removeDrawerFromHistory(state);\n          }\n          return addDrawerToHistory(state);\n        case 'JUMP_TO':\n        case 'NAVIGATE':\n          {\n            var result = router.getStateForAction(state, action, options);\n            if (result != null && result.index !== state.index) {\n              return closeDrawer(result);\n            }\n            return result;\n          }\n        case 'GO_BACK':\n          if (isDrawerInHistory(state)) {\n            return removeDrawerFromHistory(state);\n          }\n          return router.getStateForAction(state, action, options);\n        default:\n          return router.getStateForAction(state, action, options);\n      }\n    },\n    actionCreators: DrawerActions\n  });\n}", "map": {"version": 3, "names": ["nanoid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabActions", "DrawerActions", "_objectSpread", "openDrawer", "type", "closeDrawer", "toggle<PERSON>rawer", "DrawerRouter", "_ref", "_ref$defaultStatus", "defaultStatus", "rest", "_objectWithoutProperties", "_excluded", "router", "isDrawerInHistory", "state", "_state$history", "Boolean", "history", "some", "it", "addDrawerToHistory", "concat", "_toConsumableArray", "status", "removeDrawerFromHistory", "filter", "getInitialState", "_ref2", "routeNames", "routeParamList", "routeGetIdList", "default", "stale", "key", "getRehydratedState", "partialState", "_ref3", "getStateForRouteFocus", "result", "getStateForAction", "action", "options", "index", "actionCreators"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\routers\\src\\DrawerRouter.tsx"], "sourcesContent": ["import { nanoid } from 'nanoid/non-secure';\n\nimport TabRouter, {\n  TabActionHelpers,\n  TabActions,\n  TabActionType,\n  TabNavigationState,\n  TabRouterOptions,\n} from './TabRouter';\nimport type {\n  CommonNavigationAction,\n  ParamListBase,\n  PartialState,\n  Router,\n} from './types';\nexport type DrawerStatus = 'open' | 'closed';\n\nexport type DrawerActionType =\n  | TabActionType\n  | {\n      type: 'OPEN_DRAWER' | 'CLOSE_DRAWER' | 'TOGGLE_DRAWER';\n      source?: string;\n      target?: string;\n    };\n\nexport type DrawerRouterOptions = TabRouterOptions & {\n  defaultStatus?: DrawerStatus;\n};\n\nexport type DrawerNavigationState<ParamList extends ParamListBase> = Omit<\n  TabNavigationState<ParamList>,\n  'type' | 'history'\n> & {\n  /**\n   * Type of the router, in this case, it's drawer.\n   */\n  type: 'drawer';\n  /**\n   * Default status of the drawer.\n   */\n  default: DrawerStatus;\n  /**\n   * List of previously visited route keys and drawer open status.\n   */\n  history: (\n    | { type: 'route'; key: string }\n    | { type: 'drawer'; status: DrawerStatus }\n  )[];\n};\n\nexport type DrawerActionHelpers<ParamList extends ParamListBase> =\n  TabActionHelpers<ParamList> & {\n    /**\n     * Open the drawer sidebar.\n     */\n    openDrawer(): void;\n\n    /**\n     * Close the drawer sidebar.\n     */\n    closeDrawer(): void;\n\n    /**\n     * Open the drawer sidebar if closed, or close if opened.\n     */\n    toggleDrawer(): void;\n  };\n\nexport const DrawerActions = {\n  ...TabActions,\n  openDrawer(): DrawerActionType {\n    return { type: 'OPEN_DRAWER' };\n  },\n  closeDrawer(): DrawerActionType {\n    return { type: 'CLOSE_DRAWER' };\n  },\n  toggleDrawer(): DrawerActionType {\n    return { type: 'TOGGLE_DRAWER' };\n  },\n};\n\nexport default function DrawerRouter({\n  defaultStatus = 'closed',\n  ...rest\n}: DrawerRouterOptions): Router<\n  DrawerNavigationState<ParamListBase>,\n  DrawerActionType | CommonNavigationAction\n> {\n  const router = TabRouter(rest) as unknown as Router<\n    DrawerNavigationState<ParamListBase>,\n    TabActionType | CommonNavigationAction\n  >;\n\n  const isDrawerInHistory = (\n    state:\n      | DrawerNavigationState<ParamListBase>\n      | PartialState<DrawerNavigationState<ParamListBase>>\n  ) => Boolean(state.history?.some((it) => it.type === 'drawer'));\n\n  const addDrawerToHistory = (\n    state: DrawerNavigationState<ParamListBase>\n  ): DrawerNavigationState<ParamListBase> => {\n    if (isDrawerInHistory(state)) {\n      return state;\n    }\n\n    return {\n      ...state,\n      history: [\n        ...state.history,\n        {\n          type: 'drawer',\n          status: defaultStatus === 'open' ? 'closed' : 'open',\n        },\n      ],\n    };\n  };\n\n  const removeDrawerFromHistory = (\n    state: DrawerNavigationState<ParamListBase>\n  ): DrawerNavigationState<ParamListBase> => {\n    if (!isDrawerInHistory(state)) {\n      return state;\n    }\n\n    return {\n      ...state,\n      history: state.history.filter((it) => it.type !== 'drawer'),\n    };\n  };\n\n  const openDrawer = (\n    state: DrawerNavigationState<ParamListBase>\n  ): DrawerNavigationState<ParamListBase> => {\n    if (defaultStatus === 'open') {\n      return removeDrawerFromHistory(state);\n    }\n\n    return addDrawerToHistory(state);\n  };\n\n  const closeDrawer = (\n    state: DrawerNavigationState<ParamListBase>\n  ): DrawerNavigationState<ParamListBase> => {\n    if (defaultStatus === 'open') {\n      return addDrawerToHistory(state);\n    }\n\n    return removeDrawerFromHistory(state);\n  };\n\n  return {\n    ...router,\n\n    type: 'drawer',\n\n    getInitialState({ routeNames, routeParamList, routeGetIdList }) {\n      const state = router.getInitialState({\n        routeNames,\n        routeParamList,\n        routeGetIdList,\n      });\n\n      return {\n        ...state,\n        default: defaultStatus,\n        stale: false,\n        type: 'drawer',\n        key: `drawer-${nanoid()}`,\n      };\n    },\n\n    getRehydratedState(\n      partialState,\n      { routeNames, routeParamList, routeGetIdList }\n    ) {\n      if (partialState.stale === false) {\n        return partialState;\n      }\n\n      let state = router.getRehydratedState(partialState, {\n        routeNames,\n        routeParamList,\n        routeGetIdList,\n      });\n\n      if (isDrawerInHistory(partialState)) {\n        // Re-sync the drawer entry in history to correct it if it was wrong\n        state = removeDrawerFromHistory(state);\n        state = addDrawerToHistory(state);\n      }\n\n      return {\n        ...state,\n        default: defaultStatus,\n        type: 'drawer',\n        key: `drawer-${nanoid()}`,\n      };\n    },\n\n    getStateForRouteFocus(state, key) {\n      const result = router.getStateForRouteFocus(state, key);\n\n      return closeDrawer(result);\n    },\n\n    getStateForAction(state, action, options) {\n      switch (action.type) {\n        case 'OPEN_DRAWER':\n          return openDrawer(state);\n\n        case 'CLOSE_DRAWER':\n          return closeDrawer(state);\n\n        case 'TOGGLE_DRAWER':\n          if (isDrawerInHistory(state)) {\n            return removeDrawerFromHistory(state);\n          }\n\n          return addDrawerToHistory(state);\n\n        case 'JUMP_TO':\n        case 'NAVIGATE': {\n          const result = router.getStateForAction(state, action, options);\n\n          if (result != null && result.index !== state.index) {\n            return closeDrawer(result as DrawerNavigationState<ParamListBase>);\n          }\n\n          return result;\n        }\n\n        case 'GO_BACK':\n          if (isDrawerInHistory(state)) {\n            return removeDrawerFromHistory(state);\n          }\n\n          return router.getStateForAction(state, action, options);\n\n        default:\n          return router.getStateForAction(state, action, options);\n      }\n    },\n\n    actionCreators: DrawerActions,\n  };\n}\n"], "mappings": ";;;;;;AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAE1C,OAAOC,SAAS,IAEdC,UAAU;AAgEZ,OAAO,IAAMC,aAAa,GAAAC,aAAA,CAAAA,aAAA,KACrBF,UAAU;EACbG,UAAU,WAAVA,UAAUA,CAAA,EAAqB;IAC7B,OAAO;MAAEC,IAAI,EAAE;IAAc,CAAC;EAChC,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAAA,EAAqB;IAC9B,OAAO;MAAED,IAAI,EAAE;IAAe,CAAC;EACjC,CAAC;EACDE,YAAY,WAAZA,YAAYA,CAAA,EAAqB;IAC/B,OAAO;MAAEF,IAAI,EAAE;IAAgB,CAAC;EAClC;AAAA,EACD;AAED,eAAe,SAASG,YAAYA,CAAAC,IAAA,EAMlC;EAAA,IAAAC,kBAAA,GAHoBD,IAAA,CAFpBE,aAAa;IAAbA,aAAa,GAAAD,kBAAA,cAAG,QAAQ,GAAAA,kBAAA;IACrBE,IAAA,GAAAC,wBAAA,CACiBJ,IAAA,EAAAK,SAAA;EAIpB,IAAMC,MAAM,GAAGf,SAAS,CAACY,IAAI,CAG5B;EAED,IAAMI,iBAAiB,GACrB,SADIA,iBAAiBA,CACrBC,KAEsD;IAAA,IAAAC,cAAA;IAAA,OACnDC,OAAO,EAAAD,cAAA,GAACD,KAAK,CAACG,OAAO,cAAAF,cAAA,uBAAbA,cAAA,CAAeG,IAAI,CAAE,UAAAC,EAAE;MAAA,OAAKA,EAAE,CAACjB,IAAI,KAAK,QAAQ;IAAA,EAAC,CAAC;EAAA;EAE/D,IAAMkB,kBAAkB,GACtB,SADIA,kBAAkBA,CACtBN,KAA2C,EACF;IACzC,IAAID,iBAAiB,CAACC,KAAK,CAAC,EAAE;MAC5B,OAAOA,KAAK;IACd;IAEA,OAAAd,aAAA,CAAAA,aAAA,KACKc,KAAK;MACRG,OAAO,KAAAI,MAAA,CAAAC,kBAAA,CACFR,KAAK,CAACG,OAAO,IAChB;QACEf,IAAI,EAAE,QAAQ;QACdqB,MAAM,EAAEf,aAAa,KAAK,MAAM,GAAG,QAAQ,GAAG;MAChD,CAAC;IAAA;EAGP,CAAC;EAED,IAAMgB,uBAAuB,GAC3B,SADIA,uBAAuBA,CAC3BV,KAA2C,EACF;IACzC,IAAI,CAACD,iBAAiB,CAACC,KAAK,CAAC,EAAE;MAC7B,OAAOA,KAAK;IACd;IAEA,OAAAd,aAAA,CAAAA,aAAA,KACKc,KAAK;MACRG,OAAO,EAAEH,KAAK,CAACG,OAAO,CAACQ,MAAM,CAAE,UAAAN,EAAE;QAAA,OAAKA,EAAE,CAACjB,IAAI,KAAK,QAAQ;MAAA;IAAA;EAE9D,CAAC;EAED,IAAMD,UAAU,GACd,SADIA,UAAUA,CACda,KAA2C,EACF;IACzC,IAAIN,aAAa,KAAK,MAAM,EAAE;MAC5B,OAAOgB,uBAAuB,CAACV,KAAK,CAAC;IACvC;IAEA,OAAOM,kBAAkB,CAACN,KAAK,CAAC;EAClC,CAAC;EAED,IAAMX,WAAW,GACf,SADIA,WAAWA,CACfW,KAA2C,EACF;IACzC,IAAIN,aAAa,KAAK,MAAM,EAAE;MAC5B,OAAOY,kBAAkB,CAACN,KAAK,CAAC;IAClC;IAEA,OAAOU,uBAAuB,CAACV,KAAK,CAAC;EACvC,CAAC;EAED,OAAAd,aAAA,CAAAA,aAAA,KACKY,MAAM;IAETV,IAAI,EAAE,QAAQ;IAEdwB,eAAe,WAAfA,eAAeA,CAAAC,KAAA,EAAiD;MAAA,IAA9CC,UAAU,GAAkCD,KAAA,CAA5CC,UAAU;QAAEC,cAAc,GAAkBF,KAAA,CAAhCE,cAAc;QAAEC,cAAA,GAAgBH,KAAA,CAAhBG,cAAA;MAC5C,IAAMhB,KAAK,GAAGF,MAAM,CAACc,eAAe,CAAC;QACnCE,UAAU,EAAVA,UAAU;QACVC,cAAc,EAAdA,cAAc;QACdC,cAAA,EAAAA;MACF,CAAC,CAAC;MAEF,OAAA9B,aAAA,CAAAA,aAAA,KACKc,KAAK;QACRiB,OAAO,EAAEvB,aAAa;QACtBwB,KAAK,EAAE,KAAK;QACZ9B,IAAI,EAAE,QAAQ;QACd+B,GAAG,EAAG,UAASrC,MAAM,EAAG;MAAA;IAE5B,CAAC;IAEDsC,kBAAkB,WAAlBA,kBAAkBA,CAChBC,YAAY,EAAAC,KAAA,EAEZ;MAAA,IADER,UAAU,GAAkCQ,KAAA,CAA5CR,UAAU;QAAEC,cAAc,GAAkBO,KAAA,CAAhCP,cAAc;QAAEC,cAAA,GAAgBM,KAAA,CAAhBN,cAAA;MAE9B,IAAIK,YAAY,CAACH,KAAK,KAAK,KAAK,EAAE;QAChC,OAAOG,YAAY;MACrB;MAEA,IAAIrB,KAAK,GAAGF,MAAM,CAACsB,kBAAkB,CAACC,YAAY,EAAE;QAClDP,UAAU,EAAVA,UAAU;QACVC,cAAc,EAAdA,cAAc;QACdC,cAAA,EAAAA;MACF,CAAC,CAAC;MAEF,IAAIjB,iBAAiB,CAACsB,YAAY,CAAC,EAAE;QAEnCrB,KAAK,GAAGU,uBAAuB,CAACV,KAAK,CAAC;QACtCA,KAAK,GAAGM,kBAAkB,CAACN,KAAK,CAAC;MACnC;MAEA,OAAAd,aAAA,CAAAA,aAAA,KACKc,KAAK;QACRiB,OAAO,EAAEvB,aAAa;QACtBN,IAAI,EAAE,QAAQ;QACd+B,GAAG,EAAG,UAASrC,MAAM,EAAG;MAAA;IAE5B,CAAC;IAEDyC,qBAAqB,WAArBA,qBAAqBA,CAACvB,KAAK,EAAEmB,GAAG,EAAE;MAChC,IAAMK,MAAM,GAAG1B,MAAM,CAACyB,qBAAqB,CAACvB,KAAK,EAAEmB,GAAG,CAAC;MAEvD,OAAO9B,WAAW,CAACmC,MAAM,CAAC;IAC5B,CAAC;IAEDC,iBAAiB,WAAjBA,iBAAiBA,CAACzB,KAAK,EAAE0B,MAAM,EAAEC,OAAO,EAAE;MACxC,QAAQD,MAAM,CAACtC,IAAI;QACjB,KAAK,aAAa;UAChB,OAAOD,UAAU,CAACa,KAAK,CAAC;QAE1B,KAAK,cAAc;UACjB,OAAOX,WAAW,CAACW,KAAK,CAAC;QAE3B,KAAK,eAAe;UAClB,IAAID,iBAAiB,CAACC,KAAK,CAAC,EAAE;YAC5B,OAAOU,uBAAuB,CAACV,KAAK,CAAC;UACvC;UAEA,OAAOM,kBAAkB,CAACN,KAAK,CAAC;QAElC,KAAK,SAAS;QACd,KAAK,UAAU;UAAE;YACf,IAAMwB,MAAM,GAAG1B,MAAM,CAAC2B,iBAAiB,CAACzB,KAAK,EAAE0B,MAAM,EAAEC,OAAO,CAAC;YAE/D,IAAIH,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACI,KAAK,KAAK5B,KAAK,CAAC4B,KAAK,EAAE;cAClD,OAAOvC,WAAW,CAACmC,MAAM,CAAyC;YACpE;YAEA,OAAOA,MAAM;UACf;QAEA,KAAK,SAAS;UACZ,IAAIzB,iBAAiB,CAACC,KAAK,CAAC,EAAE;YAC5B,OAAOU,uBAAuB,CAACV,KAAK,CAAC;UACvC;UAEA,OAAOF,MAAM,CAAC2B,iBAAiB,CAACzB,KAAK,EAAE0B,MAAM,EAAEC,OAAO,CAAC;QAEzD;UACE,OAAO7B,MAAM,CAAC2B,iBAAiB,CAACzB,KAAK,EAAE0B,MAAM,EAAEC,OAAO,CAAC;MAAC;IAE9D,CAAC;IAEDE,cAAc,EAAE5C;EAAA;AAEpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}