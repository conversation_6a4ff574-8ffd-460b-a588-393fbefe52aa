{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nvar _checkSerializableWithoutCircularReference = function checkSerializableWithoutCircularReference(o, seen, location) {\n  if (o === undefined || o === null || typeof o === 'boolean' || typeof o === 'number' || typeof o === 'string') {\n    return {\n      serializable: true\n    };\n  }\n  if (Object.prototype.toString.call(o) !== '[object Object]' && !Array.isArray(o)) {\n    return {\n      serializable: false,\n      location: location,\n      reason: typeof o === 'function' ? 'Function' : String(o)\n    };\n  }\n  if (seen.has(o)) {\n    return {\n      serializable: false,\n      reason: 'Circular reference',\n      location: location\n    };\n  }\n  seen.add(o);\n  if (Array.isArray(o)) {\n    for (var i = 0; i < o.length; i++) {\n      var childResult = _checkSerializableWithoutCircularReference(o[i], new Set(seen), [].concat(_toConsumableArray(location), [i]));\n      if (!childResult.serializable) {\n        return childResult;\n      }\n    }\n  } else {\n    for (var key in o) {\n      var _childResult = _checkSerializableWithoutCircularReference(o[key], new Set(seen), [].concat(_toConsumableArray(location), [key]));\n      if (!_childResult.serializable) {\n        return _childResult;\n      }\n    }\n  }\n  return {\n    serializable: true\n  };\n};\nexport default function checkSerializable(o) {\n  return _checkSerializableWithoutCircularReference(o, new Set(), []);\n}", "map": {"version": 3, "names": ["checkSerializableWithoutCircularReference", "o", "seen", "location", "undefined", "serializable", "Object", "prototype", "toString", "call", "Array", "isArray", "reason", "String", "has", "add", "i", "length", "childResult", "Set", "concat", "_toConsumableArray", "key", "checkSerializable"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\core\\src\\checkSerializable.tsx"], "sourcesContent": ["const checkSerializableWithoutCircularReference = (\n  o: { [key: string]: any },\n  seen: Set<any>,\n  location: (string | number)[]\n):\n  | { serializable: true }\n  | {\n      serializable: false;\n      location: (string | number)[];\n      reason: string;\n    } => {\n  if (\n    o === undefined ||\n    o === null ||\n    typeof o === 'boolean' ||\n    typeof o === 'number' ||\n    typeof o === 'string'\n  ) {\n    return { serializable: true };\n  }\n\n  if (\n    Object.prototype.toString.call(o) !== '[object Object]' &&\n    !Array.isArray(o)\n  ) {\n    return {\n      serializable: false,\n      location,\n      reason: typeof o === 'function' ? 'Function' : String(o),\n    };\n  }\n\n  if (seen.has(o)) {\n    return {\n      serializable: false,\n      reason: 'Circular reference',\n      location,\n    };\n  }\n\n  seen.add(o);\n\n  if (Array.isArray(o)) {\n    for (let i = 0; i < o.length; i++) {\n      const childResult = checkSerializableWithoutCircularReference(\n        o[i],\n        new Set<any>(seen),\n        [...location, i]\n      );\n\n      if (!childResult.serializable) {\n        return childResult;\n      }\n    }\n  } else {\n    for (const key in o) {\n      const childResult = checkSerializableWithoutCircularReference(\n        o[key],\n        new Set<any>(seen),\n        [...location, key]\n      );\n\n      if (!childResult.serializable) {\n        return childResult;\n      }\n    }\n  }\n\n  return { serializable: true };\n};\n\nexport default function checkSerializable(o: { [key: string]: any }) {\n  return checkSerializableWithoutCircularReference(o, new Set<any>(), []);\n}\n"], "mappings": ";AAAA,IAAMA,0CAAyC,GAAG,SAA5CA,yCAAyCA,CAC7CC,CAAyB,EACzBC,IAAc,EACdC,QAA6B,EAOtB;EACP,IACEF,CAAC,KAAKG,SAAS,IACfH,CAAC,KAAK,IAAI,IACV,OAAOA,CAAC,KAAK,SAAS,IACtB,OAAOA,CAAC,KAAK,QAAQ,IACrB,OAAOA,CAAC,KAAK,QAAQ,EACrB;IACA,OAAO;MAAEI,YAAY,EAAE;IAAK,CAAC;EAC/B;EAEA,IACEC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,CAAC,CAAC,KAAK,iBAAiB,IACvD,CAACS,KAAK,CAACC,OAAO,CAACV,CAAC,CAAC,EACjB;IACA,OAAO;MACLI,YAAY,EAAE,KAAK;MACnBF,QAAQ,EAARA,QAAQ;MACRS,MAAM,EAAE,OAAOX,CAAC,KAAK,UAAU,GAAG,UAAU,GAAGY,MAAM,CAACZ,CAAC;IACzD,CAAC;EACH;EAEA,IAAIC,IAAI,CAACY,GAAG,CAACb,CAAC,CAAC,EAAE;IACf,OAAO;MACLI,YAAY,EAAE,KAAK;MACnBO,MAAM,EAAE,oBAAoB;MAC5BT,QAAA,EAAAA;IACF,CAAC;EACH;EAEAD,IAAI,CAACa,GAAG,CAACd,CAAC,CAAC;EAEX,IAAIS,KAAK,CAACC,OAAO,CAACV,CAAC,CAAC,EAAE;IACpB,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,CAAC,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;MACjC,IAAME,WAAW,GAAGlB,0CAAyC,CAC3DC,CAAC,CAACe,CAAC,CAAC,EACJ,IAAIG,GAAG,CAAMjB,IAAI,CAAC,KAAAkB,MAAA,CAAAC,kBAAA,CACdlB,QAAQ,IAAEa,CAAC,EAAC,CACjB;MAED,IAAI,CAACE,WAAW,CAACb,YAAY,EAAE;QAC7B,OAAOa,WAAW;MACpB;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAMI,GAAG,IAAIrB,CAAC,EAAE;MACnB,IAAMiB,YAAW,GAAGlB,0CAAyC,CAC3DC,CAAC,CAACqB,GAAG,CAAC,EACN,IAAIH,GAAG,CAAMjB,IAAI,CAAC,KAAAkB,MAAA,CAAAC,kBAAA,CACdlB,QAAQ,IAAEmB,GAAG,EAAC,CACnB;MAED,IAAI,CAACJ,YAAW,CAACb,YAAY,EAAE;QAC7B,OAAOa,YAAW;MACpB;IACF;EACF;EAEA,OAAO;IAAEb,YAAY,EAAE;EAAK,CAAC;AAC/B,CAAC;AAED,eAAe,SAASkB,iBAAiBA,CAACtB,CAAyB,EAAE;EACnE,OAAOD,0CAAyC,CAACC,CAAC,EAAE,IAAIkB,GAAG,EAAO,EAAE,EAAE,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}