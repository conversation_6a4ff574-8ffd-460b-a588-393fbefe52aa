# 🚀 Guide de Démarrage - Projet AquaTrack

## ❌ Erreur Courante
```
Impossible de se connecter au serveur. Vérifiez votre connexion internet et que le serveur est démarré.
```

## ✅ Solution - Démarrage Correct

### 1. **Terminal 1 - Serveur Backend**
```bash
# Naviguer vers le dossier server
cd "C:\Users\<USER>\OneDrive\Desktop\Facturation stage\samle-react-app\server"

# Démarrer le serveur
node server.js
```

**Résultat attendu :**
```
🔄 Démarrage du serveur AquaTrack...
✅ Connexion à la base de données "Facutration" réussie
🚀 Serveur Facturation démarré sur http://localhost:4000
📱 Accessible depuis le réseau sur http://***********:4000
🗄️ Mode: Base de données connectée
📡 Routes disponibles:
  - GET  /api/clients (tous les clients)
  - GET  /api/clients/:id/contracts (contrats du client) ⭐ CRITIQUE
  - GET  /api/clients/qr/:codeqr (recherche client par QR) 📱 NOUVEAU
✅ PRÊT À RECEVOIR LES REQUÊTES !
```

### 2. **Terminal 2 - Application React**
```bash
# Naviguer vers le dossier principal
cd "C:\Users\<USER>\OneDrive\Desktop\Facturation stage\samle-react-app"

# Démarrer l'application
npm start
```

**Résultat attendu :**
```
Compiled successfully!

You can now view samle-react-app in the browser.

  Local:            http://localhost:3000
  On Your Network:  http://***********:3000
```

## 🔧 Vérification des Services

### Backend (Port 4000)
- Ouvrir : http://localhost:4000
- Doit afficher : "Serveur Facutration fonctionnel - Base de données connectée"

### Frontend (Port 3000)
- Ouvrir : http://localhost:3000
- Doit afficher l'application AquaTrack

## 🎯 Test de la Fonctionnalité QR

### Saisie Manuelle (Données Statiques)
1. Aller sur la page "Scanner QR"
2. Cliquer "Saisir manuellement"
3. Tester avec ces codes :
   - **QR001** → Jean Dupont (Tunis)
   - **QR002** → Marie Martin (Sfax)
   - **QR003** → Ahmed Ben Ali (Sousse)

### Scanner Caméra
1. Cliquer "Démarrer le scan"
2. Autoriser l'accès caméra
3. Scanner les codes QR du fichier `test-qr-codes.html`

## 🐛 Dépannage

### Serveur ne démarre pas
```bash
# Vérifier les dépendances
cd server
npm install

# Redémarrer
node server.js
```

### Application React ne démarre pas
```bash
# Vérifier les dépendances
npm install

# Redémarrer
npm start
```

### Erreur de port occupé
```bash
# Tuer les processus sur le port 4000
netstat -ano | findstr :4000
taskkill /PID [PID_NUMBER] /F

# Tuer les processus sur le port 3000
netstat -ano | findstr :3000
taskkill /PID [PID_NUMBER] /F
```

## 📱 URLs Importantes

- **Application** : http://localhost:3000
- **API Backend** : http://localhost:4000
- **Test QR Codes** : Ouvrir `test-qr-codes.html`
- **Page Scanner** : http://localhost:3000/qr-scanner

## ✨ Fonctionnalités Disponibles

- ✅ **Scanner QR avec caméra PC**
- ✅ **Saisie manuelle de codes QR**
- ✅ **Recherche client par QR**
- ✅ **Affichage informations complètes**
- ✅ **Navigation vers Consommation**
- ✅ **Données statiques et dynamiques**

---

**🔥 Suivez ces étapes dans l'ordre pour éviter l'erreur de connexion !**
