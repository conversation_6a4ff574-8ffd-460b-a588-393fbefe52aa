// Serveur de test pour les secteurs
console.log('🧪 SERVEUR TEST SECTEURS - PORT 4002');
console.log('===================================');

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 4002;

// Middleware CORS très permissif pour les tests
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  credentials: true
}));

app.use(express.json());

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toLocaleTimeString()} - ${req.method} ${req.path}`);
  next();
});

// Données de test pour les secteurs (simulant la table secteur)
const secteursTest = [
  { ids: 1, nom: 'Centre-Ville', latitude: 33.5731, longitude: -7.5898 },
  { ids: 2, nom: 'Quartier Industriel', latitude: 33.5831, longitude: -7.5998 },
  { ids: 3, nom: 'Zone Résidentielle Nord', latitude: 33.5931, longitude: -7.6098 },
  { ids: 4, nom: 'Zone Résidentielle Sud', latitude: 33.5631, longitude: -7.5798 },
  { ids: 5, nom: 'Quartier Commercial', latitude: 33.5531, longitude: -7.5698 }
];

// Route principale
app.get('/', (req, res) => {
  console.log('✅ Route / appelée');
  res.json({
    message: 'Serveur test secteurs actif',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString(),
    secteurs_disponibles: secteursTest.length
  });
});

// Route de test pour les secteurs
app.get('/api/secteurs', (req, res) => {
  console.log('🔍 Route /api/secteurs appelée');
  
  try {
    console.log(`✅ Retour de ${secteursTest.length} secteurs`);
    
    const response = {
      success: true,
      data: secteursTest,
      count: secteursTest.length,
      message: `${secteursTest.length} secteur(s) trouvé(s)`,
      timestamp: new Date().toISOString()
    };
    
    console.log('📊 Réponse envoyée:', JSON.stringify(response, null, 2));
    
    res.json(response);
    
  } catch (error) {
    console.error('❌ Erreur dans /api/secteurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: error.message
    });
  }
});

// Route de test pour les clients d'un secteur
app.get('/api/secteurs/:id/clients', (req, res) => {
  const { id } = req.params;
  console.log(`🔍 Route /api/secteurs/${id}/clients appelée`);

  const clientsParSecteur = {
    '1': [ // Centre-Ville
      { idclient: 1, nom: 'Benali', prenom: 'Fatima', adresse: '12 Rue Mohammed V', ville: 'Setrou', tel: '0661234567', email: '<EMAIL>', ids: 1, secteur_nom: 'Centre-Ville' },
      { idclient: 2, nom: 'Alami', prenom: 'Mohammed', adresse: '25 Avenue Hassan II', ville: 'Setrou', tel: '0662345678', email: '<EMAIL>', ids: 1, secteur_nom: 'Centre-Ville' },
      { idclient: 3, nom: 'Tazi', prenom: 'Aicha', adresse: '8 Place de la Liberté', ville: 'Setrou', tel: '0663456789', email: '<EMAIL>', ids: 1, secteur_nom: 'Centre-Ville' }
    ],
    '2': [ // Quartier Industriel
      { idclient: 4, nom: 'Benjelloun', prenom: 'Youssef', adresse: '45 Zone Industrielle', ville: 'Setrou', tel: '0664567890', email: '<EMAIL>', ids: 2, secteur_nom: 'Quartier Industriel' },
      { idclient: 5, nom: 'Lahlou', prenom: 'Khadija', adresse: '67 Rue de l\'Industrie', ville: 'Setrou', tel: '0665678901', email: '<EMAIL>', ids: 2, secteur_nom: 'Quartier Industriel' }
    ],
    '3': [ // Zone Résidentielle Nord
      { idclient: 6, nom: 'Fassi', prenom: 'Omar', adresse: '123 Quartier Nord', ville: 'Setrou', tel: '0666789012', email: '<EMAIL>', ids: 3, secteur_nom: 'Zone Résidentielle Nord' },
      { idclient: 7, nom: 'Chraibi', prenom: 'Salma', adresse: '89 Résidence Al Amal', ville: 'Setrou', tel: '0667890123', email: '<EMAIL>', ids: 3, secteur_nom: 'Zone Résidentielle Nord' },
      { idclient: 8, nom: 'Idrissi', prenom: 'Hassan', adresse: '34 Villa des Roses', ville: 'Setrou', tel: '0668901234', email: '<EMAIL>', ids: 3, secteur_nom: 'Zone Résidentielle Nord' }
    ],
    '4': [ // Zone Résidentielle Sud
      { idclient: 9, nom: 'Berrada', prenom: 'Nadia', adresse: '56 Quartier Sud', ville: 'Setrou', tel: '0669012345', email: '<EMAIL>', ids: 4, secteur_nom: 'Zone Résidentielle Sud' },
      { idclient: 10, nom: 'Kettani', prenom: 'Rachid', adresse: '78 Résidence Al Baraka', ville: 'Setrou', tel: '0660123456', email: '<EMAIL>', ids: 4, secteur_nom: 'Zone Résidentielle Sud' }
    ],
    '5': [ // Quartier Commercial
      { idclient: 11, nom: 'Amrani', prenom: 'Leila', adresse: '90 Centre Commercial', ville: 'Setrou', tel: '0661234567', email: '<EMAIL>', ids: 5, secteur_nom: 'Quartier Commercial' },
      { idclient: 12, nom: 'Zouaki', prenom: 'Karim', adresse: '12 Marché Central', ville: 'Setrou', tel: '0662345678', email: '<EMAIL>', ids: 5, secteur_nom: 'Quartier Commercial' }
    ]
  };

  const clients = clientsParSecteur[id] || [];
  const secteur = secteursTest.find(s => s.ids.toString() === id);

  console.log(`✅ ${clients.length} client(s) trouvé(s) dans le secteur ${id} (${secteur?.nom || 'Inconnu'})`);

  if (clients.length > 0) {
    console.log(`👥 Clients du secteur "${secteur?.nom}":`);
    clients.forEach((client, index) => {
      console.log(`   ${index + 1}. ${client.nom} ${client.prenom} - ${client.adresse}`);
    });
  }

  res.json({
    success: true,
    data: clients,
    count: clients.length,
    secteur: secteur || null,
    message: `${clients.length} client(s) trouvé(s) dans le secteur "${secteur?.nom || id}"`
  });
});

// Route de test pour l'authentification
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Route /api/auth/login appelée');
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'Tech123') {
    console.log('✅ Connexion ré<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      },
      token: 'test-token-123'
    });
  } else {
    console.log('❌ Échec de connexion pour:', email);
    res.status(401).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// Gestion des erreurs
app.use((error, req, res, next) => {
  console.error('❌ Erreur serveur:', error);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: error.message
  });
});

// Démarrage du serveur
console.log('🚀 Tentative de démarrage du serveur test...');

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log('');
  console.log('🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯');
  console.log('🎯                                      🎯');
  console.log('🎯    SERVEUR TEST SECTEURS ACTIF      🎯');
  console.log('🎯           PORT 4002                  🎯');
  console.log('🎯                                      🎯');
  console.log('🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯');
  console.log('');
  console.log('📡 URL: http://localhost:' + PORT);
  console.log('🔍 Test secteurs: http://localhost:' + PORT + '/api/secteurs');
  console.log('');
  console.log('📊 Secteurs de test disponibles:');
  secteursTest.forEach(secteur => {
    console.log(`   ${secteur.ids}. ${secteur.nom} (${secteur.latitude}, ${secteur.longitude})`);
  });
  console.log('');
  console.log('✅ SERVEUR PRÊT POUR LES TESTS');
  console.log('⚠️  Appuyez sur Ctrl+C pour arrêter');
  console.log('');
});

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.log('');
    console.log('❌ ERREUR: Port 4002 déjà utilisé');
    console.log('💡 Solutions:');
    console.log('   1. Arrêtez les autres serveurs: taskkill /f /im node.exe');
    console.log('   2. Ou utilisez un autre port');
  } else {
    console.log('❌ Erreur serveur:', error.message);
  }
});

console.log('📝 Serveur test configuré, en attente de démarrage...');
