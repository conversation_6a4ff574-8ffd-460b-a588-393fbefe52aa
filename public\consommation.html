<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AquaTrack - Consommation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #007AFF 0%, #0056b3 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .status-card {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #28a745;
        }
        
        .status-card h3 {
            color: #155724;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background: rgba(255,255,255,0.7);
            padding: 15px;
            border-radius: 10px;
        }
        
        .info-item strong {
            color: #155724;
            display: block;
            margin-bottom: 5px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
        }
        
        .feature-card h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 40px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            min-width: 200px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007AFF 0%, #0056b3 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
        }
        
        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(23, 162, 184, 0.3);
        }
        
        .api-list {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }
        
        .api-list h4 {
            color: #1976d2;
            margin-bottom: 20px;
            font-size: 1.4em;
        }
        
        .api-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #007AFF;
        }
        
        .api-item code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e83e8c;
        }
        
        .timestamp {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 AquaTrack - Consommation</h1>
            <p>Interface de gestion des consommations d'eau</p>
        </div>
        
        <div class="content">
            <div class="status-card">
                <h3>✅ Serveur Opérationnel sur Port 4002</h3>
                <div class="status-info">
                    <div class="info-item">
                        <strong>URL Actuelle:</strong>
                        http://localhost:4002/consommation
                    </div>
                    <div class="info-item">
                        <strong>Status:</strong>
                        <span style="color: #28a745;">🟢 En ligne</span>
                    </div>
                    <div class="info-item">
                        <strong>Port:</strong>
                        4002
                    </div>
                    <div class="info-item">
                        <strong>Dernière mise à jour:</strong>
                        <span id="timestamp"></span>
                    </div>
                </div>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h4>Application Mobile</h4>
                    <p>Interface React Native complète avec authentification, gestion des secteurs, clients et consommations.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🗺️</div>
                    <h4>Cartes Intégrées</h4>
                    <p>Visualisation des secteurs et clients sur Google Maps avec marqueurs interactifs et géolocalisation.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💧</div>
                    <h4>Gestion Consommations</h4>
                    <p>Saisie et suivi des consommations d'eau avec calculs automatiques et génération de factures.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h4>Gestion Clients</h4>
                    <p>Base de données complète des clients avec informations détaillées et historique des consommations.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔐</div>
                    <h4>Authentification</h4>
                    <p>Système de connexion sécurisé avec rôles utilisateurs (Technicien/Administrateur).</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h4>APIs REST</h4>
                    <p>Interface de programmation complète pour l'intégration avec d'autres systèmes.</p>
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-primary" target="_blank">
                    📱 Ouvrir l'Application Mobile
                </a>
                <a href="http://localhost:4002/api/secteurs" class="btn btn-success" target="_blank">
                    🗺️ Voir les Secteurs (API)
                </a>
                <a href="http://localhost:4002/api/clients" class="btn btn-info" target="_blank">
                    👥 Voir les Clients (API)
                </a>
            </div>
            
            <div class="api-list">
                <h4>🔧 APIs Disponibles</h4>
                <div class="api-item">
                    <strong>Authentification:</strong> <code>POST /api/auth/login</code>
                </div>
                <div class="api-item">
                    <strong>Secteurs:</strong> <code>GET /api/secteurs</code>
                </div>
                <div class="api-item">
                    <strong>Clients:</strong> <code>GET /api/clients</code>
                </div>
                <div class="api-item">
                    <strong>Clients par secteur:</strong> <code>GET /api/secteurs/:id/clients</code>
                </div>
                <div class="api-item">
                    <strong>Consommations:</strong> <code>GET/POST /api/consommations</code>
                </div>
            </div>
            
            <div class="status-card" style="background: #fff3cd; border-color: #ffeaa7; border-left-color: #ffc107;">
                <h3 style="color: #856404;">🔑 Comptes de Test</h3>
                <div class="status-info">
                    <div class="info-item">
                        <strong>Technicien:</strong>
                        <EMAIL> / Tech123
                    </div>
                    <div class="info-item">
                        <strong>Administrateur:</strong>
                        <EMAIL> / Admin123
                    </div>
                </div>
            </div>
            
            <div class="timestamp">
                Page générée le <span id="fullTimestamp"></span>
            </div>
        </div>
    </div>
    
    <script>
        // Mettre à jour les timestamps
        const now = new Date();
        document.getElementById('timestamp').textContent = now.toLocaleTimeString();
        document.getElementById('fullTimestamp').textContent = now.toLocaleString();
        
        // Test de connexion aux APIs
        async function testAPIs() {
            const apis = [
                { name: 'Secteurs', url: '/api/secteurs' },
                { name: 'Clients', url: '/api/clients' }
            ];
            
            for (const api of apis) {
                try {
                    const response = await fetch(api.url);
                    if (response.ok) {
                        console.log(`✅ API ${api.name} OK`);
                    } else {
                        console.log(`❌ API ${api.name} erreur: ${response.status}`);
                    }
                } catch (error) {
                    console.log(`❌ API ${api.name} inaccessible: ${error.message}`);
                }
            }
        }
        
        // Tester les APIs au chargement
        testAPIs();
        
        // Actualiser la page toutes les 30 secondes
        setInterval(() => {
            const now = new Date();
            document.getElementById('timestamp').textContent = now.toLocaleTimeString();
        }, 30000);
    </script>
</body>
</html>
