{"ast": null, "code": "export default function findLastIndex(array, callback) {\n  for (var i = array.length - 1; i >= 0; i--) {\n    if (callback(array[i])) {\n      return i;\n    }\n  }\n  return -1;\n}", "map": {"version": 3, "names": ["findLastIndex", "array", "callback", "i", "length"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\stack\\src\\utils\\findLastIndex.tsx"], "sourcesContent": ["export default function findLastIndex<T>(\n  array: T[],\n  callback: (value: T) => boolean\n) {\n  for (var i = array.length - 1; i >= 0; i--) {\n    if (callback(array[i])) {\n      return i;\n    }\n  }\n\n  return -1;\n}\n"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CACnCC,KAAU,EACVC,QAA+B,EAC/B;EACA,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1C,IAAID,QAAQ,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,EAAE;MACtB,OAAOA,CAAC;IACV;EACF;EAEA,OAAO,CAAC,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}