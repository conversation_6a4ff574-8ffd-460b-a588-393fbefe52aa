"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.withDevServer = exports.withAlias = exports.withOptimizations = void 0;
var withOptimizations_1 = require("./withOptimizations");
Object.defineProperty(exports, "withOptimizations", { enumerable: true, get: function () { return __importDefault(withOptimizations_1).default; } });
var withAlias_1 = require("./withAlias");
Object.defineProperty(exports, "withAlias", { enumerable: true, get: function () { return __importDefault(withAlias_1).default; } });
var withDevServer_1 = require("./withDevServer");
Object.defineProperty(exports, "withDevServer", { enumerable: true, get: function () { return __importDefault(withDevServer_1).default; } });
//# sourceMappingURL=index.js.map