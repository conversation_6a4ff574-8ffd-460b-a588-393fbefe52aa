{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nexport default function Background(_ref) {\n  var style = _ref.style,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useTheme = useTheme(),\n    colors = _useTheme.colors;\n  return React.createElement(View, _extends({}, rest, {\n    style: [{\n      flex: 1,\n      backgroundColor: colors.background\n    }, style]\n  }));\n}", "map": {"version": 3, "names": ["useTheme", "React", "View", "Background", "_ref", "style", "rest", "_objectWithoutProperties", "_excluded", "_useTheme", "colors", "createElement", "_extends", "flex", "backgroundColor", "background"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\elements\\src\\Background.tsx"], "sourcesContent": ["import { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport { View, ViewProps } from 'react-native';\n\ntype Props = ViewProps & {\n  children: React.ReactNode;\n};\n\nexport default function Background({ style, ...rest }: Props) {\n  const { colors } = useTheme();\n\n  return (\n    <View\n      {...rest}\n      style={[{ flex: 1, backgroundColor: colors.background }, style]}\n    />\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAO9B,eAAe,SAASC,UAAUA,CAAAC,IAAA,EAA4B;EAAA,IAAzBC,KAAK,GAAkBD,IAAA,CAAvBC,KAAK;IAAKC,IAAA,GAAAC,wBAAA,CAAaH,IAAA,EAAAI,SAAA;EAC1D,IAAAC,SAAA,GAAmBT,QAAQ,EAAE;IAArBU,MAAA,GAAAD,SAAA,CAAAC,MAAA;EAER,OACET,KAAA,CAAAU,aAAA,CAACT,IAAI,EAAAU,QAAA,KACCN,IAAI;IACRD,KAAK,EAAE,CAAC;MAAEQ,IAAI,EAAE,CAAC;MAAEC,eAAe,EAAEJ,MAAM,CAACK;IAAW,CAAC,EAAEV,KAAK;EAAE,GAChE;AAEN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}