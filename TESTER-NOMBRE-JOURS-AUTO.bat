@echo off
title Test Calcul Automatique Nombre de Jours
color 0A

echo.
echo ========================================
echo    📅 TEST CALCUL NOMBRE DE JOURS
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR...
echo.
start "🧪 Serveur Jours" cmd /k "title SERVEUR NOMBRE JOURS && color 0B && echo ========================================== && echo    🧪 SERVEUR NOMBRE JOURS && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Calcul: Automatique && echo ✅ Périodes: Différence calculée && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 5. DEMARRAGE DE L'APPLICATION...
echo.
cd mobile
start "📱 App Jours" cmd /k "title APPLICATION NOMBRE JOURS && color 0D && echo ========================================== && echo    📱 APPLICATION NOMBRE JOURS && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Calcul: Automatique && echo ✅ Formule: Période actuelle - Dernière période && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 6. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 7. OUVERTURE POUR TEST...
start http://localhost:19006

echo.
echo ========================================
echo    📅 CALCUL AUTOMATIQUE NOMBRE DE JOURS
echo ========================================
echo.
echo 🎯 FONCTIONNALITÉ AJOUTÉE:
echo.
echo ✅ CALCUL AUTOMATIQUE:
echo    - Le champ "📅 Nombre de jours" se calcule automatiquement
echo    - Formule: Période actuelle - Dernière période du client
echo    - Déclenchement: Quand client ET période sont sélectionnés
echo.
echo ✅ INDICATION VISUELLE:
echo    - Champ avec fond vert clair quand auto-calculé
echo    - Bordure verte pour indiquer le calcul automatique
echo    - Message: "✅ Calculé automatiquement : [période] - dernière période"
echo.
echo 📊 DONNÉES DE TEST - DERNIÈRES PÉRIODES:
echo.
echo 👤 Benali Fatima (Client 1):
echo    📅 Dernière période: 2024-11 (Novembre 2024)
echo    📊 Dernière consommation: 45 m³
echo.
echo 👤 Alami Mohammed (Client 2):
echo    📅 Dernière période: 2024-11 (Novembre 2024)
echo    📊 Dernière consommation: 38 m³
echo.
echo 👤 Tazi Aicha (Client 3):
echo    📅 Dernière période: 2024-11 (Novembre 2024)
echo    📊 Dernière consommation: 52 m³
echo.
echo 👤 Benjelloun Youssef (Client 4):
echo    📅 Dernière période: 2024-11 (Novembre 2024)
echo    📊 Dernière consommation: 67 m³
echo.
echo 👤 Lahlou Khadija (Client 5):
echo    📅 Dernière période: 2024-11 (Novembre 2024)
echo    📊 Dernière consommation: 41 m³
echo.
echo 📅 EXEMPLES DE CALCUL:
echo.
echo 🧮 Si période actuelle = 2024-12 (Décembre 2024):
echo    📅 Dernière période = 2024-11 (Novembre 2024)
echo    📊 Calcul: 31 décembre 2024 - 30 novembre 2024 = 31 jours
echo    ✅ Résultat: 31 jours
echo.
echo 🧮 Si période actuelle = 2025-01 (Janvier 2025):
echo    📅 Dernière période = 2024-11 (Novembre 2024)
echo    📊 Calcul: 31 janvier 2025 - 30 novembre 2024 = 62 jours
echo    ✅ Résultat: 62 jours
echo.
echo 🧮 Si période actuelle = 2025-02 (Février 2025):
echo    📅 Dernière période = 2024-11 (Novembre 2024)
echo    📊 Calcul: 28 février 2025 - 30 novembre 2024 = 90 jours
echo    ✅ Résultat: 90 jours
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 TESTEZ LE CALCUL AUTOMATIQUE:
echo.
echo    a) 📍 ÉTAPE 1 - Sélection secteur et client:
echo       - Sélectionnez "Centre-Ville" (secteur)
echo       - Sélectionnez "Benali Fatima" (client)
echo       - Le champ "📅 Nombre de jours" affiche:
echo         "Sélectionnez client et période"
echo.
echo    b) 📅 ÉTAPE 2 - Sélection période:
echo       - Saisissez "2024-12" dans le champ Période
echo       - Attendez la validation (500ms)
echo       - Le champ "📅 Nombre de jours" affiche:
echo         "Calcul automatique en cours..."
echo       - Puis se remplit avec: "31"
echo.
echo    c) ✅ ÉTAPE 3 - Vérification calcul:
echo       - Champ avec fond vert clair
echo       - Bordure verte
echo       - Message: "✅ Calculé automatiquement : 2024-12 - dernière période"
echo       - Valeur: 31 jours
echo.
echo    d) 🔄 ÉTAPE 4 - Test autres périodes:
echo       - Changez pour "2025-01" → Doit afficher "62" jours
echo       - Changez pour "2025-02" → Doit afficher "90" jours
echo       - Changez pour "2025-03" → Doit afficher "121" jours
echo.
echo    e) 👤 ÉTAPE 5 - Test autres clients:
echo       - Changez pour "Alami Mohammed"
echo       - Même calcul car même dernière période (2024-11)
echo       - Changez pour "Benjelloun Youssef"
echo       - Même calcul car même dernière période (2024-11)
echo.
echo    f) 📝 ÉTAPE 6 - Modification manuelle:
echo       - Vous pouvez toujours modifier la valeur manuellement
echo       - Le champ reste éditable
echo       - L'indication visuelle reste présente
echo.
echo 📊 COMPORTEMENTS ATTENDUS:
echo.
echo ✅ AUCUN CLIENT SÉLECTIONNÉ:
echo    📅 Nombre de jours: [Sélectionnez client et période]
echo    🎨 Apparence: Champ normal (blanc)
echo    ℹ️ Message: Aucun
echo.
echo ✅ CLIENT SANS PÉRIODE:
echo    📅 Nombre de jours: [Sélectionnez client et période]
echo    🎨 Apparence: Champ normal (blanc)
echo    ℹ️ Message: Aucun
echo.
echo ✅ CLIENT ET PÉRIODE SÉLECTIONNÉS:
echo    📅 Nombre de jours: [31] (exemple pour 2024-12)
echo    🎨 Apparence: Fond vert clair, bordure verte
echo    ℹ️ Message: "✅ Calculé automatiquement : 2024-12 - dernière période"
echo.
echo ✅ CALCUL EN COURS:
echo    📅 Nombre de jours: [Calcul automatique en cours...]
echo    🎨 Apparence: Champ normal (blanc)
echo    ℹ️ Message: Aucun
echo.
echo 🔧 FONCTIONNALITÉS TESTÉES:
echo.
echo ✅ Calcul automatique basé sur la différence de périodes
echo ✅ Déclenchement lors du changement de client ou période
echo ✅ Indication visuelle du calcul automatique
echo ✅ Possibilité de modification manuelle
echo ✅ Gestion des cas d'erreur (périodes invalides)
echo ✅ Logs détaillés pour debug
echo ✅ Placeholder dynamique informatif
echo.
echo 🧮 FORMULE DE CALCUL:
echo.
echo ✅ Méthode: Différence entre dates de fin de mois
echo ✅ Période actuelle: Dernier jour du mois sélectionné
echo ✅ Dernière période: Dernier jour du mois de la dernière consommation
echo ✅ Calcul: (Date actuelle - Date dernière) en jours
echo ✅ Validation: Résultat positif et ≤ 365 jours
echo.
echo 🎨 APPARENCE DU CHAMP AUTO-CALCULÉ:
echo.
echo ✅ Label: "📅 Nombre de jours"
echo ✅ Fond: Vert clair (#f0fff4)
echo ✅ Bordure: Verte (#28a745), épaisseur 2px
echo ✅ Message: Vert, italique, centré
echo ✅ Icône: ✅ pour indiquer le succès
echo.
echo 🔍 DEBUG ET LOGS:
echo.
echo Pour voir les logs détaillés:
echo 1. Ouvrez la console du navigateur (F12)
echo 2. Onglet "Console"
echo 3. Recherchez les messages:
echo    - "🔄 MISE À JOUR NOMBRE DE JOURS"
echo    - "🔍 CALCUL NOMBRE DE JOURS"
echo    - "📅 Période actuelle sélectionnée"
echo    - "📅 Dernière période du client"
echo    - "🔢 Période actuelle / Dernière période"
echo    - "📅 Date actuelle / Date dernière"
echo    - "📊 NOMBRE DE JOURS CALCULÉ"
echo    - "✅ CALCUL RÉUSSI" ou "❌ ERREUR"
echo.
echo 🌐 URLS DE TEST:
echo    - Application: http://localhost:19006
echo    - Console logs: F12 → Console
echo.
echo ✅ CALCUL AUTOMATIQUE NOMBRE DE JOURS IMPLÉMENTÉ !
echo    Le champ se calcule maintenant automatiquement en fonction
echo    de la différence entre la période actuelle et la dernière
echo    période de consommation du client.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
