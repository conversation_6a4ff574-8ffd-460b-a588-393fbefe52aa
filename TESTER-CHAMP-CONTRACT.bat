@echo off
title Test Champ Contract
color 0A

echo.
echo ========================================
echo    📄 TEST CHAMP CONTRACT
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR...
echo.
start "🧪 Serveur Test" cmd /k "title SERVEUR TEST CONTRACT && color 0B && echo ========================================== && echo    🧪 SERVEUR TEST CONTRACT && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Champ Contract: Troisieme champ && echo ✅ Donnees: Contrats par client && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 5. DEMARRAGE DE L'APPLICATION...
echo.
cd mobile
start "📱 App Contract" cmd /k "title APPLICATION CONTRACT && color 0D && echo ========================================== && echo    📱 APPLICATION CONTRACT && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Champ Contract: Troisieme champ && echo ✅ Filtrage: Secteur → Client → Contract && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 6. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 7. OUVERTURE POUR TEST...
start http://localhost:19006

echo.
echo ========================================
echo    📄 TEST CHAMP CONTRACT
echo ========================================
echo.
echo 🎯 CHAMP CONTRACT AJOUTÉ COMME TROISIÈME CHAMP:
echo.
echo ✅ ORDRE DES CHAMPS:
echo    1. 📍 Secteur * (Premier champ)
echo    2. 👤 Client * (Deuxième champ)
echo    3. 📄 Contrat * (Troisième champ) ← NOUVEAU
echo    4. 📅 Période (Quatrième champ)
echo    5. Consommation Précédente
echo    6. Consommation Actuelle
echo    7. Nombre de jours
echo.
echo 📊 CONTRATS DE TEST PAR CLIENT:
echo.
echo 👤 Benali Fatima (Client 1):
echo    📄 1 contrat: QR001 - Sensus
echo.
echo 👤 Alami Mohammed (Client 2):
echo    📄 1 contrat: QR002 - Itron
echo.
echo 👤 Tazi Aicha (Client 3):
echo    📄 1 contrat: QR003 - Sensus
echo.
echo 👤 Benjelloun Youssef (Client 4):
echo    📄 2 contrats: QR004 - Elster, QR005 - Itron
echo.
echo 👤 Lahlou Khadija (Client 5):
echo    📄 1 contrat: QR006 - Sensus
echo.
echo 👤 Fassi Omar (Client 6):
echo    📄 1 contrat: QR007 - Elster
echo.
echo 👤 Chraibi Salma (Client 7):
echo    📄 1 contrat: QR008 - Itron
echo.
echo 👤 Idrissi Hassan (Client 8):
echo    📄 1 contrat: QR009 - Sensus
echo.
echo 👤 Berrada Nadia (Client 9):
echo    📄 1 contrat: QR010 - Elster
echo.
echo 👤 Kettani Rachid (Client 10):
echo    📄 1 contrat: QR011 - Itron
echo.
echo 👤 Amrani Leila (Client 11):
echo    📄 1 contrat: QR012 - Sensus
echo.
echo 👤 Zouaki Karim (Client 12):
echo    📄 1 contrat: QR013 - Elster
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 TESTEZ LA SÉQUENCE COMPLÈTE:
echo.
echo    a) 📍 ÉTAPE 1 - Secteur:
echo       - Sélectionnez "Centre-Ville"
echo       - Le champ Client doit se mettre à jour
echo       - Le champ Contract doit rester désactivé
echo.
echo    b) 👤 ÉTAPE 2 - Client:
echo       - Sélectionnez "Benali Fatima"
echo       - Le champ Contract doit se mettre à jour automatiquement
echo       - Doit afficher: "Contrat 1 - QR: QR001" (sélection automatique)
echo.
echo    c) 📄 ÉTAPE 3 - Contract:
echo       - Doit être sélectionné automatiquement (1 seul contrat)
echo       - Message: "✅ Contrat unique sélectionné automatiquement"
echo.
echo    d) 🔄 ÉTAPE 4 - Test client avec plusieurs contrats:
echo       - Changez pour "Benjelloun Youssef" (Client 4)
echo       - Le champ Contract doit afficher un menu déroulant
echo       - Options: "1. Contrat 4 - QR: QR004", "2. Contrat 5 - QR: QR005"
echo.
echo 📊 COMPORTEMENTS ATTENDUS:
echo.
echo ✅ AUCUN CLIENT SÉLECTIONNÉ:
echo    📄 Contract: [⬆️ Sélectionnez d'abord un client ci-dessus]
echo.
echo ✅ CLIENT AVEC 1 CONTRAT:
echo    📄 Contract: [Contrat X - QR: QRXXX] (lecture seule)
echo    ✅ Message: "Contrat unique sélectionné automatiquement"
echo.
echo ✅ CLIENT AVEC PLUSIEURS CONTRATS:
echo    📄 Contract: [-- Sélectionner un contrat (2 disponibles) --]
echo                 1. Contrat 4 - QR: QR004
echo                 2. Contrat 5 - QR: QR005
echo.
echo ✅ CLIENT SANS CONTRAT:
echo    📄 Contract: [❌ Aucun contrat pour ce client]
echo    💡 Message: "Contactez l'administrateur"
echo.
echo 🎨 APPARENCE DU CHAMP:
echo.
echo ✅ Fond orange clair (#fff8e1)
echo ✅ Bordure orange (#ff9800)
echo ✅ Label orange: "📄 Contrat *"
echo ✅ Position: Troisième champ
echo ✅ Largeur: Pleine largeur comme les autres champs
echo.
echo 🔧 FONCTIONNALITÉS TESTÉES:
echo.
echo ✅ Affichage conditionnel selon la sélection client
echo ✅ Sélection automatique si contrat unique
echo ✅ Menu déroulant si plusieurs contrats
echo ✅ Message informatif si aucun contrat
echo ✅ Données de test avec QR codes et marques
echo ✅ Intégration dans le flux Secteur → Client → Contract
echo.
echo 🌐 URLS DE TEST:
echo    - Application: http://localhost:19006
echo    - Console logs: F12 → Console (pour voir les contrats chargés)
echo.
echo ✅ CHAMP CONTRACT IMPLÉMENTÉ COMME TROISIÈME CHAMP !
echo    Testez maintenant la sélection de secteur, client,
echo    et vérifiez que le champ Contract apparaît correctement.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
