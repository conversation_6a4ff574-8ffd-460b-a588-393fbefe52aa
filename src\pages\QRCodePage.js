import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Camera } from 'expo-camera';
import { BarCodeScanner } from 'expo-barcode-scanner';

const QRCodePage = ({ navigation }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [clientInfo, setClientInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showClientInfo, setShowClientInfo] = useState(false);

  // Demander la permission de la caméra
  useEffect(() => {
    const getBarCodeScannerPermissions = async () => {
      const { status } = await BarCodeScanner.requestPermissionsAsync();
      setHasPermission(status === 'granted');
    };

    getBarCodeScannerPermissions();
  }, []);

  const handleStartScan = async () => {
    if (hasPermission === null) {
      Alert.alert('Permission', 'Demande de permission en cours...');
      return;
    }
    if (hasPermission === false) {
      Alert.alert('Permission refusée', 'Veuillez autoriser l\'accès à la caméra pour scanner les codes QR');
      return;
    }

    setIsScanning(true);
    setScanned(false);
    setClientInfo(null);
    setShowClientInfo(false);
  };

  const handleBarCodeScanned = async ({ type, data }) => {
    if (scanned) return;

    setScanned(true);
    setLoading(true);

    console.log('🔍 QR Code scanné:', data);

    try {
      // Rechercher le client par code QR
      const response = await fetch(`http://localhost:4000/api/clients/qr/${data}`);
      const result = await response.json();

      if (result.success && result.data) {
        setClientInfo(result.data);
        setShowClientInfo(true);
        setIsScanning(false);

        // Vibrer si disponible
        if (navigator.vibrate) {
          navigator.vibrate([200, 100, 200]);
        }

        Alert.alert(
          '✅ Client trouvé !',
          `${result.data.nom} ${result.data.prenom}`,
          [
            { text: 'Voir détails', onPress: () => setShowClientInfo(true) },
            { text: 'Scanner autre', onPress: () => handleStartScan() }
          ]
        );
      } else {
        Alert.alert(
          '❌ Client non trouvé',
          'Ce code QR ne correspond à aucun client enregistré',
          [
            { text: 'Réessayer', onPress: () => handleStartScan() },
            { text: 'Annuler', onPress: () => setIsScanning(false) }
          ]
        );
      }
    } catch (error) {
      console.error('❌ Erreur lors de la recherche:', error);
      Alert.alert(
        '❌ Erreur',
        'Impossible de rechercher le client. Vérifiez votre connexion.',
        [
          { text: 'Réessayer', onPress: () => handleStartScan() },
          { text: 'Annuler', onPress: () => setIsScanning(false) }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour fermer les modales
  const handleCloseScanner = () => {
    setIsScanning(false);
    setScanned(false);
  };

  const handleCloseClientInfo = () => {
    setShowClientInfo(false);
    setClientInfo(null);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#8b5cf6" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Scanner QR</Text>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => navigation.openDrawer()}
        >
          <Ionicons name="menu" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.scannerContainer}>
          <View style={styles.scannerFrame}>
            {isScanning ? (
              <View style={styles.scanningIndicator}>
                <Ionicons name="scan" size={80} color="#8b5cf6" />
                <Text style={styles.scanningText}>Scan en cours...</Text>
              </View>
            ) : (
              <View style={styles.scannerPlaceholder}>
                <Ionicons name="qr-code-outline" size={120} color="#d1d5db" />
                <Text style={styles.placeholderText}>
                  Appuyez sur "Démarrer le scan" pour commencer
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.scanButton, isScanning && styles.scanButtonDisabled]}
            onPress={handleStartScan}
            disabled={isScanning}
          >
            <Ionicons 
              name={isScanning ? "hourglass-outline" : "scan"} 
              size={24} 
              color="white" 
              style={styles.buttonIcon}
            />
            <Text style={styles.scanButtonText}>
              {isScanning ? 'Scan en cours...' : 'Démarrer le scan'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>Instructions :</Text>
          <Text style={styles.infoText}>
            • Pointez la caméra vers le code QR du client
          </Text>
          <Text style={styles.infoText}>
            • Assurez-vous que le code est bien visible
          </Text>
          <Text style={styles.infoText}>
            • Le scan se fera automatiquement
          </Text>
        </View>
      </View>

      {/* Modal Scanner Caméra */}
      <Modal
        visible={isScanning}
        animationType="slide"
        onRequestClose={handleCloseScanner}
      >
        <View style={styles.cameraContainer}>
          <View style={styles.cameraHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleCloseScanner}
            >
              <Ionicons name="close" size={30} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.cameraTitle}>Scanner le code QR</Text>
          </View>

          {hasPermission === null ? (
            <View style={styles.permissionContainer}>
              <Text style={styles.permissionText}>Demande de permission caméra...</Text>
            </View>
          ) : hasPermission === false ? (
            <View style={styles.permissionContainer}>
              <Text style={styles.permissionText}>Accès caméra refusé</Text>
              <TouchableOpacity
                style={styles.permissionButton}
                onPress={() => BarCodeScanner.requestPermissionsAsync()}
              >
                <Text style={styles.permissionButtonText}>Autoriser</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <BarCodeScanner
              onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
              style={styles.camera}
            >
              <View style={styles.scanOverlay}>
                <View style={styles.scanFrame}>
                  <View style={styles.scanCorner} />
                  <View style={[styles.scanCorner, styles.scanCornerTopRight]} />
                  <View style={[styles.scanCorner, styles.scanCornerBottomLeft]} />
                  <View style={[styles.scanCorner, styles.scanCornerBottomRight]} />
                </View>
                <Text style={styles.scanInstructions}>
                  Placez le code QR dans le cadre
                </Text>
                {loading && (
                  <View style={styles.loadingContainer}>
                    <Text style={styles.loadingText}>Recherche du client...</Text>
                  </View>
                )}
              </View>
            </BarCodeScanner>
          )}
        </View>
      </Modal>

      {/* Modal Informations Client */}
      <Modal
        visible={showClientInfo}
        animationType="slide"
        onRequestClose={handleCloseClientInfo}
      >
        <View style={styles.clientInfoContainer}>
          <View style={styles.clientInfoHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleCloseClientInfo}
            >
              <Ionicons name="close" size={30} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.clientInfoTitle}>Informations Client</Text>
          </View>

          {clientInfo && (
            <ScrollView style={styles.clientInfoContent}>
              <View style={styles.clientCard}>
                <View style={styles.clientHeader}>
                  <Ionicons name="person-circle" size={60} color="#2196F3" />
                  <View style={styles.clientNameContainer}>
                    <Text style={styles.clientName}>
                      {clientInfo.nom} {clientInfo.prenom}
                    </Text>
                    <Text style={styles.clientStatus}>
                      Statut: {clientInfo.statut || 'Actif'}
                    </Text>
                  </View>
                </View>

                <View style={styles.clientDetails}>
                  <View style={styles.detailRow}>
                    <Ionicons name="card-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>ID Client:</Text>
                    <Text style={styles.detailValue}>{clientInfo.idclient}</Text>
                  </View>

                  <View style={styles.detailRow}>
                    <Ionicons name="location-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Adresse:</Text>
                    <Text style={styles.detailValue}>{clientInfo.adresse}</Text>
                  </View>

                  <View style={styles.detailRow}>
                    <Ionicons name="business-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Ville:</Text>
                    <Text style={styles.detailValue}>{clientInfo.ville}</Text>
                  </View>

                  <View style={styles.detailRow}>
                    <Ionicons name="call-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Téléphone:</Text>
                    <Text style={styles.detailValue}>{clientInfo.tel}</Text>
                  </View>

                  <View style={styles.detailRow}>
                    <Ionicons name="mail-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Email:</Text>
                    <Text style={styles.detailValue}>{clientInfo.email}</Text>
                  </View>

                  {clientInfo.secteur && (
                    <View style={styles.detailRow}>
                      <Ionicons name="map-outline" size={20} color="#666" />
                      <Text style={styles.detailLabel}>Secteur:</Text>
                      <Text style={styles.detailValue}>{clientInfo.secteur}</Text>
                    </View>
                  )}
                </View>

                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => {
                      handleCloseClientInfo();
                      navigation.navigate('Consommation', { clientId: clientInfo.idclient });
                    }}
                  >
                    <Ionicons name="water-outline" size={20} color="#fff" />
                    <Text style={styles.actionButtonText}>Consommation</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.actionButton, styles.secondaryButton]}
                    onPress={() => {
                      handleCloseClientInfo();
                      handleStartScan();
                    }}
                  >
                    <Ionicons name="qr-code-outline" size={20} color="#2196F3" />
                    <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                      Scanner autre
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </ScrollView>
          )}
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  menuButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  scannerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  scannerFrame: {
    width: 300,
    height: 300,
    borderWidth: 2,
    borderColor: '#8b5cf6',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  scanningIndicator: {
    alignItems: 'center',
  },
  scanningText: {
    marginTop: 20,
    fontSize: 18,
    color: '#8b5cf6',
    fontWeight: '600',
  },
  scannerPlaceholder: {
    alignItems: 'center',
    padding: 20,
  },
  placeholderText: {
    marginTop: 20,
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  buttonContainer: {
    marginBottom: 30,
  },
  scanButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  scanButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  buttonIcon: {
    marginRight: 12,
  },
  scanButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  infoContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 8,
    lineHeight: 24,
  },
  // Styles pour la caméra
  cameraContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  cameraHeader: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
  },
  closeButton: {
    padding: 8,
  },
  cameraTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center',
    marginRight: 40,
  },
  camera: {
    flex: 1,
  },
  scanOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  scanFrame: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  scanCorner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#fff',
    borderWidth: 3,
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  scanCornerTopRight: {
    top: 0,
    right: 0,
    left: 'auto',
    borderLeftWidth: 0,
    borderRightWidth: 3,
  },
  scanCornerBottomLeft: {
    bottom: 0,
    top: 'auto',
    borderTopWidth: 0,
    borderBottomWidth: 3,
  },
  scanCornerBottomRight: {
    bottom: 0,
    right: 0,
    top: 'auto',
    left: 'auto',
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 3,
    borderBottomWidth: 3,
  },
  scanInstructions: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
    marginTop: 30,
    paddingHorizontal: 20,
  },
  loadingContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: 'rgba(139, 92, 246, 0.9)',
    borderRadius: 10,
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionText: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: '#8b5cf6',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 10,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Styles pour les informations client
  clientInfoContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  clientInfoHeader: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
  },
  clientInfoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center',
    marginRight: 40,
  },
  clientInfoContent: {
    flex: 1,
    padding: 20,
  },
  clientCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  clientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  clientNameContainer: {
    marginLeft: 15,
    flex: 1,
  },
  clientName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 5,
  },
  clientStatus: {
    fontSize: 14,
    color: '#6b7280',
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  clientDetails: {
    marginBottom: 25,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  detailLabel: {
    fontSize: 16,
    color: '#6b7280',
    marginLeft: 10,
    minWidth: 100,
  },
  detailValue: {
    fontSize: 16,
    color: '#1f2937',
    fontWeight: '500',
    flex: 1,
    marginLeft: 10,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 15,
  },
  actionButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    flex: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#2196F3',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButtonText: {
    color: '#2196F3',
  },
});

export default QRCodePage;
