import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import jsQR from 'jsqr';

const QRCodePage = ({ navigation }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [clientInfo, setClientInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showClientInfo, setShowClientInfo] = useState(false);
  const [stream, setStream] = useState(null);
  const [showManualInput, setShowManualInput] = useState(false);
  const [manualQRCode, setManualQRCode] = useState('');

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const scanIntervalRef = useRef(null);

  // Demander la permission de la caméra Web
  useEffect(() => {
    const checkCameraPermission = async () => {
      try {
        // Vérifier si l'API getUserMedia est disponible
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          console.log('❌ API caméra non supportée');
          setHasPermission(false);
          return;
        }

        // Tester l'accès à la caméra
        const testStream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: 'environment' } // Caméra arrière si disponible
        });
        testStream.getTracks().forEach(track => track.stop()); // Arrêter le test
        setHasPermission(true);
        console.log('✅ Permission caméra accordée');
      } catch (error) {
        console.log('❌ Permission caméra refusée:', error);
        setHasPermission(false);
      }
    };

    checkCameraPermission();
  }, []);

  // Nettoyer les ressources au démontage du composant
  useEffect(() => {
    return () => {
      if (scanIntervalRef.current) {
        clearInterval(scanIntervalRef.current);
      }
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [stream]);

  const handleStartScan = async () => {
    if (hasPermission === null) {
      Alert.alert('Permission', 'Vérification de la caméra en cours...');
      return;
    }
    if (hasPermission === false) {
      Alert.alert('Permission refusée', 'Veuillez autoriser l\'accès à la caméra pour scanner les codes QR');
      return;
    }

    try {
      console.log('🎥 Démarrage de la caméra...');

      // Démarrer le flux vidéo
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Caméra arrière si disponible
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      setStream(mediaStream);
      setIsScanning(true);
      setScanned(false);
      setClientInfo(null);
      setShowClientInfo(false);

      // Attendre que la modal soit ouverte puis démarrer la vidéo
      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;
          videoRef.current.play();
          startQRScanning();
        }
      }, 100);

    } catch (error) {
      console.error('❌ Erreur accès caméra:', error);
      Alert.alert(
        'Erreur Caméra',
        'Impossible d\'accéder à la caméra. Vérifiez les permissions.',
        [{ text: 'OK' }]
      );
    }
  };

  // Fonction pour scanner les QR codes
  const startQRScanning = () => {
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current);
    }

    scanIntervalRef.current = setInterval(() => {
      if (videoRef.current && canvasRef.current && !scanned) {
        const video = videoRef.current;
        const canvas = canvasRef.current;
        const context = canvas.getContext('2d');

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          context.drawImage(video, 0, 0, canvas.width, canvas.height);

          const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
          const code = jsQR(imageData.data, imageData.width, imageData.height);

          if (code) {
            console.log('🔍 QR Code détecté:', code.data);
            handleQRCodeDetected(code.data);
          }
        }
      }
    }, 100); // Scanner toutes les 100ms
  };

  // Fonction appelée quand un QR code est détecté
  const handleQRCodeDetected = async (qrData) => {
    if (scanned) return;

    setScanned(true);
    setLoading(true);

    // Arrêter le scanning
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current);
    }

    console.log('🔍 QR Code scanné:', qrData);

    try {
      // Rechercher le client par code QR
      const response = await fetch(`http://localhost:4000/api/clients/qr/${qrData}`);
      const result = await response.json();

      if (result.success && result.data) {
        setClientInfo(result.data);
        setShowClientInfo(true);
        handleCloseScanner(); // Fermer la caméra

        Alert.alert(
          '✅ Client trouvé !',
          `${result.data.nom} ${result.data.prenom}`,
          [
            { text: 'Voir détails', onPress: () => setShowClientInfo(true) },
            { text: 'Scanner autre', onPress: () => handleStartScan() }
          ]
        );
      } else {
        Alert.alert(
          '❌ Client non trouvé',
          'Ce code QR ne correspond à aucun client enregistré',
          [
            { text: 'Réessayer', onPress: () => handleStartScan() },
            { text: 'Annuler', onPress: () => handleCloseScanner() }
          ]
        );
      }
    } catch (error) {
      console.error('❌ Erreur lors de la recherche:', error);
      Alert.alert(
        '❌ Erreur',
        'Impossible de rechercher le client. Vérifiez votre connexion.',
        [
          { text: 'Réessayer', onPress: () => handleStartScan() },
          { text: 'Annuler', onPress: () => handleCloseScanner() }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour fermer les modales
  const handleCloseScanner = () => {
    // Arrêter le scanning
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current);
    }

    // Arrêter le flux vidéo
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }

    setIsScanning(false);
    setScanned(false);
  };

  const handleCloseClientInfo = () => {
    setShowClientInfo(false);
    setClientInfo(null);
  };

  // Fonction pour ouvrir la saisie manuelle
  const handleManualInput = () => {
    setShowManualInput(true);
    setManualQRCode('');
  };

  // Fonction pour fermer la saisie manuelle
  const handleCloseManualInput = () => {
    setShowManualInput(false);
    setManualQRCode('');
  };

  // Données statiques pour la saisie manuelle
  const staticClientsData = {
    'QR001': {
      idclient: 1,
      nom: 'Dupont',
      prenom: 'Jean',
      adresse: '123 Rue de la Paix',
      ville: 'Tunis',
      tel: '71234567',
      email: '<EMAIL>',
      statut: 'Actif',
      secteur: 'Centre Ville',
      codeqr: 'QR001',
      idcontract: 1,
      marquecompteur: 'Sensus',
      numseriecompteur: 'SN123456',
      posx: '36.8075',
      posy: '10.1825'
    },
    'QR002': {
      idclient: 2,
      nom: 'Martin',
      prenom: 'Marie',
      adresse: '456 Avenue Habib Bourguiba',
      ville: 'Sfax',
      tel: '74567890',
      email: '<EMAIL>',
      statut: 'Actif',
      secteur: 'Sfax Nord',
      codeqr: 'QR002',
      idcontract: 2,
      marquecompteur: 'Itron',
      numseriecompteur: 'IT789012',
      posx: '34.7416',
      posy: '10.7613'
    },
    'QR003': {
      idclient: 3,
      nom: 'Ben Ali',
      prenom: 'Ahmed',
      adresse: '789 Rue de la République',
      ville: 'Sousse',
      tel: '73456789',
      email: '<EMAIL>',
      statut: 'Actif',
      secteur: 'Sousse Centre',
      codeqr: 'QR003',
      idcontract: 3,
      marquecompteur: 'Elster',
      numseriecompteur: 'EL345678',
      posx: '35.8256',
      posy: '10.6369'
    }
  };

  // Fonction pour valider le code QR saisi manuellement (données statiques)
  const handleSubmitManualQR = () => {
    if (!manualQRCode.trim()) {
      Alert.alert('Erreur', 'Veuillez saisir un code QR');
      return;
    }

    setLoading(true);
    console.log('🔍 Code QR saisi manuellement:', manualQRCode);

    // Simuler un délai de recherche
    setTimeout(() => {
      const qrCode = manualQRCode.trim().toUpperCase();
      const clientData = staticClientsData[qrCode];

      if (clientData) {
        setClientInfo(clientData);
        setShowClientInfo(true);
        handleCloseManualInput(); // Fermer la saisie manuelle

        Alert.alert(
          '✅ Client trouvé !',
          `${clientData.nom} ${clientData.prenom}`,
          [
            { text: 'Voir détails', onPress: () => setShowClientInfo(true) },
            { text: 'Saisir autre', onPress: () => handleManualInput() }
          ]
        );
      } else {
        Alert.alert(
          '❌ Client non trouvé',
          'Ce code QR ne correspond à aucun client enregistré.\n\nCodes disponibles: QR001, QR002, QR003',
          [
            { text: 'Réessayer', onPress: () => setManualQRCode('') },
            { text: 'Annuler', onPress: () => handleCloseManualInput() }
          ]
        );
      }
      setLoading(false);
    }, 1000); // Délai de 1 seconde pour simuler la recherche
  };

  // Ajouter le CSS pour l'animation de rotation
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#8b5cf6" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Scanner QR</Text>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => navigation.openDrawer()}
        >
          <Ionicons name="menu" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.scannerContainer}>
          <View style={styles.scannerFrame}>
            {isScanning ? (
              <View style={styles.scanningIndicator}>
                <Ionicons name="scan" size={80} color="#8b5cf6" />
                <Text style={styles.scanningText}>Scan en cours...</Text>
              </View>
            ) : (
              <View style={styles.scannerPlaceholder}>
                <Ionicons name="qr-code-outline" size={120} color="#d1d5db" />
                <Text style={styles.placeholderText}>
                  Appuyez sur "Démarrer le scan" pour commencer
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.scanButton, isScanning && styles.scanButtonDisabled]}
            onPress={handleStartScan}
            disabled={isScanning}
          >
            <Ionicons 
              name={isScanning ? "hourglass-outline" : "scan"} 
              size={24} 
              color="white" 
              style={styles.buttonIcon}
            />
            <Text style={styles.scanButtonText}>
              {isScanning ? 'Scan en cours...' : 'Démarrer le scan'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.manualButton}
            onPress={handleManualInput}
          >
            <Ionicons
              name="create-outline"
              size={24}
              color="#8b5cf6"
            />
            <Text style={styles.manualButtonText}>
              Saisir manuellement
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>Instructions :</Text>
          <Text style={styles.infoText}>
            • Pointez la caméra vers le code QR du client
          </Text>
          <Text style={styles.infoText}>
            • Assurez-vous que le code est bien visible
          </Text>
          <Text style={styles.infoText}>
            • Le scan se fera automatiquement
          </Text>
        </View>
      </View>

      {/* Modal Scanner Caméra */}
      <Modal
        visible={isScanning}
        animationType="slide"
        onRequestClose={handleCloseScanner}
      >
        <View style={styles.cameraContainer}>
          <View style={styles.cameraHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleCloseScanner}
            >
              <Ionicons name="close" size={30} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.cameraTitle}>Scanner le code QR</Text>
          </View>

          {hasPermission === null ? (
            <View style={styles.permissionContainer}>
              <Text style={styles.permissionText}>Vérification de la caméra...</Text>
            </View>
          ) : hasPermission === false ? (
            <View style={styles.permissionContainer}>
              <Text style={styles.permissionText}>Accès caméra refusé</Text>
              <Text style={styles.permissionSubText}>
                Veuillez autoriser l'accès à la caméra dans les paramètres de votre navigateur
              </Text>
              <TouchableOpacity
                style={styles.permissionButton}
                onPress={() => window.location.reload()}
              >
                <Text style={styles.permissionButtonText}>Recharger la page</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <div style={{
              position: 'relative',
              width: '100%',
              height: '100%',
              backgroundColor: '#000'
            }}>
              <video
                ref={videoRef}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
                autoPlay
                playsInline
                muted
              />
              <canvas
                ref={canvasRef}
                style={{ display: 'none' }}
              />
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: 'rgba(0,0,0,0.3)'
              }}>
                <div style={{
                  width: '250px',
                  height: '250px',
                  position: 'relative',
                  border: '2px solid #fff',
                  borderRadius: '20px'
                }}>
                  {/* Coins du cadre */}
                  <div style={{
                    position: 'absolute',
                    top: '-2px',
                    left: '-2px',
                    width: '30px',
                    height: '30px',
                    borderTop: '4px solid #8b5cf6',
                    borderLeft: '4px solid #8b5cf6'
                  }} />
                  <div style={{
                    position: 'absolute',
                    top: '-2px',
                    right: '-2px',
                    width: '30px',
                    height: '30px',
                    borderTop: '4px solid #8b5cf6',
                    borderRight: '4px solid #8b5cf6'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '-2px',
                    left: '-2px',
                    width: '30px',
                    height: '30px',
                    borderBottom: '4px solid #8b5cf6',
                    borderLeft: '4px solid #8b5cf6'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '-2px',
                    right: '-2px',
                    width: '30px',
                    height: '30px',
                    borderBottom: '4px solid #8b5cf6',
                    borderRight: '4px solid #8b5cf6'
                  }} />
                </div>
                <p style={{
                  color: '#fff',
                  fontSize: '18px',
                  textAlign: 'center',
                  marginTop: '30px',
                  textShadow: '0 2px 4px rgba(0,0,0,0.5)'
                }}>
                  Placez le code QR dans le cadre
                </p>
                {loading && (
                  <div style={{
                    marginTop: '20px',
                    padding: '15px 25px',
                    backgroundColor: 'rgba(139, 92, 246, 0.9)',
                    borderRadius: '25px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px'
                  }}>
                    <div style={{
                      width: '20px',
                      height: '20px',
                      border: '2px solid #fff',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }} />
                    <span style={{
                      color: '#fff',
                      fontSize: '16px',
                      fontWeight: 'bold'
                    }}>
                      Recherche du client...
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </View>
      </Modal>

      {/* Modal Informations Client */}
      <Modal
        visible={showClientInfo}
        animationType="slide"
        onRequestClose={handleCloseClientInfo}
      >
        <View style={styles.clientInfoContainer}>
          <View style={styles.clientInfoHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleCloseClientInfo}
            >
              <Ionicons name="close" size={30} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.clientInfoTitle}>Informations Client</Text>
          </View>

          {clientInfo && (
            <ScrollView style={styles.clientInfoContent}>
              <View style={styles.clientCard}>
                <View style={styles.clientHeader}>
                  <Ionicons name="person-circle" size={60} color="#2196F3" />
                  <View style={styles.clientNameContainer}>
                    <Text style={styles.clientName}>
                      {clientInfo.nom} {clientInfo.prenom}
                    </Text>
                    <Text style={styles.clientStatus}>
                      Statut: {clientInfo.statut || 'Actif'}
                    </Text>
                  </View>
                </View>

                <View style={styles.clientDetails}>
                  <View style={styles.detailRow}>
                    <Ionicons name="card-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>ID Client:</Text>
                    <Text style={styles.detailValue}>{clientInfo.idclient}</Text>
                  </View>

                  <View style={styles.detailRow}>
                    <Ionicons name="location-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Adresse:</Text>
                    <Text style={styles.detailValue}>{clientInfo.adresse}</Text>
                  </View>

                  <View style={styles.detailRow}>
                    <Ionicons name="business-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Ville:</Text>
                    <Text style={styles.detailValue}>{clientInfo.ville}</Text>
                  </View>

                  <View style={styles.detailRow}>
                    <Ionicons name="call-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Téléphone:</Text>
                    <Text style={styles.detailValue}>{clientInfo.tel}</Text>
                  </View>

                  <View style={styles.detailRow}>
                    <Ionicons name="mail-outline" size={20} color="#666" />
                    <Text style={styles.detailLabel}>Email:</Text>
                    <Text style={styles.detailValue}>{clientInfo.email}</Text>
                  </View>

                  {clientInfo.secteur && (
                    <View style={styles.detailRow}>
                      <Ionicons name="map-outline" size={20} color="#666" />
                      <Text style={styles.detailLabel}>Secteur:</Text>
                      <Text style={styles.detailValue}>{clientInfo.secteur}</Text>
                    </View>
                  )}
                </View>

                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => {
                      handleCloseClientInfo();
                      navigation.navigate('Consommation', { clientId: clientInfo.idclient });
                    }}
                  >
                    <Ionicons name="water-outline" size={20} color="#fff" />
                    <Text style={styles.actionButtonText}>Consommation</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.actionButton, styles.secondaryButton]}
                    onPress={() => {
                      handleCloseClientInfo();
                      handleStartScan();
                    }}
                  >
                    <Ionicons name="qr-code-outline" size={20} color="#2196F3" />
                    <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                      Scanner autre
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </ScrollView>
          )}
        </View>
      </Modal>

      {/* Modal Saisie Manuelle */}
      <Modal
        visible={showManualInput}
        animationType="slide"
        onRequestClose={handleCloseManualInput}
      >
        <View style={styles.manualInputContainer}>
          <View style={styles.manualInputHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleCloseManualInput}
            >
              <Ionicons name="close" size={30} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.manualInputTitle}>Saisir Code QR</Text>
          </View>

          <View style={styles.manualInputContent}>
            <View style={styles.inputCard}>
              <View style={styles.inputHeader}>
                <Ionicons name="qr-code-outline" size={60} color="#8b5cf6" />
                <Text style={styles.inputTitle}>
                  Saisie manuelle du code QR
                </Text>
                <Text style={styles.inputSubtitle}>
                  Entrez le code QR du client pour rechercher ses informations
                </Text>
              </View>

              <View style={styles.inputSection}>
                <Text style={styles.inputLabel}>Code QR :</Text>
                <View style={styles.inputContainer}>
                  <Ionicons name="barcode-outline" size={20} color="#666" />
                  <input
                    type="text"
                    value={manualQRCode}
                    onChange={(e) => setManualQRCode(e.target.value)}
                    placeholder="Ex: QR001, QR002..."
                    style={{
                      flex: 1,
                      marginLeft: '10px',
                      padding: '12px',
                      border: 'none',
                      outline: 'none',
                      fontSize: '16px',
                      backgroundColor: 'transparent'
                    }}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleSubmitManualQR();
                      }
                    }}
                  />
                </View>
              </View>

              <View style={styles.manualActionButtons}>
                <TouchableOpacity
                  style={styles.submitButton}
                  onPress={handleSubmitManualQR}
                  disabled={loading || !manualQRCode.trim()}
                >
                  {loading ? (
                    <div style={{
                      width: '20px',
                      height: '20px',
                      border: '2px solid #fff',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }} />
                  ) : (
                    <Ionicons name="search-outline" size={20} color="#fff" />
                  )}
                  <Text style={styles.submitButtonText}>
                    {loading ? 'Recherche...' : 'Rechercher'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={handleCloseManualInput}
                >
                  <Ionicons name="close-outline" size={20} color="#666" />
                  <Text style={styles.cancelButtonText}>Annuler</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.exampleSection}>
                <Text style={styles.exampleTitle}>Codes QR disponibles :</Text>
                <Text style={styles.exampleText}>• QR001 → Jean Dupont (Tunis)</Text>
                <Text style={styles.exampleText}>• QR002 → Marie Martin (Sfax)</Text>
                <Text style={styles.exampleText}>• QR003 → Ahmed Ben Ali (Sousse)</Text>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  menuButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  scannerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  scannerFrame: {
    width: 300,
    height: 300,
    borderWidth: 2,
    borderColor: '#8b5cf6',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  scanningIndicator: {
    alignItems: 'center',
  },
  scanningText: {
    marginTop: 20,
    fontSize: 18,
    color: '#8b5cf6',
    fontWeight: '600',
  },
  scannerPlaceholder: {
    alignItems: 'center',
    padding: 20,
  },
  placeholderText: {
    marginTop: 20,
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  buttonContainer: {
    marginBottom: 30,
  },
  scanButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  scanButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  buttonIcon: {
    marginRight: 12,
  },
  scanButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  infoContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 8,
    lineHeight: 24,
  },
  // Styles pour la caméra
  cameraContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  cameraHeader: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
  },
  closeButton: {
    padding: 8,
  },
  cameraTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center',
    marginRight: 40,
  },
  camera: {
    flex: 1,
  },
  scanOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  scanFrame: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  scanCorner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#fff',
    borderWidth: 3,
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  scanCornerTopRight: {
    top: 0,
    right: 0,
    left: 'auto',
    borderLeftWidth: 0,
    borderRightWidth: 3,
  },
  scanCornerBottomLeft: {
    bottom: 0,
    top: 'auto',
    borderTopWidth: 0,
    borderBottomWidth: 3,
  },
  scanCornerBottomRight: {
    bottom: 0,
    right: 0,
    top: 'auto',
    left: 'auto',
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 3,
    borderBottomWidth: 3,
  },
  scanInstructions: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
    marginTop: 30,
    paddingHorizontal: 20,
  },
  loadingContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: 'rgba(139, 92, 246, 0.9)',
    borderRadius: 10,
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionText: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: '#8b5cf6',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 10,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  permissionSubText: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  cameraViewContainer: {
    flex: 1,
    position: 'relative',
  },
  videoElement: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
  hiddenCanvas: {
    display: 'none',
  },
  // Styles pour les informations client
  clientInfoContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  clientInfoHeader: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
  },
  clientInfoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center',
    marginRight: 40,
  },
  clientInfoContent: {
    flex: 1,
    padding: 20,
  },
  clientCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  clientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  clientNameContainer: {
    marginLeft: 15,
    flex: 1,
  },
  clientName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 5,
  },
  clientStatus: {
    fontSize: 14,
    color: '#6b7280',
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  clientDetails: {
    marginBottom: 25,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  detailLabel: {
    fontSize: 16,
    color: '#6b7280',
    marginLeft: 10,
    minWidth: 100,
  },
  detailValue: {
    fontSize: 16,
    color: '#1f2937',
    fontWeight: '500',
    flex: 1,
    marginLeft: 10,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 15,
  },
  actionButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    flex: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#2196F3',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButtonText: {
    color: '#2196F3',
  },
  // Styles pour le bouton saisie manuelle
  manualButton: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#8b5cf6',
    marginTop: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  manualButtonText: {
    color: '#8b5cf6',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  // Styles pour la modal de saisie manuelle
  manualInputContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  manualInputHeader: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
  },
  manualInputTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center',
    marginRight: 40,
  },
  manualInputContent: {
    flex: 1,
    padding: 20,
  },
  inputCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 25,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  inputHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  inputTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 15,
    marginBottom: 8,
    textAlign: 'center',
  },
  inputSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  inputSection: {
    marginBottom: 30,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 10,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  manualActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 15,
    marginBottom: 25,
  },
  submitButton: {
    backgroundColor: '#8b5cf6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 25,
    borderRadius: 12,
    flex: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  cancelButton: {
    backgroundColor: '#f3f4f6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 25,
    borderRadius: 12,
    flex: 1,
  },
  cancelButtonText: {
    color: '#6b7280',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  exampleSection: {
    backgroundColor: '#f0f9ff',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#3b82f6',
  },
  exampleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 8,
  },
  exampleText: {
    fontSize: 14,
    color: '#1e40af',
    marginBottom: 4,
  },
});

export default QRCodePage;
