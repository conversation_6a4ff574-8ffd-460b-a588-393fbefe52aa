@echo off
title Test Consommation Précédente Automatique
color 0A

echo.
echo ========================================
echo    📊 TEST CONSOMMATION PRÉCÉDENTE
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR...
echo.
start "🧪 Serveur Consommation" cmd /k "title SERVEUR CONSOMMATION && color 0B && echo ========================================== && echo    🧪 SERVEUR CONSOMMATION && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Consommation: Auto-remplissage && echo ✅ Dernières données: Disponibles && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 5. DEMARRAGE DE L'APPLICATION...
echo.
cd mobile
start "📱 App Consommation" cmd /k "title APPLICATION CONSOMMATION && color 0D && echo ========================================== && echo    📱 APPLICATION CONSOMMATION && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Consommation: Auto-remplissage && echo ✅ Dernières valeurs: Récupérées && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 6. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 7. OUVERTURE POUR TEST...
start http://localhost:19006

echo.
echo ========================================
echo    📊 CONSOMMATION PRÉCÉDENTE AUTOMATIQUE
echo ========================================
echo.
echo 🎯 FONCTIONNALITÉ AJOUTÉE:
echo.
echo ✅ AUTO-REMPLISSAGE:
echo    - Le champ "📊 Consommation Précédente (m³)" se remplit automatiquement
echo    - Récupère la dernière consommation du client sélectionné
echo    - Évite la ressaisie manuelle pour le technicien
echo.
echo ✅ INDICATION VISUELLE:
echo    - Champ avec fond vert clair quand auto-rempli
echo    - Bordure verte pour indiquer le remplissage automatique
echo    - Message: "✅ Dernière consommation récupérée automatiquement"
echo.
echo 📊 DERNIÈRES CONSOMMATIONS PAR CLIENT:
echo.
echo 👤 Benali Fatima (Client 1):
echo    📊 Dernière consommation: 45 m³ (Novembre 2024)
echo    📅 Date: 15/11/2024
echo.
echo 👤 Alami Mohammed (Client 2):
echo    📊 Dernière consommation: 38 m³ (Novembre 2024)
echo    📅 Date: 18/11/2024
echo.
echo 👤 Tazi Aicha (Client 3):
echo    📊 Dernière consommation: 52 m³ (Novembre 2024)
echo    📅 Date: 20/11/2024
echo.
echo 👤 Benjelloun Youssef (Client 4):
echo    📊 Dernière consommation: 67 m³ (Novembre 2024)
echo    📅 Date: 12/11/2024
echo.
echo 👤 Lahlou Khadija (Client 5):
echo    📊 Dernière consommation: 41 m³ (Novembre 2024)
echo    📅 Date: 25/11/2024
echo.
echo 👤 Fassi Omar (Client 6):
echo    📊 Dernière consommation: 55 m³ (Novembre 2024)
echo    📅 Date: 10/11/2024
echo.
echo 👤 Chraibi Salma (Client 7):
echo    📊 Dernière consommation: 33 m³ (Novembre 2024)
echo    📅 Date: 22/11/2024
echo.
echo 👤 Idrissi Hassan (Client 8):
echo    📊 Dernière consommation: 48 m³ (Novembre 2024)
echo    📅 Date: 14/11/2024
echo.
echo 👤 Berrada Nadia (Client 9):
echo    📊 Dernière consommation: 39 m³ (Novembre 2024)
echo    📅 Date: 28/11/2024
echo.
echo 👤 Kettani Rachid (Client 10):
echo    📊 Dernière consommation: 44 m³ (Novembre 2024)
echo    📅 Date: 16/11/2024
echo.
echo 👤 Amrani Leila (Client 11):
echo    📊 Dernière consommation: 51 m³ (Novembre 2024)
echo    📅 Date: 19/11/2024
echo.
echo 👤 Zouaki Karim (Client 12):
echo    📊 Dernière consommation: 36 m³ (Novembre 2024)
echo    📅 Date: 21/11/2024
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 TESTEZ L'AUTO-REMPLISSAGE:
echo.
echo    a) 📍 ÉTAPE 1 - Sélection secteur:
echo       - Sélectionnez "Centre-Ville"
echo       - Le champ "📊 Consommation Précédente" affiche:
echo         "Sélectionnez d'abord un client"
echo.
echo    b) 👤 ÉTAPE 2 - Sélection client:
echo       - Sélectionnez "Benali Fatima"
echo       - Le champ "📊 Consommation Précédente" affiche:
echo         "Chargement automatique..."
echo       - Puis se remplit avec: "45"
echo.
echo    c) ✅ ÉTAPE 3 - Vérification auto-remplissage:
echo       - Champ avec fond vert clair
echo       - Bordure verte
echo       - Message: "✅ Dernière consommation récupérée automatiquement"
echo       - Valeur: 45 m³
echo.
echo    d) 🔄 ÉTAPE 4 - Test autre client:
echo       - Changez pour "Alami Mohammed"
echo       - Le champ se met à jour automatiquement: "38"
echo       - Changez pour "Benjelloun Youssef"
echo       - Le champ se met à jour automatiquement: "67"
echo.
echo    e) 📝 ÉTAPE 5 - Modification manuelle:
echo       - Vous pouvez toujours modifier la valeur manuellement
echo       - Le champ reste éditable
echo       - L'indication visuelle reste présente
echo.
echo 📊 COMPORTEMENTS ATTENDUS:
echo.
echo ✅ AUCUN CLIENT SÉLECTIONNÉ:
echo    📊 Consommation Précédente: [Sélectionnez d'abord un client]
echo    🎨 Apparence: Champ normal (blanc)
echo    ℹ️ Message: Aucun
echo.
echo ✅ CLIENT EN COURS DE SÉLECTION:
echo    📊 Consommation Précédente: [Chargement automatique...]
echo    🎨 Apparence: Champ normal (blanc)
echo    ℹ️ Message: Aucun
echo.
echo ✅ CLIENT AVEC DERNIÈRE CONSOMMATION:
echo    📊 Consommation Précédente: [45] (exemple Benali Fatima)
echo    🎨 Apparence: Fond vert clair, bordure verte
echo    ℹ️ Message: "✅ Dernière consommation récupérée automatiquement"
echo.
echo ✅ CLIENT SANS HISTORIQUE:
echo    📊 Consommation Précédente: [0]
echo    🎨 Apparence: Champ normal (blanc)
echo    ℹ️ Message: Aucun
echo.
echo 🔧 FONCTIONNALITÉS TESTÉES:
echo.
echo ✅ Récupération automatique de la dernière consommation
echo ✅ Mise à jour du champ lors du changement de client
echo ✅ Indication visuelle du remplissage automatique
echo ✅ Possibilité de modification manuelle
echo ✅ Réinitialisation quand aucun client sélectionné
echo ✅ Gestion des clients sans historique
echo ✅ Logs détaillés dans la console pour debug
echo.
echo 🎨 APPARENCE DU CHAMP AUTO-REMPLI:
echo.
echo ✅ Label: "📊 Consommation Précédente (m³)"
echo ✅ Fond: Vert clair (#f0fff4)
echo ✅ Bordure: Verte (#28a745), épaisseur 2px
echo ✅ Message: Vert, italique, centré
echo ✅ Icône: ✅ pour indiquer le succès
echo.
echo 🔍 DEBUG ET LOGS:
echo.
echo Pour voir les logs détaillés:
echo 1. Ouvrez la console du navigateur (F12)
echo 2. Onglet "Console"
echo 3. Recherchez les messages:
echo    - "🔍 DEBUT fetchDerniereConsommation"
echo    - "📊 Dernière consommation de test disponible"
echo    - "✅ Utilisation des données de test"
echo    - "🎯 CONSOMMATION PRÉCÉDENTE AUTOMATIQUE"
echo.
echo 🌐 URLS DE TEST:
echo    - Application: http://localhost:19006
echo    - Console logs: F12 → Console
echo.
echo ✅ CONSOMMATION PRÉCÉDENTE AUTOMATIQUE IMPLÉMENTÉE !
echo    Le champ se remplit maintenant automatiquement avec
echo    la dernière consommation du client sélectionné.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
