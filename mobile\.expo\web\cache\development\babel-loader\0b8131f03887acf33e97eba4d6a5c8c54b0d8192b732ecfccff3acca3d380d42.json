{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport { PermissionStatus, UnavailabilityError } from 'expo-modules-core';\nfunction getUserMedia(constraints) {\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    return navigator.mediaDevices.getUserMedia(constraints);\n  }\n  var getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || function () {\n    var error = new Error('Permission unimplemented');\n    error.code = 0;\n    error.name = 'NotAllowedError';\n    throw error;\n  };\n  return new Promise(function (resolve, reject) {\n    getUserMedia.call(navigator, constraints, resolve, reject);\n  });\n}\nfunction handleGetUserMediaError(_ref) {\n  var message = _ref.message;\n  if (message === 'Permission dismissed') {\n    return {\n      status: PermissionStatus.UNDETERMINED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: false\n    };\n  } else {\n    return {\n      status: PermissionStatus.DENIED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: false\n    };\n  }\n}\nfunction handleRequestPermissionsAsync() {\n  return _handleRequestPermissionsAsync.apply(this, arguments);\n}\nfunction _handleRequestPermissionsAsync() {\n  _handleRequestPermissionsAsync = _asyncToGenerator(function* () {\n    try {\n      yield getUserMedia({\n        video: true\n      });\n      return {\n        status: PermissionStatus.GRANTED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: true\n      };\n    } catch (_ref2) {\n      var message = _ref2.message;\n      return handleGetUserMediaError({\n        message: message\n      });\n    }\n  });\n  return _handleRequestPermissionsAsync.apply(this, arguments);\n}\nfunction handlePermissionsQueryAsync() {\n  return _handlePermissionsQueryAsync.apply(this, arguments);\n}\nfunction _handlePermissionsQueryAsync() {\n  _handlePermissionsQueryAsync = _asyncToGenerator(function* () {\n    var _navigator, _navigator$permission;\n    if (!((_navigator = navigator) != null && (_navigator$permission = _navigator.permissions) != null && _navigator$permission.query)) {\n      throw new UnavailabilityError('expo-barcode-scanner', 'navigator.permissions API is not available');\n    }\n    var _yield$navigator$perm = yield navigator.permissions.query({\n        name: 'camera'\n      }),\n      state = _yield$navigator$perm.state;\n    switch (state) {\n      case 'prompt':\n        return {\n          status: PermissionStatus.UNDETERMINED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: false\n        };\n      case 'granted':\n        return {\n          status: PermissionStatus.GRANTED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: true\n        };\n      case 'denied':\n        return {\n          status: PermissionStatus.DENIED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: false\n        };\n    }\n  });\n  return _handlePermissionsQueryAsync.apply(this, arguments);\n}\nexport default {\n  get name() {\n    return 'ExpoBarCodeScannerModule';\n  },\n  get BarCodeType() {\n    return {\n      code39mod43: 'code39mod43',\n      code138: 'code138',\n      interleaved2of5: 'interleaved2of5',\n      aztec: 'aztec',\n      ean13: 'ean13',\n      ean8: 'ean8',\n      qr: 'qr',\n      pdf417: 'pdf417',\n      upc_e: 'upc_e',\n      datamatrix: 'datamatrix',\n      code39: 'code39',\n      code93: 'code93',\n      itf14: 'itf14',\n      codabar: 'codabar',\n      code128: 'code128',\n      upc_a: 'upc_a'\n    };\n  },\n  get Type() {\n    return {\n      front: 'front',\n      back: 'back'\n    };\n  },\n  requestPermissionsAsync: function () {\n    var _requestPermissionsAsync = _asyncToGenerator(function* () {\n      return handleRequestPermissionsAsync();\n    });\n    function requestPermissionsAsync() {\n      return _requestPermissionsAsync.apply(this, arguments);\n    }\n    return requestPermissionsAsync;\n  }(),\n  getPermissionsAsync: function () {\n    var _getPermissionsAsync = _asyncToGenerator(function* () {\n      return handlePermissionsQueryAsync();\n    });\n    function getPermissionsAsync() {\n      return _getPermissionsAsync.apply(this, arguments);\n    }\n    return getPermissionsAsync;\n  }()\n};", "map": {"version": 3, "names": ["PermissionStatus", "UnavailabilityError", "getUserMedia", "constraints", "navigator", "mediaDevices", "webkitGetUserMedia", "mozGetUserMedia", "error", "Error", "code", "name", "Promise", "resolve", "reject", "call", "handleGetUserMediaError", "_ref", "message", "status", "UNDETERMINED", "expires", "canAskAgain", "granted", "DENIED", "handleRequestPermissionsAsync", "_handleRequestPermissionsAsync", "apply", "arguments", "_asyncToGenerator", "video", "GRANTED", "_ref2", "handlePermissionsQueryAsync", "_handlePermissionsQueryAsync", "_navigator", "_navigator$permission", "permissions", "query", "_yield$navigator$perm", "state", "BarCodeType", "code39mod43", "code138", "interleaved2of5", "aztec", "ean13", "ean8", "qr", "pdf417", "upc_e", "datamatrix", "code39", "code93", "itf14", "codabar", "code128", "upc_a", "Type", "front", "back", "requestPermissionsAsync", "_requestPermissionsAsync", "getPermissionsAsync", "_getPermissionsAsync"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\expo-barcode-scanner\\src\\ExpoBarCodeScannerModule.web.ts"], "sourcesContent": ["import { PermissionResponse, PermissionStatus, UnavailabilityError } from 'expo-modules-core';\n\nfunction getUserMedia(constraints: MediaStreamConstraints): Promise<MediaStream> {\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    return navigator.mediaDevices.getUserMedia(constraints);\n  }\n\n  // Some browsers partially implement mediaDevices. We can't just assign an object\n  // with getUserMedia as it would overwrite existing properties.\n  // Here, we will just add the getUserMedia property if it's missing.\n\n  // First get ahold of the legacy getUserMedia, if present\n  const getUserMedia =\n    // TODO: this method is deprecated, migrate to https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n    navigator.getUserMedia ||\n    navigator.webkitGetUserMedia ||\n    navigator.mozGetUserMedia ||\n    function () {\n      const error: any = new Error('Permission unimplemented');\n      error.code = 0;\n      error.name = 'NotAllowedError';\n      throw error;\n    };\n\n  return new Promise((resolve, reject) => {\n    getUserMedia.call(navigator, constraints, resolve, reject);\n  });\n}\n\nfunction handleGetUserMediaError({ message }: { message: string }): PermissionResponse {\n  // name: NotAllowedError\n  // code: 0\n  if (message === 'Permission dismissed') {\n    return {\n      status: PermissionStatus.UNDETERMINED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: false,\n    };\n  } else {\n    return {\n      status: PermissionStatus.DENIED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: false,\n    };\n  }\n}\n\nasync function handleRequestPermissionsAsync(): Promise<PermissionResponse> {\n  try {\n    await getUserMedia({\n      video: true,\n    });\n    return {\n      status: PermissionStatus.GRANTED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: true,\n    };\n  } catch ({ message }) {\n    return handleGetUserMediaError({ message });\n  }\n}\n\nasync function handlePermissionsQueryAsync(): Promise<PermissionResponse> {\n  if (!navigator?.permissions?.query) {\n    throw new UnavailabilityError(\n      'expo-barcode-scanner',\n      'navigator.permissions API is not available'\n    );\n  }\n\n  const { state } = await navigator.permissions.query({ name: 'camera' });\n  switch (state) {\n    case 'prompt':\n      return {\n        status: PermissionStatus.UNDETERMINED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n    case 'granted':\n      return {\n        status: PermissionStatus.GRANTED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: true,\n      };\n    case 'denied':\n      return {\n        status: PermissionStatus.DENIED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n  }\n}\n\nexport default {\n  get name(): string {\n    return 'ExpoBarCodeScannerModule';\n  },\n  get BarCodeType() {\n    return {\n      code39mod43: 'code39mod43',\n      code138: 'code138',\n      interleaved2of5: 'interleaved2of5',\n      aztec: 'aztec',\n      ean13: 'ean13',\n      ean8: 'ean8',\n      qr: 'qr',\n      pdf417: 'pdf417',\n      upc_e: 'upc_e',\n      datamatrix: 'datamatrix',\n      code39: 'code39',\n      code93: 'code93',\n      itf14: 'itf14',\n      codabar: 'codabar',\n      code128: 'code128',\n      upc_a: 'upc_a',\n    };\n  },\n  get Type() {\n    return { front: 'front', back: 'back' };\n  },\n  async requestPermissionsAsync(): Promise<PermissionResponse> {\n    return handleRequestPermissionsAsync();\n  },\n  async getPermissionsAsync(): Promise<PermissionResponse> {\n    return handlePermissionsQueryAsync();\n  },\n};\n"], "mappings": ";AAAA,SAA6BA,gBAAgB,EAAEC,mBAAmB,QAAQ,mBAAmB;AAE7F,SAASC,YAAYA,CAACC,WAAmC;EACvD,IAAIC,SAAS,CAACC,YAAY,IAAID,SAAS,CAACC,YAAY,CAACH,YAAY,EAAE;IACjE,OAAOE,SAAS,CAACC,YAAY,CAACH,YAAY,CAACC,WAAW,CAAC;;EAQzD,IAAMD,YAAY,GAEhBE,SAAS,CAACF,YAAY,IACtBE,SAAS,CAACE,kBAAkB,IAC5BF,SAAS,CAACG,eAAe,IACzB;IACE,IAAMC,KAAK,GAAQ,IAAIC,KAAK,CAAC,0BAA0B,CAAC;IACxDD,KAAK,CAACE,IAAI,GAAG,CAAC;IACdF,KAAK,CAACG,IAAI,GAAG,iBAAiB;IAC9B,MAAMH,KAAK;EACb,CAAC;EAEH,OAAO,IAAII,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAI;IACrCZ,YAAY,CAACa,IAAI,CAACX,SAAS,EAAED,WAAW,EAAEU,OAAO,EAAEC,MAAM,CAAC;EAC5D,CAAC,CAAC;AACJ;AAEA,SAASE,uBAAuBA,CAAAC,IAAA,EAAiC;EAAA,IAA9BC,OAAO,GAAAD,IAAA,CAAPC,OAAO;EAGxC,IAAIA,OAAO,KAAK,sBAAsB,EAAE;IACtC,OAAO;MACLC,MAAM,EAAEnB,gBAAgB,CAACoB,YAAY;MACrCC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;KACV;GACF,MAAM;IACL,OAAO;MACLJ,MAAM,EAAEnB,gBAAgB,CAACwB,MAAM;MAC/BH,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;KACV;;AAEL;AAAC,SAEcE,6BAA6BA,CAAA;EAAA,OAAAC,8BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,+BAAA;EAAAA,8BAAA,GAAAG,iBAAA,CAA5C,aAA4C;IAC1C,IAAI;MACF,MAAM3B,YAAY,CAAC;QACjB4B,KAAK,EAAE;OACR,CAAC;MACF,OAAO;QACLX,MAAM,EAAEnB,gBAAgB,CAAC+B,OAAO;QAChCV,OAAO,EAAE,OAAO;QAChBC,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE;OACV;KACF,CAAC,OAAAS,KAAA,EAAoB;MAAA,IAAXd,OAAO,GAAAc,KAAA,CAAPd,OAAO;MAChB,OAAOF,uBAAuB,CAAC;QAAEE,OAAO,EAAPA;MAAO,CAAE,CAAC;;EAE/C,CAAC;EAAA,OAAAQ,8BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEcK,2BAA2BA,CAAA;EAAA,OAAAC,4BAAA,CAAAP,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAM,6BAAA;EAAAA,4BAAA,GAAAL,iBAAA,CAA1C,aAA0C;IAAA,IAAAM,UAAA,EAAAC,qBAAA;IACxC,IAAI,GAAAD,UAAA,GAAC/B,SAAS,cAAAgC,qBAAA,GAATD,UAAA,CAAWE,WAAW,aAAtBD,qBAAA,CAAwBE,KAAK,GAAE;MAClC,MAAM,IAAIrC,mBAAmB,CAC3B,sBAAsB,EACtB,4CAA4C,CAC7C;;IAGH,IAAAsC,qBAAA,SAAwBnC,SAAS,CAACiC,WAAW,CAACC,KAAK,CAAC;QAAE3B,IAAI,EAAE;MAAQ,CAAE,CAAC;MAA/D6B,KAAK,GAAAD,qBAAA,CAALC,KAAK;IACb,QAAQA,KAAK;MACX,KAAK,QAAQ;QACX,OAAO;UACLrB,MAAM,EAAEnB,gBAAgB,CAACoB,YAAY;UACrCC,OAAO,EAAE,OAAO;UAChBC,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE;SACV;MACH,KAAK,SAAS;QACZ,OAAO;UACLJ,MAAM,EAAEnB,gBAAgB,CAAC+B,OAAO;UAChCV,OAAO,EAAE,OAAO;UAChBC,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE;SACV;MACH,KAAK,QAAQ;QACX,OAAO;UACLJ,MAAM,EAAEnB,gBAAgB,CAACwB,MAAM;UAC/BH,OAAO,EAAE,OAAO;UAChBC,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE;SACV;;EAEP,CAAC;EAAA,OAAAW,4BAAA,CAAAP,KAAA,OAAAC,SAAA;AAAA;AAED,eAAe;EACb,IAAIjB,IAAIA,CAAA;IACN,OAAO,0BAA0B;EACnC,CAAC;EACD,IAAI8B,WAAWA,CAAA;IACb,OAAO;MACLC,WAAW,EAAE,aAAa;MAC1BC,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,iBAAiB;MAClCC,KAAK,EAAE,OAAO;MACdC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,MAAM;MACZC,EAAE,EAAE,IAAI;MACRC,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,YAAY;MACxBC,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;KACR;EACH,CAAC;EACD,IAAIC,IAAIA,CAAA;IACN,OAAO;MAAEC,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAE;EACzC,CAAC;EACKC,uBAAuB;IAAA,IAAAC,wBAAA,GAAAjC,iBAAA;MAC3B,OAAOJ,6BAA6B,EAAE;IACxC,CAAC;IAAA,SAFKoC,uBAAuBA,CAAA;MAAA,OAAAC,wBAAA,CAAAnC,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAvBiC,uBAAuB;EAAA;EAGvBE,mBAAmB;IAAA,IAAAC,oBAAA,GAAAnC,iBAAA;MACvB,OAAOI,2BAA2B,EAAE;IACtC,CAAC;IAAA,SAFK8B,mBAAmBA,CAAA;MAAA,OAAAC,oBAAA,CAAArC,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAnBmC,mBAAmB;EAAA;CAG1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}