{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport AppContainer from \"./AppContainer\";\nimport invariant from 'fbjs/lib/invariant';\nimport render, { hydrate } from \"../render\";\nimport StyleSheet from \"../StyleSheet\";\nimport React from 'react';\nexport default function renderApplication(RootComponent, WrapperComponent, callback, options) {\n  var shouldHydrate = options.hydrate,\n    initialProps = options.initialProps,\n    rootTag = options.rootTag;\n  var renderFn = shouldHydrate ? hydrate : render;\n  invariant(rootTag, 'Expect to have a valid rootTag, instead got ', rootTag);\n  return renderFn(React.createElement(AppContainer, {\n    WrapperComponent: WrapperComponent,\n    ref: callback,\n    rootTag: rootTag\n  }, React.createElement(RootComponent, initialProps)), rootTag);\n}\nexport function getApplication(RootComponent, initialProps, WrapperComponent) {\n  var element = React.createElement(AppContainer, {\n    WrapperComponent: WrapperComponent,\n    rootTag: {}\n  }, React.createElement(RootComponent, initialProps));\n  var getStyleElement = function getStyleElement(props) {\n    var sheet = StyleSheet.getSheet();\n    return React.createElement(\"style\", _extends({}, props, {\n      dangerouslySetInnerHTML: {\n        __html: sheet.textContent\n      },\n      id: sheet.id\n    }));\n  };\n  return {\n    element: element,\n    getStyleElement: getStyleElement\n  };\n}", "map": {"version": 3, "names": ["_extends", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invariant", "render", "hydrate", "StyleSheet", "React", "renderApplication", "RootComponent", "WrapperComponent", "callback", "options", "shouldHydrate", "initialProps", "rootTag", "renderFn", "createElement", "ref", "getApplication", "element", "getStyleElement", "props", "sheet", "getSheet", "dangerouslySetInnerHTML", "__html", "textContent", "id"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/AppRegistry/renderApplication.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport AppContainer from './AppContainer';\nimport invariant from 'fbjs/lib/invariant';\nimport render, { hydrate } from '../render';\nimport StyleSheet from '../StyleSheet';\nimport React from 'react';\nexport default function renderApplication(RootComponent, WrapperComponent, callback, options) {\n  var shouldHydrate = options.hydrate,\n    initialProps = options.initialProps,\n    rootTag = options.rootTag;\n  var renderFn = shouldHydrate ? hydrate : render;\n  invariant(rootTag, 'Expect to have a valid rootTag, instead got ', rootTag);\n  return renderFn(/*#__PURE__*/React.createElement(AppContainer, {\n    WrapperComponent: WrapperComponent,\n    ref: callback,\n    rootTag: rootTag\n  }, /*#__PURE__*/React.createElement(RootComponent, initialProps)), rootTag);\n}\nexport function getApplication(RootComponent, initialProps, WrapperComponent) {\n  var element = /*#__PURE__*/React.createElement(AppContainer, {\n    WrapperComponent: WrapperComponent,\n    rootTag: {}\n  }, /*#__PURE__*/React.createElement(RootComponent, initialProps));\n  // Don't escape CSS text\n  var getStyleElement = props => {\n    var sheet = StyleSheet.getSheet();\n    return /*#__PURE__*/React.createElement(\"style\", _extends({}, props, {\n      dangerouslySetInnerHTML: {\n        __html: sheet.textContent\n      },\n      id: sheet.id\n    }));\n  };\n  return {\n    element,\n    getStyleElement\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AAWrD,OAAOC,YAAY;AACnB,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,MAAM,IAAIC,OAAO;AACxB,OAAOC,UAAU;AACjB,OAAOC,KAAK,MAAM,OAAO;AACzB,eAAe,SAASC,iBAAiBA,CAACC,aAAa,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC5F,IAAIC,aAAa,GAAGD,OAAO,CAACP,OAAO;IACjCS,YAAY,GAAGF,OAAO,CAACE,YAAY;IACnCC,OAAO,GAAGH,OAAO,CAACG,OAAO;EAC3B,IAAIC,QAAQ,GAAGH,aAAa,GAAGR,OAAO,GAAGD,MAAM;EAC/CD,SAAS,CAACY,OAAO,EAAE,8CAA8C,EAAEA,OAAO,CAAC;EAC3E,OAAOC,QAAQ,CAAcT,KAAK,CAACU,aAAa,CAACf,YAAY,EAAE;IAC7DQ,gBAAgB,EAAEA,gBAAgB;IAClCQ,GAAG,EAAEP,QAAQ;IACbI,OAAO,EAAEA;EACX,CAAC,EAAeR,KAAK,CAACU,aAAa,CAACR,aAAa,EAAEK,YAAY,CAAC,CAAC,EAAEC,OAAO,CAAC;AAC7E;AACA,OAAO,SAASI,cAAcA,CAACV,aAAa,EAAEK,YAAY,EAAEJ,gBAAgB,EAAE;EAC5E,IAAIU,OAAO,GAAgBb,KAAK,CAACU,aAAa,CAACf,YAAY,EAAE;IAC3DQ,gBAAgB,EAAEA,gBAAgB;IAClCK,OAAO,EAAE,CAAC;EACZ,CAAC,EAAeR,KAAK,CAACU,aAAa,CAACR,aAAa,EAAEK,YAAY,CAAC,CAAC;EAEjE,IAAIO,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,KAAK,EAAI;IAC7B,IAAIC,KAAK,GAAGjB,UAAU,CAACkB,QAAQ,CAAC,CAAC;IACjC,OAAoBjB,KAAK,CAACU,aAAa,CAAC,OAAO,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;MACnEG,uBAAuB,EAAE;QACvBC,MAAM,EAAEH,KAAK,CAACI;MAChB,CAAC;MACDC,EAAE,EAAEL,KAAK,CAACK;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;EACD,OAAO;IACLR,OAAO,EAAPA,OAAO;IACPC,eAAe,EAAfA;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}