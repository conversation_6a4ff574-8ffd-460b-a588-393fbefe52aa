@echo off
title Ouverture Projet AquaTrack Complet
color 0B

echo.
echo ========================================
echo    🚀 OUVERTURE PROJET AQUATRACK COMPLET
echo ========================================
echo.

echo 🛑 1. ARRET DE TOUS LES PROCESSUS...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🔧 3. VERIFICATION DES DEPENDANCES...
if not exist "node_modules" (
    echo 📦 Installation des dependances principales...
    npm install
)

if not exist "backend\node_modules" (
    echo 📦 Installation des dependances backend...
    cd backend
    npm install
    cd ..
)

if not exist "mobile\node_modules" (
    echo 📦 Installation des dependances mobile...
    cd mobile
    npm install
    cd ..
)

echo.
echo 🖥️  4. DEMARRAGE DU BACKEND (Port 4000)...
echo.
echo Choix du serveur backend:
echo [1] Serveur avec base de donnees PostgreSQL (backend\server-db.js)
echo [2] Serveur simple sans base de donnees (serveur-urgence.js)
echo [3] Serveur de test (simple-server.js)
echo.
set /p choice="Choisissez le serveur backend (1, 2 ou 3): "

if "%choice%"=="1" (
    echo 🗄️  Demarrage du serveur avec base de donnees...
    start "🖥️ Backend PostgreSQL" cmd /k "title Backend AquaTrack PostgreSQL && color 0A && echo ========================================== && echo    🖥️ BACKEND AQUATRACK POSTGRESQL && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Base: PostgreSQL Facturation && echo ✅ API: http://localhost:4000 && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo    - <EMAIL> / Admin123 && echo. && echo 📡 Demarrage... && echo. && node backend\server-db.js"
) else if "%choice%"=="2" (
    echo 🆘 Demarrage du serveur d'urgence...
    start "🆘 Backend Urgence" cmd /k "title Backend AquaTrack Urgence && color 0E && echo ========================================== && echo    🆘 BACKEND AQUATRACK URGENCE && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Mode: Test sans base de donnees && echo ✅ API: http://localhost:4000 && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo    - <EMAIL> / Admin123 && echo. && echo 📡 Demarrage... && echo. && node serveur-urgence.js"
) else (
    echo 🔧 Demarrage du serveur de test...
    start "🔧 Backend Test" cmd /k "title Backend AquaTrack Test && color 0C && echo ========================================== && echo    🔧 BACKEND AQUATRACK TEST && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Mode: Test simple && echo ✅ API: http://localhost:4000 && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo    - <EMAIL> / Admin123 && echo. && echo 📡 Demarrage... && echo. && node simple-server.js"
)

echo.
echo ⏳ 5. ATTENTE DU BACKEND (10 secondes)...
timeout /t 10 /nobreak >nul

echo.
echo 📱 6. DEMARRAGE DU FRONTEND...
echo.
echo Choix du frontend:
echo [1] Application React Web (Port 3000)
echo [2] Application Mobile Expo Web (Port 19006)
echo [3] Application React Native Mobile (Port 8081)
echo.
set /p frontend_choice="Choisissez le frontend (1, 2 ou 3): "

if "%frontend_choice%"=="1" (
    echo 🌐 Demarrage de l'application React Web...
    start "📱 Frontend React Web" cmd /k "title Frontend AquaTrack React Web && color 0D && echo ========================================== && echo    📱 FRONTEND AQUATRACK REACT WEB && echo ========================================== && echo. && echo ✅ Port: 3000 && echo ✅ URL: http://localhost:3000 && echo ✅ Type: React Web && echo. && echo 📡 Demarrage... && echo. && npm start"
) else if "%frontend_choice%"=="2" (
    echo 📱 Demarrage de l'application Mobile Expo Web...
    cd mobile
    start "📱 Frontend Mobile Expo" cmd /k "title Frontend AquaTrack Mobile Expo && color 0F && echo ========================================== && echo    📱 FRONTEND AQUATRACK MOBILE EXPO && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ URL: http://localhost:19006 && echo ✅ Type: Expo Web && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
    cd ..
) else (
    echo 📱 Demarrage de l'application React Native Mobile...
    cd react-native
    start "📱 Frontend React Native" cmd /k "title Frontend AquaTrack React Native && color 0A && echo ========================================== && echo    📱 FRONTEND AQUATRACK REACT NATIVE && echo ========================================== && echo. && echo ✅ Port: 8081 && echo ✅ URL: http://localhost:8081 && echo ✅ Type: React Native && echo. && echo 📡 Demarrage... && echo. && npx expo start"
    cd ..
)

echo.
echo ⏳ 7. ATTENTE DU FRONTEND (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 8. OUVERTURE DES NAVIGATEURS...
start http://localhost:4000

if "%frontend_choice%"=="1" (
    start http://localhost:3000
) else if "%frontend_choice%"=="2" (
    start http://localhost:19006
) else (
    start http://localhost:8081
)

echo.
echo ========================================
echo    ✅ PROJET AQUATRACK OUVERT !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
if "%frontend_choice%"=="1" (
    echo 📱 Frontend: http://localhost:3000 (React Web)
) else if "%frontend_choice%"=="2" (
    echo 📱 Frontend: http://localhost:19006 (Mobile Expo)
) else (
    echo 📱 Frontend: http://localhost:8081 (React Native)
)
echo.
echo 🔑 Comptes de test:
echo    - <EMAIL> / Tech123
echo    - <EMAIL> / Admin123
echo.
echo 📋 Instructions:
echo    1. Deux fenetres se sont ouvertes (Backend + Frontend)
echo    2. Deux navigateurs se sont ouverts
echo    3. Testez d'abord le backend sur http://localhost:4000
echo    4. Puis testez l'application frontend
echo    5. Utilisez les comptes de test pour vous connecter
echo.
echo ⚠️  IMPORTANT: Gardez les deux fenetres ouvertes !
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
