{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nvar _excluded = [\"state\", \"descriptors\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { HeaderShownContext, SafeAreaProviderCompat } from '@react-navigation/elements';\nimport { StackActions } from '@react-navigation/native';\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { SafeAreaInsetsContext } from 'react-native-safe-area-context';\nimport ModalPresentationContext from \"../../utils/ModalPresentationContext\";\nimport { GestureHandlerRootView } from \"../GestureHandler\";\nimport HeaderContainer from \"../Header/HeaderContainer\";\nimport CardStack from \"./CardStack\";\nvar GestureHandlerWrapper = GestureHandlerRootView != null ? GestureHandlerRootView : View;\nvar isArrayEqual = function isArrayEqual(a, b) {\n  return a.length === b.length && a.every(function (it, index) {\n    return it === b[index];\n  });\n};\nvar StackView = function (_React$Component) {\n  function StackView() {\n    var _this;\n    _classCallCheck(this, StackView);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, StackView, [].concat(args));\n    _this.state = {\n      routes: [],\n      previousRoutes: [],\n      previousDescriptors: {},\n      openingRouteKeys: [],\n      closingRouteKeys: [],\n      replacingRouteKeys: [],\n      descriptors: {}\n    };\n    _this.getPreviousRoute = function (_ref2) {\n      var route = _ref2.route;\n      var _this$state = _this.state,\n        closingRouteKeys = _this$state.closingRouteKeys,\n        replacingRouteKeys = _this$state.replacingRouteKeys;\n      var routes = _this.state.routes.filter(function (r) {\n        return r.key === route.key || !closingRouteKeys.includes(r.key) && !replacingRouteKeys.includes(r.key);\n      });\n      var index = routes.findIndex(function (r) {\n        return r.key === route.key;\n      });\n      return routes[index - 1];\n    };\n    _this.renderScene = function (_ref3) {\n      var route = _ref3.route;\n      var descriptor = _this.state.descriptors[route.key] || _this.props.descriptors[route.key];\n      if (!descriptor) {\n        return null;\n      }\n      return descriptor.render();\n    };\n    _this.renderHeader = function (props) {\n      return React.createElement(HeaderContainer, props);\n    };\n    _this.handleOpenRoute = function (_ref4) {\n      var route = _ref4.route;\n      var _this$props = _this.props,\n        state = _this$props.state,\n        navigation = _this$props.navigation;\n      var _this$state2 = _this.state,\n        closingRouteKeys = _this$state2.closingRouteKeys,\n        replacingRouteKeys = _this$state2.replacingRouteKeys;\n      if (closingRouteKeys.some(function (key) {\n        return key === route.key;\n      }) && replacingRouteKeys.every(function (key) {\n        return key !== route.key;\n      }) && state.routeNames.includes(route.name) && !state.routes.some(function (r) {\n        return r.key === route.key;\n      })) {\n        navigation.navigate(route);\n      } else {\n        _this.setState(function (state) {\n          return {\n            routes: state.replacingRouteKeys.length ? state.routes.filter(function (r) {\n              return !state.replacingRouteKeys.includes(r.key);\n            }) : state.routes,\n            openingRouteKeys: state.openingRouteKeys.filter(function (key) {\n              return key !== route.key;\n            }),\n            closingRouteKeys: state.closingRouteKeys.filter(function (key) {\n              return key !== route.key;\n            }),\n            replacingRouteKeys: []\n          };\n        });\n      }\n    };\n    _this.handleCloseRoute = function (_ref5) {\n      var route = _ref5.route;\n      var _this$props2 = _this.props,\n        state = _this$props2.state,\n        navigation = _this$props2.navigation;\n      if (state.routes.some(function (r) {\n        return r.key === route.key;\n      })) {\n        navigation.dispatch(_objectSpread(_objectSpread({}, StackActions.pop()), {}, {\n          source: route.key,\n          target: state.key\n        }));\n      } else {\n        _this.setState(function (state) {\n          return {\n            routes: state.routes.filter(function (r) {\n              return r.key !== route.key;\n            }),\n            openingRouteKeys: state.openingRouteKeys.filter(function (key) {\n              return key !== route.key;\n            }),\n            closingRouteKeys: state.closingRouteKeys.filter(function (key) {\n              return key !== route.key;\n            })\n          };\n        });\n      }\n    };\n    _this.handleTransitionStart = function (_ref6, closing) {\n      var route = _ref6.route;\n      return _this.props.navigation.emit({\n        type: 'transitionStart',\n        data: {\n          closing: closing\n        },\n        target: route.key\n      });\n    };\n    _this.handleTransitionEnd = function (_ref7, closing) {\n      var route = _ref7.route;\n      return _this.props.navigation.emit({\n        type: 'transitionEnd',\n        data: {\n          closing: closing\n        },\n        target: route.key\n      });\n    };\n    _this.handleGestureStart = function (_ref8) {\n      var route = _ref8.route;\n      _this.props.navigation.emit({\n        type: 'gestureStart',\n        target: route.key\n      });\n    };\n    _this.handleGestureEnd = function (_ref9) {\n      var route = _ref9.route;\n      _this.props.navigation.emit({\n        type: 'gestureEnd',\n        target: route.key\n      });\n    };\n    _this.handleGestureCancel = function (_ref10) {\n      var route = _ref10.route;\n      _this.props.navigation.emit({\n        type: 'gestureCancel',\n        target: route.key\n      });\n    };\n    return _this;\n  }\n  _inherits(StackView, _React$Component);\n  return _createClass(StackView, [{\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props3 = this.props,\n        state = _this$props3.state,\n        _ = _this$props3.descriptors,\n        rest = _objectWithoutProperties(_this$props3, _excluded);\n      var _this$state3 = this.state,\n        routes = _this$state3.routes,\n        descriptors = _this$state3.descriptors,\n        openingRouteKeys = _this$state3.openingRouteKeys,\n        closingRouteKeys = _this$state3.closingRouteKeys;\n      return React.createElement(GestureHandlerWrapper, {\n        style: styles.container\n      }, React.createElement(SafeAreaProviderCompat, null, React.createElement(SafeAreaInsetsContext.Consumer, null, function (insets) {\n        return React.createElement(ModalPresentationContext.Consumer, null, function (isParentModal) {\n          return React.createElement(HeaderShownContext.Consumer, null, function (isParentHeaderShown) {\n            return React.createElement(CardStack, _extends({\n              insets: insets,\n              isParentHeaderShown: isParentHeaderShown,\n              isParentModal: isParentModal,\n              getPreviousRoute: _this2.getPreviousRoute,\n              routes: routes,\n              openingRouteKeys: openingRouteKeys,\n              closingRouteKeys: closingRouteKeys,\n              onOpenRoute: _this2.handleOpenRoute,\n              onCloseRoute: _this2.handleCloseRoute,\n              onTransitionStart: _this2.handleTransitionStart,\n              onTransitionEnd: _this2.handleTransitionEnd,\n              renderHeader: _this2.renderHeader,\n              renderScene: _this2.renderScene,\n              state: state,\n              descriptors: descriptors,\n              onGestureStart: _this2.handleGestureStart,\n              onGestureEnd: _this2.handleGestureEnd,\n              onGestureCancel: _this2.handleGestureCancel\n            }, rest));\n          });\n        });\n      })));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, state) {\n      if ((props.state.routes === state.previousRoutes || isArrayEqual(props.state.routes.map(function (r) {\n        return r.key;\n      }), state.previousRoutes.map(function (r) {\n        return r.key;\n      }))) && state.routes.length) {\n        var _routes = state.routes;\n        var _previousRoutes = state.previousRoutes;\n        var _descriptors = props.descriptors;\n        var previousDescriptors = state.previousDescriptors;\n        if (props.descriptors !== state.previousDescriptors) {\n          _descriptors = state.routes.reduce(function (acc, route) {\n            acc[route.key] = props.descriptors[route.key] || state.descriptors[route.key];\n            return acc;\n          }, {});\n          previousDescriptors = props.descriptors;\n        }\n        if (props.state.routes !== state.previousRoutes) {\n          var map = props.state.routes.reduce(function (acc, route) {\n            acc[route.key] = route;\n            return acc;\n          }, {});\n          _routes = state.routes.map(function (route) {\n            return map[route.key] || route;\n          });\n          _previousRoutes = props.state.routes;\n        }\n        return {\n          routes: _routes,\n          previousRoutes: _previousRoutes,\n          descriptors: _descriptors,\n          previousDescriptors: previousDescriptors\n        };\n      }\n      var routes = props.state.index < props.state.routes.length - 1 ? props.state.routes.slice(0, props.state.index + 1) : props.state.routes;\n      var openingRouteKeys = state.openingRouteKeys,\n        closingRouteKeys = state.closingRouteKeys,\n        replacingRouteKeys = state.replacingRouteKeys,\n        previousRoutes = state.previousRoutes;\n      var previousFocusedRoute = previousRoutes[previousRoutes.length - 1];\n      var nextFocusedRoute = routes[routes.length - 1];\n      var isAnimationEnabled = function isAnimationEnabled(key) {\n        var descriptor = props.descriptors[key] || state.descriptors[key];\n        return descriptor ? descriptor.options.animationEnabled !== false : true;\n      };\n      var getAnimationTypeForReplace = function getAnimationTypeForReplace(key) {\n        var _descriptor$options$a;\n        var descriptor = props.descriptors[key] || state.descriptors[key];\n        return (_descriptor$options$a = descriptor.options.animationTypeForReplace) != null ? _descriptor$options$a : 'push';\n      };\n      if (previousFocusedRoute && previousFocusedRoute.key !== nextFocusedRoute.key) {\n        if (!previousRoutes.some(function (r) {\n          return r.key === nextFocusedRoute.key;\n        })) {\n          if (isAnimationEnabled(nextFocusedRoute.key) && !openingRouteKeys.includes(nextFocusedRoute.key)) {\n            openingRouteKeys = [].concat(_toConsumableArray(openingRouteKeys), [nextFocusedRoute.key]);\n            closingRouteKeys = closingRouteKeys.filter(function (key) {\n              return key !== nextFocusedRoute.key;\n            });\n            replacingRouteKeys = replacingRouteKeys.filter(function (key) {\n              return key !== nextFocusedRoute.key;\n            });\n            if (!routes.some(function (r) {\n              return r.key === previousFocusedRoute.key;\n            })) {\n              openingRouteKeys = openingRouteKeys.filter(function (key) {\n                return key !== previousFocusedRoute.key;\n              });\n              if (getAnimationTypeForReplace(nextFocusedRoute.key) === 'pop') {\n                closingRouteKeys = [].concat(_toConsumableArray(closingRouteKeys), [previousFocusedRoute.key]);\n                openingRouteKeys = openingRouteKeys.filter(function (key) {\n                  return key !== nextFocusedRoute.key;\n                });\n                routes = [].concat(_toConsumableArray(routes), [previousFocusedRoute]);\n              } else {\n                replacingRouteKeys = [].concat(_toConsumableArray(replacingRouteKeys), [previousFocusedRoute.key]);\n                closingRouteKeys = closingRouteKeys.filter(function (key) {\n                  return key !== previousFocusedRoute.key;\n                });\n                routes = routes.slice();\n                routes.splice(routes.length - 1, 0, previousFocusedRoute);\n              }\n            }\n          }\n        } else if (!routes.some(function (r) {\n          return r.key === previousFocusedRoute.key;\n        })) {\n          if (isAnimationEnabled(previousFocusedRoute.key) && !closingRouteKeys.includes(previousFocusedRoute.key)) {\n            closingRouteKeys = [].concat(_toConsumableArray(closingRouteKeys), [previousFocusedRoute.key]);\n            openingRouteKeys = openingRouteKeys.filter(function (key) {\n              return key !== previousFocusedRoute.key;\n            });\n            replacingRouteKeys = replacingRouteKeys.filter(function (key) {\n              return key !== previousFocusedRoute.key;\n            });\n            routes = [].concat(_toConsumableArray(routes), [previousFocusedRoute]);\n          }\n        } else {}\n      } else if (replacingRouteKeys.length || closingRouteKeys.length) {\n        var _routes2;\n        routes = routes.slice();\n        (_routes2 = routes).splice.apply(_routes2, [routes.length - 1, 0].concat(_toConsumableArray(state.routes.filter(function (_ref) {\n          var key = _ref.key;\n          return isAnimationEnabled(key) ? replacingRouteKeys.includes(key) || closingRouteKeys.includes(key) : false;\n        }))));\n      }\n      if (!routes.length) {\n        throw new Error('There should always be at least one route in the navigation state.');\n      }\n      var descriptors = routes.reduce(function (acc, route) {\n        acc[route.key] = props.descriptors[route.key] || state.descriptors[route.key];\n        return acc;\n      }, {});\n      return {\n        routes: routes,\n        previousRoutes: props.state.routes,\n        previousDescriptors: props.descriptors,\n        openingRouteKeys: openingRouteKeys,\n        closingRouteKeys: closingRouteKeys,\n        replacingRouteKeys: replacingRouteKeys,\n        descriptors: descriptors\n      };\n    }\n  }]);\n}(React.Component);\nexport { StackView as default };\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  }\n});", "map": {"version": 3, "names": ["HeaderShownContext", "SafeAreaProviderCompat", "StackActions", "React", "StyleSheet", "View", "SafeAreaInsetsContext", "ModalPresentationContext", "GestureHandlerRootView", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CardStack", "GestureHandlerWrapper", "isArrayEqual", "a", "b", "length", "every", "it", "index", "StackView", "_React$Component", "_this", "_classCallCheck", "_len", "arguments", "args", "Array", "_key", "_callSuper", "concat", "state", "routes", "previousRoutes", "previousDescriptors", "openingRouteKeys", "closingRouteKeys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "descriptors", "getPreviousRoute", "_ref2", "route", "_this$state", "filter", "r", "key", "includes", "findIndex", "renderScene", "_ref3", "descriptor", "props", "render", "renderHeader", "createElement", "handleOpenRoute", "_ref4", "_this$props", "navigation", "_this$state2", "some", "routeNames", "name", "navigate", "setState", "handleCloseRoute", "_ref5", "_this$props2", "dispatch", "_objectSpread", "pop", "source", "target", "handleTransitionStart", "_ref6", "closing", "emit", "type", "data", "handleTransitionEnd", "_ref7", "handleGestureStart", "_ref8", "handleGestureEnd", "_ref9", "handleGestureCancel", "_ref10", "_inherits", "_createClass", "value", "_this2", "_this$props3", "_", "rest", "_objectWithoutProperties", "_excluded", "_this$state3", "style", "styles", "container", "Consumer", "insets", "isParentModal", "isParentHeaderShown", "_extends", "onOpenRoute", "onCloseRoute", "onTransitionStart", "onTransitionEnd", "onGestureStart", "onGestureEnd", "onGestureCancel", "getDerivedStateFromProps", "map", "reduce", "acc", "slice", "previousFocusedRoute", "nextFocusedRoute", "isAnimationEnabled", "options", "animationEnabled", "getAnimationTypeForReplace", "_descriptor$options$a", "animationTypeForReplace", "_toConsumableArray", "splice", "_routes2", "apply", "_ref", "Error", "Component", "default", "create", "flex"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\stack\\src\\views\\Stack\\StackView.tsx"], "sourcesContent": ["import {\n  HeaderShownContext,\n  SafeAreaProviderCompat,\n} from '@react-navigation/elements';\nimport {\n  ParamListBase,\n  Route,\n  StackActions,\n  StackNavigationState,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport { StyleSheet, View } from 'react-native';\nimport {\n  EdgeInsets,\n  SafeAreaInsetsContext,\n} from 'react-native-safe-area-context';\n\nimport type {\n  StackDescriptorMap,\n  StackNavigationConfig,\n  StackNavigationHelpers,\n} from '../../types';\nimport ModalPresentationContext from '../../utils/ModalPresentationContext';\nimport { GestureHandlerRootView } from '../GestureHandler';\nimport HeaderContainer, {\n  Props as HeaderContainerProps,\n} from '../Header/HeaderContainer';\nimport CardStack from './CardStack';\n\ntype Props = StackNavigationConfig & {\n  state: StackNavigationState<ParamListBase>;\n  navigation: StackNavigationHelpers;\n  descriptors: StackDescriptorMap;\n};\n\ntype State = {\n  // Local copy of the routes which are actually rendered\n  routes: Route<string>[];\n  // Previous routes, to compare whether routes have changed or not\n  previousRoutes: Route<string>[];\n  // Previous descriptors, to compare whether descriptors have changed or not\n  previousDescriptors: StackDescriptorMap;\n  // List of routes being opened, we need to animate pushing of these new routes\n  openingRouteKeys: string[];\n  // List of routes being closed, we need to animate popping of these routes\n  closingRouteKeys: string[];\n  // List of routes being replaced, we need to keep a copy until the new route animates in\n  replacingRouteKeys: string[];\n  // Since the local routes can vary from the routes from props, we need to keep the descriptors for old routes\n  // Otherwise we won't be able to access the options for routes that were removed\n  descriptors: StackDescriptorMap;\n};\n\nconst GestureHandlerWrapper = GestureHandlerRootView ?? View;\n\n/**\n * Compare two arrays with primitive values as the content.\n * We need to make sure that both values and order match.\n */\nconst isArrayEqual = (a: any[], b: any[]) =>\n  a.length === b.length && a.every((it, index) => it === b[index]);\n\nexport default class StackView extends React.Component<Props, State> {\n  static getDerivedStateFromProps(\n    props: Readonly<Props>,\n    state: Readonly<State>\n  ) {\n    // If there was no change in routes, we don't need to compute anything\n    if (\n      (props.state.routes === state.previousRoutes ||\n        isArrayEqual(\n          props.state.routes.map((r) => r.key),\n          state.previousRoutes.map((r) => r.key)\n        )) &&\n      state.routes.length\n    ) {\n      let routes = state.routes;\n      let previousRoutes = state.previousRoutes;\n      let descriptors = props.descriptors;\n      let previousDescriptors = state.previousDescriptors;\n\n      if (props.descriptors !== state.previousDescriptors) {\n        descriptors = state.routes.reduce<StackDescriptorMap>((acc, route) => {\n          acc[route.key] =\n            props.descriptors[route.key] || state.descriptors[route.key];\n\n          return acc;\n        }, {});\n\n        previousDescriptors = props.descriptors;\n      }\n\n      if (props.state.routes !== state.previousRoutes) {\n        // if any route objects have changed, we should update them\n        const map = props.state.routes.reduce<Record<string, Route<string>>>(\n          (acc, route) => {\n            acc[route.key] = route;\n            return acc;\n          },\n          {}\n        );\n\n        routes = state.routes.map((route) => map[route.key] || route);\n        previousRoutes = props.state.routes;\n      }\n\n      return {\n        routes,\n        previousRoutes,\n        descriptors,\n        previousDescriptors,\n      };\n    }\n\n    // Here we determine which routes were added or removed to animate them\n    // We keep a copy of the route being removed in local state to be able to animate it\n\n    let routes =\n      props.state.index < props.state.routes.length - 1\n        ? // Remove any extra routes from the state\n          // The last visible route should be the focused route, i.e. at current index\n          props.state.routes.slice(0, props.state.index + 1)\n        : props.state.routes;\n\n    // Now we need to determine which routes were added and removed\n    let {\n      openingRouteKeys,\n      closingRouteKeys,\n      replacingRouteKeys,\n      previousRoutes,\n    } = state;\n\n    const previousFocusedRoute = previousRoutes[previousRoutes.length - 1] as\n      | Route<string>\n      | undefined;\n    const nextFocusedRoute = routes[routes.length - 1];\n\n    const isAnimationEnabled = (key: string) => {\n      const descriptor = props.descriptors[key] || state.descriptors[key];\n\n      return descriptor ? descriptor.options.animationEnabled !== false : true;\n    };\n\n    const getAnimationTypeForReplace = (key: string) => {\n      const descriptor = props.descriptors[key] || state.descriptors[key];\n\n      return descriptor.options.animationTypeForReplace ?? 'push';\n    };\n\n    if (\n      previousFocusedRoute &&\n      previousFocusedRoute.key !== nextFocusedRoute.key\n    ) {\n      // We only need to animate routes if the focused route changed\n      // Animating previous routes won't be visible coz the focused route is on top of everything\n\n      if (!previousRoutes.some((r) => r.key === nextFocusedRoute.key)) {\n        // A new route has come to the focus, we treat this as a push\n        // A replace can also trigger this, the animation should look like push\n\n        if (\n          isAnimationEnabled(nextFocusedRoute.key) &&\n          !openingRouteKeys.includes(nextFocusedRoute.key)\n        ) {\n          // In this case, we need to animate pushing the focused route\n          // We don't care about animating any other added routes because they won't be visible\n          openingRouteKeys = [...openingRouteKeys, nextFocusedRoute.key];\n\n          closingRouteKeys = closingRouteKeys.filter(\n            (key) => key !== nextFocusedRoute.key\n          );\n          replacingRouteKeys = replacingRouteKeys.filter(\n            (key) => key !== nextFocusedRoute.key\n          );\n\n          if (!routes.some((r) => r.key === previousFocusedRoute.key)) {\n            // The previous focused route isn't present in state, we treat this as a replace\n\n            openingRouteKeys = openingRouteKeys.filter(\n              (key) => key !== previousFocusedRoute.key\n            );\n\n            if (getAnimationTypeForReplace(nextFocusedRoute.key) === 'pop') {\n              closingRouteKeys = [\n                ...closingRouteKeys,\n                previousFocusedRoute.key,\n              ];\n\n              // By default, new routes have a push animation, so we add it to `openingRouteKeys` before\n              // But since user configured it to animate the old screen like a pop, we need to add this without animation\n              // So remove it from `openingRouteKeys` which will remove the animation\n              openingRouteKeys = openingRouteKeys.filter(\n                (key) => key !== nextFocusedRoute.key\n              );\n\n              // Keep the route being removed at the end to animate it out\n              routes = [...routes, previousFocusedRoute];\n            } else {\n              replacingRouteKeys = [\n                ...replacingRouteKeys,\n                previousFocusedRoute.key,\n              ];\n\n              closingRouteKeys = closingRouteKeys.filter(\n                (key) => key !== previousFocusedRoute.key\n              );\n\n              // Keep the old route in the state because it's visible under the new route, and removing it will feel abrupt\n              // We need to insert it just before the focused one (the route being pushed)\n              // After the push animation is completed, routes being replaced will be removed completely\n              routes = routes.slice();\n              routes.splice(routes.length - 1, 0, previousFocusedRoute);\n            }\n          }\n        }\n      } else if (!routes.some((r) => r.key === previousFocusedRoute.key)) {\n        // The previously focused route was removed, we treat this as a pop\n\n        if (\n          isAnimationEnabled(previousFocusedRoute.key) &&\n          !closingRouteKeys.includes(previousFocusedRoute.key)\n        ) {\n          closingRouteKeys = [...closingRouteKeys, previousFocusedRoute.key];\n\n          // Sometimes a route can be closed before the opening animation finishes\n          // So we also need to remove it from the opening list\n          openingRouteKeys = openingRouteKeys.filter(\n            (key) => key !== previousFocusedRoute.key\n          );\n          replacingRouteKeys = replacingRouteKeys.filter(\n            (key) => key !== previousFocusedRoute.key\n          );\n\n          // Keep a copy of route being removed in the state to be able to animate it\n          routes = [...routes, previousFocusedRoute];\n        }\n      } else {\n        // Looks like some routes were re-arranged and no focused routes were added/removed\n        // i.e. the currently focused route already existed and the previously focused route still exists\n        // We don't know how to animate this\n      }\n    } else if (replacingRouteKeys.length || closingRouteKeys.length) {\n      // Keep the routes we are closing or replacing if animation is enabled for them\n      routes = routes.slice();\n      routes.splice(\n        routes.length - 1,\n        0,\n        ...state.routes.filter(({ key }) =>\n          isAnimationEnabled(key)\n            ? replacingRouteKeys.includes(key) || closingRouteKeys.includes(key)\n            : false\n        )\n      );\n    }\n\n    if (!routes.length) {\n      throw new Error(\n        'There should always be at least one route in the navigation state.'\n      );\n    }\n\n    const descriptors = routes.reduce<StackDescriptorMap>((acc, route) => {\n      acc[route.key] =\n        props.descriptors[route.key] || state.descriptors[route.key];\n\n      return acc;\n    }, {});\n\n    return {\n      routes,\n      previousRoutes: props.state.routes,\n      previousDescriptors: props.descriptors,\n      openingRouteKeys,\n      closingRouteKeys,\n      replacingRouteKeys,\n      descriptors,\n    };\n  }\n\n  state: State = {\n    routes: [],\n    previousRoutes: [],\n    previousDescriptors: {},\n    openingRouteKeys: [],\n    closingRouteKeys: [],\n    replacingRouteKeys: [],\n    descriptors: {},\n  };\n\n  private getPreviousRoute = ({ route }: { route: Route<string> }) => {\n    const { closingRouteKeys, replacingRouteKeys } = this.state;\n    const routes = this.state.routes.filter(\n      (r) =>\n        r.key === route.key ||\n        (!closingRouteKeys.includes(r.key) &&\n          !replacingRouteKeys.includes(r.key))\n    );\n\n    const index = routes.findIndex((r) => r.key === route.key);\n\n    return routes[index - 1];\n  };\n\n  private renderScene = ({ route }: { route: Route<string> }) => {\n    const descriptor =\n      this.state.descriptors[route.key] || this.props.descriptors[route.key];\n\n    if (!descriptor) {\n      return null;\n    }\n\n    return descriptor.render();\n  };\n\n  private renderHeader = (props: HeaderContainerProps) => {\n    return <HeaderContainer {...props} />;\n  };\n\n  private handleOpenRoute = ({ route }: { route: Route<string> }) => {\n    const { state, navigation } = this.props;\n    const { closingRouteKeys, replacingRouteKeys } = this.state;\n\n    if (\n      closingRouteKeys.some((key) => key === route.key) &&\n      replacingRouteKeys.every((key) => key !== route.key) &&\n      state.routeNames.includes(route.name) &&\n      !state.routes.some((r) => r.key === route.key)\n    ) {\n      // If route isn't present in current state, but was closing, assume that a close animation was cancelled\n      // So we need to add this route back to the state\n      navigation.navigate(route);\n    } else {\n      this.setState((state) => ({\n        routes: state.replacingRouteKeys.length\n          ? state.routes.filter(\n              (r) => !state.replacingRouteKeys.includes(r.key)\n            )\n          : state.routes,\n        openingRouteKeys: state.openingRouteKeys.filter(\n          (key) => key !== route.key\n        ),\n        closingRouteKeys: state.closingRouteKeys.filter(\n          (key) => key !== route.key\n        ),\n        replacingRouteKeys: [],\n      }));\n    }\n  };\n\n  private handleCloseRoute = ({ route }: { route: Route<string> }) => {\n    const { state, navigation } = this.props;\n\n    if (state.routes.some((r) => r.key === route.key)) {\n      // If a route exists in state, trigger a pop\n      // This will happen in when the route was closed from the card component\n      // e.g. When the close animation triggered from a gesture ends\n      navigation.dispatch({\n        ...StackActions.pop(),\n        source: route.key,\n        target: state.key,\n      });\n    } else {\n      // We need to clean up any state tracking the route and pop it immediately\n      this.setState((state) => ({\n        routes: state.routes.filter((r) => r.key !== route.key),\n        openingRouteKeys: state.openingRouteKeys.filter(\n          (key) => key !== route.key\n        ),\n        closingRouteKeys: state.closingRouteKeys.filter(\n          (key) => key !== route.key\n        ),\n      }));\n    }\n  };\n\n  private handleTransitionStart = (\n    { route }: { route: Route<string> },\n    closing: boolean\n  ) =>\n    this.props.navigation.emit({\n      type: 'transitionStart',\n      data: { closing },\n      target: route.key,\n    });\n\n  private handleTransitionEnd = (\n    { route }: { route: Route<string> },\n    closing: boolean\n  ) =>\n    this.props.navigation.emit({\n      type: 'transitionEnd',\n      data: { closing },\n      target: route.key,\n    });\n\n  private handleGestureStart = ({ route }: { route: Route<string> }) => {\n    this.props.navigation.emit({\n      type: 'gestureStart',\n      target: route.key,\n    });\n  };\n\n  private handleGestureEnd = ({ route }: { route: Route<string> }) => {\n    this.props.navigation.emit({\n      type: 'gestureEnd',\n      target: route.key,\n    });\n  };\n\n  private handleGestureCancel = ({ route }: { route: Route<string> }) => {\n    this.props.navigation.emit({\n      type: 'gestureCancel',\n      target: route.key,\n    });\n  };\n\n  render() {\n    const {\n      state,\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      descriptors: _,\n      ...rest\n    } = this.props;\n\n    const { routes, descriptors, openingRouteKeys, closingRouteKeys } =\n      this.state;\n\n    return (\n      <GestureHandlerWrapper style={styles.container}>\n        <SafeAreaProviderCompat>\n          <SafeAreaInsetsContext.Consumer>\n            {(insets) => (\n              <ModalPresentationContext.Consumer>\n                {(isParentModal) => (\n                  <HeaderShownContext.Consumer>\n                    {(isParentHeaderShown) => (\n                      <CardStack\n                        insets={insets as EdgeInsets}\n                        isParentHeaderShown={isParentHeaderShown}\n                        isParentModal={isParentModal}\n                        getPreviousRoute={this.getPreviousRoute}\n                        routes={routes}\n                        openingRouteKeys={openingRouteKeys}\n                        closingRouteKeys={closingRouteKeys}\n                        onOpenRoute={this.handleOpenRoute}\n                        onCloseRoute={this.handleCloseRoute}\n                        onTransitionStart={this.handleTransitionStart}\n                        onTransitionEnd={this.handleTransitionEnd}\n                        renderHeader={this.renderHeader}\n                        renderScene={this.renderScene}\n                        state={state}\n                        descriptors={descriptors}\n                        onGestureStart={this.handleGestureStart}\n                        onGestureEnd={this.handleGestureEnd}\n                        onGestureCancel={this.handleGestureCancel}\n                        {...rest}\n                      />\n                    )}\n                  </HeaderShownContext.Consumer>\n                )}\n              </ModalPresentationContext.Consumer>\n            )}\n          </SafeAreaInsetsContext.Consumer>\n        </SafeAreaProviderCompat>\n      </GestureHandlerWrapper>\n    );\n  }\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SACEA,kBAAkB,EAClBC,sBAAsB,QACjB,4BAA4B;AACnC,SAGEC,YAAY,QAEP,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAE9B,SAEEC,qBAAqB,QAChB,gCAAgC;AAOvC,OAAOC,wBAAwB;AAC/B,SAASC,sBAAsB;AAC/B,OAAOC,eAAe;AAGtB,OAAOC,SAAS;AA0BhB,IAAMC,qBAAqB,GAAGH,sBAAsB,WAAtBA,sBAAsB,GAAIH,IAAI;AAM5D,IAAMO,YAAY,GAAG,SAAfA,YAAYA,CAAIC,CAAQ,EAAEC,CAAQ;EAAA,OACtCD,CAAC,CAACE,MAAM,KAAKD,CAAC,CAACC,MAAM,IAAIF,CAAC,CAACG,KAAK,CAAC,UAACC,EAAE,EAAEC,KAAK;IAAA,OAAKD,EAAE,KAAKH,CAAC,CAACI,KAAK,CAAC;EAAA,EAAC;AAAA;AAAA,IAE7CC,SAAS,aAAAC,gBAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,KAAA;IAAAC,eAAA,OAAAH,SAAA;IAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAT,MAAA,EAAAU,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;IAAA;IAAAN,KAAA,GAAAO,UAAA,OAAAT,SAAA,KAAAU,MAAA,CAAAJ,IAAA;IAAAJ,KAAA,CAyN5BS,KAAK,GAAU;MACbC,MAAM,EAAE,EAAE;MACVC,cAAc,EAAE,EAAE;MAClBC,mBAAmB,EAAE,CAAC,CAAC;MACvBC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE,EAAE;MACpBC,kBAAkB,EAAE,EAAE;MACtBC,WAAW,EAAE,CAAC;IAChB,CAAC;IAAAhB,KAAA,CAEOiB,gBAAgB,GAAG,UAAAC,KAAA,EAAyC;MAAA,IAAtCC,KAAA,GAAiCD,KAAA,CAAjCC,KAAA;MAC5B,IAAAC,WAAA,GAAiDpB,KAAA,CAAKS,KAAK;QAAnDK,gBAAgB,GAAAM,WAAA,CAAhBN,gBAAgB;QAAEC,kBAAA,GAAAK,WAAA,CAAAL,kBAAA;MAC1B,IAAML,MAAM,GAAGV,KAAA,CAAKS,KAAK,CAACC,MAAM,CAACW,MAAM,CACpC,UAAAC,CAAC;QAAA,OACAA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG,IAClB,CAACT,gBAAgB,CAACU,QAAQ,CAACF,CAAC,CAACC,GAAG,CAAC,IAChC,CAACR,kBAAkB,CAACS,QAAQ,CAACF,CAAC,CAACC,GAAG,CAAE;MAAA,EACzC;MAED,IAAM1B,KAAK,GAAGa,MAAM,CAACe,SAAS,CAAE,UAAAH,CAAC;QAAA,OAAKA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG;MAAA,EAAC;MAE1D,OAAOb,MAAM,CAACb,KAAK,GAAG,CAAC,CAAC;IAC1B,CAAC;IAAAG,KAAA,CAEO0B,WAAW,GAAG,UAAAC,KAAA,EAAyC;MAAA,IAAtCR,KAAA,GAAiCQ,KAAA,CAAjCR,KAAA;MACvB,IAAMS,UAAU,GACd5B,KAAA,CAAKS,KAAK,CAACO,WAAW,CAACG,KAAK,CAACI,GAAG,CAAC,IAAIvB,KAAA,CAAK6B,KAAK,CAACb,WAAW,CAACG,KAAK,CAACI,GAAG,CAAC;MAExE,IAAI,CAACK,UAAU,EAAE;QACf,OAAO,IAAI;MACb;MAEA,OAAOA,UAAU,CAACE,MAAM,EAAE;IAC5B,CAAC;IAAA9B,KAAA,CAEO+B,YAAY,GAAI,UAAAF,KAA2B,EAAK;MACtD,OAAO/C,KAAA,CAAAkD,aAAA,CAAC5C,eAAe,EAAKyC,KAAK,CAAI;IACvC,CAAC;IAAA7B,KAAA,CAEOiC,eAAe,GAAG,UAAAC,KAAA,EAAyC;MAAA,IAAtCf,KAAA,GAAiCe,KAAA,CAAjCf,KAAA;MAC3B,IAAAgB,WAAA,GAA8BnC,KAAA,CAAK6B,KAAK;QAAhCpB,KAAK,GAAA0B,WAAA,CAAL1B,KAAK;QAAE2B,UAAA,GAAAD,WAAA,CAAAC,UAAA;MACf,IAAAC,YAAA,GAAiDrC,KAAA,CAAKS,KAAK;QAAnDK,gBAAgB,GAAAuB,YAAA,CAAhBvB,gBAAgB;QAAEC,kBAAA,GAAAsB,YAAA,CAAAtB,kBAAA;MAE1B,IACED,gBAAgB,CAACwB,IAAI,CAAE,UAAAf,GAAG;QAAA,OAAKA,GAAG,KAAKJ,KAAK,CAACI,GAAG;MAAA,EAAC,IACjDR,kBAAkB,CAACpB,KAAK,CAAE,UAAA4B,GAAG;QAAA,OAAKA,GAAG,KAAKJ,KAAK,CAACI,GAAG;MAAA,EAAC,IACpDd,KAAK,CAAC8B,UAAU,CAACf,QAAQ,CAACL,KAAK,CAACqB,IAAI,CAAC,IACrC,CAAC/B,KAAK,CAACC,MAAM,CAAC4B,IAAI,CAAE,UAAAhB,CAAC;QAAA,OAAKA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG;MAAA,EAAC,EAC9C;QAGAa,UAAU,CAACK,QAAQ,CAACtB,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLnB,KAAA,CAAK0C,QAAQ,CAAE,UAAAjC,KAAK;UAAA,OAAM;YACxBC,MAAM,EAAED,KAAK,CAACM,kBAAkB,CAACrB,MAAM,GACnCe,KAAK,CAACC,MAAM,CAACW,MAAM,CAChB,UAAAC,CAAC;cAAA,OAAK,CAACb,KAAK,CAACM,kBAAkB,CAACS,QAAQ,CAACF,CAAC,CAACC,GAAG,CAAC;YAAA,EACjD,GACDd,KAAK,CAACC,MAAM;YAChBG,gBAAgB,EAAEJ,KAAK,CAACI,gBAAgB,CAACQ,MAAM,CAC5C,UAAAE,GAAG;cAAA,OAAKA,GAAG,KAAKJ,KAAK,CAACI,GAAG;YAAA,EAC3B;YACDT,gBAAgB,EAAEL,KAAK,CAACK,gBAAgB,CAACO,MAAM,CAC5C,UAAAE,GAAG;cAAA,OAAKA,GAAG,KAAKJ,KAAK,CAACI,GAAG;YAAA,EAC3B;YACDR,kBAAkB,EAAE;UACtB,CAAC;QAAA,CAAC,CAAC;MACL;IACF,CAAC;IAAAf,KAAA,CAEO2C,gBAAgB,GAAG,UAAAC,KAAA,EAAyC;MAAA,IAAtCzB,KAAA,GAAiCyB,KAAA,CAAjCzB,KAAA;MAC5B,IAAA0B,YAAA,GAA8B7C,KAAA,CAAK6B,KAAK;QAAhCpB,KAAK,GAAAoC,YAAA,CAALpC,KAAK;QAAE2B,UAAA,GAAAS,YAAA,CAAAT,UAAA;MAEf,IAAI3B,KAAK,CAACC,MAAM,CAAC4B,IAAI,CAAE,UAAAhB,CAAC;QAAA,OAAKA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG;MAAA,EAAC,EAAE;QAIjDa,UAAU,CAACU,QAAQ,CAAAC,aAAA,CAAAA,aAAA,KACdlE,YAAY,CAACmE,GAAG,EAAE;UACrBC,MAAM,EAAE9B,KAAK,CAACI,GAAG;UACjB2B,MAAM,EAAEzC,KAAK,CAACc;QAAA,EACf,CAAC;MACJ,CAAC,MAAM;QAELvB,KAAA,CAAK0C,QAAQ,CAAE,UAAAjC,KAAK;UAAA,OAAM;YACxBC,MAAM,EAAED,KAAK,CAACC,MAAM,CAACW,MAAM,CAAE,UAAAC,CAAC;cAAA,OAAKA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG;YAAA,EAAC;YACvDV,gBAAgB,EAAEJ,KAAK,CAACI,gBAAgB,CAACQ,MAAM,CAC5C,UAAAE,GAAG;cAAA,OAAKA,GAAG,KAAKJ,KAAK,CAACI,GAAG;YAAA,EAC3B;YACDT,gBAAgB,EAAEL,KAAK,CAACK,gBAAgB,CAACO,MAAM,CAC5C,UAAAE,GAAG;cAAA,OAAKA,GAAG,KAAKJ,KAAK,CAACI,GAAG;YAAA;UAE9B,CAAC;QAAA,CAAC,CAAC;MACL;IACF,CAAC;IAAAvB,KAAA,CAEOmD,qBAAqB,GAAG,UAAAC,KAAA,EAE9BC,OAAgB;MAAA,IADdlC,KAAA,GAAiCiC,KAAA,CAAjCjC,KAAA;MAAiC,OAGnCnB,KAAA,CAAK6B,KAAK,CAACO,UAAU,CAACkB,IAAI,CAAC;QACzBC,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE;UAAEH,OAAA,EAAAA;QAAQ,CAAC;QACjBH,MAAM,EAAE/B,KAAK,CAACI;MAChB,CAAC,CAAC;IAAA;IAAAvB,KAAA,CAEIyD,mBAAmB,GAAG,UAAAC,KAAA,EAE5BL,OAAgB;MAAA,IADdlC,KAAA,GAAiCuC,KAAA,CAAjCvC,KAAA;MAAiC,OAGnCnB,KAAA,CAAK6B,KAAK,CAACO,UAAU,CAACkB,IAAI,CAAC;QACzBC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE;UAAEH,OAAA,EAAAA;QAAQ,CAAC;QACjBH,MAAM,EAAE/B,KAAK,CAACI;MAChB,CAAC,CAAC;IAAA;IAAAvB,KAAA,CAEI2D,kBAAkB,GAAG,UAAAC,KAAA,EAAyC;MAAA,IAAtCzC,KAAA,GAAiCyC,KAAA,CAAjCzC,KAAA;MAC9BnB,KAAA,CAAK6B,KAAK,CAACO,UAAU,CAACkB,IAAI,CAAC;QACzBC,IAAI,EAAE,cAAc;QACpBL,MAAM,EAAE/B,KAAK,CAACI;MAChB,CAAC,CAAC;IACJ,CAAC;IAAAvB,KAAA,CAEO6D,gBAAgB,GAAG,UAAAC,KAAA,EAAyC;MAAA,IAAtC3C,KAAA,GAAiC2C,KAAA,CAAjC3C,KAAA;MAC5BnB,KAAA,CAAK6B,KAAK,CAACO,UAAU,CAACkB,IAAI,CAAC;QACzBC,IAAI,EAAE,YAAY;QAClBL,MAAM,EAAE/B,KAAK,CAACI;MAChB,CAAC,CAAC;IACJ,CAAC;IAAAvB,KAAA,CAEO+D,mBAAmB,GAAG,UAAAC,MAAA,EAAyC;MAAA,IAAtC7C,KAAA,GAAiC6C,MAAA,CAAjC7C,KAAA;MAC/BnB,KAAA,CAAK6B,KAAK,CAACO,UAAU,CAACkB,IAAI,CAAC;QACzBC,IAAI,EAAE,eAAe;QACrBL,MAAM,EAAE/B,KAAK,CAACI;MAChB,CAAC,CAAC;IACJ,CAAC;IAAA,OAAAvB,KAAA;EAAA;EAAAiE,SAAA,CAAAnE,SAAA,EAAAC,gBAAA;EAAA,OAAAmE,YAAA,CAAApE,SAAA;IAAAyB,GAAA;IAAA4C,KAAA,EAED,SAAArC,MAAMA,CAAA,EAAG;MAAA,IAAAsC,MAAA;MACP,IAAAC,YAAA,GAKI,IAAI,CAACxC,KAAK;QAJZpB,KAAK,GAAA4D,YAAA,CAAL5D,KAAK;QAEQ6D,CAAC,GAAAD,YAAA,CAAdrD,WAAW;QACRuD,IAAA,GAAAC,wBAAA,CAAAH,YAAA,EAAAI,SAAA;MAGL,IAAAC,YAAA,GACE,IAAI,CAACjE,KAAK;QADJC,MAAM,GAAAgE,YAAA,CAANhE,MAAM;QAAEM,WAAW,GAAA0D,YAAA,CAAX1D,WAAW;QAAEH,gBAAgB,GAAA6D,YAAA,CAAhB7D,gBAAgB;QAAEC,gBAAA,GAAA4D,YAAA,CAAA5D,gBAAA;MAG/C,OACEhC,KAAA,CAAAkD,aAAA,CAAC1C,qBAAqB;QAACqF,KAAK,EAAEC,MAAM,CAACC;MAAU,GAC7C/F,KAAA,CAAAkD,aAAA,CAACpD,sBAAsB,QACrBE,KAAA,CAAAkD,aAAA,CAAC/C,qBAAqB,CAAC6F,QAAQ,QAC3B,UAAAC,MAAM;QAAA,OACNjG,KAAA,CAAAkD,aAAA,CAAC9C,wBAAwB,CAAC4F,QAAQ,QAC9B,UAAAE,aAAa;UAAA,OACblG,KAAA,CAAAkD,aAAA,CAACrD,kBAAkB,CAACmG,QAAQ,QACxB,UAAAG,mBAAmB;YAAA,OACnBnG,KAAA,CAAAkD,aAAA,CAAC3C,SAAS,EAAA6F,QAAA;cACRH,MAAM,EAAEA,MAAqB;cAC7BE,mBAAmB,EAAEA,mBAAoB;cACzCD,aAAa,EAAEA,aAAc;cAC7B/D,gBAAgB,EAAEmD,MAAI,CAACnD,gBAAiB;cACxCP,MAAM,EAAEA,MAAO;cACfG,gBAAgB,EAAEA,gBAAiB;cACnCC,gBAAgB,EAAEA,gBAAiB;cACnCqE,WAAW,EAAEf,MAAI,CAACnC,eAAgB;cAClCmD,YAAY,EAAEhB,MAAI,CAACzB,gBAAiB;cACpC0C,iBAAiB,EAAEjB,MAAI,CAACjB,qBAAsB;cAC9CmC,eAAe,EAAElB,MAAI,CAACX,mBAAoB;cAC1C1B,YAAY,EAAEqC,MAAI,CAACrC,YAAa;cAChCL,WAAW,EAAE0C,MAAI,CAAC1C,WAAY;cAC9BjB,KAAK,EAAEA,KAAM;cACbO,WAAW,EAAEA,WAAY;cACzBuE,cAAc,EAAEnB,MAAI,CAACT,kBAAmB;cACxC6B,YAAY,EAAEpB,MAAI,CAACP,gBAAiB;cACpC4B,eAAe,EAAErB,MAAI,CAACL;YAAoB,GACtCQ,IAAI,EAEX;UAAA,EAEJ;QAAA,EAEJ;MAAA,EAC8B,CACV,CACH;IAE5B;EAAA;IAAAhD,GAAA;IAAA4C,KAAA,EAnZA,SAAOuB,wBAAwBA,CAC7B7D,KAAsB,EACtBpB,KAAsB,EACtB;MAEA,IACE,CAACoB,KAAK,CAACpB,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,cAAc,IAC1CpB,YAAY,CACVsC,KAAK,CAACpB,KAAK,CAACC,MAAM,CAACiF,GAAG,CAAE,UAAArE,CAAC;QAAA,OAAKA,CAAC,CAACC,GAAG;MAAA,EAAC,EACpCd,KAAK,CAACE,cAAc,CAACgF,GAAG,CAAE,UAAArE,CAAC;QAAA,OAAKA,CAAC,CAACC,GAAG;MAAA,EAAC,CACvC,KACHd,KAAK,CAACC,MAAM,CAAChB,MAAM,EACnB;QACA,IAAIgB,OAAM,GAAGD,KAAK,CAACC,MAAM;QACzB,IAAIC,eAAc,GAAGF,KAAK,CAACE,cAAc;QACzC,IAAIK,YAAW,GAAGa,KAAK,CAACb,WAAW;QACnC,IAAIJ,mBAAmB,GAAGH,KAAK,CAACG,mBAAmB;QAEnD,IAAIiB,KAAK,CAACb,WAAW,KAAKP,KAAK,CAACG,mBAAmB,EAAE;UACnDI,YAAW,GAAGP,KAAK,CAACC,MAAM,CAACkF,MAAM,CAAqB,UAACC,GAAG,EAAE1E,KAAK,EAAK;YACpE0E,GAAG,CAAC1E,KAAK,CAACI,GAAG,CAAC,GACZM,KAAK,CAACb,WAAW,CAACG,KAAK,CAACI,GAAG,CAAC,IAAId,KAAK,CAACO,WAAW,CAACG,KAAK,CAACI,GAAG,CAAC;YAE9D,OAAOsE,GAAG;UACZ,CAAC,EAAE,CAAC,CAAC,CAAC;UAENjF,mBAAmB,GAAGiB,KAAK,CAACb,WAAW;QACzC;QAEA,IAAIa,KAAK,CAACpB,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,cAAc,EAAE;UAE/C,IAAMgF,GAAG,GAAG9D,KAAK,CAACpB,KAAK,CAACC,MAAM,CAACkF,MAAM,CACnC,UAACC,GAAG,EAAE1E,KAAK,EAAK;YACd0E,GAAG,CAAC1E,KAAK,CAACI,GAAG,CAAC,GAAGJ,KAAK;YACtB,OAAO0E,GAAG;UACZ,CAAC,EACD,CAAC,CAAC,CACH;UAEDnF,OAAM,GAAGD,KAAK,CAACC,MAAM,CAACiF,GAAG,CAAE,UAAAxE,KAAK;YAAA,OAAKwE,GAAG,CAACxE,KAAK,CAACI,GAAG,CAAC,IAAIJ,KAAK;UAAA,EAAC;UAC7DR,eAAc,GAAGkB,KAAK,CAACpB,KAAK,CAACC,MAAM;QACrC;QAEA,OAAO;UACLA,MAAM,EAANA,OAAM;UACNC,cAAc,EAAdA,eAAc;UACdK,WAAW,EAAXA,YAAW;UACXJ,mBAAA,EAAAA;QACF,CAAC;MACH;MAKA,IAAIF,MAAM,GACRmB,KAAK,CAACpB,KAAK,CAACZ,KAAK,GAAGgC,KAAK,CAACpB,KAAK,CAACC,MAAM,CAAChB,MAAM,GAAG,CAAC,GAG7CmC,KAAK,CAACpB,KAAK,CAACC,MAAM,CAACoF,KAAK,CAAC,CAAC,EAAEjE,KAAK,CAACpB,KAAK,CAACZ,KAAK,GAAG,CAAC,CAAC,GAClDgC,KAAK,CAACpB,KAAK,CAACC,MAAM;MAGxB,IACEG,gBAAgB,GAIdJ,KAAK,CAJPI,gBAAgB;QAChBC,gBAAgB,GAGdL,KAAK,CAHPK,gBAAgB;QAChBC,kBAAkB,GAEhBN,KAAK,CAFPM,kBAAkB;QAClBJ,cAAA,GACEF,KAAK,CADPE,cAAA;MAGF,IAAMoF,oBAAoB,GAAGpF,cAAc,CAACA,cAAc,CAACjB,MAAM,GAAG,CAAC,CAExD;MACb,IAAMsG,gBAAgB,GAAGtF,MAAM,CAACA,MAAM,CAAChB,MAAM,GAAG,CAAC,CAAC;MAElD,IAAMuG,kBAAkB,GAAI,SAAtBA,kBAAkBA,CAAI1E,GAAW,EAAK;QAC1C,IAAMK,UAAU,GAAGC,KAAK,CAACb,WAAW,CAACO,GAAG,CAAC,IAAId,KAAK,CAACO,WAAW,CAACO,GAAG,CAAC;QAEnE,OAAOK,UAAU,GAAGA,UAAU,CAACsE,OAAO,CAACC,gBAAgB,KAAK,KAAK,GAAG,IAAI;MAC1E,CAAC;MAED,IAAMC,0BAA0B,GAAI,SAA9BA,0BAA0BA,CAAI7E,GAAW,EAAK;QAAA,IAAA8E,qBAAA;QAClD,IAAMzE,UAAU,GAAGC,KAAK,CAACb,WAAW,CAACO,GAAG,CAAC,IAAId,KAAK,CAACO,WAAW,CAACO,GAAG,CAAC;QAEnE,QAAA8E,qBAAA,GAAOzE,UAAU,CAACsE,OAAO,CAACI,uBAAuB,YAAAD,qBAAA,GAAI,MAAM;MAC7D,CAAC;MAED,IACEN,oBAAoB,IACpBA,oBAAoB,CAACxE,GAAG,KAAKyE,gBAAgB,CAACzE,GAAG,EACjD;QAIA,IAAI,CAACZ,cAAc,CAAC2B,IAAI,CAAE,UAAAhB,CAAC;UAAA,OAAKA,CAAC,CAACC,GAAG,KAAKyE,gBAAgB,CAACzE,GAAG;QAAA,EAAC,EAAE;UAI/D,IACE0E,kBAAkB,CAACD,gBAAgB,CAACzE,GAAG,CAAC,IACxC,CAACV,gBAAgB,CAACW,QAAQ,CAACwE,gBAAgB,CAACzE,GAAG,CAAC,EAChD;YAGAV,gBAAgB,MAAAL,MAAA,CAAA+F,kBAAA,CAAO1F,gBAAgB,IAAEmF,gBAAgB,CAACzE,GAAG,EAAC;YAE9DT,gBAAgB,GAAGA,gBAAgB,CAACO,MAAM,CACvC,UAAAE,GAAG;cAAA,OAAKA,GAAG,KAAKyE,gBAAgB,CAACzE,GAAG;YAAA,EACtC;YACDR,kBAAkB,GAAGA,kBAAkB,CAACM,MAAM,CAC3C,UAAAE,GAAG;cAAA,OAAKA,GAAG,KAAKyE,gBAAgB,CAACzE,GAAG;YAAA,EACtC;YAED,IAAI,CAACb,MAAM,CAAC4B,IAAI,CAAE,UAAAhB,CAAC;cAAA,OAAKA,CAAC,CAACC,GAAG,KAAKwE,oBAAoB,CAACxE,GAAG;YAAA,EAAC,EAAE;cAG3DV,gBAAgB,GAAGA,gBAAgB,CAACQ,MAAM,CACvC,UAAAE,GAAG;gBAAA,OAAKA,GAAG,KAAKwE,oBAAoB,CAACxE,GAAG;cAAA,EAC1C;cAED,IAAI6E,0BAA0B,CAACJ,gBAAgB,CAACzE,GAAG,CAAC,KAAK,KAAK,EAAE;gBAC9DT,gBAAgB,MAAAN,MAAA,CAAA+F,kBAAA,CACXzF,gBAAgB,IACnBiF,oBAAoB,CAACxE,GAAG,EACzB;gBAKDV,gBAAgB,GAAGA,gBAAgB,CAACQ,MAAM,CACvC,UAAAE,GAAG;kBAAA,OAAKA,GAAG,KAAKyE,gBAAgB,CAACzE,GAAG;gBAAA,EACtC;gBAGDb,MAAM,MAAAF,MAAA,CAAA+F,kBAAA,CAAO7F,MAAM,IAAEqF,oBAAoB,EAAC;cAC5C,CAAC,MAAM;gBACLhF,kBAAkB,MAAAP,MAAA,CAAA+F,kBAAA,CACbxF,kBAAkB,IACrBgF,oBAAoB,CAACxE,GAAG,EACzB;gBAEDT,gBAAgB,GAAGA,gBAAgB,CAACO,MAAM,CACvC,UAAAE,GAAG;kBAAA,OAAKA,GAAG,KAAKwE,oBAAoB,CAACxE,GAAG;gBAAA,EAC1C;gBAKDb,MAAM,GAAGA,MAAM,CAACoF,KAAK,EAAE;gBACvBpF,MAAM,CAAC8F,MAAM,CAAC9F,MAAM,CAAChB,MAAM,GAAG,CAAC,EAAE,CAAC,EAAEqG,oBAAoB,CAAC;cAC3D;YACF;UACF;QACF,CAAC,MAAM,IAAI,CAACrF,MAAM,CAAC4B,IAAI,CAAE,UAAAhB,CAAC;UAAA,OAAKA,CAAC,CAACC,GAAG,KAAKwE,oBAAoB,CAACxE,GAAG;QAAA,EAAC,EAAE;UAGlE,IACE0E,kBAAkB,CAACF,oBAAoB,CAACxE,GAAG,CAAC,IAC5C,CAACT,gBAAgB,CAACU,QAAQ,CAACuE,oBAAoB,CAACxE,GAAG,CAAC,EACpD;YACAT,gBAAgB,MAAAN,MAAA,CAAA+F,kBAAA,CAAOzF,gBAAgB,IAAEiF,oBAAoB,CAACxE,GAAG,EAAC;YAIlEV,gBAAgB,GAAGA,gBAAgB,CAACQ,MAAM,CACvC,UAAAE,GAAG;cAAA,OAAKA,GAAG,KAAKwE,oBAAoB,CAACxE,GAAG;YAAA,EAC1C;YACDR,kBAAkB,GAAGA,kBAAkB,CAACM,MAAM,CAC3C,UAAAE,GAAG;cAAA,OAAKA,GAAG,KAAKwE,oBAAoB,CAACxE,GAAG;YAAA,EAC1C;YAGDb,MAAM,MAAAF,MAAA,CAAA+F,kBAAA,CAAO7F,MAAM,IAAEqF,oBAAoB,EAAC;UAC5C;QACF,CAAC,MAAM,CAGL;MAEJ,CAAC,MAAM,IAAIhF,kBAAkB,CAACrB,MAAM,IAAIoB,gBAAgB,CAACpB,MAAM,EAAE;QAAA,IAAA+G,QAAA;QAE/D/F,MAAM,GAAGA,MAAM,CAACoF,KAAK,EAAE;QACvB,CAAAW,QAAA,GAAA/F,MAAM,EAAC8F,MAAM,CAAAE,KAAA,CAAAD,QAAA,GACX/F,MAAM,CAAChB,MAAM,GAAG,CAAC,EACjB,CAAC,EAAAc,MAAA,CAAA+F,kBAAA,CACE9F,KAAK,CAACC,MAAM,CAACW,MAAM,CAAC,UAAAsF,IAAA;UAAA,IAAGpF,GAAA,GAAKoF,IAAA,CAALpF,GAAA;UAAK,OAC7B0E,kBAAkB,CAAC1E,GAAG,CAAC,GACnBR,kBAAkB,CAACS,QAAQ,CAACD,GAAG,CAAC,IAAIT,gBAAgB,CAACU,QAAQ,CAACD,GAAG,CAAC,GAClE,KAAK;QAAA,EACV,GACF;MACH;MAEA,IAAI,CAACb,MAAM,CAAChB,MAAM,EAAE;QAClB,MAAM,IAAIkH,KAAK,CACb,oEAAoE,CACrE;MACH;MAEA,IAAM5F,WAAW,GAAGN,MAAM,CAACkF,MAAM,CAAqB,UAACC,GAAG,EAAE1E,KAAK,EAAK;QACpE0E,GAAG,CAAC1E,KAAK,CAACI,GAAG,CAAC,GACZM,KAAK,CAACb,WAAW,CAACG,KAAK,CAACI,GAAG,CAAC,IAAId,KAAK,CAACO,WAAW,CAACG,KAAK,CAACI,GAAG,CAAC;QAE9D,OAAOsE,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN,OAAO;QACLnF,MAAM,EAANA,MAAM;QACNC,cAAc,EAAEkB,KAAK,CAACpB,KAAK,CAACC,MAAM;QAClCE,mBAAmB,EAAEiB,KAAK,CAACb,WAAW;QACtCH,gBAAgB,EAAhBA,gBAAgB;QAChBC,gBAAgB,EAAhBA,gBAAgB;QAChBC,kBAAkB,EAAlBA,kBAAkB;QAClBC,WAAA,EAAAA;MACF,CAAC;IACH;EAAA;AAAA,EAvNqClC,KAAK,CAAC+H,SAAS;AAAA,SAAjC/G,SAAS,IAAAgH,OAAA;AAuZ9B,IAAMlC,MAAM,GAAG7F,UAAU,CAACgI,MAAM,CAAC;EAC/BlC,SAAS,EAAE;IACTmC,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}