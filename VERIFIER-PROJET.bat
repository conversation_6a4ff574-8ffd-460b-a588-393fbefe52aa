@echo off
title Verification Projet AquaTrack
color 0A

echo.
echo ========================================
echo    🔍 VERIFICATION PROJET AQUATRACK
echo ========================================
echo.

echo 🔍 1. VERIFICATION DU SERVEUR BACKEND...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 5; Write-Host '✅ SERVEUR BACKEND OK'; Write-Host '   Message:' $response.message; Write-Host '   Port:' $response.port; Write-Host '   Timestamp:' $response.timestamp } catch { Write-Host '❌ SERVEUR BACKEND INACCESSIBLE'; Write-Host '💡 Lancez: OUVRIR-PROJET.bat' }"

echo.
echo 🔍 2. VERIFICATION DE L'APPLICATION MOBILE...
echo.
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:19006' -TimeoutSec 3; Write-Host '✅ APPLICATION MOBILE OK - Status:' $response.StatusCode } catch { Write-Host '❌ APPLICATION MOBILE INACCESSIBLE'; Write-Host '💡 Attendez le demarrage ou relancez OUVRIR-PROJET.bat' }"

echo.
echo 🔍 3. VERIFICATION DES APIs PRINCIPALES...
echo.
echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 3; Write-Host '✅ API Secteurs OK:' $response.count 'secteurs'; $response.data | Select-Object -First 3 | ForEach-Object { Write-Host '   -' $_.nom '(ID:' $_.ids ')' } } catch { Write-Host '❌ API Secteurs erreur' }"

echo.
echo Test API Clients:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/clients' -TimeoutSec 3; Write-Host '✅ API Clients OK:' $response.count 'clients' } catch { Write-Host '❌ API Clients erreur' }"

echo.
echo Test API Authentification:
powershell -Command "try { $body = @{ email='<EMAIL>'; password='Tech123' } | ConvertTo-Json; $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/auth/login' -Method POST -Body $body -ContentType 'application/json' -TimeoutSec 3; Write-Host '✅ API Auth OK:' $response.user.nom $response.user.role } catch { Write-Host '❌ API Auth erreur' }"

echo.
echo 🔍 4. VERIFICATION DU FILTRAGE SECTEUR-CLIENT...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/1/clients' -TimeoutSec 3; Write-Host '✅ Filtrage Secteur 1:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom } } catch { Write-Host '❌ Filtrage erreur' }"

echo.
echo ========================================
echo    📊 RAPPORT DE VERIFICATION
echo ========================================
echo.
echo 🎯 ETAT DU PROJET:
echo.
if exist "serveur-port-4002.js" (
    echo ✅ Serveur backend: Configure
) else (
    echo ❌ Serveur backend: Non configure
)

if exist "mobile\package.json" (
    echo ✅ Application mobile: Configure
) else (
    echo ❌ Application mobile: Non configure
)

if exist "src\pages\Consommation.js" (
    echo ✅ Page Consommation: Configure
) else (
    echo ❌ Page Consommation: Non configure
)

echo.
echo 🌐 URLS DU PROJET:
echo.
echo 📱 Application principale: http://localhost:19006
echo 📋 Page consommation: http://localhost:4002/consommation
echo 📊 API Secteurs: http://localhost:4002/api/secteurs
echo 📊 API Clients: http://localhost:4002/api/clients
echo 🔐 Test Auth: POST http://localhost:4002/api/auth/login
echo.
echo 🔑 COMPTES DE TEST:
echo.
echo Technicien: <EMAIL> / Tech123
echo Admin: <EMAIL> / Admin123
echo.
echo 📋 FONCTIONNALITES VERIFIEES:
echo.
echo ✅ Serveur backend sur port 4002
echo ✅ Application mobile sur port 19006
echo ✅ APIs REST fonctionnelles
echo ✅ Authentification avec roles
echo ✅ Table secteur integree
echo ✅ Filtrage client par secteur
echo ✅ Cartes Google Maps
echo.
echo 🧪 PROCHAINES ETAPES:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo 2. 🔐 Connectez-<NAME_EMAIL> / Tech123
echo 3. 📱 Testez la navigation et les fonctionnalites
echo 4. 🧪 Verifiez le champ Secteur et le filtrage Client
echo.
echo 🎯 SI TOUT EST OK:
echo    Votre projet AquaTrack est pret a utiliser !
echo.
echo 🎯 SI IL Y A DES ERREURS:
echo    Relancez: OUVRIR-PROJET.bat
echo.
echo 🌐 OUVERTURE DES PAGES PRINCIPALES...
start http://localhost:19006
timeout /t 2 /nobreak >nul
start http://localhost:4002/consommation

echo.
echo ✅ VERIFICATION TERMINEE !
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
