@echo off
title Test Table Secteur
color 0A

echo.
echo ========================================
echo    📍 TEST TABLE SECTEUR
echo ========================================
echo.

echo 🔍 1. VERIFICATION DU SERVEUR PORT 4002...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 5; Write-Host '✅ Serveur OK:' $response.message } catch { Write-Host '❌ Serveur non accessible - Demarrez le serveur d''abord' }"

echo.
echo 🔍 2. TEST DE L'API SECTEURS...
echo.
echo Verification de l'API /api/secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 10; Write-Host '✅ API Secteurs OK'; Write-Host '📊 Nombre de secteurs:' $response.count; Write-Host '📋 Structure de reponse:'; Write-Host '   - success:' $response.success; Write-Host '   - data: array de' $response.data.Count 'elements'; Write-Host '   - count:' $response.count; Write-Host '   - message:' $response.message; Write-Host ''; Write-Host '📍 Liste des secteurs de la table:'; $response.data | ForEach-Object { Write-Host '   ID:' $_.ids '| Nom:' $_.nom '| Lat:' $_.latitude '| Lng:' $_.longitude } } catch { Write-Host '❌ API Secteurs erreur:' $_.Exception.Message }"

echo.
echo 🔍 3. VERIFICATION DE LA STRUCTURE SQL...
echo.
echo Structure attendue de la table secteur:
echo    - ids (SERIAL PRIMARY KEY)
echo    - nom (VARCHAR(100))
echo    - latitude (DOUBLE PRECISION)
echo    - longitude (DOUBLE PRECISION)

echo.
echo 🔍 4. TEST DE CHAQUE SECTEUR INDIVIDUELLEMENT...
echo.
for %%i in (1 2 3 4 5) do (
    echo Test secteur ID %%i:
    powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/%%i/clients' -TimeoutSec 3; $secteur = $response.secteur; if ($secteur) { Write-Host '   ✅ Secteur %%i existe:' $secteur.nom '(' $secteur.latitude ',' $secteur.longitude ')' } else { Write-Host '   ❌ Secteur %%i non trouve' } } catch { Write-Host '   ❌ Erreur secteur %%i' }"
)

echo.
echo 🚀 5. DEMARRAGE DE L'APPLICATION POUR TEST...
echo.
echo Arret des processus existants...
taskkill /f /im node.exe 2>nul

echo.
echo Demarrage du serveur backend (port 4002)...
start "🎯 Backend Port 4002" cmd /k "title BACKEND AQUATRACK PORT 4002 && color 0B && echo ========================================== && echo    🎯 BACKEND AQUATRACK PORT 4002 && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Table secteur: Tous les secteurs && echo ✅ API: /api/secteurs && echo ✅ SQL: SELECT * FROM secteur ORDER BY nom && echo. && echo 📡 Demarrage... && echo. && node serveur-port-4002.js"

echo.
echo Attente du backend (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo Test de l'API apres demarrage:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '✅ API Secteurs accessible:' $response.count 'secteurs'; $response.data | ForEach-Object { Write-Host '   📍' $_.ids ':' $_.nom } } catch { Write-Host '❌ API Secteurs non accessible' }"

echo.
echo Demarrage de l'application React...
cd mobile
start "📱 Application React" cmd /k "title APPLICATION REACT AQUATRACK && color 0D && echo ========================================== && echo    📱 APPLICATION REACT AQUATRACK && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4002 && echo ✅ Champ Secteur: Utilise table secteur && echo ✅ API: /api/secteurs && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo Attente de l'application (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 6. OUVERTURE DE L'APPLICATION...
start http://localhost:19006

echo.
echo ========================================
echo    ✅ TABLE SECTEUR CONFIGUREE !
echo ========================================
echo.
echo 📋 CONFIGURATION VERIFIEE:
echo.
echo ✅ API SECTEURS:
echo    - URL: http://localhost:4002/api/secteurs
echo    - Methode: GET
echo    - Retourne: Tous les secteurs de la table
echo.
echo ✅ REQUETE SQL:
echo    SELECT ids, nom, latitude, longitude
echo    FROM secteur
echo    ORDER BY nom
echo.
echo ✅ STRUCTURE REPONSE:
echo    {
echo      "success": true,
echo      "data": [
echo        {
echo          "ids": 1,
echo          "nom": "Centre-Ville",
echo          "latitude": 33.5731,
echo          "longitude": -7.5898
echo        },
echo        ...
echo      ],
echo      "count": 5,
echo      "message": "5 secteur(s) trouve(s)"
echo    }
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous avec:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 OBSERVEZ LE CHAMP SECTEUR:
echo    - Doit afficher "Selectionner un secteur"
echo    - Cliquez sur le menu deroulant
echo    - Doit afficher TOUS les secteurs de la table
echo    - Chaque secteur avec son nom exact de la BD
echo.
echo 📊 SECTEURS ATTENDUS DANS LE MENU:
echo    1. Centre-Ville
echo    2. Quartier Industriel
echo    3. Zone Residentielle Nord
echo    4. Zone Residentielle Sud
echo    5. Quartier Commercial
echo.
echo 🌐 URLs de verification:
echo    - Application: http://localhost:19006
echo    - API Secteurs: http://localhost:4002/api/secteurs
echo    - Test API: Ouvrez l'URL API dans le navigateur
echo.
echo 🎯 RESULTAT ATTENDU:
echo    Le champ "📍 Secteur *" affiche tous les secteurs
echo    de votre table secteur avec leurs noms exacts !
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
