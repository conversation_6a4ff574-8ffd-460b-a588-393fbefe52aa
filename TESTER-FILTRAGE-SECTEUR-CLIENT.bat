@echo off
title Test Filtrage Secteur → Client
color 0A

echo.
echo ========================================
echo    🔍 TEST FILTRAGE SECTEUR → CLIENT
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR AVEC FILTRAGE...
echo.
start "🧪 Serveur Filtrage" cmd /k "title SERVEUR FILTRAGE SECTEUR-CLIENT && color 0B && echo ========================================== && echo    🧪 SERVEUR FILTRAGE SECTEUR-CLIENT && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ API Secteurs: 5 secteurs && echo ✅ API Clients: Filtrage par secteur && echo ✅ Donnees: Clients repartis par secteur && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 🔍 5. TEST DES APIs DE FILTRAGE...
echo.
echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '✅ API Secteurs:' $response.count 'secteurs disponibles' } catch { Write-Host '❌ API Secteurs erreur' }"

echo.
echo Test filtrage par secteur:
for %%i in (1 2 3 4 5) do (
    echo.
    echo Secteur %%i:
    powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/%%i/clients' -TimeoutSec 3; $secteur = $response.secteur; Write-Host '   ✅ Secteur:' $secteur.nom; Write-Host '   👥 Clients:' $response.count; $response.data | ForEach-Object { Write-Host '      -' $_.nom $_.prenom } } catch { Write-Host '   ❌ Erreur secteur %%i' }"
)

echo.
echo 📱 6. DEMARRAGE DE L'APPLICATION...
echo.
cd mobile
start "📱 App Filtrage" cmd /k "title APPLICATION FILTRAGE && color 0D && echo ========================================== && echo    📱 APPLICATION FILTRAGE && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Filtrage: Secteur → Client && echo ✅ Donnees: Garanties avec fallback && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 7. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 8. OUVERTURE POUR TEST...
start http://localhost:19006

echo.
echo ========================================
echo    🎯 TEST FILTRAGE SECTEUR → CLIENT
echo ========================================
echo.
echo 🔍 FILTRAGE IMPLEMENTÉ:
echo.
echo ✅ SECTEUR 1 - Centre-Ville:
echo    👥 3 clients: Benali Fatima, Alami Mohammed, Tazi Aicha
echo.
echo ✅ SECTEUR 2 - Quartier Industriel:
echo    👥 2 clients: Benjelloun Youssef, Lahlou Khadija
echo.
echo ✅ SECTEUR 3 - Zone Residentielle Nord:
echo    👥 3 clients: Fassi Omar, Chraibi Salma, Idrissi Hassan
echo.
echo ✅ SECTEUR 4 - Zone Residentielle Sud:
echo    👥 2 clients: Berrada Nadia, Kettani Rachid
echo.
echo ✅ SECTEUR 5 - Quartier Commercial:
echo    👥 2 clients: Amrani Leila, Zouaki Karim
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 TESTEZ LE FILTRAGE:
echo.
echo    a) 📍 Champ Secteur:
echo       - Cliquez sur "Selectionner un secteur"
echo       - Vous devez voir les 5 secteurs
echo.
echo    b) 👤 Champ Client (AVANT selection):
echo       - Doit afficher: "⬆️ Selectionnez d'abord un secteur ci-dessus"
echo.
echo    c) 🎯 SELECTIONNEZ UN SECTEUR:
echo       - Choisissez "Centre-Ville" (secteur 1)
echo       - Le champ Client doit se mettre a jour automatiquement
echo.
echo    d) 👥 Champ Client (APRES selection):
echo       - Doit afficher: "-- Selectionner un client (3 dans ce secteur) --"
echo       - Options: 1. Benali Fatima, 2. Alami Mohammed, 3. Tazi Aicha
echo.
echo    e) 🔄 TESTEZ AUTRES SECTEURS:
echo       - Changez pour "Quartier Industriel"
echo       - Le champ Client doit afficher 2 clients differents
echo       - Changez pour "Zone Residentielle Nord"
echo       - Le champ Client doit afficher 3 autres clients
echo.
echo 📊 COMPORTEMENT ATTENDU:
echo.
echo ✅ SECTEUR NON SELECTIONNE:
echo    📍 Secteur: [Selectionner un secteur]
echo    👤 Client: [⬆️ Selectionnez d'abord un secteur ci-dessus]
echo.
echo ✅ SECTEUR SELECTIONNE (ex: Centre-Ville):
echo    📍 Secteur: [Centre-Ville]
echo    👤 Client: [-- Selectionner un client (3 dans ce secteur) --]
echo                1. Benali Fatima - Setrou
echo                2. Alami Mohammed - Setrou
echo                3. Tazi Aicha - Setrou
echo.
echo ✅ CHANGEMENT DE SECTEUR:
echo    - Le champ Client se vide automatiquement
echo    - Nouveaux clients du nouveau secteur apparaissent
echo    - Nombre de clients mis a jour
echo.
echo 🔧 EN CAS DE PROBLEME:
echo.
echo 1. Ouvrez la console du navigateur (F12)
echo 2. Regardez les logs:
echo    - 🔍 DEBUT fetchClientsDuSecteur
echo    - 👥 FILTRAGE RÉUSSI
echo    - 🎯 Le champ Client affiche maintenant X client(s)
echo.
echo 🌐 URLS DE TEST:
echo    - Application: http://localhost:19006
echo    - API Secteurs: http://localhost:4002/api/secteurs
echo    - API Clients Secteur 1: http://localhost:4002/api/secteurs/1/clients
echo    - API Clients Secteur 2: http://localhost:4002/api/secteurs/2/clients
echo.
echo ✅ FILTRAGE SECTEUR → CLIENT IMPLEMENTÉ !
echo    Testez maintenant la selection de secteur et
echo    verifiez que le champ Client se filtre automatiquement.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
