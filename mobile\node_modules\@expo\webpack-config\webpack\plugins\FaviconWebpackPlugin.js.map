{"version": 3, "file": "FaviconWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/FaviconWebpackPlugin.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAC1B,uCAA6E;AAC7E,qCAAyD;AAEzD,wFAAkG;AAElG,MAAqB,oBAAqB,SAAQ,iCAAuB;IACvE,YACU,UAAsD,EACtD,OAA2B;QAEnC,KAAK,EAAE,CAAC;QAHA,eAAU,GAAV,UAAU,CAA4C;QACtD,YAAO,GAAP,OAAO,CAAoB;IAGrC,CAAC;IAED,KAAK,CAAC,WAAW,CACf,QAAkB,EAClB,WAAwB,EACxB,IAAoB;QAEpB,MAAM,MAAM,GAAG,QAAQ,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC;QAEvE,SAAS,SAAS,CAAC,IAAY,EAAE,OAAe;YAC9C,MAAM,CAAC,GAAG,CAAC,eAAK,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,SAAS,UAAU,CAAC,IAAY,EAAE,OAAe;YAC/C,MAAM,CAAC,IAAI,CAAC,eAAK,CAAC,MAAM,CAAC,UAAU,IAAI,KAAK,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,UAAU,CAAC,SAAS,EAAE,sDAAsD,CAAC,CAAC;YAC9E,OAAO,IAAI,CAAC;SACb;QAED,MAAM,MAAM,GAAG,MAAM,IAAA,+BAAoB,EAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzE,MAAM,KAAK,GAAmB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,WAC7D,OAAA,MAAA,CAAC,CAAC,GAAG,0CAAE,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAA,EAAA,CACnC,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,MAAM,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC,GAAI,CAAC;YACvC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WACnC,OAAA,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAA,CAAC,CAAC,GAAG,0CAAE,QAAQ,CAAC,UAAU,CAAC,CAAA,EAAA,CACrE,CAAC;YACF,IAAI,aAAa,EAAE;gBACjB,SAAS,CACP,SAAS,EACT,2BAA2B,UAAU,CAAC,GAAG,IACvC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EACtD,QAAQ,CACT,CAAC;aACH;iBAAM;gBACL,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,iBAAO,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;gBACnF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACrC;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AArDD,uCAqDC", "sourcesContent": ["import chalk from 'chalk';\nimport { generateFaviconAsync, IconOptions, ProjectOptions } from 'expo-pwa';\nimport { Compilation, Compiler, sources } from 'webpack';\n\nimport ModifyHtmlWebpackPlugin, { HTMLLinkNode, HTMLPluginData } from './ModifyHtmlWebpackPlugin';\n\nexport default class FaviconWebpackPlugin extends ModifyHtmlWebpackPlugin {\n  constructor(\n    private pwaOptions: ProjectOptions & { links: HTMLLinkNode[] },\n    private favicon: IconOptions | null\n  ) {\n    super();\n  }\n\n  async modifyAsync(\n    compiler: Compiler,\n    compilation: Compilation,\n    data: HTMLPluginData\n  ): Promise<HTMLPluginData> {\n    const logger = compiler.getInfrastructureLogger('chrome-icons-plugin');\n\n    function logNotice(type: string, message: string) {\n      logger.log(chalk.magenta(`\\u203A ${type}: ${chalk.gray(message)}`));\n    }\n\n    function logWarning(type: string, message: string) {\n      logger.warn(chalk.yellow(`\\u203A ${type}: ${chalk.gray(message)}`));\n    }\n\n    if (!this.favicon) {\n      logWarning('Favicon', 'No template image found, skipping auto generation...');\n      return data;\n    }\n\n    const assets = await generateFaviconAsync(this.pwaOptions, this.favicon);\n\n    const links: HTMLLinkNode[] = this.pwaOptions.links.filter(v =>\n      v.rel?.split(' ').includes('icon')\n    );\n\n    for (const asset of assets) {\n      const { attributes = {} } = asset.tag!;\n      const faviconExists = links.some(v =>\n        v.sizes ? v.sizes === attributes.sizes : v.rel?.includes('shortcut')\n      );\n      if (faviconExists) {\n        logNotice(\n          'Favicon',\n          `Using custom <link rel=\"${attributes.rel}\"${\n            attributes.sizes ? ` sizes=\"${attributes.sizes}\"` : ''\n          } .../>`\n        );\n      } else {\n        compilation.emitAsset(asset.asset.path, new sources.RawSource(asset.asset.source));\n        data.assetTags.meta.push(asset.tag);\n      }\n    }\n    return data;\n  }\n}\n"]}