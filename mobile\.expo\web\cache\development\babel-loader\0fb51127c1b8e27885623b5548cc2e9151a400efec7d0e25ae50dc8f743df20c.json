{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"enabled\"],\n  _excluded2 = [\"enabled\", \"active\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nvar Screens;\ntry {\n  Screens = require('react-native-screens');\n} catch (e) {}\nexport var MaybeScreenContainer = function MaybeScreenContainer(_ref) {\n  var enabled = _ref.enabled,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  if (Screens != null) {\n    return React.createElement(Screens.ScreenContainer, _extends({\n      enabled: enabled\n    }, rest));\n  }\n  return React.createElement(View, rest);\n};\nexport var MaybeScreen = function MaybeScreen(_ref2) {\n  var enabled = _ref2.enabled,\n    active = _ref2.active,\n    rest = _objectWithoutProperties(_ref2, _excluded2);\n  if (Screens != null) {\n    return React.createElement(Screens.Screen, _extends({\n      enabled: enabled,\n      activityState: active\n    }, rest));\n  }\n  return React.createElement(View, rest);\n};", "map": {"version": 3, "names": ["React", "View", "Screens", "require", "e", "MaybeScreenContainer", "_ref", "enabled", "rest", "_objectWithoutProperties", "_excluded", "createElement", "ScreenContainer", "_extends", "MaybeScreen", "_ref2", "active", "_excluded2", "Screen", "activityState"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\stack\\src\\views\\Screens.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated, View, ViewProps } from 'react-native';\n\nlet Screens: typeof import('react-native-screens') | undefined;\n\ntry {\n  Screens = require('react-native-screens');\n} catch (e) {\n  // Ignore\n}\n\nexport const MaybeScreenContainer = ({\n  enabled,\n  ...rest\n}: ViewProps & {\n  enabled: boolean;\n  children: React.ReactNode;\n}) => {\n  if (Screens != null) {\n    return <Screens.ScreenContainer enabled={enabled} {...rest} />;\n  }\n\n  return <View {...rest} />;\n};\n\nexport const MaybeScreen = ({\n  enabled,\n  active,\n  ...rest\n}: ViewProps & {\n  enabled: boolean;\n  active: 0 | 1 | Animated.AnimatedInterpolation<0 | 1>;\n  children: React.ReactNode;\n  freezeOnBlur?: boolean;\n}) => {\n  if (Screens != null) {\n    return (\n      <Screens.Screen enabled={enabled} activityState={active} {...rest} />\n    );\n  }\n\n  return <View {...rest} />;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAG9B,IAAIC,OAA0D;AAE9D,IAAI;EACFA,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC3C,CAAC,CAAC,OAAOC,CAAC,EAAE,CACV;AAGF,OAAO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAGC,IAAA,EAM9B;EAAA,IALJC,OAAO,GAKRD,IAAA,CALCC,OAAO;IACJC,IAAA,GAAAC,wBAAA,CAIJH,IAAA,EAAAI,SAAA;EACC,IAAIR,OAAO,IAAI,IAAI,EAAE;IACnB,OAAOF,KAAA,CAAAW,aAAA,CAACT,OAAO,CAACU,eAAe,EAAAC,QAAA;MAACN,OAAO,EAAEA;IAAQ,GAAKC,IAAI,EAAI;EAChE;EAEA,OAAOR,KAAA,CAAAW,aAAA,CAACV,IAAI,EAAKO,IAAI,CAAI;AAC3B,CAAC;AAED,OAAO,IAAMM,WAAW,GAAG,SAAdA,WAAWA,CAAGC,KAAA,EASrB;EAAA,IARJR,OAAO,GAQRQ,KAAA,CARCR,OAAO;IACPS,MAAM,GAOPD,KAAA,CAPCC,MAAM;IACHR,IAAA,GAAAC,wBAAA,CAMJM,KAAA,EAAAE,UAAA;EACC,IAAIf,OAAO,IAAI,IAAI,EAAE;IACnB,OACEF,KAAA,CAAAW,aAAA,CAACT,OAAO,CAACgB,MAAM,EAAAL,QAAA;MAACN,OAAO,EAAEA,OAAQ;MAACY,aAAa,EAAEH;IAAO,GAAKR,IAAI,EAAI;EAEzE;EAEA,OAAOR,KAAA,CAAAW,aAAA,CAACV,IAAI,EAAKO,IAAI,CAAI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}