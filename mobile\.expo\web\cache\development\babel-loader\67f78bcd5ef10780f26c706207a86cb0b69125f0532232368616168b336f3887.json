{"ast": null, "code": "import TextInputState from \"../TextInputState\";\nvar dismissKeyboard = function dismissKeyboard() {\n  TextInputState.blurTextInput(TextInputState.currentlyFocusedField());\n};\nexport default dismissKeyboard;", "map": {"version": 3, "names": ["TextInputState", "dismissKeyboard", "blurTextInput", "currentlyFocusedField"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/modules/dismissKeyboard/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport TextInputState from '../TextInputState';\nvar dismissKeyboard = () => {\n  TextInputState.blurTextInput(TextInputState.currentlyFocusedField());\n};\nexport default dismissKeyboard;"], "mappings": "AASA,OAAOA,cAAc;AACrB,IAAIC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EAC1BD,cAAc,CAACE,aAAa,CAACF,cAAc,CAACG,qBAAqB,CAAC,CAAC,CAAC;AACtE,CAAC;AACD,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}