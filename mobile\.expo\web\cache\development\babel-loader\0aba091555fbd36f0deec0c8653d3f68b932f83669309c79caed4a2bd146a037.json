{"ast": null, "code": "export default function unmountComponentAtNode(rootTag) {\n  rootTag.unmount();\n  return true;\n}", "map": {"version": 3, "names": ["unmountComponentAtNode", "rootTag", "unmount"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/unmountComponentAtNode/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nexport default function unmountComponentAtNode(rootTag) {\n  rootTag.unmount();\n  return true;\n}"], "mappings": "AASA,eAAe,SAASA,sBAAsBA,CAACC,OAAO,EAAE;EACtDA,OAAO,CAACC,OAAO,CAAC,CAAC;EACjB,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}