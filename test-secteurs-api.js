// Test des API secteurs et clients par secteur
console.log('🧪 TEST DES API SECTEURS ET CLIENTS');
console.log('===================================');

async function testAPIs() {
  const baseUrl = 'http://localhost:4000';
  
  try {
    console.log('\n1. 🔍 Test API Secteurs...');
    const secteursResponse = await fetch(`${baseUrl}/api/secteurs`);
    const secteursData = await secteursResponse.json();
    
    if (secteursData.success) {
      console.log(`✅ ${secteursData.data.length} secteurs récupérés`);
      secteursData.data.forEach(secteur => {
        console.log(`   - ${secteur.nom} (ID: ${secteur.ids})`);
      });
      
      console.log('\n2. 🔍 Test API Clients par Secteur...');
      
      // Tester chaque secteur
      for (const secteur of secteursData.data) {
        console.log(`\n   📍 Secteur: ${secteur.nom} (ID: ${secteur.ids})`);
        
        const clientsResponse = await fetch(`${baseUrl}/api/secteurs/${secteur.ids}/clients`);
        const clientsData = await clientsResponse.json();
        
        if (clientsData.success) {
          console.log(`   ✅ ${clientsData.data.length} client(s) trouvé(s)`);
          
          clientsData.data.forEach(client => {
            console.log(`      - ${client.nom} ${client.prenom}`);
            console.log(`        📍 ${client.adresse}`);
            console.log(`        🗺️ Coordonnées: ${client.latitude?.toFixed(4)}, ${client.longitude?.toFixed(4)}`);
          });
          
          if (clientsData.data.length > 0) {
            // Générer l'URL Google Maps
            const secteurInfo = clientsData.secteur;
            const markers = clientsData.data.map(client => 
              `${client.latitude},${client.longitude}`
            ).join('/');
            
            const mapsUrl = `https://www.google.com/maps/dir/${secteurInfo.latitude},${secteurInfo.longitude}/${markers}`;
            console.log(`      🌐 URL Google Maps: ${mapsUrl}`);
          }
        } else {
          console.log(`   ❌ Erreur: ${clientsData.message}`);
        }
      }
      
      console.log('\n3. ✅ TESTS TERMINÉS AVEC SUCCÈS !');
      console.log('\n📋 Résumé:');
      console.log(`   - ${secteursData.data.length} secteurs disponibles`);
      console.log('   - API /api/secteurs fonctionne');
      console.log('   - API /api/secteurs/:id/clients fonctionne');
      console.log('   - Coordonnées GPS générées pour chaque client');
      console.log('   - URLs Google Maps générées');
      
    } else {
      console.log('❌ Erreur récupération secteurs:', secteursData.message);
    }
    
  } catch (error) {
    console.error('❌ Erreur lors des tests:', error.message);
    console.log('\n💡 Solutions possibles:');
    console.log('1. Vérifiez que le serveur est démarré sur le port 4000');
    console.log('2. Exécutez: node serveur-urgence.js');
    console.log('3. Testez manuellement: http://localhost:4000/api/secteurs');
  }
}

// Attendre un peu que le serveur démarre
setTimeout(() => {
  testAPIs();
}, 3000);
