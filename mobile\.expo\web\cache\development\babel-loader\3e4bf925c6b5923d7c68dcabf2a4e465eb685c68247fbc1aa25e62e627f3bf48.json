{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport { CodedError, Platform } from 'expo-modules-core';\nimport { KeepAwakeEventState } from \"./KeepAwake.types\";\nvar wakeLockMap = {};\nexport default {\n  isAvailableAsync: function () {\n    var _isAvailableAsync = _asyncToGenerator(function* () {\n      if (Platform.isDOMAvailable) {\n        return 'wakeLock' in navigator;\n      }\n      return false;\n    });\n    function isAvailableAsync() {\n      return _isAvailableAsync.apply(this, arguments);\n    }\n    return isAvailableAsync;\n  }(),\n  activate: function () {\n    var _activate = _asyncToGenerator(function* (tag) {\n      if (!Platform.isDOMAvailable) {\n        return;\n      }\n      var wakeLock = yield navigator.wakeLock.request('screen');\n      wakeLockMap[tag] = wakeLock;\n    });\n    function activate(_x) {\n      return _activate.apply(this, arguments);\n    }\n    return activate;\n  }(),\n  deactivate: function () {\n    var _deactivate = _asyncToGenerator(function* (tag) {\n      if (!Platform.isDOMAvailable) {\n        return;\n      }\n      if (wakeLockMap[tag]) {\n        var _wakeLockMap$tag$rele, _wakeLockMap$tag;\n        (_wakeLockMap$tag$rele = (_wakeLockMap$tag = wakeLockMap[tag]).release) == null ? void 0 : _wakeLockMap$tag$rele.call(_wakeLockMap$tag);\n        delete wakeLockMap[tag];\n      } else {\n        throw new CodedError('ERR_KEEP_AWAKE_TAG_INVALID', `The wake lock with tag ${tag} has not activated yet`);\n      }\n    });\n    function deactivate(_x2) {\n      return _deactivate.apply(this, arguments);\n    }\n    return deactivate;\n  }(),\n  addListenerForTag: function addListenerForTag(tag, listener) {\n    var eventListener = function eventListener() {\n      listener({\n        state: KeepAwakeEventState.RELEASE\n      });\n    };\n    var sentinel = wakeLockMap[tag];\n    if (sentinel) {\n      if ('addEventListener' in sentinel) {\n        sentinel.addEventListener == null ? void 0 : sentinel.addEventListener('release', eventListener);\n      } else {\n        sentinel.onrelease = eventListener;\n      }\n    }\n    return {\n      remove: function remove() {\n        var sentinel = wakeLockMap[tag];\n        if (sentinel) {\n          if (sentinel.removeEventListener) {\n            sentinel.removeEventListener('release', eventListener);\n          } else {\n            sentinel.onrelease = null;\n          }\n        }\n      }\n    };\n  }\n};", "map": {"version": 3, "names": ["CodedError", "Platform", "KeepAwakeEventState", "wakeLockMap", "isAvailableAsync", "_isAvailableAsync", "_asyncToGenerator", "isDOMAvailable", "navigator", "apply", "arguments", "activate", "_activate", "tag", "wakeLock", "request", "_x", "deactivate", "_deactivate", "_wakeLockMap$tag$rele", "_wakeLockMap$tag", "release", "call", "_x2", "addListenerForTag", "listener", "eventListener", "state", "RELEASE", "sentinel", "addEventListener", "onrelease", "remove", "removeEventListener"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\expo-keep-awake\\src\\ExpoKeepAwake.web.ts"], "sourcesContent": ["import { CodedError, Platform, Subscription } from 'expo-modules-core';\n\nimport { KeepAwakeEventState, KeepAwakeListener } from './KeepAwake.types';\n\nconst wakeLockMap: Record<string, WakeLockSentinel> = {};\n\ntype WakeLockSentinel = {\n  onrelease: null | ((event: any) => void);\n  released: boolean;\n  type: 'screen';\n  release?: Function;\n\n  addEventListener?: (event: string, listener: (event: any) => void) => void;\n  removeEventListener?: (event: string, listener: (event: any) => void) => void;\n};\n\ndeclare const navigator: {\n  wakeLock: {\n    request(type: 'screen'): Promise<WakeLockSentinel>;\n  };\n};\n\n/** Wraps the webWakeLock API https://developer.mozilla.org/en-US/docs/Web/API/Screen_Wake_Lock_API */\nexport default {\n  async isAvailableAsync() {\n    if (Platform.isDOMAvailable) {\n      return 'wakeLock' in navigator;\n    }\n    return false;\n  },\n  async activate(tag: string) {\n    if (!Platform.isDOMAvailable) {\n      return;\n    }\n    const wakeLock = await navigator.wakeLock.request('screen');\n    wakeLockMap[tag] = wakeLock;\n  },\n  async deactivate(tag: string) {\n    if (!Platform.isDOMAvailable) {\n      return;\n    }\n    if (wakeLockMap[tag]) {\n      wakeLockMap[tag].release?.();\n      delete wakeLockMap[tag];\n    } else {\n      throw new CodedError(\n        'ERR_KEEP_AWAKE_TAG_INVALID',\n        `The wake lock with tag ${tag} has not activated yet`\n      );\n    }\n  },\n  addListenerForTag(tag: string, listener: KeepAwakeListener): Subscription {\n    const eventListener = () => {\n      listener({ state: KeepAwakeEventState.RELEASE });\n    };\n    const sentinel = wakeLockMap[tag];\n    if (sentinel) {\n      if ('addEventListener' in sentinel) {\n        sentinel.addEventListener?.('release', eventListener);\n      } else {\n        sentinel.onrelease = eventListener;\n      }\n    }\n    return {\n      remove: () => {\n        const sentinel = wakeLockMap[tag];\n        if (sentinel) {\n          if (sentinel.removeEventListener) {\n            sentinel.removeEventListener('release', eventListener);\n          } else {\n            sentinel.onrelease = null;\n          }\n        }\n      },\n    };\n  },\n};\n"], "mappings": ";AAAA,SAASA,UAAU,EAAEC,QAAQ,QAAsB,mBAAmB;AAEtE,SAASC,mBAAmB;AAE5B,IAAMC,WAAW,GAAqC,EAAE;AAmBxD,eAAe;EACPC,gBAAgB;IAAA,IAAAC,iBAAA,GAAAC,iBAAA;MACpB,IAAIL,QAAQ,CAACM,cAAc,EAAE;QAC3B,OAAO,UAAU,IAAIC,SAAS;;MAEhC,OAAO,KAAK;IACd,CAAC;IAAA,SALKJ,gBAAgBA,CAAA;MAAA,OAAAC,iBAAA,CAAAI,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAhBN,gBAAgB;EAAA;EAMhBO,QAAQ;IAAA,IAAAC,SAAA,GAAAN,iBAAA,YAACO,GAAW;MACxB,IAAI,CAACZ,QAAQ,CAACM,cAAc,EAAE;QAC5B;;MAEF,IAAMO,QAAQ,SAASN,SAAS,CAACM,QAAQ,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC3DZ,WAAW,CAACU,GAAG,CAAC,GAAGC,QAAQ;IAC7B,CAAC;IAAA,SANKH,QAAQA,CAAAK,EAAA;MAAA,OAAAJ,SAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;IAAA,OAARC,QAAQ;EAAA;EAORM,UAAU;IAAA,IAAAC,WAAA,GAAAZ,iBAAA,YAACO,GAAW;MAC1B,IAAI,CAACZ,QAAQ,CAACM,cAAc,EAAE;QAC5B;;MAEF,IAAIJ,WAAW,CAACU,GAAG,CAAC,EAAE;QAAA,IAAAM,qBAAA,EAAAC,gBAAA;QACpB,CAAAD,qBAAA,IAAAC,gBAAA,GAAAjB,WAAW,CAACU,GAAG,CAAC,EAACQ,OAAO,qBAAxBF,qBAAA,CAAAG,IAAA,CAAAF,gBAA0B,CAAE;QAC5B,OAAOjB,WAAW,CAACU,GAAG,CAAC;OACxB,MAAM;QACL,MAAM,IAAIb,UAAU,CAClB,4BAA4B,EAC5B,0BAA0Ba,GAAG,wBAAwB,CACtD;;IAEL,CAAC;IAAA,SAbKI,UAAUA,CAAAM,GAAA;MAAA,OAAAL,WAAA,CAAAT,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAVO,UAAU;EAAA;EAchBO,iBAAiB,WAAjBA,iBAAiBA,CAACX,GAAW,EAAEY,QAA2B;IACxD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAQ;MACzBD,QAAQ,CAAC;QAAEE,KAAK,EAAEzB,mBAAmB,CAAC0B;MAAO,CAAE,CAAC;IAClD,CAAC;IACD,IAAMC,QAAQ,GAAG1B,WAAW,CAACU,GAAG,CAAC;IACjC,IAAIgB,QAAQ,EAAE;MACZ,IAAI,kBAAkB,IAAIA,QAAQ,EAAE;QAClCA,QAAQ,CAACC,gBAAgB,oBAAzBD,QAAQ,CAACC,gBAAgB,CAAG,SAAS,EAAEJ,aAAa,CAAC;OACtD,MAAM;QACLG,QAAQ,CAACE,SAAS,GAAGL,aAAa;;;IAGtC,OAAO;MACLM,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAO;QACX,IAAMH,QAAQ,GAAG1B,WAAW,CAACU,GAAG,CAAC;QACjC,IAAIgB,QAAQ,EAAE;UACZ,IAAIA,QAAQ,CAACI,mBAAmB,EAAE;YAChCJ,QAAQ,CAACI,mBAAmB,CAAC,SAAS,EAAEP,aAAa,CAAC;WACvD,MAAM;YACLG,QAAQ,CAACE,SAAS,GAAG,IAAI;;;MAG/B;KACD;EACH;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}