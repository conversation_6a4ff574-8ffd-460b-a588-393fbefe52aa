@echo off
title AquaTrack - Démarrage Mobile sur PC
color 0A

echo.
echo ========================================
echo    💧 AQUATRACK - MOBILE SUR PC 💧
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
echo ✅ Processus arrêtés

echo.
echo ⏳ 2. ATTENTE DE LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🖥️  3. DEMARRAGE DU BACKEND (Port 4000)...
start "Backend AquaTrack" cmd /k "title Backend AquaTrack && color 0B && echo ========================================== && echo    🖥️ SERVEUR BACKEND AQUATRACK && echo ========================================== && echo ✅ Port: 4000 && echo ✅ API: http://localhost:4000 && echo. && echo 🚀 Démarrage du serveur... && echo. && node simple-server.js"

echo ⏳ Attente du démarrage backend...
timeout /t 8 /nobreak >nul

echo.
echo 📱 4. DEMARRAGE DE L'APPLICATION MOBILE...
cd mobile
start "Mobile AquaTrack" cmd /k "title Mobile AquaTrack && color 0C && echo ========================================== && echo    📱 APPLICATION MOBILE AQUATRACK && echo ========================================== && echo ✅ Port: 19006 && echo ✅ URL: http://localhost:19006 && echo. && echo 🚀 Démarrage de l'application mobile... && echo. && npx expo start --web"

echo.
echo ⏳ 5. ATTENTE DU DEMARRAGE MOBILE...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 6. OUVERTURE DES NAVIGATEURS...
start http://localhost:4000
timeout /t 3 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ AQUATRACK DEMARRE AVEC SUCCES !
echo ========================================
echo.
echo 📡 Backend API: http://localhost:4000
echo 📱 Application Mobile: http://localhost:19006
echo.
echo 🔑 Comptes de test:
echo    - Technicien: <EMAIL> / Tech123
echo    - Admin: <EMAIL> / Admin123
echo.
echo 📋 Instructions:
echo    1. Le backend s'ouvre dans une fenêtre séparée
echo    2. L'application mobile s'ouvre dans une autre fenêtre
echo    3. Les navigateurs s'ouvrent automatiquement
echo    4. Utilisez les comptes de test pour vous connecter
echo.
echo ✅ Les deux services sont maintenant actifs !
echo.
echo Appuyez sur une touche pour fermer cette fenêtre...
pause > nul
