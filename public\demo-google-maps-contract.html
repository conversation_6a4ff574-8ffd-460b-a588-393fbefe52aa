<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Google Maps Contract</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #4285f4;
        }
        
        .form-group {
            margin-bottom: 20px;
            padding: 12px;
            border-radius: 8px;
            border-width: 1px;
            border-style: solid;
        }
        
        .form-group.contract {
            background: #fff8e1;
            border-color: #ff9800;
        }
        
        .contract-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .form-label {
            font-weight: bold;
            font-size: 16px;
            color: #ff9800;
        }
        
        .maps-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .maps-button:hover {
            background: #3367d6;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            background: #f9fafb;
            color: #6b7280;
        }
        
        .contract-info {
            font-size: 12px;
            color: #28a745;
            font-style: italic;
            margin-top: 5px;
            text-align: center;
        }
        
        .maps-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            border: 2px solid #4285f4;
        }
        
        .maps-title {
            font-size: 16px;
            font-weight: bold;
            color: #4285f4;
            margin-bottom: 12px;
            text-align: center;
        }
        
        .maps-wrapper {
            position: relative;
            height: 300px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .maps-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .maps-overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 10px;
            max-width: 200px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .contracts-list-title {
            font-size: 12px;
            font-weight: bold;
            color: #4285f4;
            margin-bottom: 8px;
        }
        
        .contract-item {
            margin-bottom: 8px;
            padding-bottom: 6px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .contract-item:last-child {
            border-bottom: none;
        }
        
        .contract-item-text {
            font-size: 11px;
            font-weight: 600;
            color: #333;
        }
        
        .contract-item-details {
            font-size: 10px;
            color: #666;
            margin-top: 2px;
        }
        
        .contract-item-coords {
            font-size: 9px;
            color: #888;
            margin-top: 1px;
        }
        
        .close-maps-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 10px;
            float: right;
        }
        
        .close-maps-button:hover {
            background: #c82333;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .feature-card.maps {
            border-color: #4285f4;
            background: #f8fbff;
        }
        
        .feature-card h4 {
            color: #4285f4;
            margin-bottom: 10px;
        }
        
        .coordinates-data {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        
        .coordinates-data h3 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .coord-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .coord-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid #4285f4;
        }
        
        .coord-client {
            font-weight: bold;
            color: #4285f4;
            margin-bottom: 8px;
        }
        
        .coord-contract {
            font-size: 0.9em;
            margin: 4px 0;
            padding: 4px 0;
            border-bottom: 1px solid #eee;
        }
        
        .coord-contract:last-child {
            border-bottom: none;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-maps {
            background: #4285f4;
            color: white;
        }
        
        .btn-maps:hover {
            background: #3367d6;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ Google Maps Contract</h1>
            <p>Démonstration du bouton Google Maps intégré au champ Contract</p>
        </div>
        
        <div class="content">
            <div class="demo-form">
                <h3 style="color: #4285f4; margin-bottom: 20px;">📱 Simulation du Champ Contract avec Google Maps</h3>
                
                <div class="form-group contract">
                    <div class="contract-header">
                        <label class="form-label">📄 Contrat *</label>
                        <button id="mapsButton" class="maps-button" onclick="toggleMaps()">
                            🗺️ Voir sur Google Maps
                        </button>
                    </div>
                    
                    <input 
                        type="text" 
                        class="form-input" 
                        value="Contrat 1 - QR: QR001"
                        readonly
                    >
                    <div class="contract-info">
                        ✅ Contrat unique sélectionné automatiquement
                    </div>
                </div>
                
                <div id="mapsContainer" class="maps-container" style="display: none;">
                    <div class="maps-title">
                        🗺️ Localisation des Contrats - Benali Fatima
                    </div>
                    
                    <div class="maps-wrapper">
                        <iframe
                            class="maps-iframe"
                            src="https://www.google.com/maps/embed/v1/view?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dw901SwHHqfeWM&center=33.5731,-7.5898&zoom=16&maptype=satellite"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade"
                            title="Localisation des Contrats"
                        ></iframe>
                        
                        <div class="maps-overlay">
                            <div class="contracts-list-title">
                                📄 Contrats sur la carte (1)
                            </div>
                            
                            <div class="contract-item">
                                <div class="contract-item-text">📍 Contrat 1</div>
                                <div class="contract-item-details">QR: QR001 | Sensus</div>
                                <div class="contract-item-coords">📍 33.5731, -7.5898</div>
                            </div>
                            
                            <button class="close-maps-button" onclick="toggleMaps()">
                                ✕ Fermer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="features-grid">
                <div class="feature-card maps">
                    <h4>🗺️ Intégration Google Maps</h4>
                    <ul style="line-height: 1.6; color: #666;">
                        <li>✅ Bouton à côté du champ Contract</li>
                        <li>✅ Affichage dans la même page</li>
                        <li>✅ Vue satellite avec zoom automatique</li>
                        <li>✅ Overlay avec détails des contrats</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>📄 Gestion des Contrats</h4>
                    <ul style="line-height: 1.6; color: #666;">
                        <li>✅ Bouton visible seulement si contrats</li>
                        <li>✅ Support client unique ou multiple</li>
                        <li>✅ Coordonnées GPS précises</li>
                        <li>✅ Informations QR et marque</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>🎯 Interface Utilisateur</h4>
                    <ul style="line-height: 1.6; color: #666;">
                        <li>✅ Bouton toggle (Voir/Masquer)</li>
                        <li>✅ Overlay informatif</li>
                        <li>✅ Bouton fermer intégré</li>
                        <li>✅ Design responsive</li>
                    </ul>
                </div>
            </div>
            
            <div class="coordinates-data">
                <h3>📍 Coordonnées GPS des Contrats</h3>
                <div class="coord-grid">
                    <div class="coord-card">
                        <div class="coord-client">👤 Benali Fatima</div>
                        <div class="coord-contract">📄 Contrat 1 - QR: QR001<br>📍 33.5731, -7.5898 (Centre-Ville)</div>
                    </div>
                    
                    <div class="coord-card">
                        <div class="coord-client">👤 Alami Mohammed</div>
                        <div class="coord-contract">📄 Contrat 2 - QR: QR002<br>📍 33.5735, -7.5895 (Centre-Ville)</div>
                    </div>
                    
                    <div class="coord-card">
                        <div class="coord-client">👤 Benjelloun Youssef</div>
                        <div class="coord-contract">📄 Contrat 4 - QR: QR004<br>📍 33.5831, -7.5998 (Industriel)</div>
                        <div class="coord-contract">📄 Contrat 5 - QR: QR005<br>📍 33.5835, -7.5995 (Industriel)</div>
                    </div>
                    
                    <div class="coord-card">
                        <div class="coord-client">👤 Lahlou Khadija</div>
                        <div class="coord-contract">📄 Contrat 6 - QR: QR006<br>📍 33.5840, -7.5990 (Industriel)</div>
                    </div>
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-primary" target="_blank">
                    📱 Tester l'Application Réelle
                </a>
                <button class="btn btn-maps" onclick="simulateMultipleContracts()">
                    🧪 Simuler Plusieurs Contrats
                </button>
            </div>
            
            <div style="background: #d4edda; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #28a745;">
                <h3 style="color: #155724; margin-bottom: 15px;">✅ Google Maps Intégré</h3>
                <ul style="color: #155724; line-height: 1.6;">
                    <li>✅ <strong>Bouton conditionnel :</strong> Visible seulement si client avec contrats</li>
                    <li>✅ <strong>Affichage intégré :</strong> Google Maps dans la même page</li>
                    <li>✅ <strong>Centrage automatique :</strong> Sur les coordonnées du/des contrat(s)</li>
                    <li>✅ <strong>Overlay informatif :</strong> Détails des contrats avec QR et marque</li>
                    <li>✅ <strong>Contrôles intuitifs :</strong> Boutons Voir/Masquer et Fermer</li>
                    <li>✅ <strong>Vue satellite :</strong> Zoom 16 pour une vue détaillée</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        let mapsVisible = false;
        
        function toggleMaps() {
            const mapsContainer = document.getElementById('mapsContainer');
            const mapsButton = document.getElementById('mapsButton');
            
            mapsVisible = !mapsVisible;
            
            if (mapsVisible) {
                mapsContainer.style.display = 'block';
                mapsButton.textContent = '🗺️ Masquer Google Maps';
            } else {
                mapsContainer.style.display = 'none';
                mapsButton.textContent = '🗺️ Voir sur Google Maps';
            }
        }
        
        function simulateMultipleContracts() {
            // Simuler un client avec plusieurs contrats
            const mapsContainer = document.getElementById('mapsContainer');
            const title = mapsContainer.querySelector('.maps-title');
            const contractsList = mapsContainer.querySelector('.contracts-list-title');
            const overlay = mapsContainer.querySelector('.maps-overlay');
            
            title.textContent = '🗺️ Localisation des Contrats - Benjelloun Youssef';
            contractsList.textContent = '📄 Contrats sur la carte (2)';
            
            // Remplacer le contenu de l'overlay
            const contractsHtml = `
                <div class="contracts-list-title">📄 Contrats sur la carte (2)</div>
                <div class="contract-item">
                    <div class="contract-item-text">📍 Contrat 4</div>
                    <div class="contract-item-details">QR: QR004 | Elster</div>
                    <div class="contract-item-coords">📍 33.5831, -7.5998</div>
                </div>
                <div class="contract-item">
                    <div class="contract-item-text">📍 Contrat 5</div>
                    <div class="contract-item-details">QR: QR005 | Itron</div>
                    <div class="contract-item-coords">📍 33.5835, -7.5995</div>
                </div>
                <button class="close-maps-button" onclick="toggleMaps()">✕ Fermer</button>
            `;
            
            overlay.innerHTML = contractsHtml;
            
            // Afficher la carte si elle n'est pas visible
            if (!mapsVisible) {
                toggleMaps();
            }
            
            alert('Simulation : Client avec 2 contrats\n\nLa carte affiche maintenant :\n- Benjelloun Youssef\n- 2 contrats dans l\'overlay\n- Coordonnées des deux contrats');
        }
    </script>
</body>
</html>
