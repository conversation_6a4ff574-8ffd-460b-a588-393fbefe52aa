{"version": 3, "file": "createAllLoaders.js", "sourceRoot": "", "sources": ["../../src/loaders/createAllLoaders.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,mCAAiC;AACjC,sFAA2D;AAG3D,4EAAoD;AACpD,gCAA6D;AAG7D,MAAM,kBAAkB,GAAG,IAAA,gBAAO,EAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;AAE/D,0FAA0F;AAC1F,0FAA0F;AAC1F,gCAAgC;AAChC,EAAE;AACF,mFAAmF;AACnF,yFAAyF;AACzF,mDAAmD;AACnD,MAAM,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,MAAM,EAAE,EAAE,CAAC,CAAC;AAEzF,8DAA8D;AAC9D,oCAAoC;AACvB,QAAA,mBAAmB,GAAgB;IAC9C,IAAI,EAAE,CAAC,SAAS,CAAC;IACjB,IAAI,EAAE,OAAO;IACb,QAAQ,EAAE,YAAY;IACtB,MAAM,EAAE;QACN,gBAAgB,EAAE;YAChB,OAAO,EAAE,oBAAoB;SAC9B;KACF;CACF,CAAC;AAEF;;;;;;;GAOG;AACH,wBAAwB;AACX,QAAA,eAAe,GAAgB;IAC1C,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAC1D,IAAI,EAAE,OAAO;IACb,MAAM,EAAE;QACN,gBAAgB,EAAE;YAChB,OAAO,EAAE,oBAAoB;SAC9B;KACF;CACF,CAAC;AAEF;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAgB;IAC7C,gEAAgE;IAChE,uEAAuE;IACvE,kEAAkE;IAClE,gCAAgC;IAChC,OAAO,EAAE,CAAC,IAAI,EAAE,wBAAwB,EAAE,SAAS,EAAE,SAAS,CAAC;IAC/D,IAAI,EAAE,gBAAgB;CACvB,CAAC;AAEF;;;;GAIG;AACU,QAAA,eAAe,GAAgB;IAC1C,IAAI,EAAE,UAAU;IAChB,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;CACtE,CAAC;AAEF,SAAS,sBAAsB;IAC7B,OAAO,YAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;AACxC,CAAC;AAED;;;;;GAKG;AACH,SAAwB,gBAAgB,CACtC,GAA8F;IAE9F,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,IAAI,sBAAsB,EAAE,CAAC;IAC9D,aAAa;IACb,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,IAAI,IAAA,eAAS,EAAC,GAAG,CAAC,CAAC;IAC1C,aAAa;IACb,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,IAAA,cAAQ,EAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IAEhE,MAAM,QAAQ,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAE3D,IAAI,QAAQ,EAAE;QACZ,yCAAyC;QACzC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;KAClC;IAED,MAAM,gBAAgB,GAAG,GAAG,CAAC,IAAI,KAAK,aAAa,CAAC;IACpD,MAAM,eAAe,GAAG,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC;IAClD,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,IAAA,oBAAc,EAAC,GAAG,CAAC,CAAC;IAE5D,uCAAuC;IACvC,MAAM,eAAe,GAAG,CAAC,UAAkC,EAAE,EAAE;QAC7D,MAAM,OAAO,GAAG;YACd,gBAAgB,IAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC;YACnD,eAAe,IAAI;gBACjB,MAAM,EAAE,iCAAoB,CAAC,MAAM;gBACnC,2EAA2E;gBAC3E,+DAA+D;gBAC/D,OAAO,EAAE,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;aACzE;YACD;gBACE,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;gBACrC,OAAO,EAAE,UAAU;aACpB;SACF,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAElB,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IAEF,OAAO;QACL,2BAAmB;QACnB,uBAAe;QACf,kBAAkB,CAAC,GAAG,CAAC;QACvB;YACE,IAAI,EAAE,UAAU;YAChB,GAAG,EAAE,eAAe,CAAC;gBACnB,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB;gBAClE,OAAO,EAAE;oBACP,IAAI,EAAE,MAAM;iBACb;aACF,CAAC;YACF,mDAAmD;YACnD,qDAAqD;YACrD,gEAAgE;YAChE,qDAAqD;YACrD,WAAW,EAAE,IAAI;SAClB;QACD,mCAAmC;QACnC,0BAAkB;KACnB,CAAC,MAAM,CAAC,OAAO,CAAkB,CAAC;AACrC,CAAC;AA7DD,mCA6DC;AAED;;;;;;GAMG;AACH,SAAgB,kBAAkB,CAChC,GAA8F;;IAE9F,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,IAAI,sBAAsB,EAAE,CAAC;IAC9D,aAAa;IACb,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,IAAI,IAAA,eAAS,EAAC,GAAG,CAAC,CAAC;IAE1C,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,IAAA,cAAQ,EAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IAEhE,MAAM,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAEhE,wDAAwD;IACxD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC;IAEnD,MAAM,gBAAgB,GAAG,IAAI,IAAI,GAAG,CAAC,WAAW,CAAC;IAEjD,OAAO,IAAA,2BAAiB,EAAC;QACvB,WAAW,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI;QAC/B,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,QAAQ,EAAE,GAAG,CAAC,QAAQ;QACtB,gBAAgB;QAChB,OAAO;QACP,OAAO,EAAE,CAAC,GAAG,OAAO,EAAE,GAAG,CAAC,CAAA,MAAA,GAAG,CAAC,KAAK,0CAAE,oCAAoC,KAAI,EAAE,CAAC,CAAC;QACjF,GAAG;KACJ,CAAC,CAAC;AACL,CAAC;AAzBD,gDAyBC", "sourcesContent": ["import fs from 'fs';\nimport { boolish } from 'getenv';\nimport MiniCssExtractPlugin from 'mini-css-extract-plugin';\nimport { RuleSetRule } from 'webpack';\n\nimport createBabelLoader from './createBabelLoader';\nimport { getConfig, getPaths, getPublicPaths } from '../env';\nimport { Environment } from '../types';\n\nconst shouldUseSourceMap = boolish('GENERATE_SOURCEMAP', true);\n\n// Inline resources as Base64 when there is less reason to parallelize their download. The\n// heuristic we use is whether the resource would fit within a TCP/IP packet that we would\n// send to request the resource.\n//\n// An Ethernet MTU is usually 1500. IP headers are 20 (v4) or 40 (v6) bytes and TCP\n// headers are 40 bytes. HTTP response headers vary and are around 400 bytes. This leaves\n// about 1000 bytes for content to fit in a packet.\nconst imageInlineSizeLimit = parseInt(process.env.IMAGE_INLINE_SIZE_LIMIT || '1000', 10);\n\n// TODO: Merge this config once `image/avif` is in the mime-db\n// https://github.com/jshttp/mime-db\nexport const avifImageLoaderRule: RuleSetRule = {\n  test: [/\\.avif$/],\n  type: 'asset',\n  mimetype: 'image/avif',\n  parser: {\n    dataUrlCondition: {\n      maxSize: imageInlineSizeLimit,\n    },\n  },\n};\n\n/**\n * This is needed for webpack to import static images in JavaScript files.\n * \"url\" loader works like \"file\" loader except that it embeds assets\n * smaller than specified limit in bytes as data URLs to avoid requests.\n * A missing `test` is equivalent to a match.\n *\n * @category loaders\n */\n// TODO: Bacon: Move SVG\nexport const imageLoaderRule: RuleSetRule = {\n  test: [/\\.bmp$/, /\\.gif$/, /\\.jpe?g$/, /\\.png$/, /\\.svg$/],\n  type: 'asset',\n  parser: {\n    dataUrlCondition: {\n      maxSize: imageInlineSizeLimit,\n    },\n  },\n};\n\n/**\n * \"file\" loader makes sure those assets get served by WebpackDevServer.\n * When you `import` an asset, you get its (virtual) filename.\n * In production, they would get copied to the `build` folder.\n * This loader doesn't use a \"test\" so it will catch all modules\n * that fall through the other loaders.\n *\n * @category loaders\n */\nexport const fallbackLoaderRule: RuleSetRule = {\n  // Exclude `js` files to keep \"css\" loader working as it injects\n  // its runtime that would otherwise be processed through \"file\" loader.\n  // Also exclude `html` and `json` extensions so they get processed\n  // by webpacks internal loaders.\n  exclude: [/^$/, /\\.(js|mjs|jsx|ts|tsx)$/, /\\.html$/, /\\.json$/],\n  type: 'asset/resource',\n};\n\n/**\n * Default CSS loader.\n *\n * @category loaders\n */\nexport const styleLoaderRule: RuleSetRule = {\n  test: /\\.(css)$/,\n  use: [require.resolve('style-loader'), require.resolve('css-loader')],\n};\n\nfunction getPossibleProjectRoot(): string {\n  return fs.realpathSync(process.cwd());\n}\n\n/**\n * Create the fallback loader for parsing any unhandled file type.\n *\n * @param env\n * @category loaders\n */\nexport default function createAllLoaders(\n  env: Pick<Environment, 'projectRoot' | 'locations' | 'mode' | 'config' | 'platform' | 'babel'>\n): RuleSetRule[] {\n  env.projectRoot = env.projectRoot || getPossibleProjectRoot();\n  // @ts-ignore\n  env.config = env.config || getConfig(env);\n  // @ts-ignore\n  env.locations = env.locations || getPaths(env.projectRoot, env);\n\n  const isNative = ['ios', 'android'].includes(env.platform);\n\n  if (isNative) {\n    // TODO: Support fallback loader + assets\n    return [getBabelLoaderRule(env)];\n  }\n\n  const isEnvDevelopment = env.mode === 'development';\n  const isEnvProduction = env.mode === 'production';\n  const { publicPath: publicUrlOrPath } = getPublicPaths(env);\n\n  // common function to get style loaders\n  const getStyleLoaders = (cssOptions: RuleSetRule['options']) => {\n    const loaders = [\n      isEnvDevelopment && require.resolve('style-loader'),\n      isEnvProduction && {\n        loader: MiniCssExtractPlugin.loader,\n        // css is located in `static/css`, use '../../' to locate index.html folder\n        // in production `paths.publicUrlOrPath` can be a relative path\n        options: publicUrlOrPath.startsWith('.') ? { publicPath: '../../' } : {},\n      },\n      {\n        loader: require.resolve('css-loader'),\n        options: cssOptions,\n      },\n    ].filter(Boolean);\n\n    return loaders;\n  };\n\n  return [\n    avifImageLoaderRule,\n    imageLoaderRule,\n    getBabelLoaderRule(env),\n    {\n      test: /\\.(css)$/,\n      use: getStyleLoaders({\n        importLoaders: 1,\n        sourceMap: isEnvProduction ? shouldUseSourceMap : isEnvDevelopment,\n        modules: {\n          mode: 'icss',\n        },\n      }),\n      // Don't consider CSS imports dead code even if the\n      // containing package claims to have no side effects.\n      // Remove this when webpack adds a warning or an error for this.\n      // See https://github.com/webpack/webpack/issues/6571\n      sideEffects: true,\n    },\n    // This needs to be the last loader\n    fallbackLoaderRule,\n  ].filter(Boolean) as RuleSetRule[];\n}\n\n/**\n * Creates a Rule for loading application code and packages that work with the Expo ecosystem.\n * This method attempts to emulate how Metro loads ES modules in the `node_modules` folder.\n *\n * @param env partial Environment object.\n * @category loaders\n */\nexport function getBabelLoaderRule(\n  env: Pick<Environment, 'projectRoot' | 'config' | 'locations' | 'mode' | 'platform' | 'babel'>\n): RuleSetRule {\n  env.projectRoot = env.projectRoot || getPossibleProjectRoot();\n  // @ts-ignore\n  env.config = env.config || getConfig(env);\n\n  env.locations = env.locations || getPaths(env.projectRoot, env);\n\n  const { web: { build: { babel = {} } = {} } = {} } = env.config;\n\n  // TODO: deprecate app.json method in favor of env.babel\n  const { root, verbose, include = [], use } = babel;\n\n  const babelProjectRoot = root || env.projectRoot;\n\n  return createBabelLoader({\n    projectRoot: env.locations.root,\n    mode: env.mode,\n    platform: env.platform,\n    babelProjectRoot,\n    verbose,\n    include: [...include, ...(env.babel?.dangerouslyAddModulePathsToTranspile || [])],\n    use,\n  });\n}\n"]}