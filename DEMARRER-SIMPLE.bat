@echo off
echo ========================================
echo    DEMARRAGE SIMPLE AQUATRACK
echo ========================================
echo.

echo 1. Demarrage du backend...
start "Backend AquaTrack" cmd /k "node simple-server.js"

echo.
echo 2. Attente de 10 secondes...
timeout /t 10 /nobreak

echo.
echo 3. Demarrage de l'application mobile...
start "Mobile AquaTrack" cmd /k "cd mobile && npx expo start --web"

echo.
echo 4. Attente de 15 secondes...
timeout /t 15 /nobreak

echo.
echo 5. Ouverture des navigateurs...
start http://localhost:4000
start http://localhost:19006

echo.
echo ========================================
echo    AQUATRACK DEMARRE !
echo ========================================
echo.
echo Backend: http://localhost:4000
echo Mobile: http://localhost:19006
echo.
echo Comptes de test:
echo - <EMAIL> / Tech123
echo - <EMAIL> / Admin123
echo.
pause
