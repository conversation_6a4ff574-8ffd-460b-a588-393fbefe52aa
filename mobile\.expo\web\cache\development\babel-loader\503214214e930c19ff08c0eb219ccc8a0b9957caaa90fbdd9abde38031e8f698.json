{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _excluded = [\"enabled\", \"onValueChange\", \"selectedValue\", \"itemStyle\", \"mode\", \"prompt\", \"dropdownIconColor\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport unstable_createElement from \"react-native-web/dist/exports/createElement\";\nimport { forwardRef } from 'react';\nimport PickerItem from \"./PickerItem\";\nvar Select = forwardRef(function (props, forwardedRef) {\n  return unstable_createElement('select', _objectSpread(_objectSpread({}, props), {}, {\n    ref: forwardedRef\n  }));\n});\nvar Picker = forwardRef(function (props, forwardedRef) {\n  var enabled = props.enabled,\n    onValueChange = props.onValueChange,\n    selectedValue = props.selectedValue,\n    itemStyle = props.itemStyle,\n    mode = props.mode,\n    prompt = props.prompt,\n    dropdownIconColor = props.dropdownIconColor,\n    other = _objectWithoutProperties(props, _excluded);\n  var handleChange = React.useCallback(function (e) {\n    var _e$target = e.target,\n      selectedIndex = _e$target.selectedIndex,\n      value = _e$target.value;\n    if (onValueChange) {\n      onValueChange(value, selectedIndex);\n    }\n  }, [onValueChange]);\n  return (React.createElement(Select, _extends({\n      disabled: enabled === false ? true : undefined,\n      onChange: handleChange,\n      ref: forwardedRef,\n      value: selectedValue\n    }, other))\n  );\n});\nPicker.Item = PickerItem;\nexport default Picker;", "map": {"version": 3, "names": ["React", "unstable_createElement", "forwardRef", "PickerItem", "Select", "props", "forwardedRef", "_objectSpread", "ref", "Picker", "enabled", "onValueChange", "selected<PERSON><PERSON><PERSON>", "itemStyle", "mode", "prompt", "dropdownIconColor", "other", "_objectWithoutProperties", "_excluded", "handleChange", "useCallback", "e", "_e$target", "target", "selectedIndex", "value", "createElement", "_extends", "disabled", "undefined", "onChange", "<PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-native-picker\\picker\\js\\Picker.web.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * @flow\n *\n */\n\nimport * as React from 'react';\nimport {unstable_createElement} from 'react-native-web';\nimport {forwardRef} from 'react';\nimport type {ViewProps} from 'react-native-web/src/exports/View/types';\nimport type {GenericStyleProp} from 'react-native-web/src/types';\nimport type {TextStyle} from 'react-native-web/src/exports/Text/types';\nimport PickerItem from './PickerItem';\n\ntype PickerProps = {\n  ...ViewProps,\n  children?: typeof PickerItem | Array<typeof PickerItem>,\n  enabled?: boolean,\n  onValueChange?: (number | string, number) => void,\n  selectedValue?: number | string,\n  /**\n   * dropdownIconColor\n   * Not used for Web.\n   */\n  dropdownIconColor?: string,\n  /* compat */\n  itemStyle?: GenericStyleProp<TextStyle>,\n  mode?: string,\n  prompt?: string,\n};\n\nconst Select = forwardRef((props: $FlowFixMe, forwardedRef: $FlowFixMe) =>\n  unstable_createElement('select', {\n    ...props,\n    ref: forwardedRef,\n  }),\n);\n\nconst Picker: React$AbstractComponent<PickerProps, empty> = forwardRef<\n  PickerProps,\n  $FlowFixMe,\n>((props, forwardedRef) => {\n  const {\n    enabled,\n    onValueChange,\n    selectedValue,\n    itemStyle,\n    mode,\n    prompt,\n    dropdownIconColor,\n    ...other\n  } = props;\n\n  const handleChange = React.useCallback<any>(\n    (e: Object) => {\n      const {selectedIndex, value} = e.target;\n      if (onValueChange) {\n        onValueChange(value, selectedIndex);\n      }\n    },\n    [onValueChange],\n  );\n\n  return (\n    // $FlowFixMe\n    <Select\n      disabled={enabled === false ? true : undefined}\n      onChange={handleChange}\n      ref={forwardedRef}\n      value={selectedValue}\n      {...other}\n    />\n  );\n});\n\n// $FlowFixMe\nPicker.Item = PickerItem;\n\nexport default Picker;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAOA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,sBAAA;AAE9B,SAAQC,UAAU,QAAO,OAAO;AAIhC,OAAOC,UAAU;AAmBjB,IAAMC,MAAM,GAAGF,UAAU,CAAC,UAACG,KAAiB,EAAEC,YAAwB;EAAA,OACpEL,sBAAsB,CAAC,QAAQ,EAAAM,aAAA,CAAAA,aAAA,KAC1BF,KAAK;IACRG,GAAG,EAAEF;EAAA,EACN,CACH;AAAA,EAAC;AAED,IAAMG,MAAmD,GAAGP,UAAU,CAGpE,UAACG,KAAK,EAAEC,YAAY,EAAK;EACzB,IACEI,OAAO,GAQLL,KAAK,CARPK,OAAO;IACPC,aAAa,GAOXN,KAAK,CAPPM,aAAa;IACbC,aAAa,GAMXP,KAAK,CANPO,aAAa;IACbC,SAAS,GAKPR,KAAK,CALPQ,SAAS;IACTC,IAAI,GAIFT,KAAK,CAJPS,IAAI;IACJC,MAAM,GAGJV,KAAK,CAHPU,MAAM;IACNC,iBAAiB,GAEfX,KAAK,CAFPW,iBAAiB;IACdC,KAAA,GAAAC,wBAAA,CACDb,KAAK,EAAAc,SAAA;EAET,IAAMC,YAAY,GAAGpB,KAAK,CAACqB,WAAW,CACnC,UAAAC,CAAS,EAAK;IACb,IAAAC,SAAA,GAA+BD,CAAC,CAACE,MAAM;MAAhCC,aAAa,GAAAF,SAAA,CAAbE,aAAa;MAAEC,KAAA,GAAAH,SAAA,CAAAG,KAAA;IACtB,IAAIf,aAAa,EAAE;MACjBA,aAAa,CAACe,KAAK,EAAED,aAAa,CAAC;IACrC;EACF,CAAC,EACD,CAACd,aAAa,CAChB,CAAC;EAED,QAEEX,KAAA,CAAA2B,aAAA,CAACvB,MAAM,EAAAwB,QAAA;MACLC,QAAQ,EAAEnB,OAAO,KAAK,KAAK,GAAG,IAAI,GAAGoB,SAAU;MAC/CC,QAAQ,EAAEX,YAAa;MACvBZ,GAAG,EAAEF,YAAa;MAClBoB,KAAK,EAAEd;IAAc,GACjBK,KAAK,CACV;EAAA;AAEL,CAAC,CAAC;AAGFR,MAAM,CAACuB,IAAI,GAAG7B,UAAU;AAExB,eAAeM,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}