import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';

const DemoFormScreen = ({ navigation }) => {
  const [secteurs, setSecteurs] = useState([]);
  const [selectedSecteur, setSelectedSecteur] = useState('');
  const [clients, setClients] = useState([]);
  const [selectedClient, setSelectedClient] = useState('');

  const API_BASE_URL = 'http://localhost:4000';

  useEffect(() => {
    fetchSecteurs();
  }, []);

  const fetchSecteurs = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/secteurs`);
      const data = await response.json();
      if (data.success) {
        setSecteurs(data.data);
      }
    } catch (error) {
      console.error('Erreur récupération secteurs:', error);
    }
  };

  const fetchClientsBySecteur = async (secteurId) => {
    if (!secteurId) return;
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/secteurs/${secteurId}/clients`);
      const data = await response.json();
      
      if (data.success) {
        setClients(data.data);
      }
    } catch (error) {
      console.error('Erreur récupération clients:', error);
    }
  };

  const handleSecteurChange = (secteurId) => {
    setSelectedSecteur(secteurId);
    setClients([]);
    setSelectedClient('');
    
    if (secteurId) {
      fetchClientsBySecteur(secteurId);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* En-tête */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>← Retour</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>📋 Démonstration Ordre des Champs</Text>
          <Text style={styles.headerSubtitle}>
            Nouveau formulaire avec champ Client en 2ème position
          </Text>
        </View>

        {/* Instructions */}
        <View style={styles.instructionsCard}>
          <Text style={styles.instructionsTitle}>📋 Ordre des Champs</Text>
          <Text style={styles.instructionsText}>
            1️⃣ Secteur (obligatoire){'\n'}
            2️⃣ Client (obligatoire) ← NOUVEAU !{'\n'}
            3️⃣ Informations du Client ← NOUVEAU !{'\n'}
            4️⃣ Carte du Secteur{'\n'}
            5️⃣ Période{'\n'}
            6️⃣ Consommation...
          </Text>
        </View>

        {/* Formulaire de démonstration */}
        <View style={styles.form}>
          {/* 1er champ : Secteur */}
          <View style={styles.formGroup}>
            <View style={styles.fieldHeader}>
              <Text style={styles.fieldNumber}>1️⃣</Text>
              <Text style={styles.label}>🗺️ Secteur *</Text>
            </View>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={selectedSecteur}
                onValueChange={handleSecteurChange}
                style={styles.picker}
              >
                <Picker.Item label="-- Sélectionnez un secteur --" value="" />
                {secteurs.map((secteur) => (
                  <Picker.Item
                    key={secteur.ids}
                    label={secteur.nom}
                    value={secteur.ids.toString()}
                  />
                ))}
              </Picker>
            </View>
          </View>

          {/* 2ème champ : Client */}
          <View style={styles.formGroup}>
            <View style={styles.fieldHeader}>
              <Text style={styles.fieldNumber}>2️⃣</Text>
              <Text style={styles.label}>👤 Client *</Text>
              <Text style={styles.newBadge}>NOUVEAU</Text>
            </View>
            {selectedSecteur ? (
              clients.length > 0 ? (
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={selectedClient}
                    onValueChange={setSelectedClient}
                    style={styles.picker}
                  >
                    <Picker.Item label="-- Sélectionnez un client --" value="" />
                    {clients.map((client, index) => (
                      <Picker.Item
                        key={client.idclient}
                        label={`${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`}
                        value={client.idclient.toString()}
                      />
                    ))}
                  </Picker>
                </View>
              ) : (
                <View style={styles.noDataContainer}>
                  <Text style={styles.noDataText}>
                    Aucun client dans ce secteur
                  </Text>
                </View>
              )
            ) : (
              <View style={styles.disabledContainer}>
                <Text style={styles.disabledText}>
                  Sélectionnez d'abord un secteur
                </Text>
              </View>
            )}
          </View>

          {/* 3ème champ : Informations du Client */}
          {selectedClient && (
            <View style={styles.formGroup}>
              <View style={styles.fieldHeader}>
                <Text style={styles.fieldNumber}>3️⃣</Text>
                <Text style={styles.label}>ℹ️ Informations du Client</Text>
                <Text style={styles.newBadge}>NOUVEAU</Text>
              </View>
              <View style={styles.clientInfoContainer}>
                {(() => {
                  const clientInfo = clients.find(c => c.idclient.toString() === selectedClient);
                  return clientInfo ? (
                    <>
                      <Text style={styles.clientInfoName}>
                        👤 {clientInfo.nom} {clientInfo.prenom}
                      </Text>
                      <Text style={styles.clientInfoAddress}>
                        🏠 {clientInfo.adresse}, {clientInfo.ville}
                      </Text>
                      <Text style={styles.clientInfoContact}>
                        📞 {clientInfo.tel} | 📧 {clientInfo.email}
                      </Text>
                      <Text style={styles.clientInfoCoords}>
                        📍 GPS: {clientInfo.latitude?.toFixed(6)}, {clientInfo.longitude?.toFixed(6)}
                      </Text>
                    </>
                  ) : null;
                })()}
              </View>
            </View>
          )}

          {/* 4ème champ : Carte */}
          <View style={styles.formGroup}>
            <View style={styles.fieldHeader}>
              <Text style={styles.fieldNumber}>4️⃣</Text>
              <Text style={styles.label}>🗺️ Carte du Secteur</Text>
            </View>
            <View style={styles.mapPlaceholder}>
              <Text style={styles.mapPlaceholderText}>
                {selectedSecteur ? 
                  `Carte du secteur avec ${clients.length} client(s)` : 
                  'Sélectionnez un secteur pour voir la carte'
                }
              </Text>
            </View>
          </View>

          {/* 5ème champ : Période */}
          <View style={styles.formGroup}>
            <View style={styles.fieldHeader}>
              <Text style={styles.fieldNumber}>5️⃣</Text>
              <Text style={styles.label}>📅 Période (YYYY-MM) *</Text>
            </View>
            <View style={styles.inputPlaceholder}>
              <Text style={styles.inputPlaceholderText}>2024-01</Text>
            </View>
          </View>

          {/* 6ème champ : Consommation */}
          <View style={styles.formGroup}>
            <View style={styles.fieldHeader}>
              <Text style={styles.fieldNumber}>6️⃣</Text>
              <Text style={styles.label}>💧 Consommation Actuelle (m³) *</Text>
            </View>
            <View style={styles.inputPlaceholder}>
              <Text style={styles.inputPlaceholderText}>Saisir la consommation...</Text>
            </View>
          </View>
        </View>

        {/* Résumé */}
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>📊 Résumé des Modifications</Text>
          <Text style={styles.summaryText}>
            ✅ Champ "Client" ajouté en 2ème position{'\n'}
            ✅ Sélection automatique selon le secteur{'\n'}
            ✅ Affichage des informations détaillées{'\n'}
            ✅ Intégration avec la carte existante{'\n'}
            ✅ Messages d'aide contextuels
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 20,
    paddingTop: 10,
  },
  backButton: {
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 15,
  },
  backButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    textAlign: 'center',
  },
  instructionsCard: {
    backgroundColor: '#e8f5e8',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    borderLeftWidth: 5,
    borderLeftColor: '#28a745',
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#155724',
    marginBottom: 10,
  },
  instructionsText: {
    fontSize: 14,
    color: '#155724',
    lineHeight: 22,
  },
  form: {
    margin: 15,
  },
  formGroup: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  fieldHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  fieldNumber: {
    fontSize: 18,
    marginRight: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  newBadge: {
    backgroundColor: '#ff6b6b',
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#f8f9fa',
  },
  picker: {
    height: 50,
  },
  noDataContainer: {
    backgroundColor: '#fff3cd',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  noDataText: {
    color: '#856404',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  disabledContainer: {
    backgroundColor: '#f8d7da',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#f5c6cb',
  },
  disabledText: {
    color: '#721c24',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  clientInfoContainer: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  clientInfoName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  clientInfoAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  clientInfoContact: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  clientInfoCoords: {
    fontSize: 12,
    color: '#999',
    fontFamily: 'monospace',
  },
  mapPlaceholder: {
    backgroundColor: '#e3f2fd',
    padding: 30,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
    alignItems: 'center',
  },
  mapPlaceholderText: {
    color: '#1976d2',
    fontSize: 14,
    textAlign: 'center',
  },
  inputPlaceholder: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  inputPlaceholderText: {
    color: '#999',
    fontSize: 14,
  },
  summaryCard: {
    backgroundColor: '#d4edda',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    borderLeftWidth: 5,
    borderLeftColor: '#28a745',
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#155724',
    marginBottom: 10,
  },
  summaryText: {
    fontSize: 14,
    color: '#155724',
    lineHeight: 22,
  },
});

export default DemoFormScreen;
