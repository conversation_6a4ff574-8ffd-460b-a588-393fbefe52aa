{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _excluded = [\"emit\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { CommonActions } from '@react-navigation/routers';\nimport * as React from 'react';\nimport NavigationBuilderContext from \"./NavigationBuilderContext\";\nexport default function useNavigationCache(_ref) {\n  var state = _ref.state,\n    getState = _ref.getState,\n    navigation = _ref.navigation,\n    _setOptions = _ref.setOptions,\n    router = _ref.router,\n    emitter = _ref.emitter;\n  var _React$useContext = React.useContext(NavigationBuilderContext),\n    stackRef = _React$useContext.stackRef;\n  var cache = React.useMemo(function () {\n    return {\n      current: {}\n    };\n  }, [getState, navigation, _setOptions, router, emitter]);\n  var actions = _objectSpread(_objectSpread({}, router.actionCreators), CommonActions);\n  cache.current = state.routes.reduce(function (acc, route) {\n    var previous = cache.current[route.key];\n    if (previous) {\n      acc[route.key] = previous;\n    } else {\n      var emit = navigation.emit,\n        rest = _objectWithoutProperties(navigation, _excluded);\n      var _dispatch = function dispatch(thunk) {\n        var action = typeof thunk === 'function' ? thunk(getState()) : thunk;\n        if (action != null) {\n          navigation.dispatch(_objectSpread({\n            source: route.key\n          }, action));\n        }\n      };\n      var withStack = function withStack(callback) {\n        var isStackSet = false;\n        try {\n          if (process.env.NODE_ENV !== 'production' && stackRef && !stackRef.current) {\n            stackRef.current = new Error().stack;\n            isStackSet = true;\n          }\n          callback();\n        } finally {\n          if (isStackSet && stackRef) {\n            stackRef.current = undefined;\n          }\n        }\n      };\n      var helpers = Object.keys(actions).reduce(function (acc, name) {\n        acc[name] = function () {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          return withStack(function () {\n            return (_dispatch(actions[name].apply(actions, args))\n            );\n          });\n        };\n        return acc;\n      }, {});\n      acc[route.key] = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, rest), helpers), emitter.create(route.key)), {}, {\n        dispatch: function dispatch(thunk) {\n          return withStack(function () {\n            return _dispatch(thunk);\n          });\n        },\n        getParent: function getParent(id) {\n          if (id !== undefined && id === rest.getId()) {\n            return acc[route.key];\n          }\n          return rest.getParent(id);\n        },\n        setOptions: function setOptions(options) {\n          return _setOptions(function (o) {\n            return _objectSpread(_objectSpread({}, o), {}, _defineProperty({}, route.key, _objectSpread(_objectSpread({}, o[route.key]), options)));\n          });\n        },\n        isFocused: function isFocused() {\n          var state = getState();\n          if (state.routes[state.index].key !== route.key) {\n            return false;\n          }\n          return navigation ? navigation.isFocused() : true;\n        }\n      });\n    }\n    return acc;\n  }, {});\n  return cache.current;\n}", "map": {"version": 3, "names": ["CommonActions", "React", "NavigationBuilderContext", "useNavigationCache", "_ref", "state", "getState", "navigation", "setOptions", "router", "emitter", "_React$useContext", "useContext", "stackRef", "cache", "useMemo", "current", "actions", "_objectSpread", "actionCreators", "routes", "reduce", "acc", "route", "previous", "key", "emit", "rest", "_objectWithoutProperties", "_excluded", "dispatch", "thunk", "action", "source", "withStack", "callback", "isStackSet", "process", "env", "NODE_ENV", "Error", "stack", "undefined", "helpers", "Object", "keys", "name", "_len", "arguments", "length", "args", "Array", "_key", "apply", "create", "getParent", "id", "getId", "options", "o", "_defineProperty", "isFocused", "index"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\core\\src\\useNavigationCache.tsx"], "sourcesContent": ["import {\n  CommonActions,\n  NavigationAction,\n  NavigationState,\n  ParamListBase,\n  Router,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationBuilderContext from './NavigationBuilderContext';\nimport type { NavigationHelpers, NavigationProp } from './types';\nimport type { NavigationEventEmitter } from './useEventEmitter';\n\ntype Options<\n  State extends NavigationState,\n  EventMap extends Record<string, any>\n> = {\n  state: State;\n  getState: () => State;\n  navigation: NavigationHelpers<ParamListBase> &\n    Partial<NavigationProp<ParamListBase, string, any, any, any>>;\n  setOptions: (\n    cb: (options: Record<string, object>) => Record<string, object>\n  ) => void;\n  router: Router<State, NavigationAction>;\n  emitter: NavigationEventEmitter<EventMap>;\n};\n\ntype NavigationCache<\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends Record<string, any>\n> = Record<\n  string,\n  NavigationProp<\n    ParamListBase,\n    string,\n    string | undefined,\n    State,\n    ScreenOptions,\n    EventMap\n  >\n>;\n\n/**\n * Hook to cache navigation objects for each screen in the navigator.\n * It's important to cache them to make sure navigation objects don't change between renders.\n * This lets us apply optimizations like `React.memo` to minimize re-rendering screens.\n */\nexport default function useNavigationCache<\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends Record<string, any>\n>({\n  state,\n  getState,\n  navigation,\n  setOptions,\n  router,\n  emitter,\n}: Options<State, EventMap>) {\n  const { stackRef } = React.useContext(NavigationBuilderContext);\n\n  // Cache object which holds navigation objects for each screen\n  // We use `React.useMemo` instead of `React.useRef` coz we want to invalidate it when deps change\n  // In reality, these deps will rarely change, if ever\n  const cache = React.useMemo(\n    () => ({ current: {} as NavigationCache<State, ScreenOptions, EventMap> }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [getState, navigation, setOptions, router, emitter]\n  );\n\n  const actions = {\n    ...router.actionCreators,\n    ...CommonActions,\n  };\n\n  cache.current = state.routes.reduce<\n    NavigationCache<State, ScreenOptions, EventMap>\n  >((acc, route) => {\n    const previous = cache.current[route.key];\n\n    type Thunk =\n      | NavigationAction\n      | ((state: State) => NavigationAction | null | undefined);\n\n    if (previous) {\n      // If a cached navigation object already exists, reuse it\n      acc[route.key] = previous;\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const { emit, ...rest } = navigation;\n\n      const dispatch = (thunk: Thunk) => {\n        const action = typeof thunk === 'function' ? thunk(getState()) : thunk;\n\n        if (action != null) {\n          navigation.dispatch({ source: route.key, ...action });\n        }\n      };\n\n      const withStack = (callback: () => void) => {\n        let isStackSet = false;\n\n        try {\n          if (\n            process.env.NODE_ENV !== 'production' &&\n            stackRef &&\n            !stackRef.current\n          ) {\n            // Capture the stack trace for devtools\n            stackRef.current = new Error().stack;\n            isStackSet = true;\n          }\n\n          callback();\n        } finally {\n          if (isStackSet && stackRef) {\n            stackRef.current = undefined;\n          }\n        }\n      };\n\n      const helpers = Object.keys(actions).reduce<Record<string, () => void>>(\n        (acc, name) => {\n          acc[name] = (...args: any) =>\n            withStack(() =>\n              // @ts-expect-error: name is a valid key, but TypeScript is dumb\n              dispatch(actions[name](...args))\n            );\n\n          return acc;\n        },\n        {}\n      );\n\n      acc[route.key] = {\n        ...rest,\n        ...helpers,\n        // FIXME: too much work to fix the types for now\n        ...(emitter.create(route.key) as any),\n        dispatch: (thunk: Thunk) => withStack(() => dispatch(thunk)),\n        getParent: (id?: string) => {\n          if (id !== undefined && id === rest.getId()) {\n            // If the passed id is the same as the current navigation id,\n            // we return the cached navigation object for the relevant route\n            return acc[route.key];\n          }\n\n          return rest.getParent(id);\n        },\n        setOptions: (options: object) =>\n          setOptions((o) => ({\n            ...o,\n            [route.key]: { ...o[route.key], ...options },\n          })),\n        isFocused: () => {\n          const state = getState();\n\n          if (state.routes[state.index].key !== route.key) {\n            return false;\n          }\n\n          // If the current screen is focused, we also need to check if parent navigator is focused\n          // This makes sure that we return the focus state in the whole tree, not just this navigator\n          return navigation ? navigation.isFocused() : true;\n        },\n      };\n    }\n\n    return acc;\n  }, {});\n\n  return cache.current;\n}\n"], "mappings": ";;;;;AAAA,SACEA,aAAa,QAKR,2BAA2B;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB;AAwC/B,eAAe,SAASC,kBAAkBA,CAAAC,IAAA,EAWb;EAAA,IAN3BC,KAAK,GAMoBD,IAAA,CANzBC,KAAK;IACLC,QAAQ,GAKiBF,IAAA,CALzBE,QAAQ;IACRC,UAAU,GAIeH,IAAA,CAJzBG,UAAU;IACVC,WAAU,GAGeJ,IAAA,CAHzBI,UAAU;IACVC,MAAM,GAEmBL,IAAA,CAFzBK,MAAM;IACNC,OAAA,GACyBN,IAAA,CADzBM,OAAA;EAEA,IAAAC,iBAAA,GAAqBV,KAAK,CAACW,UAAU,CAACV,wBAAwB,CAAC;IAAvDW,QAAA,GAAAF,iBAAA,CAAAE,QAAA;EAKR,IAAMC,KAAK,GAAGb,KAAK,CAACc,OAAO,CACzB;IAAA,OAAO;MAAEC,OAAO,EAAE,CAAC;IAAqD,CAAC;EAAA,CAAC,EAE1E,CAACV,QAAQ,EAAEC,UAAU,EAAEC,WAAU,EAAEC,MAAM,EAAEC,OAAO,CAAC,CACpD;EAED,IAAMO,OAAO,GAAAC,aAAA,CAAAA,aAAA,KACRT,MAAM,CAACU,cAAc,GACrBnB,aAAA,CACJ;EAEDc,KAAK,CAACE,OAAO,GAAGX,KAAK,CAACe,MAAM,CAACC,MAAM,CAEjC,UAACC,GAAG,EAAEC,KAAK,EAAK;IAChB,IAAMC,QAAQ,GAAGV,KAAK,CAACE,OAAO,CAACO,KAAK,CAACE,GAAG,CAAC;IAMzC,IAAID,QAAQ,EAAE;MAEZF,GAAG,CAACC,KAAK,CAACE,GAAG,CAAC,GAAGD,QAAQ;IAC3B,CAAC,MAAM;MAEL,IAAQE,IAAI,GAAcnB,UAAU,CAA5BmB,IAAI;QAAKC,IAAA,GAAAC,wBAAA,CAASrB,UAAU,EAAAsB,SAAA;MAEpC,IAAMC,SAAQ,GAAI,SAAZA,QAAQA,CAAIC,KAAY,EAAK;QACjC,IAAMC,MAAM,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACzB,QAAQ,EAAE,CAAC,GAAGyB,KAAK;QAEtE,IAAIC,MAAM,IAAI,IAAI,EAAE;UAClBzB,UAAU,CAACuB,QAAQ,CAAAZ,aAAA;YAAGe,MAAM,EAAEV,KAAK,CAACE;UAAG,GAAKO,MAAA,CAAQ,CAAC;QACvD;MACF,CAAC;MAED,IAAME,SAAS,GAAI,SAAbA,SAASA,CAAIC,QAAoB,EAAK;QAC1C,IAAIC,UAAU,GAAG,KAAK;QAEtB,IAAI;UACF,IACEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrC1B,QAAQ,IACR,CAACA,QAAQ,CAACG,OAAO,EACjB;YAEAH,QAAQ,CAACG,OAAO,GAAG,IAAIwB,KAAK,EAAE,CAACC,KAAK;YACpCL,UAAU,GAAG,IAAI;UACnB;UAEAD,QAAQ,EAAE;QACZ,CAAC,SAAS;UACR,IAAIC,UAAU,IAAIvB,QAAQ,EAAE;YAC1BA,QAAQ,CAACG,OAAO,GAAG0B,SAAS;UAC9B;QACF;MACF,CAAC;MAED,IAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC5B,OAAO,CAAC,CAACI,MAAM,CACzC,UAACC,GAAG,EAAEwB,IAAI,EAAK;QACbxB,GAAG,CAACwB,IAAI,CAAC,GAAG;UAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAIC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;YAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;UAAA;UAAA,OAClBlB,SAAS,CAAC;YAAA,QAERJ,SAAQ,CAACb,OAAO,CAAC6B,IAAI,CAAC,CAAAO,KAAA,CAAbpC,OAAO,EAAUiC,IAAI,CAAC;YAAC;UAAA,EACjC;QAAA;QAEH,OAAO5B,GAAG;MACZ,CAAC,EACD,CAAC,CAAC,CACH;MAEDA,GAAG,CAACC,KAAK,CAACE,GAAG,CAAC,GAAAP,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACTS,IAAI,GACJgB,OAAO,GAENjC,OAAO,CAAC4C,MAAM,CAAC/B,KAAK,CAACE,GAAG,CAAS;QACrCK,QAAQ,EAAG,SAAXA,QAAQA,CAAGC,KAAY;UAAA,OAAKG,SAAS,CAAC;YAAA,OAAMJ,SAAQ,CAACC,KAAK,CAAC;UAAA,EAAC;QAAA;QAC5DwB,SAAS,EAAG,SAAZA,SAASA,CAAGC,EAAW,EAAK;UAC1B,IAAIA,EAAE,KAAKd,SAAS,IAAIc,EAAE,KAAK7B,IAAI,CAAC8B,KAAK,EAAE,EAAE;YAG3C,OAAOnC,GAAG,CAACC,KAAK,CAACE,GAAG,CAAC;UACvB;UAEA,OAAOE,IAAI,CAAC4B,SAAS,CAACC,EAAE,CAAC;QAC3B,CAAC;QACDhD,UAAU,EAAG,SAAbA,UAAUA,CAAGkD,OAAe;UAAA,OAC1BlD,WAAU,CAAE,UAAAmD,CAAC;YAAA,OAAAzC,aAAA,CAAAA,aAAA,KACRyC,CAAC,OAAAC,eAAA,KACHrC,KAAK,CAACE,GAAG,EAAAP,aAAA,CAAAA,aAAA,KAAQyC,CAAC,CAACpC,KAAK,CAACE,GAAG,CAAC,GAAKiC,OAAA;UAAA,CACnC,CAAC;QAAA;QACLG,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAQ;UACf,IAAMxD,KAAK,GAAGC,QAAQ,EAAE;UAExB,IAAID,KAAK,CAACe,MAAM,CAACf,KAAK,CAACyD,KAAK,CAAC,CAACrC,GAAG,KAAKF,KAAK,CAACE,GAAG,EAAE;YAC/C,OAAO,KAAK;UACd;UAIA,OAAOlB,UAAU,GAAGA,UAAU,CAACsD,SAAS,EAAE,GAAG,IAAI;QACnD;MAAA,EACD;IACH;IAEA,OAAOvC,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,OAAOR,KAAK,CAACE,OAAO;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}