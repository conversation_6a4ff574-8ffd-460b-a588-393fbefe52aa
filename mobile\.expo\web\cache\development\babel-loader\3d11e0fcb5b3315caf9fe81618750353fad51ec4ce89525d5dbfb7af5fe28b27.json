{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nvar _excluded = [\"children\", \"screenListeners\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { CommonActions } from '@react-navigation/routers';\nimport * as React from 'react';\nimport { isValidElementType } from 'react-is';\nimport Group from \"./Group\";\nimport isArrayEqual from \"./isArrayEqual\";\nimport isRecordEqual from \"./isRecordEqual\";\nimport NavigationHelpersContext from \"./NavigationHelpersContext\";\nimport NavigationRouteContext from \"./NavigationRouteContext\";\nimport NavigationStateContext from \"./NavigationStateContext\";\nimport PreventRemoveProvider from \"./PreventRemoveProvider\";\nimport Screen from \"./Screen\";\nimport { PrivateValueStore } from \"./types\";\nimport useChildListeners from \"./useChildListeners\";\nimport useComponent from \"./useComponent\";\nimport useCurrentRender from \"./useCurrentRender\";\nimport useDescriptors from \"./useDescriptors\";\nimport useEventEmitter from \"./useEventEmitter\";\nimport useFocusedListenersChildrenAdapter from \"./useFocusedListenersChildrenAdapter\";\nimport useFocusEvents from \"./useFocusEvents\";\nimport useKeyedChildListeners from \"./useKeyedChildListeners\";\nimport useNavigationHelpers from \"./useNavigationHelpers\";\nimport useOnAction from \"./useOnAction\";\nimport useOnGetState from \"./useOnGetState\";\nimport useOnRouteFocus from \"./useOnRouteFocus\";\nimport useRegisterNavigator from \"./useRegisterNavigator\";\nimport useScheduleUpdate from \"./useScheduleUpdate\";\nPrivateValueStore;\nvar isValidKey = function isValidKey(key) {\n  return key === undefined || typeof key === 'string' && key !== '';\n};\nvar _getRouteConfigsFromChildren = function getRouteConfigsFromChildren(children, groupKey, groupOptions) {\n  var configs = React.Children.toArray(children).reduce(function (acc, child) {\n    var _child$type, _child$props;\n    if (React.isValidElement(child)) {\n      if (child.type === Screen) {\n        if (!isValidKey(child.props.navigationKey)) {\n          throw new Error(`Got an invalid 'navigationKey' prop (${JSON.stringify(child.props.navigationKey)}) for the screen '${child.props.name}'. It must be a non-empty string or 'undefined'.`);\n        }\n        acc.push({\n          keys: [groupKey, child.props.navigationKey],\n          options: groupOptions,\n          props: child.props\n        });\n        return acc;\n      }\n      if (child.type === React.Fragment || child.type === Group) {\n        if (!isValidKey(child.props.navigationKey)) {\n          throw new Error(`Got an invalid 'navigationKey' prop (${JSON.stringify(child.props.navigationKey)}) for the group. It must be a non-empty string or 'undefined'.`);\n        }\n        acc.push.apply(acc, _toConsumableArray(_getRouteConfigsFromChildren(child.props.children, child.props.navigationKey, child.type !== Group ? groupOptions : groupOptions != null ? [].concat(_toConsumableArray(groupOptions), [child.props.screenOptions]) : [child.props.screenOptions])));\n        return acc;\n      }\n    }\n    throw new Error(`A navigator can only contain 'Screen', 'Group' or 'React.Fragment' as its direct children (found ${React.isValidElement(child) ? `'${typeof child.type === 'string' ? child.type : (_child$type = child.type) === null || _child$type === void 0 ? void 0 : _child$type.name}'${child.props != null && typeof child.props === 'object' && 'name' in child.props && (_child$props = child.props) !== null && _child$props !== void 0 && _child$props.name ? ` for the screen '${child.props.name}'` : ''}` : typeof child === 'object' ? JSON.stringify(child) : `'${String(child)}'`}). To render this component in the navigator, pass it in the 'component' prop to 'Screen'.`);\n  }, []);\n  if (process.env.NODE_ENV !== 'production') {\n    configs.forEach(function (config) {\n      var _config$props = config.props,\n        name = _config$props.name,\n        children = _config$props.children,\n        component = _config$props.component,\n        getComponent = _config$props.getComponent;\n      if (typeof name !== 'string' || !name) {\n        throw new Error(`Got an invalid name (${JSON.stringify(name)}) for the screen. It must be a non-empty string.`);\n      }\n      if (children != null || component !== undefined || getComponent !== undefined) {\n        if (children != null && component !== undefined) {\n          throw new Error(`Got both 'component' and 'children' props for the screen '${name}'. You must pass only one of them.`);\n        }\n        if (children != null && getComponent !== undefined) {\n          throw new Error(`Got both 'getComponent' and 'children' props for the screen '${name}'. You must pass only one of them.`);\n        }\n        if (component !== undefined && getComponent !== undefined) {\n          throw new Error(`Got both 'component' and 'getComponent' props for the screen '${name}'. You must pass only one of them.`);\n        }\n        if (children != null && typeof children !== 'function') {\n          throw new Error(`Got an invalid value for 'children' prop for the screen '${name}'. It must be a function returning a React Element.`);\n        }\n        if (component !== undefined && !isValidElementType(component)) {\n          throw new Error(`Got an invalid value for 'component' prop for the screen '${name}'. It must be a valid React Component.`);\n        }\n        if (getComponent !== undefined && typeof getComponent !== 'function') {\n          throw new Error(`Got an invalid value for 'getComponent' prop for the screen '${name}'. It must be a function returning a React Component.`);\n        }\n        if (typeof component === 'function') {\n          if (component.name === 'component') {\n            console.warn(`Looks like you're passing an inline function for 'component' prop for the screen '${name}' (e.g. component={() => <SomeComponent />}). Passing an inline function will cause the component state to be lost on re-render and cause perf issues since it's re-created every render. You can pass the function as children to 'Screen' instead to achieve the desired behaviour.`);\n          } else if (/^[a-z]/.test(component.name)) {\n            console.warn(`Got a component with the name '${component.name}' for the screen '${name}'. React Components must start with an uppercase letter. If you're passing a regular function and not a component, pass it as children to 'Screen' instead. Otherwise capitalize your component's name.`);\n          }\n        }\n      } else {\n        throw new Error(`Couldn't find a 'component', 'getComponent' or 'children' prop for the screen '${name}'. This can happen if you passed 'undefined'. You likely forgot to export your component from the file it's defined in, or mixed up default import and named import when importing.`);\n      }\n    });\n  }\n  return configs;\n};\nexport default function useNavigationBuilder(createRouter, options) {\n  var navigatorKey = useRegisterNavigator();\n  var route = React.useContext(NavigationRouteContext);\n  var children = options.children,\n    screenListeners = options.screenListeners,\n    rest = _objectWithoutProperties(options, _excluded);\n  var _React$useRef = React.useRef(createRouter(_objectSpread(_objectSpread({}, rest), route !== null && route !== void 0 && route.params && route.params.state == null && route.params.initial !== false && typeof route.params.screen === 'string' ? {\n      initialRouteName: route.params.screen\n    } : null))),\n    router = _React$useRef.current;\n  var routeConfigs = _getRouteConfigsFromChildren(children);\n  var screens = routeConfigs.reduce(function (acc, config) {\n    if (config.props.name in acc) {\n      throw new Error(`A navigator cannot contain multiple 'Screen' components with the same name (found duplicate screen named '${config.props.name}')`);\n    }\n    acc[config.props.name] = config;\n    return acc;\n  }, {});\n  var routeNames = routeConfigs.map(function (config) {\n    return config.props.name;\n  });\n  var routeKeyList = routeNames.reduce(function (acc, curr) {\n    acc[curr] = screens[curr].keys.map(function (key) {\n      return key != null ? key : '';\n    }).join(':');\n    return acc;\n  }, {});\n  var routeParamList = routeNames.reduce(function (acc, curr) {\n    var initialParams = screens[curr].props.initialParams;\n    acc[curr] = initialParams;\n    return acc;\n  }, {});\n  var routeGetIdList = routeNames.reduce(function (acc, curr) {\n    return Object.assign(acc, _defineProperty({}, curr, screens[curr].props.getId));\n  }, {});\n  if (!routeNames.length) {\n    throw new Error(\"Couldn't find any screens for the navigator. Have you defined any screens as its children?\");\n  }\n  var isStateValid = React.useCallback(function (state) {\n    return state.type === undefined || state.type === router.type;\n  }, [router.type]);\n  var isStateInitialized = React.useCallback(function (state) {\n    return state !== undefined && state.stale === false && isStateValid(state);\n  }, [isStateValid]);\n  var _React$useContext = React.useContext(NavigationStateContext),\n    currentState = _React$useContext.state,\n    getCurrentState = _React$useContext.getState,\n    setCurrentState = _React$useContext.setState,\n    setKey = _React$useContext.setKey,\n    getKey = _React$useContext.getKey,\n    getIsInitial = _React$useContext.getIsInitial;\n  var stateCleanedUp = React.useRef(false);\n  var cleanUpState = React.useCallback(function () {\n    setCurrentState(undefined);\n    stateCleanedUp.current = true;\n  }, [setCurrentState]);\n  var setState = React.useCallback(function (state) {\n    if (stateCleanedUp.current) {\n      return;\n    }\n    setCurrentState(state);\n  }, [setCurrentState]);\n  var _React$useMemo = React.useMemo(function () {\n      var _route$params4;\n      var initialRouteParamList = routeNames.reduce(function (acc, curr) {\n        var _route$params, _route$params2, _route$params3;\n        var initialParams = screens[curr].props.initialParams;\n        var initialParamsFromParams = (route === null || route === void 0 ? void 0 : (_route$params = route.params) === null || _route$params === void 0 ? void 0 : _route$params.state) == null && (route === null || route === void 0 ? void 0 : (_route$params2 = route.params) === null || _route$params2 === void 0 ? void 0 : _route$params2.initial) !== false && (route === null || route === void 0 ? void 0 : (_route$params3 = route.params) === null || _route$params3 === void 0 ? void 0 : _route$params3.screen) === curr ? route.params.params : undefined;\n        acc[curr] = initialParams !== undefined || initialParamsFromParams !== undefined ? _objectSpread(_objectSpread({}, initialParams), initialParamsFromParams) : undefined;\n        return acc;\n      }, {});\n      if ((currentState === undefined || !isStateValid(currentState)) && (route === null || route === void 0 ? void 0 : (_route$params4 = route.params) === null || _route$params4 === void 0 ? void 0 : _route$params4.state) == null) {\n        return [router.getInitialState({\n          routeNames: routeNames,\n          routeParamList: initialRouteParamList,\n          routeGetIdList: routeGetIdList\n        }), true];\n      } else {\n        var _ref;\n        var _route$params5;\n        return [router.getRehydratedState((_ref = route === null || route === void 0 ? void 0 : (_route$params5 = route.params) === null || _route$params5 === void 0 ? void 0 : _route$params5.state) != null ? _ref : currentState, {\n          routeNames: routeNames,\n          routeParamList: initialRouteParamList,\n          routeGetIdList: routeGetIdList\n        }), false];\n      }\n    }, [currentState, router, isStateValid]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    initializedState = _React$useMemo2[0],\n    isFirstStateInitialization = _React$useMemo2[1];\n  var previousRouteKeyListRef = React.useRef(routeKeyList);\n  React.useEffect(function () {\n    previousRouteKeyListRef.current = routeKeyList;\n  });\n  var previousRouteKeyList = previousRouteKeyListRef.current;\n  var state = isStateInitialized(currentState) ? currentState : initializedState;\n  var nextState = state;\n  if (!isArrayEqual(state.routeNames, routeNames) || !isRecordEqual(routeKeyList, previousRouteKeyList)) {\n    nextState = router.getStateForRouteNamesChange(state, {\n      routeNames: routeNames,\n      routeParamList: routeParamList,\n      routeGetIdList: routeGetIdList,\n      routeKeyChanges: Object.keys(routeKeyList).filter(function (name) {\n        return previousRouteKeyList.hasOwnProperty(name) && routeKeyList[name] !== previousRouteKeyList[name];\n      })\n    });\n  }\n  var previousNestedParamsRef = React.useRef(route === null || route === void 0 ? void 0 : route.params);\n  React.useEffect(function () {\n    previousNestedParamsRef.current = route === null || route === void 0 ? void 0 : route.params;\n  }, [route === null || route === void 0 ? void 0 : route.params]);\n  if (route !== null && route !== void 0 && route.params) {\n    var previousParams = previousNestedParamsRef.current;\n    var action;\n    if (typeof route.params.state === 'object' && route.params.state != null && route.params !== previousParams) {\n      action = CommonActions.reset(route.params.state);\n    } else if (typeof route.params.screen === 'string' && (route.params.initial === false && isFirstStateInitialization || route.params !== previousParams)) {\n      action = CommonActions.navigate({\n        name: route.params.screen,\n        params: route.params.params,\n        path: route.params.path\n      });\n    }\n    var updatedState = action ? router.getStateForAction(nextState, action, {\n      routeNames: routeNames,\n      routeParamList: routeParamList,\n      routeGetIdList: routeGetIdList\n    }) : null;\n    nextState = updatedState !== null ? router.getRehydratedState(updatedState, {\n      routeNames: routeNames,\n      routeParamList: routeParamList,\n      routeGetIdList: routeGetIdList\n    }) : nextState;\n  }\n  var shouldUpdate = state !== nextState;\n  useScheduleUpdate(function () {\n    if (shouldUpdate) {\n      setState(nextState);\n    }\n  });\n  state = nextState;\n  React.useEffect(function () {\n    setKey(navigatorKey);\n    if (!getIsInitial()) {\n      setState(nextState);\n    }\n    return function () {\n      setTimeout(function () {\n        if (getCurrentState() !== undefined && getKey() === navigatorKey) {\n          cleanUpState();\n        }\n      }, 0);\n    };\n  }, []);\n  var initializedStateRef = React.useRef();\n  initializedStateRef.current = initializedState;\n  var getState = React.useCallback(function () {\n    var currentState = getCurrentState();\n    return isStateInitialized(currentState) ? currentState : initializedStateRef.current;\n  }, [getCurrentState, isStateInitialized]);\n  var emitter = useEventEmitter(function (e) {\n    var _ref2;\n    var routeNames = [];\n    var route;\n    if (e.target) {\n      var _route;\n      route = state.routes.find(function (route) {\n        return route.key === e.target;\n      });\n      if ((_route = route) !== null && _route !== void 0 && _route.name) {\n        routeNames.push(route.name);\n      }\n    } else {\n      route = state.routes[state.index];\n      routeNames.push.apply(routeNames, _toConsumableArray(Object.keys(screens).filter(function (name) {\n        var _route2;\n        return ((_route2 = route) === null || _route2 === void 0 ? void 0 : _route2.name) === name;\n      })));\n    }\n    if (route == null) {\n      return;\n    }\n    var navigation = descriptors[route.key].navigation;\n    var listeners = (_ref2 = []).concat.apply(_ref2, _toConsumableArray([screenListeners].concat(_toConsumableArray(routeNames.map(function (name) {\n      var listeners = screens[name].props.listeners;\n      return listeners;\n    }))).map(function (listeners) {\n      var map = typeof listeners === 'function' ? listeners({\n        route: route,\n        navigation: navigation\n      }) : listeners;\n      return map ? Object.keys(map).filter(function (type) {\n        return type === e.type;\n      }).map(function (type) {\n        return map === null || map === void 0 ? void 0 : map[type];\n      }) : undefined;\n    }))).filter(function (cb, i, self) {\n      return cb && self.lastIndexOf(cb) === i;\n    });\n    listeners.forEach(function (listener) {\n      return listener === null || listener === void 0 ? void 0 : listener(e);\n    });\n  });\n  useFocusEvents({\n    state: state,\n    emitter: emitter\n  });\n  React.useEffect(function () {\n    emitter.emit({\n      type: 'state',\n      data: {\n        state: state\n      }\n    });\n  }, [emitter, state]);\n  var _useChildListeners = useChildListeners(),\n    childListeners = _useChildListeners.listeners,\n    addListener = _useChildListeners.addListener;\n  var _useKeyedChildListene = useKeyedChildListeners(),\n    keyedListeners = _useKeyedChildListene.keyedListeners,\n    addKeyedListener = _useKeyedChildListene.addKeyedListener;\n  var onAction = useOnAction({\n    router: router,\n    getState: getState,\n    setState: setState,\n    key: route === null || route === void 0 ? void 0 : route.key,\n    actionListeners: childListeners.action,\n    beforeRemoveListeners: keyedListeners.beforeRemove,\n    routerConfigOptions: {\n      routeNames: routeNames,\n      routeParamList: routeParamList,\n      routeGetIdList: routeGetIdList\n    },\n    emitter: emitter\n  });\n  var onRouteFocus = useOnRouteFocus({\n    router: router,\n    key: route === null || route === void 0 ? void 0 : route.key,\n    getState: getState,\n    setState: setState\n  });\n  var navigation = useNavigationHelpers({\n    id: options.id,\n    onAction: onAction,\n    getState: getState,\n    emitter: emitter,\n    router: router\n  });\n  useFocusedListenersChildrenAdapter({\n    navigation: navigation,\n    focusedListeners: childListeners.focus\n  });\n  useOnGetState({\n    getState: getState,\n    getStateListeners: keyedListeners.getState\n  });\n  var descriptors = useDescriptors({\n    state: state,\n    screens: screens,\n    navigation: navigation,\n    screenOptions: options.screenOptions,\n    defaultScreenOptions: options.defaultScreenOptions,\n    onAction: onAction,\n    getState: getState,\n    setState: setState,\n    onRouteFocus: onRouteFocus,\n    addListener: addListener,\n    addKeyedListener: addKeyedListener,\n    router: router,\n    emitter: emitter\n  });\n  useCurrentRender({\n    state: state,\n    navigation: navigation,\n    descriptors: descriptors\n  });\n  var NavigationContent = useComponent(function (children) {\n    return React.createElement(NavigationHelpersContext.Provider, {\n      value: navigation\n    }, React.createElement(PreventRemoveProvider, null, children));\n  });\n  return {\n    state: state,\n    navigation: navigation,\n    descriptors: descriptors,\n    NavigationContent: NavigationContent\n  };\n}", "map": {"version": 3, "names": ["CommonActions", "React", "isValidElementType", "Group", "isArrayEqual", "isRecordEqual", "NavigationHelpersContext", "NavigationRouteContext", "NavigationStateContext", "PreventRemoveProvider", "Screen", "PrivateValueStore", "useChildListeners", "useComponent", "useCurrentRender", "useDescriptors", "useEventEmitter", "useFocusedListenersChildrenAdapter", "useFocusEvents", "useKeyedChildListeners", "useNavigationHelpers", "useOnAction", "useOnGetState", "useOnRouteFocus", "useRegisterNavigator", "useScheduleUpdate", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "undefined", "getRouteConfigsFromChildren", "children", "groupKey", "groupOptions", "configs", "Children", "toArray", "reduce", "acc", "child", "_child$type", "_child$props", "isValidElement", "type", "props", "navigationKey", "Error", "JSON", "stringify", "name", "push", "keys", "options", "Fragment", "apply", "_toConsumableArray", "concat", "screenOptions", "String", "process", "env", "NODE_ENV", "for<PERSON>ach", "config", "_config$props", "component", "getComponent", "console", "warn", "test", "useNavigationBuilder", "createRouter", "navigator<PERSON><PERSON>", "route", "useContext", "screenListeners", "rest", "_objectWithoutProperties", "_excluded", "_React$useRef", "useRef", "_objectSpread", "params", "state", "initial", "screen", "initialRouteName", "router", "current", "routeConfigs", "screens", "routeNames", "map", "routeKeyList", "curr", "join", "routeParamList", "initialParams", "routeGetIdList", "Object", "assign", "_defineProperty", "getId", "length", "isStateValid", "useCallback", "isStateInitialized", "stale", "_React$useContext", "currentState", "getCurrentState", "getState", "setCurrentState", "setState", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getIsInitial", "stateCleanedUp", "cleanUpState", "_React$useMemo", "useMemo", "_route$params4", "initialRouteParamList", "_route$params", "_route$params2", "_route$params3", "initialParamsFromParams", "getInitialState", "_ref", "_route$params5", "getRehydratedState", "_React$useMemo2", "_slicedToArray", "initializedState", "isFirstStateInitialization", "previousRouteKeyListRef", "useEffect", "previousRouteKeyList", "nextState", "getStateForRouteNamesChange", "routeKeyChanges", "filter", "hasOwnProperty", "previousNestedParamsRef", "previousParams", "action", "reset", "navigate", "path", "updatedState", "getStateForAction", "shouldUpdate", "setTimeout", "initializedStateRef", "emitter", "e", "_ref2", "target", "_route", "routes", "find", "index", "_route2", "navigation", "descriptors", "listeners", "cb", "i", "self", "lastIndexOf", "listener", "emit", "data", "_useChildListeners", "childListeners", "addListener", "_useKeyedChildListene", "keyedListeners", "addKeyedListener", "onAction", "actionListeners", "beforeRemoveListeners", "beforeRemove", "routerConfigOptions", "onRouteFocus", "id", "focusedListeners", "focus", "getStateListeners", "defaultScreenOptions", "NavigationContent", "createElement", "Provider", "value"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\core\\src\\useNavigationBuilder.tsx"], "sourcesContent": ["import {\n  CommonActions,\n  DefaultRouterOptions,\n  NavigationAction,\n  NavigationState,\n  ParamListBase,\n  PartialState,\n  Route,\n  Router,\n  RouterConfigOptions,\n  RouterFactory,\n} from '@react-navigation/routers';\nimport * as React from 'react';\nimport { isValidElementType } from 'react-is';\n\nimport Group from './Group';\nimport isArrayEqual from './isArrayEqual';\nimport isRecordEqual from './isRecordEqual';\nimport NavigationHelpersContext from './NavigationHelpersContext';\nimport NavigationRouteContext from './NavigationRouteContext';\nimport NavigationStateContext from './NavigationStateContext';\nimport PreventRemoveProvider from './PreventRemoveProvider';\nimport Screen from './Screen';\nimport {\n  DefaultNavigatorOptions,\n  EventMapBase,\n  EventMapCore,\n  NavigatorScreenParams,\n  PrivateValueStore,\n  RouteConfig,\n  RouteProp,\n} from './types';\nimport useChildListeners from './useChildListeners';\nimport useComponent from './useComponent';\nimport useCurrentRender from './useCurrentRender';\nimport useDescriptors, { ScreenConfigWithParent } from './useDescriptors';\nimport useEventEmitter from './useEventEmitter';\nimport useFocusedListenersChildrenAdapter from './useFocusedListenersChildrenAdapter';\nimport useFocusEvents from './useFocusEvents';\nimport useKeyedChildListeners from './useKeyedChildListeners';\nimport useNavigationHelpers from './useNavigationHelpers';\nimport useOnAction from './useOnAction';\nimport useOnGetState from './useOnGetState';\nimport useOnRouteFocus from './useOnRouteFocus';\nimport useRegisterNavigator from './useRegisterNavigator';\nimport useScheduleUpdate from './useScheduleUpdate';\n\n// This is to make TypeScript compiler happy\n// eslint-disable-next-line babel/no-unused-expressions\nPrivateValueStore;\n\ntype NavigationBuilderOptions<ScreenOptions extends {}> = {\n  /**\n   * Default options specified by the navigator.\n   * It receives the custom options in the arguments if a function is specified.\n   */\n  defaultScreenOptions?:\n    | ScreenOptions\n    | ((props: {\n        route: RouteProp<ParamListBase>;\n        navigation: any;\n        options: ScreenOptions;\n      }) => ScreenOptions);\n};\n\ntype NavigatorRoute<State extends NavigationState> = {\n  key: string;\n  params?: NavigatorScreenParams<ParamListBase, State>;\n};\n\nconst isValidKey = (key: unknown) =>\n  key === undefined || (typeof key === 'string' && key !== '');\n\n/**\n * Extract route config object from React children elements.\n *\n * @param children React Elements to extract the config from.\n */\nconst getRouteConfigsFromChildren = <\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends EventMapBase\n>(\n  children: React.ReactNode,\n  groupKey?: string,\n  groupOptions?: ScreenConfigWithParent<\n    State,\n    ScreenOptions,\n    EventMap\n  >['options']\n) => {\n  const configs = React.Children.toArray(children).reduce<\n    ScreenConfigWithParent<State, ScreenOptions, EventMap>[]\n  >((acc, child) => {\n    if (React.isValidElement(child)) {\n      if (child.type === Screen) {\n        // We can only extract the config from `Screen` elements\n        // If something else was rendered, it's probably a bug\n\n        if (!isValidKey(child.props.navigationKey)) {\n          throw new Error(\n            `Got an invalid 'navigationKey' prop (${JSON.stringify(\n              child.props.navigationKey\n            )}) for the screen '${\n              child.props.name\n            }'. It must be a non-empty string or 'undefined'.`\n          );\n        }\n\n        acc.push({\n          keys: [groupKey, child.props.navigationKey],\n          options: groupOptions,\n          props: child.props as RouteConfig<\n            ParamListBase,\n            string,\n            State,\n            ScreenOptions,\n            EventMap\n          >,\n        });\n        return acc;\n      }\n\n      if (child.type === React.Fragment || child.type === Group) {\n        if (!isValidKey(child.props.navigationKey)) {\n          throw new Error(\n            `Got an invalid 'navigationKey' prop (${JSON.stringify(\n              child.props.navigationKey\n            )}) for the group. It must be a non-empty string or 'undefined'.`\n          );\n        }\n\n        // When we encounter a fragment or group, we need to dive into its children to extract the configs\n        // This is handy to conditionally define a group of screens\n        acc.push(\n          ...getRouteConfigsFromChildren<State, ScreenOptions, EventMap>(\n            child.props.children,\n            child.props.navigationKey,\n            child.type !== Group\n              ? groupOptions\n              : groupOptions != null\n              ? [...groupOptions, child.props.screenOptions]\n              : [child.props.screenOptions]\n          )\n        );\n        return acc;\n      }\n    }\n\n    throw new Error(\n      `A navigator can only contain 'Screen', 'Group' or 'React.Fragment' as its direct children (found ${\n        React.isValidElement(child)\n          ? `'${\n              typeof child.type === 'string' ? child.type : child.type?.name\n            }'${\n              child.props != null &&\n              typeof child.props === 'object' &&\n              'name' in child.props &&\n              child.props?.name\n                ? ` for the screen '${child.props.name}'`\n                : ''\n            }`\n          : typeof child === 'object'\n          ? JSON.stringify(child)\n          : `'${String(child)}'`\n      }). To render this component in the navigator, pass it in the 'component' prop to 'Screen'.`\n    );\n  }, []);\n\n  if (process.env.NODE_ENV !== 'production') {\n    configs.forEach((config) => {\n      const { name, children, component, getComponent } = config.props;\n\n      if (typeof name !== 'string' || !name) {\n        throw new Error(\n          `Got an invalid name (${JSON.stringify(\n            name\n          )}) for the screen. It must be a non-empty string.`\n        );\n      }\n\n      if (\n        children != null ||\n        component !== undefined ||\n        getComponent !== undefined\n      ) {\n        if (children != null && component !== undefined) {\n          throw new Error(\n            `Got both 'component' and 'children' props for the screen '${name}'. You must pass only one of them.`\n          );\n        }\n\n        if (children != null && getComponent !== undefined) {\n          throw new Error(\n            `Got both 'getComponent' and 'children' props for the screen '${name}'. You must pass only one of them.`\n          );\n        }\n\n        if (component !== undefined && getComponent !== undefined) {\n          throw new Error(\n            `Got both 'component' and 'getComponent' props for the screen '${name}'. You must pass only one of them.`\n          );\n        }\n\n        if (children != null && typeof children !== 'function') {\n          throw new Error(\n            `Got an invalid value for 'children' prop for the screen '${name}'. It must be a function returning a React Element.`\n          );\n        }\n\n        if (component !== undefined && !isValidElementType(component)) {\n          throw new Error(\n            `Got an invalid value for 'component' prop for the screen '${name}'. It must be a valid React Component.`\n          );\n        }\n\n        if (getComponent !== undefined && typeof getComponent !== 'function') {\n          throw new Error(\n            `Got an invalid value for 'getComponent' prop for the screen '${name}'. It must be a function returning a React Component.`\n          );\n        }\n\n        if (typeof component === 'function') {\n          if (component.name === 'component') {\n            // Inline anonymous functions passed in the `component` prop will have the name of the prop\n            // It's relatively safe to assume that it's not a component since it should also have PascalCase name\n            // We won't catch all scenarios here, but this should catch a good chunk of incorrect use.\n            console.warn(\n              `Looks like you're passing an inline function for 'component' prop for the screen '${name}' (e.g. component={() => <SomeComponent />}). Passing an inline function will cause the component state to be lost on re-render and cause perf issues since it's re-created every render. You can pass the function as children to 'Screen' instead to achieve the desired behaviour.`\n            );\n          } else if (/^[a-z]/.test(component.name)) {\n            console.warn(\n              `Got a component with the name '${component.name}' for the screen '${name}'. React Components must start with an uppercase letter. If you're passing a regular function and not a component, pass it as children to 'Screen' instead. Otherwise capitalize your component's name.`\n            );\n          }\n        }\n      } else {\n        throw new Error(\n          `Couldn't find a 'component', 'getComponent' or 'children' prop for the screen '${name}'. This can happen if you passed 'undefined'. You likely forgot to export your component from the file it's defined in, or mixed up default import and named import when importing.`\n        );\n      }\n    });\n  }\n\n  return configs;\n};\n\n/**\n * Hook for building navigators.\n *\n * @param createRouter Factory method which returns router object.\n * @param options Options object containing `children` and additional options for the router.\n * @returns An object containing `state`, `navigation`, `descriptors` objects.\n */\nexport default function useNavigationBuilder<\n  State extends NavigationState,\n  RouterOptions extends DefaultRouterOptions,\n  ActionHelpers extends Record<string, () => void>,\n  ScreenOptions extends {},\n  EventMap extends Record<string, any>\n>(\n  createRouter: RouterFactory<State, any, RouterOptions>,\n  options: DefaultNavigatorOptions<\n    ParamListBase,\n    State,\n    ScreenOptions,\n    EventMap\n  > &\n    NavigationBuilderOptions<ScreenOptions> &\n    RouterOptions\n) {\n  const navigatorKey = useRegisterNavigator();\n\n  const route = React.useContext(NavigationRouteContext) as\n    | NavigatorRoute<State>\n    | undefined;\n\n  const { children, screenListeners, ...rest } = options;\n  const { current: router } = React.useRef<Router<State, any>>(\n    createRouter({\n      ...(rest as unknown as RouterOptions),\n      ...(route?.params &&\n      route.params.state == null &&\n      route.params.initial !== false &&\n      typeof route.params.screen === 'string'\n        ? { initialRouteName: route.params.screen }\n        : null),\n    })\n  );\n\n  const routeConfigs = getRouteConfigsFromChildren<\n    State,\n    ScreenOptions,\n    EventMap\n  >(children);\n\n  const screens = routeConfigs.reduce<\n    Record<string, ScreenConfigWithParent<State, ScreenOptions, EventMap>>\n  >((acc, config) => {\n    if (config.props.name in acc) {\n      throw new Error(\n        `A navigator cannot contain multiple 'Screen' components with the same name (found duplicate screen named '${config.props.name}')`\n      );\n    }\n\n    acc[config.props.name] = config;\n    return acc;\n  }, {});\n\n  const routeNames = routeConfigs.map((config) => config.props.name);\n  const routeKeyList = routeNames.reduce<Record<string, React.Key | undefined>>(\n    (acc, curr) => {\n      acc[curr] = screens[curr].keys.map((key) => key ?? '').join(':');\n      return acc;\n    },\n    {}\n  );\n  const routeParamList = routeNames.reduce<Record<string, object | undefined>>(\n    (acc, curr) => {\n      const { initialParams } = screens[curr].props;\n      acc[curr] = initialParams;\n      return acc;\n    },\n    {}\n  );\n  const routeGetIdList = routeNames.reduce<\n    RouterConfigOptions['routeGetIdList']\n  >(\n    (acc, curr) =>\n      Object.assign(acc, {\n        [curr]: screens[curr].props.getId,\n      }),\n    {}\n  );\n\n  if (!routeNames.length) {\n    throw new Error(\n      \"Couldn't find any screens for the navigator. Have you defined any screens as its children?\"\n    );\n  }\n\n  const isStateValid = React.useCallback(\n    (state: NavigationState | PartialState<NavigationState>) =>\n      state.type === undefined || state.type === router.type,\n    [router.type]\n  );\n\n  const isStateInitialized = React.useCallback(\n    (state: NavigationState | PartialState<NavigationState> | undefined) =>\n      state !== undefined && state.stale === false && isStateValid(state),\n    [isStateValid]\n  );\n\n  const {\n    state: currentState,\n    getState: getCurrentState,\n    setState: setCurrentState,\n    setKey,\n    getKey,\n    getIsInitial,\n  } = React.useContext(NavigationStateContext);\n\n  const stateCleanedUp = React.useRef(false);\n\n  const cleanUpState = React.useCallback(() => {\n    setCurrentState(undefined);\n    stateCleanedUp.current = true;\n  }, [setCurrentState]);\n\n  const setState = React.useCallback(\n    (state: NavigationState | PartialState<NavigationState> | undefined) => {\n      if (stateCleanedUp.current) {\n        // State might have been already cleaned up due to unmount\n        // We do not want to expose API allowing to override this\n        // This would lead to old data preservation on main navigator unmount\n        return;\n      }\n      setCurrentState(state);\n    },\n    [setCurrentState]\n  );\n\n  const [initializedState, isFirstStateInitialization] = React.useMemo(() => {\n    const initialRouteParamList = routeNames.reduce<\n      Record<string, object | undefined>\n    >((acc, curr) => {\n      const { initialParams } = screens[curr].props;\n      const initialParamsFromParams =\n        route?.params?.state == null &&\n        route?.params?.initial !== false &&\n        route?.params?.screen === curr\n          ? route.params.params\n          : undefined;\n\n      acc[curr] =\n        initialParams !== undefined || initialParamsFromParams !== undefined\n          ? {\n              ...initialParams,\n              ...initialParamsFromParams,\n            }\n          : undefined;\n\n      return acc;\n    }, {});\n\n    // If the current state isn't initialized on first render, we initialize it\n    // We also need to re-initialize it if the state passed from parent was changed (maybe due to reset)\n    // Otherwise assume that the state was provided as initial state\n    // So we need to rehydrate it to make it usable\n    if (\n      (currentState === undefined || !isStateValid(currentState)) &&\n      route?.params?.state == null\n    ) {\n      return [\n        router.getInitialState({\n          routeNames,\n          routeParamList: initialRouteParamList,\n          routeGetIdList,\n        }),\n        true,\n      ];\n    } else {\n      return [\n        router.getRehydratedState(\n          route?.params?.state ?? (currentState as PartialState<State>),\n          {\n            routeNames,\n            routeParamList: initialRouteParamList,\n            routeGetIdList,\n          }\n        ),\n        false,\n      ];\n    }\n    // We explicitly don't include routeNames, route.params etc. in the dep list\n    // below. We want to avoid forcing a new state to be calculated in those cases\n    // Instead, we handle changes to these in the nextState code below. Note\n    // that some changes to routeConfigs are explicitly ignored, such as changes\n    // to initialParams\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [currentState, router, isStateValid]);\n\n  const previousRouteKeyListRef = React.useRef(routeKeyList);\n\n  React.useEffect(() => {\n    previousRouteKeyListRef.current = routeKeyList;\n  });\n\n  const previousRouteKeyList = previousRouteKeyListRef.current;\n\n  let state =\n    // If the state isn't initialized, or stale, use the state we initialized instead\n    // The state won't update until there's a change needed in the state we have initalized locally\n    // So it'll be `undefined` or stale until the first navigation event happens\n    isStateInitialized(currentState)\n      ? (currentState as State)\n      : (initializedState as State);\n\n  let nextState: State = state;\n\n  if (\n    !isArrayEqual(state.routeNames, routeNames) ||\n    !isRecordEqual(routeKeyList, previousRouteKeyList)\n  ) {\n    // When the list of route names change, the router should handle it to remove invalid routes\n    nextState = router.getStateForRouteNamesChange(state, {\n      routeNames,\n      routeParamList,\n      routeGetIdList,\n      routeKeyChanges: Object.keys(routeKeyList).filter(\n        (name) =>\n          previousRouteKeyList.hasOwnProperty(name) &&\n          routeKeyList[name] !== previousRouteKeyList[name]\n      ),\n    });\n  }\n\n  const previousNestedParamsRef = React.useRef(route?.params);\n\n  React.useEffect(() => {\n    previousNestedParamsRef.current = route?.params;\n  }, [route?.params]);\n\n  if (route?.params) {\n    const previousParams = previousNestedParamsRef.current;\n\n    let action: CommonActions.Action | undefined;\n\n    if (\n      typeof route.params.state === 'object' &&\n      route.params.state != null &&\n      route.params !== previousParams\n    ) {\n      // If the route was updated with new state, we should reset to it\n      action = CommonActions.reset(route.params.state);\n    } else if (\n      typeof route.params.screen === 'string' &&\n      ((route.params.initial === false && isFirstStateInitialization) ||\n        route.params !== previousParams)\n    ) {\n      // If the route was updated with new screen name and/or params, we should navigate there\n      action = CommonActions.navigate({\n        name: route.params.screen,\n        params: route.params.params,\n        path: route.params.path,\n      });\n    }\n\n    // The update should be limited to current navigator only, so we call the router manually\n    const updatedState = action\n      ? router.getStateForAction(nextState, action, {\n          routeNames,\n          routeParamList,\n          routeGetIdList,\n        })\n      : null;\n\n    nextState =\n      updatedState !== null\n        ? router.getRehydratedState(updatedState, {\n            routeNames,\n            routeParamList,\n            routeGetIdList,\n          })\n        : nextState;\n  }\n\n  const shouldUpdate = state !== nextState;\n\n  useScheduleUpdate(() => {\n    if (shouldUpdate) {\n      // If the state needs to be updated, we'll schedule an update\n      setState(nextState);\n    }\n  });\n\n  // The up-to-date state will come in next render, but we don't need to wait for it\n  // We can't use the outdated state since the screens have changed, which will cause error due to mismatched config\n  // So we override the state object we return to use the latest state as soon as possible\n  state = nextState;\n\n  React.useEffect(() => {\n    setKey(navigatorKey);\n\n    if (!getIsInitial()) {\n      // If it's not initial render, we need to update the state\n      // This will make sure that our container gets notifier of state changes due to new mounts\n      // This is necessary for proper screen tracking, URL updates etc.\n      setState(nextState);\n    }\n\n    return () => {\n      // We need to clean up state for this navigator on unmount\n      // We do it in a timeout because we need to detect if another navigator mounted in the meantime\n      // For example, if another navigator has started rendering, we should skip cleanup\n      // Otherwise, our cleanup step will cleanup state for the other navigator and re-initialize it\n      setTimeout(() => {\n        if (getCurrentState() !== undefined && getKey() === navigatorKey) {\n          cleanUpState();\n        }\n      }, 0);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // We initialize this ref here to avoid a new getState getting initialized\n  // whenever initializedState changes. We want getState to have access to the\n  // latest initializedState, but don't need it to change when that happens\n  const initializedStateRef = React.useRef<State>();\n  initializedStateRef.current = initializedState;\n\n  const getState = React.useCallback((): State => {\n    const currentState = getCurrentState();\n\n    return isStateInitialized(currentState)\n      ? (currentState as State)\n      : (initializedStateRef.current as State);\n  }, [getCurrentState, isStateInitialized]);\n\n  const emitter = useEventEmitter<EventMapCore<State>>((e) => {\n    let routeNames = [];\n\n    let route: Route<string> | undefined;\n\n    if (e.target) {\n      route = state.routes.find((route) => route.key === e.target);\n\n      if (route?.name) {\n        routeNames.push(route.name);\n      }\n    } else {\n      route = state.routes[state.index];\n      routeNames.push(\n        ...Object.keys(screens).filter((name) => route?.name === name)\n      );\n    }\n\n    if (route == null) {\n      return;\n    }\n\n    const navigation = descriptors[route.key].navigation;\n\n    const listeners = ([] as (((e: any) => void) | undefined)[])\n      .concat(\n        // Get an array of listeners for all screens + common listeners on navigator\n        ...[\n          screenListeners,\n          ...routeNames.map((name) => {\n            const { listeners } = screens[name].props;\n            return listeners;\n          }),\n        ].map((listeners) => {\n          const map =\n            typeof listeners === 'function'\n              ? listeners({ route: route as any, navigation })\n              : listeners;\n\n          return map\n            ? Object.keys(map)\n                .filter((type) => type === e.type)\n                .map((type) => map?.[type])\n            : undefined;\n        })\n      )\n      // We don't want same listener to be called multiple times for same event\n      // So we remove any duplicate functions from the array\n      .filter((cb, i, self) => cb && self.lastIndexOf(cb) === i);\n\n    listeners.forEach((listener) => listener?.(e));\n  });\n\n  useFocusEvents({ state, emitter });\n\n  React.useEffect(() => {\n    emitter.emit({ type: 'state', data: { state } });\n  }, [emitter, state]);\n\n  const { listeners: childListeners, addListener } = useChildListeners();\n\n  const { keyedListeners, addKeyedListener } = useKeyedChildListeners();\n\n  const onAction = useOnAction({\n    router,\n    getState,\n    setState,\n    key: route?.key,\n    actionListeners: childListeners.action,\n    beforeRemoveListeners: keyedListeners.beforeRemove,\n    routerConfigOptions: {\n      routeNames,\n      routeParamList,\n      routeGetIdList,\n    },\n    emitter,\n  });\n\n  const onRouteFocus = useOnRouteFocus({\n    router,\n    key: route?.key,\n    getState,\n    setState,\n  });\n\n  const navigation = useNavigationHelpers<\n    State,\n    ActionHelpers,\n    NavigationAction,\n    EventMap\n  >({\n    id: options.id,\n    onAction,\n    getState,\n    emitter,\n    router,\n  });\n\n  useFocusedListenersChildrenAdapter({\n    navigation,\n    focusedListeners: childListeners.focus,\n  });\n\n  useOnGetState({\n    getState,\n    getStateListeners: keyedListeners.getState,\n  });\n\n  const descriptors = useDescriptors<\n    State,\n    ActionHelpers,\n    ScreenOptions,\n    EventMap\n  >({\n    state,\n    screens,\n    navigation,\n    screenOptions: options.screenOptions,\n    defaultScreenOptions: options.defaultScreenOptions,\n    onAction,\n    getState,\n    setState,\n    onRouteFocus,\n    addListener,\n    addKeyedListener,\n    router,\n    // @ts-expect-error: this should have both core and custom events, but too much work right now\n    emitter,\n  });\n\n  useCurrentRender({\n    state,\n    navigation,\n    descriptors,\n  });\n\n  const NavigationContent = useComponent((children: React.ReactNode) => (\n    <NavigationHelpersContext.Provider value={navigation}>\n      <PreventRemoveProvider>{children}</PreventRemoveProvider>\n    </NavigationHelpersContext.Provider>\n  ));\n\n  return {\n    state,\n    navigation,\n    descriptors,\n    NavigationContent,\n  };\n}\n"], "mappings": ";;;;;;;AAAA,SACEA,aAAa,QAUR,2BAA2B;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,UAAU;AAE7C,OAAOC,KAAK;AACZ,OAAOC,YAAY;AACnB,OAAOC,aAAa;AACpB,OAAOC,wBAAwB;AAC/B,OAAOC,sBAAsB;AAC7B,OAAOC,sBAAsB;AAC7B,OAAOC,qBAAqB;AAC5B,OAAOC,MAAM;AACb,SAKEC,iBAAiB;AAInB,OAAOC,iBAAiB;AACxB,OAAOC,YAAY;AACnB,OAAOC,gBAAgB;AACvB,OAAOC,cAAc;AACrB,OAAOC,eAAe;AACtB,OAAOC,kCAAkC;AACzC,OAAOC,cAAc;AACrB,OAAOC,sBAAsB;AAC7B,OAAOC,oBAAoB;AAC3B,OAAOC,WAAW;AAClB,OAAOC,aAAa;AACpB,OAAOC,eAAe;AACtB,OAAOC,oBAAoB;AAC3B,OAAOC,iBAAiB;AAIxBd,iBAAiB;AAqBjB,IAAMe,UAAU,GAAI,SAAdA,UAAUA,CAAIC,GAAY;EAAA,OAC9BA,GAAG,KAAKC,SAAS,IAAK,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,EAAG;AAAA;AAO9D,IAAME,4BAA2B,GAAG,SAA9BA,2BAA2BA,CAK/BC,QAAyB,EACzBC,QAAiB,EACjBC,YAIY,EACT;EACH,IAAMC,OAAO,GAAGhC,KAAK,CAACiC,QAAQ,CAACC,OAAO,CAACL,QAAQ,CAAC,CAACM,MAAM,CAErD,UAACC,GAAG,EAAEC,KAAK,EAAK;IAAA,IAAAC,WAAA,EAAAC,YAAA;IAChB,IAAIvC,KAAK,CAACwC,cAAc,CAACH,KAAK,CAAC,EAAE;MAC/B,IAAIA,KAAK,CAACI,IAAI,KAAKhC,MAAM,EAAE;QAIzB,IAAI,CAACgB,UAAU,CAACY,KAAK,CAACK,KAAK,CAACC,aAAa,CAAC,EAAE;UAC1C,MAAM,IAAIC,KAAK,CACZ,wCAAuCC,IAAI,CAACC,SAAS,CACpDT,KAAK,CAACK,KAAK,CAACC,aAAa,CACzB,qBACAN,KAAK,CAACK,KAAK,CAACK,IACb,kDAAiD,CACnD;QACH;QAEAX,GAAG,CAACY,IAAI,CAAC;UACPC,IAAI,EAAE,CAACnB,QAAQ,EAAEO,KAAK,CAACK,KAAK,CAACC,aAAa,CAAC;UAC3CO,OAAO,EAAEnB,YAAY;UACrBW,KAAK,EAAEL,KAAK,CAACK;QAOf,CAAC,CAAC;QACF,OAAON,GAAG;MACZ;MAEA,IAAIC,KAAK,CAACI,IAAI,KAAKzC,KAAK,CAACmD,QAAQ,IAAId,KAAK,CAACI,IAAI,KAAKvC,KAAK,EAAE;QACzD,IAAI,CAACuB,UAAU,CAACY,KAAK,CAACK,KAAK,CAACC,aAAa,CAAC,EAAE;UAC1C,MAAM,IAAIC,KAAK,CACZ,wCAAuCC,IAAI,CAACC,SAAS,CACpDT,KAAK,CAACK,KAAK,CAACC,aAAa,CACzB,gEAA+D,CAClE;QACH;QAIAP,GAAG,CAACY,IAAI,CAAAI,KAAA,CAARhB,GAAG,EAAAiB,kBAAA,CACEzB,4BAA2B,CAC5BS,KAAK,CAACK,KAAK,CAACb,QAAQ,EACpBQ,KAAK,CAACK,KAAK,CAACC,aAAa,EACzBN,KAAK,CAACI,IAAI,KAAKvC,KAAK,GAChB6B,YAAY,GACZA,YAAY,IAAI,IAAI,MAAAuB,MAAA,CAAAD,kBAAA,CAChBtB,YAAY,IAAEM,KAAK,CAACK,KAAK,CAACa,aAAa,KAC3C,CAAClB,KAAK,CAACK,KAAK,CAACa,aAAa,CAAC,CAChC,EACF;QACD,OAAOnB,GAAG;MACZ;IACF;IAEA,MAAM,IAAIQ,KAAK,CACZ,oGACC5C,KAAK,CAACwC,cAAc,CAACH,KAAK,CAAC,GACtB,IACC,OAAOA,KAAK,CAACI,IAAI,KAAK,QAAQ,GAAGJ,KAAK,CAACI,IAAI,IAAAH,WAAA,GAAGD,KAAK,CAACI,IAAI,cAAAH,WAAA,uBAAVA,WAAA,CAAYS,IAC3D,IACCV,KAAK,CAACK,KAAK,IAAI,IAAI,IACnB,OAAOL,KAAK,CAACK,KAAK,KAAK,QAAQ,IAC/B,MAAM,IAAIL,KAAK,CAACK,KAAK,KAAAH,YAAA,GACrBF,KAAK,CAACK,KAAK,cAAAH,YAAA,eAAXA,YAAA,CAAaQ,IAAI,GACZ,oBAAmBV,KAAK,CAACK,KAAK,CAACK,IAAK,GAAE,GACvC,EACL,EAAC,GACF,OAAOV,KAAK,KAAK,QAAQ,GACzBQ,IAAI,CAACC,SAAS,CAACT,KAAK,CAAC,GACpB,IAAGmB,MAAM,CAACnB,KAAK,CAAE,GACvB,4FAA2F,CAC7F;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC3B,OAAO,CAAC4B,OAAO,CAAE,UAAAC,MAAM,EAAK;MAC1B,IAAAC,aAAA,GAAoDD,MAAM,CAACnB,KAAK;QAAxDK,IAAI,GAAAe,aAAA,CAAJf,IAAI;QAAElB,QAAQ,GAAAiC,aAAA,CAARjC,QAAQ;QAAEkC,SAAS,GAAAD,aAAA,CAATC,SAAS;QAAEC,YAAA,GAAAF,aAAA,CAAAE,YAAA;MAEnC,IAAI,OAAOjB,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;QACrC,MAAM,IAAIH,KAAK,CACZ,wBAAuBC,IAAI,CAACC,SAAS,CACpCC,IAAI,CACJ,kDAAiD,CACpD;MACH;MAEA,IACElB,QAAQ,IAAI,IAAI,IAChBkC,SAAS,KAAKpC,SAAS,IACvBqC,YAAY,KAAKrC,SAAS,EAC1B;QACA,IAAIE,QAAQ,IAAI,IAAI,IAAIkC,SAAS,KAAKpC,SAAS,EAAE;UAC/C,MAAM,IAAIiB,KAAK,CACZ,6DAA4DG,IAAK,oCAAmC,CACtG;QACH;QAEA,IAAIlB,QAAQ,IAAI,IAAI,IAAImC,YAAY,KAAKrC,SAAS,EAAE;UAClD,MAAM,IAAIiB,KAAK,CACZ,gEAA+DG,IAAK,oCAAmC,CACzG;QACH;QAEA,IAAIgB,SAAS,KAAKpC,SAAS,IAAIqC,YAAY,KAAKrC,SAAS,EAAE;UACzD,MAAM,IAAIiB,KAAK,CACZ,iEAAgEG,IAAK,oCAAmC,CAC1G;QACH;QAEA,IAAIlB,QAAQ,IAAI,IAAI,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UACtD,MAAM,IAAIe,KAAK,CACZ,4DAA2DG,IAAK,qDAAoD,CACtH;QACH;QAEA,IAAIgB,SAAS,KAAKpC,SAAS,IAAI,CAAC1B,kBAAkB,CAAC8D,SAAS,CAAC,EAAE;UAC7D,MAAM,IAAInB,KAAK,CACZ,6DAA4DG,IAAK,wCAAuC,CAC1G;QACH;QAEA,IAAIiB,YAAY,KAAKrC,SAAS,IAAI,OAAOqC,YAAY,KAAK,UAAU,EAAE;UACpE,MAAM,IAAIpB,KAAK,CACZ,gEAA+DG,IAAK,uDAAsD,CAC5H;QACH;QAEA,IAAI,OAAOgB,SAAS,KAAK,UAAU,EAAE;UACnC,IAAIA,SAAS,CAAChB,IAAI,KAAK,WAAW,EAAE;YAIlCkB,OAAO,CAACC,IAAI,CACT,qFAAoFnB,IAAK,uRAAsR,CACjX;UACH,CAAC,MAAM,IAAI,QAAQ,CAACoB,IAAI,CAACJ,SAAS,CAAChB,IAAI,CAAC,EAAE;YACxCkB,OAAO,CAACC,IAAI,CACT,kCAAiCH,SAAS,CAAChB,IAAK,qBAAoBA,IAAK,yMAAwM,CACnR;UACH;QACF;MACF,CAAC,MAAM;QACL,MAAM,IAAIH,KAAK,CACZ,kFAAiFG,IAAK,qLAAoL,CAC5Q;MACH;IACF,CAAC,CAAC;EACJ;EAEA,OAAOf,OAAO;AAChB,CAAC;AASD,eAAe,SAASoC,oBAAoBA,CAO1CC,YAAsD,EACtDnB,OAOe,EACf;EACA,IAAMoB,YAAY,GAAG/C,oBAAoB,EAAE;EAE3C,IAAMgD,KAAK,GAAGvE,KAAK,CAACwE,UAAU,CAAClE,sBAAsB,CAExC;EAEb,IAAQuB,QAAQ,GAA+BqB,OAAO,CAA9CrB,QAAQ;IAAE4C,eAAe,GAAcvB,OAAO,CAApCuB,eAAe;IAAKC,IAAA,GAAAC,wBAAA,CAASzB,OAAO,EAAA0B,SAAA;EACtD,IAAAC,aAAA,GAA4B7E,KAAK,CAAC8E,MAAM,CACtCT,YAAY,CAAAU,aAAA,CAAAA,aAAA,KACNL,IAAiC,GACjCH,KAAK,aAALA,KAAK,eAALA,KAAK,CAAES,MAAM,IACjBT,KAAK,CAACS,MAAM,CAACC,KAAK,IAAI,IAAI,IAC1BV,KAAK,CAACS,MAAM,CAACE,OAAO,KAAK,KAAK,IAC9B,OAAOX,KAAK,CAACS,MAAM,CAACG,MAAM,KAAK,QAAQ,GACnC;MAAEC,gBAAgB,EAAEb,KAAK,CAACS,MAAM,CAACG;IAAO,CAAC,GACzC,IAAI,CACT,CAAC,CACH;IAVgBE,MAAA,GAAAR,aAAA,CAATS,OAAO;EAYf,IAAMC,YAAY,GAAG3D,4BAA2B,CAI9CC,QAAQ,CAAC;EAEX,IAAM2D,OAAO,GAAGD,YAAY,CAACpD,MAAM,CAEjC,UAACC,GAAG,EAAEyB,MAAM,EAAK;IACjB,IAAIA,MAAM,CAACnB,KAAK,CAACK,IAAI,IAAIX,GAAG,EAAE;MAC5B,MAAM,IAAIQ,KAAK,CACZ,6GAA4GiB,MAAM,CAACnB,KAAK,CAACK,IAAK,IAAG,CACnI;IACH;IAEAX,GAAG,CAACyB,MAAM,CAACnB,KAAK,CAACK,IAAI,CAAC,GAAGc,MAAM;IAC/B,OAAOzB,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,IAAMqD,UAAU,GAAGF,YAAY,CAACG,GAAG,CAAE,UAAA7B,MAAM;IAAA,OAAKA,MAAM,CAACnB,KAAK,CAACK,IAAI;EAAA,EAAC;EAClE,IAAM4C,YAAY,GAAGF,UAAU,CAACtD,MAAM,CACpC,UAACC,GAAG,EAAEwD,IAAI,EAAK;IACbxD,GAAG,CAACwD,IAAI,CAAC,GAAGJ,OAAO,CAACI,IAAI,CAAC,CAAC3C,IAAI,CAACyC,GAAG,CAAE,UAAAhE,GAAG;MAAA,OAAKA,GAAG,WAAHA,GAAG,GAAI,EAAE;IAAA,EAAC,CAACmE,IAAI,CAAC,GAAG,CAAC;IAChE,OAAOzD,GAAG;EACZ,CAAC,EACD,CAAC,CAAC,CACH;EACD,IAAM0D,cAAc,GAAGL,UAAU,CAACtD,MAAM,CACtC,UAACC,GAAG,EAAEwD,IAAI,EAAK;IACb,IAAQG,aAAA,GAAkBP,OAAO,CAACI,IAAI,CAAC,CAAClD,KAAK,CAArCqD,aAAA;IACR3D,GAAG,CAACwD,IAAI,CAAC,GAAGG,aAAa;IACzB,OAAO3D,GAAG;EACZ,CAAC,EACD,CAAC,CAAC,CACH;EACD,IAAM4D,cAAc,GAAGP,UAAU,CAACtD,MAAM,CAGtC,UAACC,GAAG,EAAEwD,IAAI;IAAA,OACRK,MAAM,CAACC,MAAM,CAAC9D,GAAG,EAAA+D,eAAA,KACdP,IAAI,EAAGJ,OAAO,CAACI,IAAI,CAAC,CAAClD,KAAK,CAAC0D,KAAA,CAC7B,CAAC;EAAA,GACJ,CAAC,CAAC,CACH;EAED,IAAI,CAACX,UAAU,CAACY,MAAM,EAAE;IACtB,MAAM,IAAIzD,KAAK,CACb,4FAA4F,CAC7F;EACH;EAEA,IAAM0D,YAAY,GAAGtG,KAAK,CAACuG,WAAW,CACnC,UAAAtB,KAAsD;IAAA,OACrDA,KAAK,CAACxC,IAAI,KAAKd,SAAS,IAAIsD,KAAK,CAACxC,IAAI,KAAK4C,MAAM,CAAC5C,IAAI;EAAA,GACxD,CAAC4C,MAAM,CAAC5C,IAAI,CAAC,CACd;EAED,IAAM+D,kBAAkB,GAAGxG,KAAK,CAACuG,WAAW,CACzC,UAAAtB,KAAkE;IAAA,OACjEA,KAAK,KAAKtD,SAAS,IAAIsD,KAAK,CAACwB,KAAK,KAAK,KAAK,IAAIH,YAAY,CAACrB,KAAK,CAAC;EAAA,GACrE,CAACqB,YAAY,CAAC,CACf;EAED,IAAAI,iBAAA,GAOI1G,KAAK,CAACwE,UAAU,CAACjE,sBAAsB,CAAC;IANnCoG,YAAY,GAAAD,iBAAA,CAAnBzB,KAAK;IACK2B,eAAe,GAAAF,iBAAA,CAAzBG,QAAQ;IACEC,eAAe,GAAAJ,iBAAA,CAAzBK,QAAQ;IACRC,MAAM,GAAAN,iBAAA,CAANM,MAAM;IACNC,MAAM,GAAAP,iBAAA,CAANO,MAAM;IACNC,YAAA,GAAAR,iBAAA,CAAAQ,YAAA;EAGF,IAAMC,cAAc,GAAGnH,KAAK,CAAC8E,MAAM,CAAC,KAAK,CAAC;EAE1C,IAAMsC,YAAY,GAAGpH,KAAK,CAACuG,WAAW,CAAC,YAAM;IAC3CO,eAAe,CAACnF,SAAS,CAAC;IAC1BwF,cAAc,CAAC7B,OAAO,GAAG,IAAI;EAC/B,CAAC,EAAE,CAACwB,eAAe,CAAC,CAAC;EAErB,IAAMC,QAAQ,GAAG/G,KAAK,CAACuG,WAAW,CAC/B,UAAAtB,KAAkE,EAAK;IACtE,IAAIkC,cAAc,CAAC7B,OAAO,EAAE;MAI1B;IACF;IACAwB,eAAe,CAAC7B,KAAK,CAAC;EACxB,CAAC,EACD,CAAC6B,eAAe,CAAC,CAClB;EAED,IAAAO,cAAA,GAAuDrH,KAAK,CAACsH,OAAO,CAAC,YAAM;MAAA,IAAAC,cAAA;MACzE,IAAMC,qBAAqB,GAAG/B,UAAU,CAACtD,MAAM,CAE7C,UAACC,GAAG,EAAEwD,IAAI,EAAK;QAAA,IAAA6B,aAAA,EAAAC,cAAA,EAAAC,cAAA;QACf,IAAQ5B,aAAA,GAAkBP,OAAO,CAACI,IAAI,CAAC,CAAClD,KAAK,CAArCqD,aAAA;QACR,IAAM6B,uBAAuB,GAC3B,CAAArD,KAAK,aAALA,KAAK,wBAAAkD,aAAA,GAALlD,KAAK,CAAES,MAAM,cAAAyC,aAAA,uBAAbA,aAAA,CAAexC,KAAK,KAAI,IAAI,IAC5B,CAAAV,KAAK,aAALA,KAAK,wBAAAmD,cAAA,GAALnD,KAAK,CAAES,MAAM,cAAA0C,cAAA,uBAAbA,cAAA,CAAexC,OAAO,MAAK,KAAK,IAChC,CAAAX,KAAK,aAALA,KAAK,wBAAAoD,cAAA,GAALpD,KAAK,CAAES,MAAM,cAAA2C,cAAA,uBAAbA,cAAA,CAAexC,MAAM,MAAKS,IAAI,GAC1BrB,KAAK,CAACS,MAAM,CAACA,MAAM,GACnBrD,SAAS;QAEfS,GAAG,CAACwD,IAAI,CAAC,GACPG,aAAa,KAAKpE,SAAS,IAAIiG,uBAAuB,KAAKjG,SAAS,GAAAoD,aAAA,CAAAA,aAAA,KAE3DgB,aAAa,GACb6B,uBAAA,IAELjG,SAAS;QAEf,OAAOS,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MAMN,IACE,CAACuE,YAAY,KAAKhF,SAAS,IAAI,CAAC2E,YAAY,CAACK,YAAY,CAAC,KAC1D,CAAApC,KAAK,aAALA,KAAK,wBAAAgD,cAAA,GAALhD,KAAK,CAAES,MAAM,cAAAuC,cAAA,uBAAbA,cAAA,CAAetC,KAAK,KAAI,IAAI,EAC5B;QACA,OAAO,CACLI,MAAM,CAACwC,eAAe,CAAC;UACrBpC,UAAU,EAAVA,UAAU;UACVK,cAAc,EAAE0B,qBAAqB;UACrCxB,cAAA,EAAAA;QACF,CAAC,CAAC,EACF,IAAI,CACL;MACH,CAAC,MAAM;QAAA,IAAA8B,IAAA;QAAA,IAAAC,cAAA;QACL,OAAO,CACL1C,MAAM,CAAC2C,kBAAkB,EAAAF,IAAA,GACvBvD,KAAK,aAALA,KAAK,wBAAAwD,cAAA,GAALxD,KAAK,CAAES,MAAM,cAAA+C,cAAA,uBAAbA,cAAA,CAAe9C,KAAK,YAAA6C,IAAA,GAAKnB,YAAoC,EAC7D;UACElB,UAAU,EAAVA,UAAU;UACVK,cAAc,EAAE0B,qBAAqB;UACrCxB,cAAA,EAAAA;QACF,CAAC,CACF,EACD,KAAK,CACN;MACH;IAOF,CAAC,EAAE,CAACW,YAAY,EAAEtB,MAAM,EAAEiB,YAAY,CAAC,CAAC;IAAA2B,eAAA,GAAAC,cAAA,CAAAb,cAAA;IA1DjCc,gBAAgB,GAAAF,eAAA;IAAEG,0BAA0B,GAAAH,eAAA;EA4DnD,IAAMI,uBAAuB,GAAGrI,KAAK,CAAC8E,MAAM,CAACa,YAAY,CAAC;EAE1D3F,KAAK,CAACsI,SAAS,CAAC,YAAM;IACpBD,uBAAuB,CAAC/C,OAAO,GAAGK,YAAY;EAChD,CAAC,CAAC;EAEF,IAAM4C,oBAAoB,GAAGF,uBAAuB,CAAC/C,OAAO;EAE5D,IAAIL,KAAK,GAIPuB,kBAAkB,CAACG,YAAY,CAAC,GAC3BA,YAAY,GACZwB,gBAA0B;EAEjC,IAAIK,SAAgB,GAAGvD,KAAK;EAE5B,IACE,CAAC9E,YAAY,CAAC8E,KAAK,CAACQ,UAAU,EAAEA,UAAU,CAAC,IAC3C,CAACrF,aAAa,CAACuF,YAAY,EAAE4C,oBAAoB,CAAC,EAClD;IAEAC,SAAS,GAAGnD,MAAM,CAACoD,2BAA2B,CAACxD,KAAK,EAAE;MACpDQ,UAAU,EAAVA,UAAU;MACVK,cAAc,EAAdA,cAAc;MACdE,cAAc,EAAdA,cAAc;MACd0C,eAAe,EAAEzC,MAAM,CAAChD,IAAI,CAAC0C,YAAY,CAAC,CAACgD,MAAM,CAC9C,UAAA5F,IAAI;QAAA,OACHwF,oBAAoB,CAACK,cAAc,CAAC7F,IAAI,CAAC,IACzC4C,YAAY,CAAC5C,IAAI,CAAC,KAAKwF,oBAAoB,CAACxF,IAAI,CAAC;MAAA;IAEvD,CAAC,CAAC;EACJ;EAEA,IAAM8F,uBAAuB,GAAG7I,KAAK,CAAC8E,MAAM,CAACP,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAES,MAAM,CAAC;EAE3DhF,KAAK,CAACsI,SAAS,CAAC,YAAM;IACpBO,uBAAuB,CAACvD,OAAO,GAAGf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAES,MAAM;EACjD,CAAC,EAAE,CAACT,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAES,MAAM,CAAC,CAAC;EAEnB,IAAIT,KAAK,aAALA,KAAK,eAALA,KAAK,CAAES,MAAM,EAAE;IACjB,IAAM8D,cAAc,GAAGD,uBAAuB,CAACvD,OAAO;IAEtD,IAAIyD,MAAwC;IAE5C,IACE,OAAOxE,KAAK,CAACS,MAAM,CAACC,KAAK,KAAK,QAAQ,IACtCV,KAAK,CAACS,MAAM,CAACC,KAAK,IAAI,IAAI,IAC1BV,KAAK,CAACS,MAAM,KAAK8D,cAAc,EAC/B;MAEAC,MAAM,GAAGhJ,aAAa,CAACiJ,KAAK,CAACzE,KAAK,CAACS,MAAM,CAACC,KAAK,CAAC;IAClD,CAAC,MAAM,IACL,OAAOV,KAAK,CAACS,MAAM,CAACG,MAAM,KAAK,QAAQ,KACrCZ,KAAK,CAACS,MAAM,CAACE,OAAO,KAAK,KAAK,IAAIkD,0BAA0B,IAC5D7D,KAAK,CAACS,MAAM,KAAK8D,cAAc,CAAC,EAClC;MAEAC,MAAM,GAAGhJ,aAAa,CAACkJ,QAAQ,CAAC;QAC9BlG,IAAI,EAAEwB,KAAK,CAACS,MAAM,CAACG,MAAM;QACzBH,MAAM,EAAET,KAAK,CAACS,MAAM,CAACA,MAAM;QAC3BkE,IAAI,EAAE3E,KAAK,CAACS,MAAM,CAACkE;MACrB,CAAC,CAAC;IACJ;IAGA,IAAMC,YAAY,GAAGJ,MAAM,GACvB1D,MAAM,CAAC+D,iBAAiB,CAACZ,SAAS,EAAEO,MAAM,EAAE;MAC1CtD,UAAU,EAAVA,UAAU;MACVK,cAAc,EAAdA,cAAc;MACdE,cAAA,EAAAA;IACF,CAAC,CAAC,GACF,IAAI;IAERwC,SAAS,GACPW,YAAY,KAAK,IAAI,GACjB9D,MAAM,CAAC2C,kBAAkB,CAACmB,YAAY,EAAE;MACtC1D,UAAU,EAAVA,UAAU;MACVK,cAAc,EAAdA,cAAc;MACdE,cAAA,EAAAA;IACF,CAAC,CAAC,GACFwC,SAAS;EACjB;EAEA,IAAMa,YAAY,GAAGpE,KAAK,KAAKuD,SAAS;EAExChH,iBAAiB,CAAC,YAAM;IACtB,IAAI6H,YAAY,EAAE;MAEhBtC,QAAQ,CAACyB,SAAS,CAAC;IACrB;EACF,CAAC,CAAC;EAKFvD,KAAK,GAAGuD,SAAS;EAEjBxI,KAAK,CAACsI,SAAS,CAAC,YAAM;IACpBtB,MAAM,CAAC1C,YAAY,CAAC;IAEpB,IAAI,CAAC4C,YAAY,EAAE,EAAE;MAInBH,QAAQ,CAACyB,SAAS,CAAC;IACrB;IAEA,OAAO,YAAM;MAKXc,UAAU,CAAC,YAAM;QACf,IAAI1C,eAAe,EAAE,KAAKjF,SAAS,IAAIsF,MAAM,EAAE,KAAK3C,YAAY,EAAE;UAChE8C,YAAY,EAAE;QAChB;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;EAEH,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMmC,mBAAmB,GAAGvJ,KAAK,CAAC8E,MAAM,EAAS;EACjDyE,mBAAmB,CAACjE,OAAO,GAAG6C,gBAAgB;EAE9C,IAAMtB,QAAQ,GAAG7G,KAAK,CAACuG,WAAW,CAAC,YAAa;IAC9C,IAAMI,YAAY,GAAGC,eAAe,EAAE;IAEtC,OAAOJ,kBAAkB,CAACG,YAAY,CAAC,GAClCA,YAAY,GACZ4C,mBAAmB,CAACjE,OAAiB;EAC5C,CAAC,EAAE,CAACsB,eAAe,EAAEJ,kBAAkB,CAAC,CAAC;EAEzC,IAAMgD,OAAO,GAAGzI,eAAe,CAAuB,UAAA0I,CAAC,EAAK;IAAA,IAAAC,KAAA;IAC1D,IAAIjE,UAAU,GAAG,EAAE;IAEnB,IAAIlB,KAAgC;IAEpC,IAAIkF,CAAC,CAACE,MAAM,EAAE;MAAA,IAAAC,MAAA;MACZrF,KAAK,GAAGU,KAAK,CAAC4E,MAAM,CAACC,IAAI,CAAE,UAAAvF,KAAK;QAAA,OAAKA,KAAK,CAAC7C,GAAG,KAAK+H,CAAC,CAACE,MAAM;MAAA,EAAC;MAE5D,KAAAC,MAAA,GAAIrF,KAAK,cAAAqF,MAAA,eAALA,MAAA,CAAO7G,IAAI,EAAE;QACf0C,UAAU,CAACzC,IAAI,CAACuB,KAAK,CAACxB,IAAI,CAAC;MAC7B;IACF,CAAC,MAAM;MACLwB,KAAK,GAAGU,KAAK,CAAC4E,MAAM,CAAC5E,KAAK,CAAC8E,KAAK,CAAC;MACjCtE,UAAU,CAACzC,IAAI,CAAAI,KAAA,CAAfqC,UAAU,EAAApC,kBAAA,CACL4C,MAAM,CAAChD,IAAI,CAACuC,OAAO,CAAC,CAACmD,MAAM,CAAE,UAAA5F,IAAI;QAAA,IAAAiH,OAAA;QAAA,OAAK,EAAAA,OAAA,GAAAzF,KAAK,cAAAyF,OAAA,uBAALA,OAAA,CAAOjH,IAAI,MAAKA,IAAI;MAAA,EAAC,EAC/D;IACH;IAEA,IAAIwB,KAAK,IAAI,IAAI,EAAE;MACjB;IACF;IAEA,IAAM0F,UAAU,GAAGC,WAAW,CAAC3F,KAAK,CAAC7C,GAAG,CAAC,CAACuI,UAAU;IAEpD,IAAME,SAAS,GAAI,CAAAT,KAAA,KAAE,EAClBpG,MAAM,CAAAF,KAAA,CAAAsG,KAAA,EAAArG,kBAAA,CAEF,CACDoB,eAAe,EAAAnB,MAAA,CAAAD,kBAAA,CACZoC,UAAU,CAACC,GAAG,CAAE,UAAA3C,IAAI,EAAK;MAC1B,IAAQoH,SAAA,GAAc3E,OAAO,CAACzC,IAAI,CAAC,CAACL,KAAK,CAAjCyH,SAAA;MACR,OAAOA,SAAS;IAClB,CAAC,CAAC,GACFzE,GAAG,CAAE,UAAAyE,SAAS,EAAK;MACnB,IAAMzE,GAAG,GACP,OAAOyE,SAAS,KAAK,UAAU,GAC3BA,SAAS,CAAC;QAAE5F,KAAK,EAAEA,KAAY;QAAE0F,UAAA,EAAAA;MAAW,CAAC,CAAC,GAC9CE,SAAS;MAEf,OAAOzE,GAAG,GACNO,MAAM,CAAChD,IAAI,CAACyC,GAAG,CAAC,CACbiD,MAAM,CAAE,UAAAlG,IAAI;QAAA,OAAKA,IAAI,KAAKgH,CAAC,CAAChH,IAAI;MAAA,EAAC,CACjCiD,GAAG,CAAE,UAAAjD,IAAI;QAAA,OAAKiD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAGjD,IAAI,CAAC;MAAA,EAAC,GAC7Bd,SAAS;IACf,CAAC,CAAC,GAIHgH,MAAM,CAAC,UAACyB,EAAE,EAAEC,CAAC,EAAEC,IAAI;MAAA,OAAKF,EAAE,IAAIE,IAAI,CAACC,WAAW,CAACH,EAAE,CAAC,KAAKC,CAAC;IAAA,EAAC;IAE5DF,SAAS,CAACvG,OAAO,CAAE,UAAA4G,QAAQ;MAAA,OAAKA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGf,CAAC,CAAC;IAAA,EAAC;EAChD,CAAC,CAAC;EAEFxI,cAAc,CAAC;IAAEgE,KAAK,EAALA,KAAK;IAAEuE,OAAA,EAAAA;EAAQ,CAAC,CAAC;EAElCxJ,KAAK,CAACsI,SAAS,CAAC,YAAM;IACpBkB,OAAO,CAACiB,IAAI,CAAC;MAAEhI,IAAI,EAAE,OAAO;MAAEiI,IAAI,EAAE;QAAEzF,KAAA,EAAAA;MAAM;IAAE,CAAC,CAAC;EAClD,CAAC,EAAE,CAACuE,OAAO,EAAEvE,KAAK,CAAC,CAAC;EAEpB,IAAA0F,kBAAA,GAAmDhK,iBAAiB,EAAE;IAAnDiK,cAAc,GAAAD,kBAAA,CAAzBR,SAAS;IAAkBU,WAAA,GAAAF,kBAAA,CAAAE,WAAA;EAEnC,IAAAC,qBAAA,GAA6C5J,sBAAsB,EAAE;IAA7D6J,cAAc,GAAAD,qBAAA,CAAdC,cAAc;IAAEC,gBAAA,GAAAF,qBAAA,CAAAE,gBAAA;EAExB,IAAMC,QAAQ,GAAG7J,WAAW,CAAC;IAC3BiE,MAAM,EAANA,MAAM;IACNwB,QAAQ,EAARA,QAAQ;IACRE,QAAQ,EAARA,QAAQ;IACRrF,GAAG,EAAE6C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE7C,GAAG;IACfwJ,eAAe,EAAEN,cAAc,CAAC7B,MAAM;IACtCoC,qBAAqB,EAAEJ,cAAc,CAACK,YAAY;IAClDC,mBAAmB,EAAE;MACnB5F,UAAU,EAAVA,UAAU;MACVK,cAAc,EAAdA,cAAc;MACdE,cAAA,EAAAA;IACF,CAAC;IACDwD,OAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAM8B,YAAY,GAAGhK,eAAe,CAAC;IACnC+D,MAAM,EAANA,MAAM;IACN3D,GAAG,EAAE6C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE7C,GAAG;IACfmF,QAAQ,EAARA,QAAQ;IACRE,QAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAMkD,UAAU,GAAG9I,oBAAoB,CAKrC;IACAoK,EAAE,EAAErI,OAAO,CAACqI,EAAE;IACdN,QAAQ,EAARA,QAAQ;IACRpE,QAAQ,EAARA,QAAQ;IACR2C,OAAO,EAAPA,OAAO;IACPnE,MAAA,EAAAA;EACF,CAAC,CAAC;EAEFrE,kCAAkC,CAAC;IACjCiJ,UAAU,EAAVA,UAAU;IACVuB,gBAAgB,EAAEZ,cAAc,CAACa;EACnC,CAAC,CAAC;EAEFpK,aAAa,CAAC;IACZwF,QAAQ,EAARA,QAAQ;IACR6E,iBAAiB,EAAEX,cAAc,CAAClE;EACpC,CAAC,CAAC;EAEF,IAAMqD,WAAW,GAAGpJ,cAAc,CAKhC;IACAmE,KAAK,EAALA,KAAK;IACLO,OAAO,EAAPA,OAAO;IACPyE,UAAU,EAAVA,UAAU;IACV1G,aAAa,EAAEL,OAAO,CAACK,aAAa;IACpCoI,oBAAoB,EAAEzI,OAAO,CAACyI,oBAAoB;IAClDV,QAAQ,EAARA,QAAQ;IACRpE,QAAQ,EAARA,QAAQ;IACRE,QAAQ,EAARA,QAAQ;IACRuE,YAAY,EAAZA,YAAY;IACZT,WAAW,EAAXA,WAAW;IACXG,gBAAgB,EAAhBA,gBAAgB;IAChB3F,MAAM,EAANA,MAAM;IAENmE,OAAA,EAAAA;EACF,CAAC,CAAC;EAEF3I,gBAAgB,CAAC;IACfoE,KAAK,EAALA,KAAK;IACLgF,UAAU,EAAVA,UAAU;IACVC,WAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAM0B,iBAAiB,GAAGhL,YAAY,CAAE,UAAAiB,QAAyB;IAAA,OAC/D7B,KAAA,CAAA6L,aAAA,CAACxL,wBAAwB,CAACyL,QAAQ;MAACC,KAAK,EAAE9B;IAAW,GACnDjK,KAAA,CAAA6L,aAAA,CAACrL,qBAAqB,QAAEqB,QAAQ,CAAyB,CAE5D;EAAA,EAAC;EAEF,OAAO;IACLoD,KAAK,EAALA,KAAK;IACLgF,UAAU,EAAVA,UAAU;IACVC,WAAW,EAAXA,WAAW;IACX0B,iBAAA,EAAAA;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}