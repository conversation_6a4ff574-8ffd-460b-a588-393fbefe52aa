{"ast": null, "code": "import normalizeValueWithProperty from \"./normalizeValueWithProperty\";\nimport canUseDOM from \"../../../modules/canUseDom\";\nvar emptyObject = {};\nvar supportsCSS3TextDecoration = !canUseDOM || window.CSS != null && window.CSS.supports != null && (window.CSS.supports('text-decoration-line', 'none') || window.CSS.supports('-webkit-text-decoration-line', 'none'));\nvar MONOSPACE_FONT_STACK = 'monospace,monospace';\nvar SYSTEM_FONT_STACK = '-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif';\nvar STYLE_SHORT_FORM_EXPANSIONS = {\n  borderColor: ['borderTopColor', 'borderRightColor', 'borderBottomColor', 'borderLeftColor'],\n  borderBlockColor: ['borderTopColor', 'borderBottomColor'],\n  borderInlineColor: ['borderRightColor', 'borderLeftColor'],\n  borderRadius: ['borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomRightRadius', 'borderBottomLeftRadius'],\n  borderStyle: ['borderTopStyle', 'borderRightStyle', 'borderBottomStyle', 'borderLeftStyle'],\n  borderBlockStyle: ['borderTopStyle', 'borderBottomStyle'],\n  borderInlineStyle: ['borderRightStyle', 'borderLeftStyle'],\n  borderWidth: ['borderTopWidth', 'borderRightWidth', 'borderBottomWidth', 'borderLeftWidth'],\n  borderBlockWidth: ['borderTopWidth', 'borderBottomWidth'],\n  borderInlineWidth: ['borderRightWidth', 'borderLeftWidth'],\n  insetBlock: ['top', 'bottom'],\n  insetInline: ['left', 'right'],\n  marginBlock: ['marginTop', 'marginBottom'],\n  marginInline: ['marginRight', 'marginLeft'],\n  paddingBlock: ['paddingTop', 'paddingBottom'],\n  paddingInline: ['paddingRight', 'paddingLeft'],\n  overflow: ['overflowX', 'overflowY'],\n  overscrollBehavior: ['overscrollBehaviorX', 'overscrollBehaviorY'],\n  borderBlockStartColor: ['borderTopColor'],\n  borderBlockStartStyle: ['borderTopStyle'],\n  borderBlockStartWidth: ['borderTopWidth'],\n  borderBlockEndColor: ['borderBottomColor'],\n  borderBlockEndStyle: ['borderBottomStyle'],\n  borderBlockEndWidth: ['borderBottomWidth'],\n  borderEndStartRadius: ['borderBottomLeftRadius'],\n  borderEndEndRadius: ['borderBottomRightRadius'],\n  borderStartStartRadius: ['borderTopLeftRadius'],\n  borderStartEndRadius: ['borderTopRightRadius'],\n  insetBlockEnd: ['bottom'],\n  insetBlockStart: ['top'],\n  marginBlockStart: ['marginTop'],\n  marginBlockEnd: ['marginBottom'],\n  paddingBlockStart: ['paddingTop'],\n  paddingBlockEnd: ['paddingBottom']\n};\nvar createReactDOMStyle = function createReactDOMStyle(style, isInline) {\n  if (!style) {\n    return emptyObject;\n  }\n  var resolvedStyle = {};\n  var _loop = function _loop() {\n    var value = style[prop];\n    if (value == null) {\n      return \"continue\";\n    }\n    if (prop === 'backgroundClip') {\n      if (value === 'text') {\n        resolvedStyle.backgroundClip = value;\n        resolvedStyle.WebkitBackgroundClip = value;\n      }\n    } else if (prop === 'flex') {\n      if (value === -1) {\n        resolvedStyle.flexGrow = 0;\n        resolvedStyle.flexShrink = 1;\n        resolvedStyle.flexBasis = 'auto';\n      } else {\n        resolvedStyle.flex = value;\n      }\n    } else if (prop === 'font') {\n      resolvedStyle[prop] = value.replace('System', SYSTEM_FONT_STACK);\n    } else if (prop === 'fontFamily') {\n      if (value.indexOf('System') > -1) {\n        var stack = value.split(/,\\s*/);\n        stack[stack.indexOf('System')] = SYSTEM_FONT_STACK;\n        resolvedStyle[prop] = stack.join(',');\n      } else if (value === 'monospace') {\n        resolvedStyle[prop] = MONOSPACE_FONT_STACK;\n      } else {\n        resolvedStyle[prop] = value;\n      }\n    } else if (prop === 'textDecorationLine') {\n      if (!supportsCSS3TextDecoration) {\n        resolvedStyle.textDecoration = value;\n      } else {\n        resolvedStyle.textDecorationLine = value;\n      }\n    } else if (prop === 'writingDirection') {\n      resolvedStyle.direction = value;\n    } else {\n      var _value = normalizeValueWithProperty(style[prop], prop);\n      var longFormProperties = STYLE_SHORT_FORM_EXPANSIONS[prop];\n      if (isInline && prop === 'inset') {\n        if (style.insetInline == null) {\n          resolvedStyle.left = _value;\n          resolvedStyle.right = _value;\n        }\n        if (style.insetBlock == null) {\n          resolvedStyle.top = _value;\n          resolvedStyle.bottom = _value;\n        }\n      } else if (isInline && prop === 'margin') {\n        if (style.marginInline == null) {\n          resolvedStyle.marginLeft = _value;\n          resolvedStyle.marginRight = _value;\n        }\n        if (style.marginBlock == null) {\n          resolvedStyle.marginTop = _value;\n          resolvedStyle.marginBottom = _value;\n        }\n      } else if (isInline && prop === 'padding') {\n        if (style.paddingInline == null) {\n          resolvedStyle.paddingLeft = _value;\n          resolvedStyle.paddingRight = _value;\n        }\n        if (style.paddingBlock == null) {\n          resolvedStyle.paddingTop = _value;\n          resolvedStyle.paddingBottom = _value;\n        }\n      } else if (longFormProperties) {\n        longFormProperties.forEach(function (longForm, i) {\n          if (style[longForm] == null) {\n            resolvedStyle[longForm] = _value;\n          }\n        });\n      } else {\n        resolvedStyle[prop] = _value;\n      }\n    }\n  };\n  for (var prop in style) {\n    var _ret = _loop();\n    if (_ret === \"continue\") continue;\n  }\n  return resolvedStyle;\n};\nexport default createReactDOMStyle;", "map": {"version": 3, "names": ["normalizeValueWithProperty", "canUseDOM", "emptyObject", "supportsCSS3TextDecoration", "window", "CSS", "supports", "MONOSPACE_FONT_STACK", "SYSTEM_FONT_STACK", "STYLE_SHORT_FORM_EXPANSIONS", "borderColor", "borderBlockColor", "borderInlineColor", "borderRadius", "borderStyle", "borderBlockStyle", "borderInlineStyle", "borderWidth", "borderBlockWidth", "borderInlineWidth", "insetBlock", "insetInline", "marginBlock", "marginInline", "paddingBlock", "paddingInline", "overflow", "overscroll<PERSON><PERSON><PERSON><PERSON>", "borderBlockStartColor", "borderBlockStartStyle", "borderBlockStartWidth", "borderBlockEndColor", "borderBlockEndStyle", "borderBlockEndWidth", "borderEndStartRadius", "borderEndEndRadius", "borderStartStartRadius", "borderStartEndRadius", "insetBlockEnd", "insetBlockStart", "marginBlockStart", "marginBlockEnd", "paddingBlockStart", "paddingBlockEnd", "createReactDOMStyle", "style", "isInline", "resolvedStyle", "_loop", "value", "prop", "backgroundClip", "WebkitBackgroundClip", "flexGrow", "flexShrink", "flexBasis", "flex", "replace", "indexOf", "stack", "split", "join", "textDecoration", "textDecorationLine", "direction", "_value", "longFormProperties", "left", "right", "top", "bottom", "marginLeft", "marginRight", "marginTop", "marginBottom", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "for<PERSON>ach", "longForm", "i", "_ret"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/StyleSheet/compiler/createReactDOMStyle.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport normalizeValueWithProperty from './normalizeValueWithProperty';\nimport canUseDOM from '../../../modules/canUseDom';\n/**\n * The browser implements the CSS cascade, where the order of properties is a\n * factor in determining which styles to paint. React Native is different. It\n * gives giving precedence to the more specific style property. For example,\n * the value of `paddingTop` takes precedence over that of `padding`.\n *\n * This module creates mutally exclusive style declarations by expanding all of\n * React Native's supported shortform properties (e.g. `padding`) to their\n * longfrom equivalents.\n */\n\nvar emptyObject = {};\nvar supportsCSS3TextDecoration = !canUseDOM || window.CSS != null && window.CSS.supports != null && (window.CSS.supports('text-decoration-line', 'none') || window.CSS.supports('-webkit-text-decoration-line', 'none'));\nvar MONOSPACE_FONT_STACK = 'monospace,monospace';\nvar SYSTEM_FONT_STACK = '-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif';\nvar STYLE_SHORT_FORM_EXPANSIONS = {\n  borderColor: ['borderTopColor', 'borderRightColor', 'borderBottomColor', 'borderLeftColor'],\n  borderBlockColor: ['borderTopColor', 'borderBottomColor'],\n  borderInlineColor: ['borderRightColor', 'borderLeftColor'],\n  borderRadius: ['borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomRightRadius', 'borderBottomLeftRadius'],\n  borderStyle: ['borderTopStyle', 'borderRightStyle', 'borderBottomStyle', 'borderLeftStyle'],\n  borderBlockStyle: ['borderTopStyle', 'borderBottomStyle'],\n  borderInlineStyle: ['borderRightStyle', 'borderLeftStyle'],\n  borderWidth: ['borderTopWidth', 'borderRightWidth', 'borderBottomWidth', 'borderLeftWidth'],\n  borderBlockWidth: ['borderTopWidth', 'borderBottomWidth'],\n  borderInlineWidth: ['borderRightWidth', 'borderLeftWidth'],\n  insetBlock: ['top', 'bottom'],\n  insetInline: ['left', 'right'],\n  marginBlock: ['marginTop', 'marginBottom'],\n  marginInline: ['marginRight', 'marginLeft'],\n  paddingBlock: ['paddingTop', 'paddingBottom'],\n  paddingInline: ['paddingRight', 'paddingLeft'],\n  overflow: ['overflowX', 'overflowY'],\n  overscrollBehavior: ['overscrollBehaviorX', 'overscrollBehaviorY'],\n  borderBlockStartColor: ['borderTopColor'],\n  borderBlockStartStyle: ['borderTopStyle'],\n  borderBlockStartWidth: ['borderTopWidth'],\n  borderBlockEndColor: ['borderBottomColor'],\n  borderBlockEndStyle: ['borderBottomStyle'],\n  borderBlockEndWidth: ['borderBottomWidth'],\n  //borderInlineStartColor: ['borderLeftColor'],\n  //borderInlineStartStyle: ['borderLeftStyle'],\n  //borderInlineStartWidth: ['borderLeftWidth'],\n  //borderInlineEndColor: ['borderRightColor'],\n  //borderInlineEndStyle: ['borderRightStyle'],\n  //borderInlineEndWidth: ['borderRightWidth'],\n  borderEndStartRadius: ['borderBottomLeftRadius'],\n  borderEndEndRadius: ['borderBottomRightRadius'],\n  borderStartStartRadius: ['borderTopLeftRadius'],\n  borderStartEndRadius: ['borderTopRightRadius'],\n  insetBlockEnd: ['bottom'],\n  insetBlockStart: ['top'],\n  //insetInlineEnd: ['right'],\n  //insetInlineStart: ['left'],\n  marginBlockStart: ['marginTop'],\n  marginBlockEnd: ['marginBottom'],\n  //marginInlineStart: ['marginLeft'],\n  //marginInlineEnd: ['marginRight'],\n  paddingBlockStart: ['paddingTop'],\n  paddingBlockEnd: ['paddingBottom']\n  //paddingInlineStart: ['marginLeft'],\n  //paddingInlineEnd: ['marginRight'],\n};\n\n/**\n * Reducer\n */\n\nvar createReactDOMStyle = (style, isInline) => {\n  if (!style) {\n    return emptyObject;\n  }\n  var resolvedStyle = {};\n  var _loop = function _loop() {\n    var value = style[prop];\n    if (\n    // Ignore everything with a null value\n    value == null) {\n      return \"continue\";\n    }\n    if (prop === 'backgroundClip') {\n      // TODO: remove once this issue is fixed\n      // https://github.com/rofrischmann/inline-style-prefixer/issues/159\n      if (value === 'text') {\n        resolvedStyle.backgroundClip = value;\n        resolvedStyle.WebkitBackgroundClip = value;\n      }\n    } else if (prop === 'flex') {\n      if (value === -1) {\n        resolvedStyle.flexGrow = 0;\n        resolvedStyle.flexShrink = 1;\n        resolvedStyle.flexBasis = 'auto';\n      } else {\n        resolvedStyle.flex = value;\n      }\n    } else if (prop === 'font') {\n      resolvedStyle[prop] = value.replace('System', SYSTEM_FONT_STACK);\n    } else if (prop === 'fontFamily') {\n      if (value.indexOf('System') > -1) {\n        var stack = value.split(/,\\s*/);\n        stack[stack.indexOf('System')] = SYSTEM_FONT_STACK;\n        resolvedStyle[prop] = stack.join(',');\n      } else if (value === 'monospace') {\n        resolvedStyle[prop] = MONOSPACE_FONT_STACK;\n      } else {\n        resolvedStyle[prop] = value;\n      }\n    } else if (prop === 'textDecorationLine') {\n      // use 'text-decoration' for browsers that only support CSS2\n      // text-decoration (e.g., IE, Edge)\n      if (!supportsCSS3TextDecoration) {\n        resolvedStyle.textDecoration = value;\n      } else {\n        resolvedStyle.textDecorationLine = value;\n      }\n    } else if (prop === 'writingDirection') {\n      resolvedStyle.direction = value;\n    } else {\n      var _value = normalizeValueWithProperty(style[prop], prop);\n      var longFormProperties = STYLE_SHORT_FORM_EXPANSIONS[prop];\n      if (isInline && prop === 'inset') {\n        if (style.insetInline == null) {\n          resolvedStyle.left = _value;\n          resolvedStyle.right = _value;\n        }\n        if (style.insetBlock == null) {\n          resolvedStyle.top = _value;\n          resolvedStyle.bottom = _value;\n        }\n      } else if (isInline && prop === 'margin') {\n        if (style.marginInline == null) {\n          resolvedStyle.marginLeft = _value;\n          resolvedStyle.marginRight = _value;\n        }\n        if (style.marginBlock == null) {\n          resolvedStyle.marginTop = _value;\n          resolvedStyle.marginBottom = _value;\n        }\n      } else if (isInline && prop === 'padding') {\n        if (style.paddingInline == null) {\n          resolvedStyle.paddingLeft = _value;\n          resolvedStyle.paddingRight = _value;\n        }\n        if (style.paddingBlock == null) {\n          resolvedStyle.paddingTop = _value;\n          resolvedStyle.paddingBottom = _value;\n        }\n      } else if (longFormProperties) {\n        longFormProperties.forEach((longForm, i) => {\n          // The value of any longform property in the original styles takes\n          // precedence over the shortform's value.\n          if (style[longForm] == null) {\n            resolvedStyle[longForm] = _value;\n          }\n        });\n      } else {\n        resolvedStyle[prop] = _value;\n      }\n    }\n  };\n  for (var prop in style) {\n    var _ret = _loop();\n    if (_ret === \"continue\") continue;\n  }\n  return resolvedStyle;\n};\nexport default createReactDOMStyle;"], "mappings": "AASA,OAAOA,0BAA0B;AACjC,OAAOC,SAAS;AAYhB,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,0BAA0B,GAAG,CAACF,SAAS,IAAIG,MAAM,CAACC,GAAG,IAAI,IAAI,IAAID,MAAM,CAACC,GAAG,CAACC,QAAQ,IAAI,IAAI,KAAKF,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,sBAAsB,EAAE,MAAM,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;AACxN,IAAIC,oBAAoB,GAAG,qBAAqB;AAChD,IAAIC,iBAAiB,GAAG,+EAA+E;AACvG,IAAIC,2BAA2B,GAAG;EAChCC,WAAW,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;EAC3FC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;EACzDC,iBAAiB,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;EAC1DC,YAAY,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,wBAAwB,CAAC;EAClHC,WAAW,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;EAC3FC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;EACzDC,iBAAiB,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;EAC1DC,WAAW,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;EAC3FC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;EACzDC,iBAAiB,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;EAC1DC,UAAU,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC7BC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC9BC,WAAW,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC;EAC1CC,YAAY,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC;EAC3CC,YAAY,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC;EAC7CC,aAAa,EAAE,CAAC,cAAc,EAAE,aAAa,CAAC;EAC9CC,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;EACpCC,kBAAkB,EAAE,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;EAClEC,qBAAqB,EAAE,CAAC,gBAAgB,CAAC;EACzCC,qBAAqB,EAAE,CAAC,gBAAgB,CAAC;EACzCC,qBAAqB,EAAE,CAAC,gBAAgB,CAAC;EACzCC,mBAAmB,EAAE,CAAC,mBAAmB,CAAC;EAC1CC,mBAAmB,EAAE,CAAC,mBAAmB,CAAC;EAC1CC,mBAAmB,EAAE,CAAC,mBAAmB,CAAC;EAO1CC,oBAAoB,EAAE,CAAC,wBAAwB,CAAC;EAChDC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC;EAC/CC,sBAAsB,EAAE,CAAC,qBAAqB,CAAC;EAC/CC,oBAAoB,EAAE,CAAC,sBAAsB,CAAC;EAC9CC,aAAa,EAAE,CAAC,QAAQ,CAAC;EACzBC,eAAe,EAAE,CAAC,KAAK,CAAC;EAGxBC,gBAAgB,EAAE,CAAC,WAAW,CAAC;EAC/BC,cAAc,EAAE,CAAC,cAAc,CAAC;EAGhCC,iBAAiB,EAAE,CAAC,YAAY,CAAC;EACjCC,eAAe,EAAE,CAAC,eAAe;AAGnC,CAAC;AAMD,IAAIC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIC,KAAK,EAAEC,QAAQ,EAAK;EAC7C,IAAI,CAACD,KAAK,EAAE;IACV,OAAO3C,WAAW;EACpB;EACA,IAAI6C,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B,IAAIC,KAAK,GAAGJ,KAAK,CAACK,IAAI,CAAC;IACvB,IAEAD,KAAK,IAAI,IAAI,EAAE;MACb,OAAO,UAAU;IACnB;IACA,IAAIC,IAAI,KAAK,gBAAgB,EAAE;MAG7B,IAAID,KAAK,KAAK,MAAM,EAAE;QACpBF,aAAa,CAACI,cAAc,GAAGF,KAAK;QACpCF,aAAa,CAACK,oBAAoB,GAAGH,KAAK;MAC5C;IACF,CAAC,MAAM,IAAIC,IAAI,KAAK,MAAM,EAAE;MAC1B,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBF,aAAa,CAACM,QAAQ,GAAG,CAAC;QAC1BN,aAAa,CAACO,UAAU,GAAG,CAAC;QAC5BP,aAAa,CAACQ,SAAS,GAAG,MAAM;MAClC,CAAC,MAAM;QACLR,aAAa,CAACS,IAAI,GAAGP,KAAK;MAC5B;IACF,CAAC,MAAM,IAAIC,IAAI,KAAK,MAAM,EAAE;MAC1BH,aAAa,CAACG,IAAI,CAAC,GAAGD,KAAK,CAACQ,OAAO,CAAC,QAAQ,EAAEjD,iBAAiB,CAAC;IAClE,CAAC,MAAM,IAAI0C,IAAI,KAAK,YAAY,EAAE;MAChC,IAAID,KAAK,CAACS,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;QAChC,IAAIC,KAAK,GAAGV,KAAK,CAACW,KAAK,CAAC,MAAM,CAAC;QAC/BD,KAAK,CAACA,KAAK,CAACD,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAGlD,iBAAiB;QAClDuC,aAAa,CAACG,IAAI,CAAC,GAAGS,KAAK,CAACE,IAAI,CAAC,GAAG,CAAC;MACvC,CAAC,MAAM,IAAIZ,KAAK,KAAK,WAAW,EAAE;QAChCF,aAAa,CAACG,IAAI,CAAC,GAAG3C,oBAAoB;MAC5C,CAAC,MAAM;QACLwC,aAAa,CAACG,IAAI,CAAC,GAAGD,KAAK;MAC7B;IACF,CAAC,MAAM,IAAIC,IAAI,KAAK,oBAAoB,EAAE;MAGxC,IAAI,CAAC/C,0BAA0B,EAAE;QAC/B4C,aAAa,CAACe,cAAc,GAAGb,KAAK;MACtC,CAAC,MAAM;QACLF,aAAa,CAACgB,kBAAkB,GAAGd,KAAK;MAC1C;IACF,CAAC,MAAM,IAAIC,IAAI,KAAK,kBAAkB,EAAE;MACtCH,aAAa,CAACiB,SAAS,GAAGf,KAAK;IACjC,CAAC,MAAM;MACL,IAAIgB,MAAM,GAAGjE,0BAA0B,CAAC6C,KAAK,CAACK,IAAI,CAAC,EAAEA,IAAI,CAAC;MAC1D,IAAIgB,kBAAkB,GAAGzD,2BAA2B,CAACyC,IAAI,CAAC;MAC1D,IAAIJ,QAAQ,IAAII,IAAI,KAAK,OAAO,EAAE;QAChC,IAAIL,KAAK,CAACxB,WAAW,IAAI,IAAI,EAAE;UAC7B0B,aAAa,CAACoB,IAAI,GAAGF,MAAM;UAC3BlB,aAAa,CAACqB,KAAK,GAAGH,MAAM;QAC9B;QACA,IAAIpB,KAAK,CAACzB,UAAU,IAAI,IAAI,EAAE;UAC5B2B,aAAa,CAACsB,GAAG,GAAGJ,MAAM;UAC1BlB,aAAa,CAACuB,MAAM,GAAGL,MAAM;QAC/B;MACF,CAAC,MAAM,IAAInB,QAAQ,IAAII,IAAI,KAAK,QAAQ,EAAE;QACxC,IAAIL,KAAK,CAACtB,YAAY,IAAI,IAAI,EAAE;UAC9BwB,aAAa,CAACwB,UAAU,GAAGN,MAAM;UACjClB,aAAa,CAACyB,WAAW,GAAGP,MAAM;QACpC;QACA,IAAIpB,KAAK,CAACvB,WAAW,IAAI,IAAI,EAAE;UAC7ByB,aAAa,CAAC0B,SAAS,GAAGR,MAAM;UAChClB,aAAa,CAAC2B,YAAY,GAAGT,MAAM;QACrC;MACF,CAAC,MAAM,IAAInB,QAAQ,IAAII,IAAI,KAAK,SAAS,EAAE;QACzC,IAAIL,KAAK,CAACpB,aAAa,IAAI,IAAI,EAAE;UAC/BsB,aAAa,CAAC4B,WAAW,GAAGV,MAAM;UAClClB,aAAa,CAAC6B,YAAY,GAAGX,MAAM;QACrC;QACA,IAAIpB,KAAK,CAACrB,YAAY,IAAI,IAAI,EAAE;UAC9BuB,aAAa,CAAC8B,UAAU,GAAGZ,MAAM;UACjClB,aAAa,CAAC+B,aAAa,GAAGb,MAAM;QACtC;MACF,CAAC,MAAM,IAAIC,kBAAkB,EAAE;QAC7BA,kBAAkB,CAACa,OAAO,CAAC,UAACC,QAAQ,EAAEC,CAAC,EAAK;UAG1C,IAAIpC,KAAK,CAACmC,QAAQ,CAAC,IAAI,IAAI,EAAE;YAC3BjC,aAAa,CAACiC,QAAQ,CAAC,GAAGf,MAAM;UAClC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLlB,aAAa,CAACG,IAAI,CAAC,GAAGe,MAAM;MAC9B;IACF;EACF,CAAC;EACD,KAAK,IAAIf,IAAI,IAAIL,KAAK,EAAE;IACtB,IAAIqC,IAAI,GAAGlC,KAAK,CAAC,CAAC;IAClB,IAAIkC,IAAI,KAAK,UAAU,EAAE;EAC3B;EACA,OAAOnC,aAAa;AACtB,CAAC;AACD,eAAeH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}