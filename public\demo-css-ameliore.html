<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - CSS Amélioré</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .content {
            padding: 30px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .before, .after {
            padding: 25px;
            border-radius: 15px;
            border: 2px solid;
        }
        
        .before {
            background: #f8f9fa;
            border-color: #dee2e6;
        }
        
        .after {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-color: #667eea;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
        }
        
        .before h3 {
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .after h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .demo-form {
            background: white;
            padding: 25px;
            border-radius: 16px;
            margin: 20px 0;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.special-1 {
            background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);
            padding: 16px;
            border-radius: 12px;
            border: 2px solid #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        
        .form-group.special-2 {
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            padding: 16px;
            border-radius: 12px;
            border: 2px solid #10b981;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
        }
        
        .form-group.special-3 {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            padding: 16px;
            border-radius: 12px;
            border: 2px solid #f59e0b;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
            letter-spacing: 0.3px;
        }
        
        .special-1 .form-label {
            color: #667eea;
            font-weight: 700;
            font-size: 17px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .special-2 .form-label {
            color: #10b981;
            font-weight: 700;
            font-size: 17px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .special-3 .form-label {
            color: #f59e0b;
            font-weight: 700;
            font-size: 17px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 14px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 15px;
            color: #1e293b;
            background: #ffffff;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        
        .auto-filled {
            background: linear-gradient(135deg, #f0fff4 0%, #dcfce7 100%);
            border-color: #10b981;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
        }
        
        .btn {
            padding: 16px 24px;
            border: none;
            border-radius: 16px;
            font-size: 17px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        
        .btn-maps {
            background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            padding: 8px 16px;
            font-size: 13px;
            border-radius: 10px;
        }
        
        .btn-error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 32px rgba(0,0,0,0.15);
        }
        
        .feature-card h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .color-item {
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            color: white;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        
        .color-blue {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .color-green {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .color-red {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        .color-orange {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        
        .highlight {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #10b981;
        }
        
        .highlight h3 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .highlight ul {
            color: #155724;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 CSS Amélioré</h1>
            <p>Interface moderne et élégante pour la page Consommation</p>
        </div>
        
        <div class="content">
            <div class="comparison">
                <div class="before">
                    <h3>❌ Avant - Style Basique</h3>
                    <ul style="line-height: 1.6; color: #6c757d;">
                        <li>Couleurs plates et ternes</li>
                        <li>Bordures fines et carrées</li>
                        <li>Ombres simples ou absentes</li>
                        <li>Espacement minimal</li>
                        <li>Typographie monotone</li>
                        <li>Interface fade et peu attrayante</li>
                    </ul>
                </div>
                
                <div class="after">
                    <h3>✅ Après - Style Moderne</h3>
                    <ul style="line-height: 1.6; color: #667eea;">
                        <li>Dégradés de couleurs élégants</li>
                        <li>Bordures arrondies et épaisses</li>
                        <li>Ombres portées sophistiquées</li>
                        <li>Espacement généreux et aéré</li>
                        <li>Typographie variée et stylisée</li>
                        <li>Interface moderne et attrayante</li>
                    </ul>
                </div>
            </div>
            
            <div class="demo-form">
                <h3 style="color: #667eea; margin-bottom: 25px; text-align: center;">📱 Aperçu du Formulaire Modernisé</h3>
                
                <div class="form-group special-1">
                    <label class="form-label">📍 Secteur</label>
                    <select class="form-select">
                        <option>Centre-Ville</option>
                        <option>Quartier Industriel</option>
                    </select>
                </div>
                
                <div class="form-group special-2">
                    <label class="form-label">👤 Client</label>
                    <select class="form-select">
                        <option>Benali Fatima</option>
                        <option>Alami Mohammed</option>
                    </select>
                </div>
                
                <div class="form-group special-3">
                    <label class="form-label">📄 Contrat <button class="btn-maps">🗺️ Google Maps</button></label>
                    <input type="text" class="form-input auto-filled" value="Contrat 1 - QR: QR001" readonly>
                </div>
                
                <div class="form-group">
                    <label class="form-label">📅 Période</label>
                    <input type="text" class="form-input" placeholder="2024-12">
                </div>
                
                <div class="form-group">
                    <label class="form-label">📊 Consommation Précédente (m³)</label>
                    <input type="text" class="form-input auto-filled" value="45" readonly>
                </div>
                
                <div class="form-group">
                    <label class="form-label">⚡ Consommation Actuelle (m³)</label>
                    <input type="number" class="form-input" placeholder="Doit être > 45">
                </div>
                
                <div class="form-group">
                    <label class="form-label">📅 Nombre de jours</label>
                    <input type="text" class="form-input auto-filled" value="31" readonly>
                </div>
                
                <button class="btn btn-success" style="width: 100%; margin: 20px 0 0 0;">
                    💾 Enregistrer la Consommation
                </button>
            </div>
            
            <div class="features">
                <div class="feature-card">
                    <h4>🎨 Dégradés Élégants</h4>
                    <p>Utilisation de dégradés linéaires à 135° pour créer des effets visuels modernes et attrayants sur tous les éléments importants.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🌟 Ombres Sophistiquées</h4>
                    <p>Ombres portées colorées et assorties aux éléments, avec différents niveaux d'élévation pour créer de la profondeur.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🔄 Transitions Fluides</h4>
                    <p>Animations et transitions CSS pour un feedback visuel immédiat et une expérience utilisateur plus agréable.</p>
                </div>
                
                <div class="feature-card">
                    <h4>📱 Design Responsive</h4>
                    <p>Interface optimisée pour tous les écrans avec des espacements et des tailles adaptés aux appareils mobiles.</p>
                </div>
            </div>
            
            <div class="color-palette">
                <div class="color-item color-blue">
                    🔵 Bleu Principal<br>
                    #667eea → #764ba2
                </div>
                <div class="color-item color-green">
                    🟢 Vert Succès<br>
                    #10b981 → #059669
                </div>
                <div class="color-item color-red">
                    🔴 Rouge Erreur<br>
                    #ef4444 → #dc2626
                </div>
                <div class="color-item color-orange">
                    🟡 Orange Contrat<br>
                    #f59e0b → #d97706
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="http://localhost:19006" class="btn btn-primary" target="_blank">
                    📱 Voir l'Application Réelle
                </a>
                <button class="btn btn-success" onclick="showDemo()">
                    ✨ Animation Démo
                </button>
                <button class="btn btn-error" onclick="showError()">
                    ❌ Test Erreur
                </button>
            </div>
            
            <div class="highlight">
                <h3>✅ Améliorations CSS Implémentées</h3>
                <ul>
                    <li>✅ <strong>Header moderne :</strong> Dégradé violet-bleu avec ombres sophistiquées</li>
                    <li>✅ <strong>Formulaire élégant :</strong> Bordures arrondies et espacement généreux</li>
                    <li>✅ <strong>Champs colorés :</strong> Dégradés spécifiques pour chaque section importante</li>
                    <li>✅ <strong>Inputs modernisés :</strong> Bordures épaisses et ombres subtiles</li>
                    <li>✅ <strong>Boutons stylisés :</strong> Dégradés et transitions fluides</li>
                    <li>✅ <strong>Palette harmonieuse :</strong> Couleurs cohérentes et contrastes optimisés</li>
                    <li>✅ <strong>Typographie améliorée :</strong> Poids variés et espacement des lettres</li>
                    <li>✅ <strong>Effets visuels :</strong> Ombres colorées et transparences subtiles</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        function showDemo() {
            const form = document.querySelector('.demo-form');
            form.style.transform = 'scale(1.02)';
            form.style.boxShadow = '0 16px 48px rgba(102, 126, 234, 0.25)';
            
            setTimeout(() => {
                form.style.transform = 'scale(1)';
                form.style.boxShadow = '0 8px 24px rgba(0,0,0,0.1)';
            }, 1000);
        }
        
        function showError() {
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                if (!input.readOnly) {
                    input.style.borderColor = '#ef4444';
                    input.style.background = 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)';
                    input.style.boxShadow = '0 4px 12px rgba(239, 68, 68, 0.15)';
                    
                    setTimeout(() => {
                        input.style.borderColor = '#e2e8f0';
                        input.style.background = '#ffffff';
                        input.style.boxShadow = '0 2px 8px rgba(0,0,0,0.05)';
                    }, 2000);
                }
            });
        }
        
        // Animation d'entrée
        window.addEventListener('load', () => {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
