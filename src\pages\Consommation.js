import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator
} from 'react-native';

// Composant Picker personnalisé compatible React Native Web
const CustomPicker = ({ selectedValue, onValueChange, children, style }) => {
  return (
    <select
      value={selectedValue}
      onChange={(e) => onValueChange(e.target.value)}
      style={{
        width: '100%',
        padding: 10, 
        fontSize: 14, 
        height: 40, 
        borderWidth: 1,
        borderColor: '#d1d5db',
        borderRadius: 6, 
        backgroundColor: 'white',
        ...style
      }}
    >
      {children}
    </select>
  );
};

const PickerItem = ({ label, value }) => {
  return <option value={value}>{label}</option>;
};

const Consommation = () => {
  const [formData, setFormData] = useState({
    periode: '',
    idClient: '',
    idContract: '',
    idSecteur: '',
    consommationActuelle: '',
    consommationPre: '',
    jours: ''
  });

  const [periodeError, setPeriodeError] = useState('');
  const [contracts, setContracts] = useState([]);
  const [secteurs, setSecteurs] = useState([]);
  const [clientsDuSecteur, setClientsDuSecteur] = useState([]);
  const [secteurSelectionne, setSecteurSelectionne] = useState(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [afficherCarte, setAfficherCarte] = useState(false);

  useEffect(() => {
    fetchSecteurs();
  }, []);

  // Fonction de validation de la période
  const validerPeriode = (periode) => {
    console.log('🔍 Validation période:', periode);

    if (!periode) {
      setPeriodeError('');
      return true;
    }

    // Vérifier le format YYYY-MM
    const formatRegex = /^\d{4}-\d{2}$/;
    if (!formatRegex.test(periode)) {
      setPeriodeError('Format invalide. Utilisez YYYY-MM (ex: 2024-12)');
      return false;
    }

    // Obtenir la date actuelle
    const maintenant = new Date();
    const moisActuel = maintenant.getFullYear() + '-' + String(maintenant.getMonth() + 1).padStart(2, '0');

    console.log('📅 Mois actuel:', moisActuel);
    console.log('📅 Période saisie:', periode);

    // Vérifier si la période est antérieure au mois actuel
    if (periode >= moisActuel) {
      const [annee, mois] = periode.split('-');
      const nomsMois = [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
      ];
      const nomMois = nomsMois[parseInt(mois) - 1] || mois;

      if (periode === moisActuel) {
        setPeriodeError(`❌ Impossible de saisir le mois actuel (${nomMois} ${annee}). Seulement les mois précédents sont autorisés.`);
      } else {
        setPeriodeError(`❌ Impossible de saisir un mois futur (${nomMois} ${annee}). Seulement les mois précédents sont autorisés.`);
      }
      return false;
    }

    // Période valide
    setPeriodeError('');
    console.log('✅ Période valide:', periode);
    return true;
  };

  // Gestionnaire de changement de période avec validation
  const handlePeriodeChange = (text) => {
    console.log('🔄 Changement période:', text);

    // Mettre à jour la valeur
    setFormData(prev => ({ ...prev, periode: text }));

    // Valider la période
    validerPeriode(text);
  };



  const fetchContracts = async (clientId) => {
    try {
      console.log('🔍 DEBUT fetchContracts - Client ID:', clientId);

      // Données de test pour les contrats par client
      const contratsParClientTest = {
        '1': [ // Benali Fatima
          { idcontract: 1, codeqr: 'QR001', datecontract: '2024-01-15', marquecompteur: 'Sensus', numseriecompteur: 'SN001', posx: 33.5731, posy: -7.5898 }
        ],
        '2': [ // Alami Mohammed
          { idcontract: 2, codeqr: 'QR002', datecontract: '2024-02-10', marquecompteur: 'Itron', numseriecompteur: 'SN002', posx: 33.5735, posy: -7.5895 }
        ],
        '3': [ // Tazi Aicha
          { idcontract: 3, codeqr: 'QR003', datecontract: '2024-01-20', marquecompteur: 'Sensus', numseriecompteur: 'SN003', posx: 33.5740, posy: -7.5890 }
        ],
        '4': [ // Benjelloun Youssef
          { idcontract: 4, codeqr: 'QR004', datecontract: '2024-03-05', marquecompteur: 'Elster', numseriecompteur: 'SN004', posx: 33.5831, posy: -7.5998 },
          { idcontract: 5, codeqr: 'QR005', datecontract: '2024-03-10', marquecompteur: 'Itron', numseriecompteur: 'SN005', posx: 33.5835, posy: -7.5995 }
        ],
        '5': [ // Lahlou Khadija
          { idcontract: 6, codeqr: 'QR006', datecontract: '2024-02-15', marquecompteur: 'Sensus', numseriecompteur: 'SN006', posx: 33.5840, posy: -7.5990 }
        ],
        '6': [ // Fassi Omar
          { idcontract: 7, codeqr: 'QR007', datecontract: '2024-01-25', marquecompteur: 'Elster', numseriecompteur: 'SN007', posx: 33.5931, posy: -7.6098 }
        ],
        '7': [ // Chraibi Salma
          { idcontract: 8, codeqr: 'QR008', datecontract: '2024-02-20', marquecompteur: 'Itron', numseriecompteur: 'SN008', posx: 33.5935, posy: -7.6095 }
        ],
        '8': [ // Idrissi Hassan
          { idcontract: 9, codeqr: 'QR009', datecontract: '2024-03-01', marquecompteur: 'Sensus', numseriecompteur: 'SN009', posx: 33.5940, posy: -7.6090 }
        ],
        '9': [ // Berrada Nadia
          { idcontract: 10, codeqr: 'QR010', datecontract: '2024-01-30', marquecompteur: 'Elster', numseriecompteur: 'SN010', posx: 33.5631, posy: -7.5798 }
        ],
        '10': [ // Kettani Rachid
          { idcontract: 11, codeqr: 'QR011', datecontract: '2024-02-25', marquecompteur: 'Itron', numseriecompteur: 'SN011', posx: 33.5635, posy: -7.5795 }
        ],
        '11': [ // Amrani Leila
          { idcontract: 12, codeqr: 'QR012', datecontract: '2024-03-15', marquecompteur: 'Sensus', numseriecompteur: 'SN012', posx: 33.5531, posy: -7.5698 }
        ],
        '12': [ // Zouaki Karim
          { idcontract: 13, codeqr: 'QR013', datecontract: '2024-02-28', marquecompteur: 'Elster', numseriecompteur: 'SN013', posx: 33.5535, posy: -7.5695 }
        ]
      };

      console.log(`📊 Contrats de test disponibles pour le client ${clientId}:`, contratsParClientTest[clientId]?.length || 0);

      // Essayer d'abord l'API, sinon utiliser les données de test
      try {
        const response = await fetch(`http://localhost:4002/api/clients/${clientId}/contracts`);
        console.log(`📥 Réponse API contrats, status: ${response.status}`);

        if (response.ok) {
          const result = await response.json();
          console.log('📊 Réponse API contrats:', result);

          if (result.success && Array.isArray(result.data) && result.data.length > 0) {
            console.log(`✅ API OK - ${result.data.length} contrats du client ${clientId}`);
            setContracts(result.data);

            // Sélection automatique si un seul contrat
            if (result.data.length === 1) {
              setFormData(prev => ({ ...prev, idContract: result.data[0].idcontract }));
              console.log('✅ Contrat unique sélectionné automatiquement');
            }
            return;
          }
        }
      } catch (apiError) {
        console.log('⚠️ API contrats non accessible, utilisation des données de test');
      }

      // Utiliser les données de test si l'API ne fonctionne pas
      const contratsDeTest = contratsParClientTest[clientId] || [];
      console.log(`✅ Utilisation des données de test: ${contratsDeTest.length} contrats pour le client ${clientId}`);

      setContracts(contratsDeTest);

      if (contratsDeTest.length > 0) {
        console.log(`📄 CONTRATS TROUVÉS - Client ${clientId}:`);
        contratsDeTest.forEach((contrat, index) => {
          console.log(`   ${index + 1}. Contrat ${contrat.idcontract} - QR: ${contrat.codeqr} - ${contrat.marquecompteur}`);
        });

        // Sélection automatique si un seul contrat
        if (contratsDeTest.length === 1) {
          setFormData(prev => ({ ...prev, idContract: contratsDeTest[0].idcontract }));
          console.log('✅ Contrat unique sélectionné automatiquement');
        }

        console.log(`🎯 Le champ Contrat affiche maintenant ${contratsDeTest.length} contrat(s) pour ce client`);
      } else {
        console.log(`ℹ️ Aucun contrat de test pour le client ${clientId}`);
      }

    } catch (err) {
      console.error('❌ ERREUR dans fetchContracts:', err);
      setContracts([]);
    }
  };

  const fetchSecteurs = async () => {
    try {
      console.log('🔍 DEBUT fetchSecteurs - Récupération des secteurs...');

      // Données de test directes de la table secteur
      const secteursDeTest = [
        { ids: 1, nom: 'Centre-Ville', latitude: 33.5731, longitude: -7.5898 },
        { ids: 2, nom: 'Quartier Industriel', latitude: 33.5831, longitude: -7.5998 },
        { ids: 3, nom: 'Zone Résidentielle Nord', latitude: 33.5931, longitude: -7.6098 },
        { ids: 4, nom: 'Zone Résidentielle Sud', latitude: 33.5631, longitude: -7.5798 },
        { ids: 5, nom: 'Quartier Commercial', latitude: 33.5531, longitude: -7.5698 }
      ];

      console.log('📊 Utilisation des données de test directes');
      console.log('� Secteurs de la table:', secteursDeTest);

      // Essayer d'abord l'API, sinon utiliser les données de test
      try {
        console.log('� Tentative API:', 'http://localhost:4002/api/secteurs');
        const response = await fetch('http://localhost:4002/api/secteurs', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        console.log('� Réponse API, status:', response.status, response.statusText);

        if (response.ok) {
          const result = await response.json();
          console.log('📊 Réponse API secteurs:', result);

          if (result.success && Array.isArray(result.data) && result.data.length > 0) {
            console.log('✅ API OK - Utilisation des données API:', result.data.length, 'secteurs');
            setSecteurs(result.data);
            return;
          }
        }
      } catch (apiError) {
        console.log('⚠️ API non accessible, utilisation des données de test');
      }

      // Utiliser les données de test si l'API ne fonctionne pas
      console.log('✅ Utilisation des données de test de la table secteur');
      setSecteurs(secteursDeTest);
      console.log('✅ Secteurs chargés depuis les données de test:', secteursDeTest.length);

    } catch (err) {
      console.error('❌ ERREUR dans fetchSecteurs:', err);

      // En cas d'erreur, utiliser les données de test par défaut
      const secteursParDefaut = [
        { ids: 1, nom: 'Centre-Ville', latitude: 33.5731, longitude: -7.5898 },
        { ids: 2, nom: 'Quartier Industriel', latitude: 33.5831, longitude: -7.5998 },
        { ids: 3, nom: 'Zone Résidentielle Nord', latitude: 33.5931, longitude: -7.6098 },
        { ids: 4, nom: 'Zone Résidentielle Sud', latitude: 33.5631, longitude: -7.5798 },
        { ids: 5, nom: 'Quartier Commercial', latitude: 33.5531, longitude: -7.5698 }
      ];

      console.log('🔄 Utilisation des secteurs par défaut');
      setSecteurs(secteursParDefaut);
    }
  };

  const fetchClientsDuSecteur = async (secteurId) => {
    try {
      console.log(`🔍 DEBUT fetchClientsDuSecteur - Secteur ID: ${secteurId}`);

      // Données de test pour les clients par secteur (relation secteur-client)
      const clientsParSecteurTest = {
        '1': [ // Centre-Ville
          { idclient: 1, nom: 'Benali', prenom: 'Fatima', adresse: '12 Rue Mohammed V', ville: 'Sefrou', tel: '0661234567', email: '<EMAIL>', ids: 1, secteur_nom: 'Centre-Ville' },
          { idclient: 2, nom: 'Alami', prenom: 'Mohammed', adresse: '25 Avenue Hassan II', ville: 'Sefrou', tel: '0662345678', email: '<EMAIL>', ids: 1, secteur_nom: 'Centre-Ville' },
          { idclient: 3, nom: 'Tazi', prenom: 'Aicha', adresse: '8 Place de la Liberté', ville: 'Sefrou', tel: '0663456789', email: '<EMAIL>', ids: 1, secteur_nom: 'Centre-Ville' }
        ],
        '2': [ // Quartier Industriel
          { idclient: 4, nom: 'Benjelloun', prenom: 'Youssef', adresse: '45 Zone Industrielle', ville: 'Sefrou', tel: '0664567890', email: '<EMAIL>', ids: 2, secteur_nom: 'Quartier Industriel' },
          { idclient: 5, nom: 'Lahlou', prenom: 'Khadija', adresse: '67 Rue de l\'Industrie', ville: 'Sefrou', tel: '0665678901', email: '<EMAIL>', ids: 2, secteur_nom: 'Quartier Industriel' }
        ],
        '3': [ // Zone Résidentielle Nord
          { idclient: 6, nom: 'Fassi', prenom: 'Omar', adresse: '123 Quartier Nord', ville: 'Sefrou', tel: '0666789012', email: '<EMAIL>', ids: 3, secteur_nom: 'Zone Résidentielle Nord' },
          { idclient: 7, nom: 'Chraibi', prenom: 'Salma', adresse: '89 Résidence Al Amal', ville: 'Sefrou', tel: '0667890123', email: '<EMAIL>', ids: 3, secteur_nom: 'Zone Résidentielle Nord' },
          { idclient: 8, nom: 'Idrissi', prenom: 'Hassan', adresse: '34 Villa des Roses', ville: 'Sefrou', tel: '0668901234', email: '<EMAIL>', ids: 3, secteur_nom: 'Zone Résidentielle Nord' }
        ],
        '4': [ // Zone Résidentielle Sud
          { idclient: 9, nom: 'Berrada', prenom: 'Nadia', adresse: '56 Quartier Sud', ville: 'Sefrou', tel: '0669012345', email: '<EMAIL>', ids: 4, secteur_nom: 'Zone Résidentielle Sud' },
          { idclient: 10, nom: 'Kettani', prenom: 'Rachid', adresse: '78 Résidence Al Baraka', ville: 'Sefrou', tel: '0660123456', email: '<EMAIL>', ids: 4, secteur_nom: 'Zone Résidentielle Sud' }
        ],
        '5': [ // Quartier Commercial
          { idclient: 11, nom: 'Amrani', prenom: 'Leila', adresse: '90 Centre Commercial', ville: 'Sefrou', tel: '0661234567', email: '<EMAIL>', ids: 5, secteur_nom: 'Quartier Commercial' },
          { idclient: 12, nom: 'Zouaki', prenom: 'Karim', adresse: '12 Marché Central', ville: 'Sefrou', tel: '0662345678', email: '<EMAIL>', ids: 5, secteur_nom: 'Quartier Commercial' }
        ]
      };

      console.log(`📊 Clients de test disponibles pour le secteur ${secteurId}:`, clientsParSecteurTest[secteurId]?.length || 0);

      // Essayer d'abord l'API, sinon utiliser les données de test
      try {
        const url = `http://localhost:4002/api/secteurs/${secteurId}/clients`;
        console.log(`📡 Tentative API: ${url}`);

        const response = await fetch(url);
        console.log(`📥 Réponse API clients, status: ${response.status}`);

        if (response.ok) {
          const result = await response.json();
          console.log('📊 Réponse API clients du secteur:', result);

          if (result.success && Array.isArray(result.data) && result.data.length > 0) {
            console.log(`✅ API OK - ${result.data.length} clients du secteur ${secteurId}`);
            setClientsDuSecteur(result.data);
            return;
          }
        }
      } catch (apiError) {
        console.log('⚠️ API clients non accessible, utilisation des données de test');
      }

      // Utiliser les données de test si l'API ne fonctionne pas
      const clientsDeTest = clientsParSecteurTest[secteurId] || [];
      console.log(`✅ Utilisation des données de test: ${clientsDeTest.length} clients pour le secteur ${secteurId}`);

      setClientsDuSecteur(clientsDeTest);

      if (clientsDeTest.length > 0) {
        console.log(`👥 FILTRAGE RÉUSSI - Clients du secteur ${secteurId}:`);
        clientsDeTest.forEach((client, index) => {
          console.log(`   ${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`);
        });
        console.log(`🎯 Le champ Client affiche maintenant ${clientsDeTest.length} client(s) de ce secteur uniquement`);
      } else {
        console.log(`ℹ️ Aucun client de test pour le secteur ${secteurId}`);
      }

    } catch (err) {
      console.error('❌ ERREUR dans fetchClientsDuSecteur:', err);
      setClientsDuSecteur([]);
    }
  };

  // Composant Google Maps intégré
  const GoogleMapsComponent = ({ clients, secteur }) => {
    if (!clients || clients.length === 0) {
      return null;
    }

    // Calculer le centre de la carte basé sur le secteur ou les clients
    const centreLatitude = secteur?.latitude || 33.5731;
    const centreLongitude = secteur?.longitude || -7.5898;

    // Créer les marqueurs pour chaque client
    const marqueurs = clients.map((client, index) => {
      const lat = client.latitude || (centreLatitude + (Math.random() - 0.5) * 0.01);
      const lng = client.longitude || (centreLongitude + (Math.random() - 0.5) * 0.01);
      return {
        lat,
        lng,
        nom: `${client.nom} ${client.prenom}`,
        adresse: client.adresse
      };
    });

    // Créer l'URL avec marqueurs multiples pour Google Maps
    const marqueurParams = marqueurs.map(m => `markers=color:red%7C${m.lat},${m.lng}`).join('&');
    const staticMapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${centreLatitude},${centreLongitude}&zoom=14&size=600x300&maptype=roadmap&${marqueurParams}&key=AIzaSyBFw0Qbyq9zTFTd-tUY6dw901SwHSR3g-0`;

    return (
      <View style={styles.mapContainer}>
        <Text style={styles.mapTitle}>
          🗺️ Carte du secteur: {secteur?.nom || 'Secteur sélectionné'}
        </Text>
        <Text style={styles.mapSubtitle}>
          📍 {clients.length} client(s) dans ce secteur
        </Text>

        {/* Carte Google Maps statique avec marqueurs */}
        <TouchableOpacity
          onPress={() => ouvrirGoogleMapsAvecClients(clients)}
          style={styles.mapImageContainer}
        >
          <img
            src={staticMapUrl}
            alt={`Carte du secteur ${secteur?.nom || ''}`}
            style={styles.mapImage}
            onError={(e) => {
              console.error('Erreur chargement carte:', e);
              e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkNhcnRlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';
            }}
          />
          <View style={styles.mapOverlay}>
            <Text style={styles.mapOverlayText}>
              👆 Cliquer pour ouvrir dans Google Maps
            </Text>
          </View>
        </TouchableOpacity>

        {/* Liste des marqueurs */}
        <View style={styles.markersInfo}>
          <Text style={styles.markersTitle}>📍 Emplacements des clients:</Text>
          {marqueurs.slice(0, 3).map((marqueur, index) => (
            <Text key={index} style={styles.markerText}>
              • {marqueur.nom} - {marqueur.adresse}
            </Text>
          ))}
          {marqueurs.length > 3 && (
            <Text style={styles.markerText}>
              ... et {marqueurs.length - 3} autre(s) client(s)
            </Text>
          )}
        </View>
      </View>
    );
  };

  const ouvrirGoogleMapsAvecClients = (clients) => {
    try {
      console.log('🗺️ Ouverture de Google Maps avec', clients.length, 'clients');

      if (clients.length === 0) {
        Alert.alert('Information', 'Aucun client trouvé dans ce secteur');
        return;
      }

      // Créer les marqueurs pour chaque client avec leurs coordonnées
      const marqueurs = clients.map((client, index) => {
        const lat = client.latitude || (33.5731 + Math.random() * 0.01);
        const lng = client.longitude || (-7.5898 + Math.random() * 0.01);
        console.log(`📍 Client ${index + 1}: ${client.nom} ${client.prenom} - Lat: ${lat}, Lng: ${lng}`);
        return `${lat},${lng}`;
      });

      // Construire l'URL Google Maps avec plusieurs marqueurs
      const premierMarqueur = marqueurs[0];
      const tousLesMarqueurs = marqueurs.join('|');

      // URL Google Maps avec marqueurs multiples
      const googleMapsUrl = `https://www.google.com/maps?q=${premierMarqueur}&markers=${tousLesMarqueurs}`;

      // Ouvrir dans un nouvel onglet
      window.open(googleMapsUrl, '_blank');

      console.log('✅ Google Maps ouvert avec', clients.length, 'clients du secteur');
      console.log('🔗 URL:', googleMapsUrl);
    } catch (error) {
      console.error('❌ Erreur ouverture Google Maps:', error);
      Alert.alert('Erreur', 'Impossible d\'ouvrir Google Maps');
    }
  };

  const handleSecteurChange = (secteurId) => {
    console.log('🔄 Changement de secteur:', secteurId);

    // Réinitialiser les données client
    setFormData(prev => ({
      ...prev,
      idSecteur: secteurId,
      idClient: '', // Réinitialiser le client sélectionné
      idContract: '' // Réinitialiser le contrat
    }));
    setContracts([]); // Vider les contrats

    if (secteurId) {
      // Trouver le secteur sélectionné
      const secteur = secteurs.find(s => s.ids === parseInt(secteurId));
      setSecteurSelectionne(secteur);

      console.log(`📍 Secteur sélectionné: ${secteur?.nom} (ID: ${secteurId})`);

      // Récupérer les clients de ce secteur spécifique
      fetchClientsDuSecteur(secteurId);
      // Afficher la carte automatiquement
      setAfficherCarte(true);
    } else {
      console.log('❌ Aucun secteur sélectionné');
      setSecteurSelectionne(null);
      setClientsDuSecteur([]);
      setAfficherCarte(false);
    }
  };

  // Fonction pour gérer la sélection du client
  const handleClientChange = (clientId) => {
    console.log('🔄 Changement de client ID:', clientId);

    // Mettre à jour le formulaire
    setFormData(prev => ({
      ...prev,
      idClient: clientId,
      idContract: '' // Réinitialiser le contrat quand on change de client
    }));

    if (clientId && clientId !== '') {
      // Trouver le client sélectionné dans la liste des clients du secteur
      const client = clientsDuSecteur.find(c => c.idclient.toString() === clientId.toString());

      if (client) {
        console.log(`✅ Client sélectionné: ${client.nom} ${client.prenom} (ID: ${clientId})`);

        // Récupérer les contrats de ce client
        fetchContracts(clientId);
      } else {
        console.log('❌ Client non trouvé dans la liste');
      }
    } else {
      console.log('❌ Aucun client sélectionné');
      setContracts([]);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setMessage('');

    // Validation de la période
    if (!validerPeriode(formData.periode)) {
      Alert.alert(
        'Période Invalide',
        periodeError || 'Veuillez saisir une période valide (mois précédent uniquement)',
        [{ text: 'OK', style: 'default' }]
      );
      setLoading(false);
      return;
    }

    // Validation des consommations
    if (parseFloat(formData.consommationActuelle) <= parseFloat(formData.consommationPre)) {
      Alert.alert('Erreur de validation', 'La consommation actuelle doit être supérieure à la consommation précédente');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('http://localhost:4002/api/consommations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          periode: formData.periode,
          consommationPre: parseInt(formData.consommationPre || 0),
          consommationActuelle: parseInt(formData.consommationActuelle),
          jours: parseInt(formData.jours || 30),
          idcont: formData.idContract,
          idsecteur: formData.idSecteur,
          idtech: 1, // ID du technicien connecté
          idtranch: 1,
          status: 'En cours'
        }),
      });

      const result = await response.json();

      if (result.success) {
        Alert.alert('Succès', 'Consommation enregistrée avec succès!');
        setFormData({
          periode: '',
          idClient: '',
          idContract: '',
          idSecteur: '',
          consommationActuelle: '',
          consommationPre: '',
          jours: ''
        });
        setContracts([]);
      } else {
        Alert.alert('Erreur', result.message || 'Erreur lors de l\'enregistrement');
      }
    } catch (err) {
      Alert.alert('Erreur de connexion', 'Impossible de se connecter au serveur');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    // Pour React Native, vous pouvez utiliser la navigation
    // navigation.goBack(); // Si vous utilisez React Navigation
    console.log('Retour demandé');
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={goBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>💧 Saisie Consommation</Text>
      </View>

      {/* Form Container */}
      <View style={styles.formContainer}>
        {/* Secteur - PREMIER CHAMP */}
        <View style={[styles.formGroup, styles.firstFormGroup]}>
          <Text style={[styles.label, styles.firstLabel]}>📍 Secteur *</Text>
          <CustomPicker
            selectedValue={formData.idSecteur}
            onValueChange={handleSecteurChange}
          >
            <PickerItem label="Sélectionner un secteur" value="" />
            {Array.isArray(secteurs) && secteurs.length > 0 ? (
              secteurs.map(secteur => {
                console.log(`🎯 Rendu secteur: ${secteur.nom} (ID: ${secteur.ids})`);
                return (
                  <PickerItem
                    key={secteur.ids}
                    label={secteur.nom}
                    value={secteur.ids}
                  />
                );
              })
            ) : (
              console.log('❌ Aucun secteur à afficher, secteurs:', secteurs) || null
            )}
          </CustomPicker>
        </View>

        {/* Client - DEUXIÈME CHAMP */}
        <View style={[styles.formGroup, styles.secondFormGroup]}>
          <Text style={[styles.label, styles.secondLabel]}>👤 Client *</Text>
          {formData.idSecteur ? (
            clientsDuSecteur.length > 0 ? (
              <CustomPicker
                selectedValue={formData.idClient}
                onValueChange={handleClientChange}
              >
                <PickerItem label={`-- Sélectionner un client (${clientsDuSecteur.length} dans ce secteur) --`} value="" />
                {clientsDuSecteur.map((client, index) => (
                  <PickerItem
                    key={client.idclient}
                    label={`${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`}
                    value={client.idclient}
                  />
                ))}
              </CustomPicker>
            ) : (
              <View style={styles.noClientsMessage}>
                <Text style={styles.noClientsText}>
                  ❌ Aucun client dans ce secteur
                </Text>
                <Text style={styles.noClientsSubText}>
                  Essayez un autre secteur
                </Text>
              </View>
            )
          ) : (
            <View style={styles.selectSecteurMessage}>
              <Text style={styles.selectSecteurText}>
                ⬆️ Sélectionnez d'abord un secteur ci-dessus
              </Text>
            </View>
          )}
        </View>

        {/* Contract - TROISIÈME CHAMP */}
        <View style={[styles.formGroup, styles.thirdFormGroup]}>
          <Text style={[styles.label, styles.thirdLabel]}>📄 Contrat *</Text>
          {formData.idClient ? (
            contracts.length > 0 ? (
              contracts.length === 1 ? (
                <View>
                  <TextInput
                    style={[styles.input, styles.readOnlyInput]}
                    value={`Contrat ${contracts[0].idcontract} - QR: ${contracts[0].codeqr || 'N/A'}`}
                    editable={false}
                  />
                  <Text style={styles.contractInfo}>
                    ✅ Contrat unique sélectionné automatiquement
                  </Text>
                </View>
              ) : (
                <CustomPicker
                  selectedValue={formData.idContract}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, idContract: value }))}
                >
                  <PickerItem label={`-- Sélectionner un contrat (${contracts.length} disponibles) --`} value="" />
                  {contracts.map((contract, index) => (
                    <PickerItem
                      key={contract.idcontract}
                      label={`${index + 1}. Contrat ${contract.idcontract} - QR: ${contract.codeqr}`}
                      value={contract.idcontract}
                    />
                  ))}
                </CustomPicker>
              )
            ) : (
              <View style={styles.noContractsMessage}>
                <Text style={styles.noContractsText}>
                  ❌ Aucun contrat pour ce client
                </Text>
                <Text style={styles.noContractsSubText}>
                  Contactez l'administrateur
                </Text>
              </View>
            )
          ) : (
            <View style={styles.selectClientMessage}>
              <Text style={styles.selectClientText}>
                ⬆️ Sélectionnez d'abord un client ci-dessus
              </Text>
            </View>
          )}
        </View>

        {/* Informations du client sélectionné */}
        {formData.idClient && clientsDuSecteur.length > 0 && (
          <View style={styles.clientInfoGroup}>
            <Text style={styles.clientInfoLabel}>ℹ️ Informations du Client</Text>
            {(() => {
              const clientSelectionne = clientsDuSecteur.find(c => c.idclient.toString() === formData.idClient.toString());
              return clientSelectionne ? (
                <View style={styles.clientInfoContainer}>
                  <Text style={styles.clientInfoName}>
                    👤 {clientSelectionne.nom} {clientSelectionne.prenom}
                  </Text>
                  <Text style={styles.clientInfoAddress}>
                    🏠 {clientSelectionne.adresse}, {clientSelectionne.ville}
                  </Text>
                  <Text style={styles.clientInfoContact}>
                    📞 {clientSelectionne.tel} | ✉️ {clientSelectionne.email}
                  </Text>
                  <Text style={styles.clientInfoSector}>
                    🗺️ Secteur: {clientSelectionne.secteur_nom}
                  </Text>
                </View>
              ) : null;
            })()}
          </View>
        )}

        {/* Période - QUATRIÈME CHAMP */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>📅 Période (YYYY-MM) *</Text>
          <Text style={styles.periodeInfo}>
            ⚠️ Seulement les mois précédents sont autorisés (pas le mois actuel ni les mois futurs)
          </Text>
          <TextInput
            style={[
              styles.input,
              periodeError ? styles.inputError : null
            ]}
            value={formData.periode}
            onChangeText={handlePeriodeChange}
            placeholder="2024-12"
            maxLength={7}
          />

          {/* Message d'erreur pour la période */}
          {periodeError ? (
            <View style={styles.errorButtonContainer}>
              <TouchableOpacity style={styles.errorButton} onPress={() => {
                Alert.alert(
                  'Période Invalide',
                  periodeError + '\n\nVeuillez saisir une période antérieure au mois actuel.',
                  [{ text: 'Compris', style: 'default' }]
                );
              }}>
                <Text style={styles.errorButtonText}>❌ {periodeError}</Text>
              </TouchableOpacity>
            </View>
          ) : null}
        </View>



        {/* Consommation Précédente */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Consommation Précédente (m³)</Text>
          <TextInput
            style={styles.input}
            value={formData.consommationPre}
            onChangeText={(text) => setFormData(prev => ({ ...prev, consommationPre: text }))}
            placeholder="0"
            keyboardType="numeric"
          />
        </View>

        {/* Consommation Actuelle */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Consommation Actuelle (m³)</Text>
          <TextInput
            style={styles.input}
            value={formData.consommationActuelle}
            onChangeText={(text) => setFormData(prev => ({ ...prev, consommationActuelle: text }))}
            placeholder="Saisir la consommation actuelle"
            keyboardType="numeric"
          />
        </View>

        {/* Nombre de jours */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Nombre de jours</Text>
          <TextInput
            style={styles.input}
            value={formData.jours}
            onChangeText={(text) => setFormData(prev => ({ ...prev, jours: text }))}
            placeholder="30"
            keyboardType="numeric"
          />
        </View>

        {/* Message */}
        {message && (
          <View style={[styles.message, message.includes('succès') ? styles.successMessage : styles.errorMessage]}>
            <Text style={styles.messageText}>{message}</Text>
          </View>
        )}

        {/* Affichage de la carte Google Maps intégrée */}
        {afficherCarte && clientsDuSecteur.length > 0 && (
          <GoogleMapsComponent
            clients={clientsDuSecteur}
            secteur={secteurSelectionne}
          />
        )}

        {/* Affichage des clients du secteur sélectionné */}
        {clientsDuSecteur.length > 0 && (
          <View style={styles.clientsSection}>
            <Text style={styles.clientsSectionTitle}>
              👥 Clients du secteur ({clientsDuSecteur.length})
            </Text>
            <ScrollView style={styles.clientsList} nestedScrollEnabled={true}>
              {clientsDuSecteur.map((client, index) => (
                <View key={client.idclient} style={styles.clientCard}>
                  <Text style={styles.clientName}>
                    {index + 1}. {client.nom} {client.prenom}
                  </Text>
                  <Text style={styles.clientAddress}>📍 {client.adresse}</Text>
                  <Text style={styles.clientContact}>📞 {client.tel}</Text>
                  {client.email && (
                    <Text style={styles.clientEmail}>✉️ {client.email}</Text>
                  )}
                </View>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.submitButtonText}>Enregistrer la Consommation</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>

  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginRight: 20,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  formContainer: {
    backgroundColor: 'white',
    margin: 20,
    marginHorizontal: 40, // Augmente les marges horizontales
    maxWidth: 500, // Limite la largeur maximale
    alignSelf: 'center', // Centre le formulaire
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  formGroup: {
    marginBottom: 15, // Réduit l'espacement entre les champs
  },
  firstFormGroup: {
    backgroundColor: '#f0f8ff', // Fond bleu clair pour le premier champ
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007bff',
    marginBottom: 20, // Plus d'espace après le premier champ
  },
  secondFormGroup: {
    backgroundColor: '#f0fff0', // Fond vert clair pour le deuxième champ
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#28a745',
    marginBottom: 20, // Plus d'espace après le deuxième champ
  },
  label: {
    fontSize: 14, // Réduit la taille de police
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6, // Réduit l'espacement
  },
  firstLabel: {
    fontSize: 16, // Plus grand pour le premier champ
    fontWeight: 'bold',
    color: '#007bff', // Couleur bleue
    marginBottom: 8,
  },
  secondLabel: {
    fontSize: 16, // Plus grand pour le deuxième champ
    fontWeight: 'bold',
    color: '#28a745', // Couleur verte
    marginBottom: 8,
  },

  thirdFormGroup: {
    backgroundColor: '#fff8e1', // Fond orange clair pour le troisième champ
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ff9800',
    marginBottom: 20,
  },
  thirdLabel: {
    fontSize: 16, // Plus grand pour le troisième champ
    fontWeight: 'bold',
    color: '#ff9800', // Couleur orange
    marginBottom: 8,
  },

  contractInfo: {
    fontSize: 12,
    color: '#28a745',
    fontStyle: 'italic',
    marginTop: 5,
    textAlign: 'center',
  },

  noContractsMessage: {
    backgroundColor: '#fff5f5',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  noContractsText: {
    fontSize: 14,
    color: '#dc2626',
    fontWeight: '600',
    textAlign: 'center',
  },
  noContractsSubText: {
    fontSize: 12,
    color: '#7f1d1d',
    textAlign: 'center',
    marginTop: 4,
  },

  selectClientMessage: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  selectClientText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
    textAlign: 'center',
  },
  // Messages pour le champ Client
  noClientsMessage: {
    backgroundColor: '#fff3cd',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  noClientsText: {
    color: '#856404',
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    fontWeight: 'bold',
  },
  noClientsSubText: {
    color: '#856404',
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 4,
  },
  selectSecteurMessage: {
    backgroundColor: '#f8d7da',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#f5c6cb',
  },
  selectSecteurText: {
    color: '#721c24',
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Styles pour les informations du client
  clientInfoGroup: {
    backgroundColor: '#e8f5e8',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#28a745',
  },
  clientInfoLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#28a745',
    marginBottom: 10,
  },
  clientInfoContainer: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#d4edda',
  },
  clientInfoName: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6,
  },
  clientInfoAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  clientInfoContact: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  clientInfoSector: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: 'bold',
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 6, // Réduit le rayon des bordures
    padding: 10, // Réduit le padding
    fontSize: 14, // Réduit la taille de police
    backgroundColor: 'white',
    height: 40, // Hauteur fixe plus petite
  },

  inputError: {
    borderColor: '#dc3545',
    borderWidth: 2,
    backgroundColor: '#fff5f5',
  },

  periodeInfo: {
    fontSize: 12,
    color: '#6c757d',
    fontStyle: 'italic',
    marginBottom: 8,
    paddingHorizontal: 4,
  },

  errorButtonContainer: {
    marginTop: 8,
  },

  errorButton: {
    backgroundColor: '#dc3545',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  errorButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },

  readOnlyInput: {
    backgroundColor: '#f9fafb',
    color: '#6b7280',
  },
  message: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  successMessage: {
    backgroundColor: '#d1fae5',
    borderColor: '#a7f3d0',
    borderWidth: 1,
  },
  errorMessage: {
    backgroundColor: '#fee2e2',
    borderColor: '#fecaca',
    borderWidth: 1,
  },
  messageText: {
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    backgroundColor: '#10b981',
    padding: 12, // Réduit le padding
    borderRadius: 6, // Réduit le rayon des bordures
    alignItems: 'center',
    marginTop: 10,
    height: 45, // Hauteur fixe plus petite
  },
  disabledButton: {
    backgroundColor: '#9ca3af',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 14, // Réduit la taille de police
    fontWeight: 'bold',
  },
  // Styles pour la section des clients du secteur
  clientsSection: {
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#007bff',
  },
  clientsSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 10,
    textAlign: 'center',
  },
  clientsList: {
    maxHeight: 200, // Limite la hauteur pour éviter que la liste soit trop longue
    marginBottom: 10,
  },
  clientCard: {
    backgroundColor: 'white',
    borderRadius: 6,
    padding: 10,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  clientName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  clientAddress: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  clientContact: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  clientEmail: {
    fontSize: 12,
    color: '#666',
  },
  // Styles pour la carte Google Maps intégrée
  mapContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#007bff',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  mapTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007bff',
    textAlign: 'center',
    marginBottom: 5,
  },
  mapSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 15,
  },
  mapImageContainer: {
    position: 'relative',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 10,
  },
  mapImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
    cursor: 'pointer',
  },
  mapOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 123, 255, 0.9)',
    padding: 8,
    alignItems: 'center',
  },
  mapOverlayText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  markersInfo: {
    backgroundColor: 'white',
    borderRadius: 6,
    padding: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  markersTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  markerText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
});

export default Consommation;
