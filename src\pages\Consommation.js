import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator
} from 'react-native';

// Composant Picker personnalisé compatible React Native Web
const CustomPicker = ({ selectedValue, onValueChange, children, style }) => {
  return (
    <select
      value={selectedValue}
      onChange={(e) => onValueChange(e.target.value)}
      style={{
        width: '100%',
        padding: 10, 
        fontSize: 14, 
        height: 40, 
        borderWidth: 1,
        borderColor: '#d1d5db',
        borderRadius: 6, 
        backgroundColor: 'white',
        ...style
      }}
    >
      {children}
    </select>
  );
};

const PickerItem = ({ label, value }) => {
  return <option value={value}>{label}</option>;
};

const Consommation = () => {
  const [formData, setFormData] = useState({
    periode: '',
    idClient: '',
    idContract: '',
    idSecteur: '',
    consommationActuelle: '',
    consommationPre: '',
    jours: ''
  });
  const [contracts, setContracts] = useState([]);
  const [secteurs, setSecteurs] = useState([]);
  const [clientsDuSecteur, setClientsDuSecteur] = useState([]);
  const [secteurSelectionne, setSecteurSelectionne] = useState(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [afficherCarte, setAfficherCarte] = useState(false);

  useEffect(() => {
    fetchSecteurs();
  }, []);



  const fetchContracts = async (clientId) => {
    try {
      console.log('🔍 Récupération des contrats pour le client:', clientId);
      // Utiliser l'API correcte pour récupérer les contrats d'un client
      const response = await fetch(`http://localhost:4002/api/clients/${clientId}/contracts`);
      if (response.ok) {
        const result = await response.json();
        console.log('📊 Réponse API contrats:', result);

        // L'API retourne { success: true, data: [...], count: X }
        if (result.success && Array.isArray(result.data)) {
          setContracts(result.data);
          console.log('✅ Contrats chargés:', result.data.length);

          if (result.data.length === 1) {
            setFormData(prev => ({ ...prev, idContract: result.data[0].idcontract }));
            console.log('✅ Contrat unique sélectionné automatiquement');
          }
        } else {
          console.error('❌ Format de réponse inattendu:', result);
          setContracts([]);
        }
      } else {
        console.error('❌ Erreur HTTP:', response.status);
        setContracts([]);
      }
    } catch (err) {
      console.error('❌ Erreur lors du chargement des contrats:', err);
      setContracts([]);
    }
  };

  const fetchSecteurs = async () => {
    try {
      console.log('🔍 Récupération des secteurs...');
      const response = await fetch('http://localhost:4002/api/secteurs');
      if (response.ok) {
        const result = await response.json();
        console.log('📊 Réponse API secteurs:', result);

        // L'API retourne { success: true, data: [...], total: X }
        if (result.success && Array.isArray(result.data)) {
          setSecteurs(result.data);
          console.log('✅ Secteurs chargés:', result.data.length);
        } else {
          console.error('❌ Format de réponse inattendu:', result);
          setSecteurs([]);
        }
      } else {
        console.error('❌ Erreur HTTP:', response.status);
        setSecteurs([]);
      }
    } catch (err) {
      console.error('❌ Erreur lors du chargement des secteurs:', err);
      setSecteurs([]);
    }
  };

  const fetchClientsDuSecteur = async (secteurId) => {
    try {
      console.log(`🔍 Récupération des clients du secteur ID: ${secteurId}`);

      // Utiliser le port 4002 comme configuré
      const url = `http://localhost:4002/api/secteurs/${secteurId}/clients`;
      console.log(`📡 URL API: ${url}`);

      const response = await fetch(url);

      if (response.ok) {
        const result = await response.json();
        console.log('📊 Réponse API clients du secteur:', result);

        if (result.success && Array.isArray(result.data)) {
          setClientsDuSecteur(result.data);
          console.log(`✅ ${result.data.length} clients du secteur chargés`);

          // Afficher les clients trouvés avec détails
          if (result.data.length > 0) {
            console.log(`👥 Clients du secteur "${result.secteur?.nom || secteurId}":`);
            result.data.forEach((client, index) => {
              console.log(`   ${index + 1}. ${client.nom} ${client.prenom}`);
              console.log(`      📍 Adresse: ${client.adresse}, ${client.ville}`);
              console.log(`      📞 Tél: ${client.tel}`);
              console.log(`      🗺️ Secteur: ${client.secteur_nom} (ID: ${client.ids})`);
              console.log(`      📧 Email: ${client.email || 'N/A'}`);
            });

            // Message de confirmation plus discret
            console.log(`🎯 FILTRAGE RÉUSSI: ${result.data.length} client(s) du secteur "${result.secteur?.nom}" disponible(s) dans le champ Client`);
          } else {
            console.log(`ℹ️ Aucun client trouvé dans le secteur ID: ${secteurId}`);
            console.log('💡 Le champ Client affichera "Aucun client dans ce secteur"');
          }
        } else {
          console.error('❌ Format de réponse API inattendu:', result);
          setClientsDuSecteur([]);
        }
      } else {
        console.error(`❌ Erreur HTTP ${response.status} lors de la récupération des clients`);
        setClientsDuSecteur([]);
      }
    } catch (err) {
      console.error('❌ Erreur lors du chargement des clients du secteur:', err);
      setClientsDuSecteur([]);
    }
  };

  // Composant Google Maps intégré
  const GoogleMapsComponent = ({ clients, secteur }) => {
    if (!clients || clients.length === 0) {
      return null;
    }

    // Calculer le centre de la carte basé sur le secteur ou les clients
    const centreLatitude = secteur?.latitude || 33.5731;
    const centreLongitude = secteur?.longitude || -7.5898;

    // Créer les marqueurs pour chaque client
    const marqueurs = clients.map((client, index) => {
      const lat = client.latitude || (centreLatitude + (Math.random() - 0.5) * 0.01);
      const lng = client.longitude || (centreLongitude + (Math.random() - 0.5) * 0.01);
      return {
        lat,
        lng,
        nom: `${client.nom} ${client.prenom}`,
        adresse: client.adresse
      };
    });

    // Créer l'URL avec marqueurs multiples pour Google Maps
    const marqueurParams = marqueurs.map(m => `markers=color:red%7C${m.lat},${m.lng}`).join('&');
    const staticMapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${centreLatitude},${centreLongitude}&zoom=14&size=600x300&maptype=roadmap&${marqueurParams}&key=AIzaSyBFw0Qbyq9zTFTd-tUY6dw901SwHSR3g-0`;

    return (
      <View style={styles.mapContainer}>
        <Text style={styles.mapTitle}>
          🗺️ Carte du secteur: {secteur?.nom || 'Secteur sélectionné'}
        </Text>
        <Text style={styles.mapSubtitle}>
          📍 {clients.length} client(s) dans ce secteur
        </Text>

        {/* Carte Google Maps statique avec marqueurs */}
        <TouchableOpacity
          onPress={() => ouvrirGoogleMapsAvecClients(clients)}
          style={styles.mapImageContainer}
        >
          <img
            src={staticMapUrl}
            alt={`Carte du secteur ${secteur?.nom || ''}`}
            style={styles.mapImage}
            onError={(e) => {
              console.error('Erreur chargement carte:', e);
              e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkNhcnRlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';
            }}
          />
          <View style={styles.mapOverlay}>
            <Text style={styles.mapOverlayText}>
              👆 Cliquer pour ouvrir dans Google Maps
            </Text>
          </View>
        </TouchableOpacity>

        {/* Liste des marqueurs */}
        <View style={styles.markersInfo}>
          <Text style={styles.markersTitle}>📍 Emplacements des clients:</Text>
          {marqueurs.slice(0, 3).map((marqueur, index) => (
            <Text key={index} style={styles.markerText}>
              • {marqueur.nom} - {marqueur.adresse}
            </Text>
          ))}
          {marqueurs.length > 3 && (
            <Text style={styles.markerText}>
              ... et {marqueurs.length - 3} autre(s) client(s)
            </Text>
          )}
        </View>
      </View>
    );
  };

  const ouvrirGoogleMapsAvecClients = (clients) => {
    try {
      console.log('🗺️ Ouverture de Google Maps avec', clients.length, 'clients');

      if (clients.length === 0) {
        Alert.alert('Information', 'Aucun client trouvé dans ce secteur');
        return;
      }

      // Créer les marqueurs pour chaque client avec leurs coordonnées
      const marqueurs = clients.map((client, index) => {
        const lat = client.latitude || (33.5731 + Math.random() * 0.01);
        const lng = client.longitude || (-7.5898 + Math.random() * 0.01);
        console.log(`📍 Client ${index + 1}: ${client.nom} ${client.prenom} - Lat: ${lat}, Lng: ${lng}`);
        return `${lat},${lng}`;
      });

      // Construire l'URL Google Maps avec plusieurs marqueurs
      const premierMarqueur = marqueurs[0];
      const tousLesMarqueurs = marqueurs.join('|');

      // URL Google Maps avec marqueurs multiples
      const googleMapsUrl = `https://www.google.com/maps?q=${premierMarqueur}&markers=${tousLesMarqueurs}`;

      // Ouvrir dans un nouvel onglet
      window.open(googleMapsUrl, '_blank');

      console.log('✅ Google Maps ouvert avec', clients.length, 'clients du secteur');
      console.log('🔗 URL:', googleMapsUrl);
    } catch (error) {
      console.error('❌ Erreur ouverture Google Maps:', error);
      Alert.alert('Erreur', 'Impossible d\'ouvrir Google Maps');
    }
  };

  const handleSecteurChange = (secteurId) => {
    console.log('🔄 Changement de secteur:', secteurId);

    // Réinitialiser les données client
    setFormData(prev => ({
      ...prev,
      idSecteur: secteurId,
      idClient: '', // Réinitialiser le client sélectionné
      idContract: '' // Réinitialiser le contrat
    }));
    setContracts([]); // Vider les contrats

    if (secteurId) {
      // Trouver le secteur sélectionné
      const secteur = secteurs.find(s => s.ids === parseInt(secteurId));
      setSecteurSelectionne(secteur);

      console.log(`📍 Secteur sélectionné: ${secteur?.nom} (ID: ${secteurId})`);

      // Récupérer les clients de ce secteur spécifique
      fetchClientsDuSecteur(secteurId);
      // Afficher la carte automatiquement
      setAfficherCarte(true);
    } else {
      console.log('❌ Aucun secteur sélectionné');
      setSecteurSelectionne(null);
      setClientsDuSecteur([]);
      setAfficherCarte(false);
    }
  };

  // Fonction pour gérer la sélection du client
  const handleClientChange = (clientId) => {
    console.log('🔄 Changement de client ID:', clientId);

    // Mettre à jour le formulaire
    setFormData(prev => ({
      ...prev,
      idClient: clientId,
      idContract: '' // Réinitialiser le contrat quand on change de client
    }));

    if (clientId && clientId !== '') {
      // Trouver le client sélectionné dans la liste des clients du secteur
      const client = clientsDuSecteur.find(c => c.idclient.toString() === clientId.toString());

      if (client) {
        console.log(`✅ Client sélectionné: ${client.nom} ${client.prenom} (ID: ${clientId})`);

        // Récupérer les contrats de ce client
        fetchContracts(clientId);
      } else {
        console.log('❌ Client non trouvé dans la liste');
      }
    } else {
      console.log('❌ Aucun client sélectionné');
      setContracts([]);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setMessage('');

    // Validation
    if (parseFloat(formData.consommationActuelle) <= parseFloat(formData.consommationPre)) {
      Alert.alert('Erreur de validation', 'La consommation actuelle doit être supérieure à la consommation précédente');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('http://localhost:4002/api/consommations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          periode: formData.periode,
          consommationPre: parseInt(formData.consommationPre || 0),
          consommationActuelle: parseInt(formData.consommationActuelle),
          jours: parseInt(formData.jours || 30),
          idcont: formData.idContract,
          idsecteur: formData.idSecteur,
          idtech: 1, // ID du technicien connecté
          idtranch: 1,
          status: 'En cours'
        }),
      });

      const result = await response.json();

      if (result.success) {
        Alert.alert('Succès', 'Consommation enregistrée avec succès!');
        setFormData({
          periode: '',
          idClient: '',
          idContract: '',
          idSecteur: '',
          consommationActuelle: '',
          consommationPre: '',
          jours: ''
        });
        setContracts([]);
      } else {
        Alert.alert('Erreur', result.message || 'Erreur lors de l\'enregistrement');
      }
    } catch (err) {
      Alert.alert('Erreur de connexion', 'Impossible de se connecter au serveur');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    // Pour React Native, vous pouvez utiliser la navigation
    // navigation.goBack(); // Si vous utilisez React Navigation
    console.log('Retour demandé');
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={goBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>💧 Saisie Consommation</Text>
      </View>

      {/* Form Container */}
      <View style={styles.formContainer}>
        {/* Secteur - PREMIER CHAMP */}
        <View style={[styles.formGroup, styles.firstFormGroup]}>
          <Text style={[styles.label, styles.firstLabel]}>📍 Secteur *</Text>
          <CustomPicker
            selectedValue={formData.idSecteur}
            onValueChange={handleSecteurChange}
          >
            <PickerItem label="Sélectionner un secteur" value="" />
            {Array.isArray(secteurs) && secteurs.map(secteur => (
              <PickerItem
                key={secteur.ids}
                label={secteur.nom}
                value={secteur.ids}
              />
            ))}
          </CustomPicker>
        </View>

        {/* Client - DEUXIÈME CHAMP */}
        <View style={[styles.formGroup, styles.secondFormGroup]}>
          <Text style={[styles.label, styles.secondLabel]}>👤 Client *</Text>
          {formData.idSecteur ? (
            clientsDuSecteur.length > 0 ? (
              <CustomPicker
                selectedValue={formData.idClient}
                onValueChange={handleClientChange}
              >
                <PickerItem label={`-- Sélectionner un client (${clientsDuSecteur.length} dans ce secteur) --`} value="" />
                {clientsDuSecteur.map((client, index) => (
                  <PickerItem
                    key={client.idclient}
                    label={`${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`}
                    value={client.idclient}
                  />
                ))}
              </CustomPicker>
            ) : (
              <View style={styles.noClientsMessage}>
                <Text style={styles.noClientsText}>
                  ❌ Aucun client dans ce secteur
                </Text>
                <Text style={styles.noClientsSubText}>
                  Essayez un autre secteur
                </Text>
              </View>
            )
          ) : (
            <View style={styles.selectSecteurMessage}>
              <Text style={styles.selectSecteurText}>
                ⬆️ Sélectionnez d'abord un secteur ci-dessus
              </Text>
            </View>
          )}
        </View>

        {/* Informations du client sélectionné */}
        {formData.idClient && clientsDuSecteur.length > 0 && (
          <View style={styles.clientInfoGroup}>
            <Text style={styles.clientInfoLabel}>ℹ️ Informations du Client</Text>
            {(() => {
              const clientSelectionne = clientsDuSecteur.find(c => c.idclient.toString() === formData.idClient.toString());
              return clientSelectionne ? (
                <View style={styles.clientInfoContainer}>
                  <Text style={styles.clientInfoName}>
                    👤 {clientSelectionne.nom} {clientSelectionne.prenom}
                  </Text>
                  <Text style={styles.clientInfoAddress}>
                    🏠 {clientSelectionne.adresse}, {clientSelectionne.ville}
                  </Text>
                  <Text style={styles.clientInfoContact}>
                    📞 {clientSelectionne.tel} | ✉️ {clientSelectionne.email}
                  </Text>
                  <Text style={styles.clientInfoSector}>
                    🗺️ Secteur: {clientSelectionne.secteur_nom}
                  </Text>
                </View>
              ) : null;
            })()}
          </View>
        )}

        {/* Période - TROISIÈME CHAMP */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>📅 Période (YYYY-MM) *</Text>
          <TextInput
            style={styles.input}
            value={formData.periode}
            onChangeText={(text) => setFormData(prev => ({ ...prev, periode: text }))}
            placeholder="2025-01"
            maxLength={7}
          />
        </View>

        {/* Contrat */}
        {Array.isArray(contracts) && contracts.length > 0 && (
          <View style={styles.formGroup}>
            <Text style={styles.label}>Contrat</Text>
            {contracts.length === 1 ? (
              <TextInput
                style={[styles.input, styles.readOnlyInput]}
                value={`Contrat ${contracts[0].idcontract} - ${contracts[0].marquecompteur || 'N/A'}`}
                editable={false}
              />
            ) : (
              <CustomPicker
                selectedValue={formData.idContract}
                onValueChange={(value) => setFormData(prev => ({ ...prev, idContract: value }))}
              >
                <PickerItem label="Sélectionner un contrat" value="" />
                {contracts.map(contract => (
                  <PickerItem
                    key={contract.idcontract}
                    label={`Contrat ${contract.idcontract} - ${contract.marquecompteur || 'N/A'}`}
                    value={contract.idcontract}
                  />
                ))}
              </CustomPicker>
            )}
          </View>
        )}

        {/* Consommation Précédente */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Consommation Précédente (m³)</Text>
          <TextInput
            style={styles.input}
            value={formData.consommationPre}
            onChangeText={(text) => setFormData(prev => ({ ...prev, consommationPre: text }))}
            placeholder="0"
            keyboardType="numeric"
          />
        </View>

        {/* Consommation Actuelle */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Consommation Actuelle (m³)</Text>
          <TextInput
            style={styles.input}
            value={formData.consommationActuelle}
            onChangeText={(text) => setFormData(prev => ({ ...prev, consommationActuelle: text }))}
            placeholder="Saisir la consommation actuelle"
            keyboardType="numeric"
          />
        </View>

        {/* Nombre de jours */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Nombre de jours</Text>
          <TextInput
            style={styles.input}
            value={formData.jours}
            onChangeText={(text) => setFormData(prev => ({ ...prev, jours: text }))}
            placeholder="30"
            keyboardType="numeric"
          />
        </View>

        {/* Message */}
        {message && (
          <View style={[styles.message, message.includes('succès') ? styles.successMessage : styles.errorMessage]}>
            <Text style={styles.messageText}>{message}</Text>
          </View>
        )}

        {/* Affichage de la carte Google Maps intégrée */}
        {afficherCarte && clientsDuSecteur.length > 0 && (
          <GoogleMapsComponent
            clients={clientsDuSecteur}
            secteur={secteurSelectionne}
          />
        )}

        {/* Affichage des clients du secteur sélectionné */}
        {clientsDuSecteur.length > 0 && (
          <View style={styles.clientsSection}>
            <Text style={styles.clientsSectionTitle}>
              👥 Clients du secteur ({clientsDuSecteur.length})
            </Text>
            <ScrollView style={styles.clientsList} nestedScrollEnabled={true}>
              {clientsDuSecteur.map((client, index) => (
                <View key={client.idclient} style={styles.clientCard}>
                  <Text style={styles.clientName}>
                    {index + 1}. {client.nom} {client.prenom}
                  </Text>
                  <Text style={styles.clientAddress}>📍 {client.adresse}</Text>
                  <Text style={styles.clientContact}>📞 {client.tel}</Text>
                  {client.email && (
                    <Text style={styles.clientEmail}>✉️ {client.email}</Text>
                  )}
                </View>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.submitButtonText}>Enregistrer la Consommation</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>

  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginRight: 20,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  formContainer: {
    backgroundColor: 'white',
    margin: 20,
    marginHorizontal: 40, // Augmente les marges horizontales
    maxWidth: 500, // Limite la largeur maximale
    alignSelf: 'center', // Centre le formulaire
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  formGroup: {
    marginBottom: 15, // Réduit l'espacement entre les champs
  },
  firstFormGroup: {
    backgroundColor: '#f0f8ff', // Fond bleu clair pour le premier champ
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007bff',
    marginBottom: 20, // Plus d'espace après le premier champ
  },
  secondFormGroup: {
    backgroundColor: '#f0fff0', // Fond vert clair pour le deuxième champ
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#28a745',
    marginBottom: 20, // Plus d'espace après le deuxième champ
  },
  label: {
    fontSize: 14, // Réduit la taille de police
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6, // Réduit l'espacement
  },
  firstLabel: {
    fontSize: 16, // Plus grand pour le premier champ
    fontWeight: 'bold',
    color: '#007bff', // Couleur bleue
    marginBottom: 8,
  },
  secondLabel: {
    fontSize: 16, // Plus grand pour le deuxième champ
    fontWeight: 'bold',
    color: '#28a745', // Couleur verte
    marginBottom: 8,
  },
  // Messages pour le champ Client
  noClientsMessage: {
    backgroundColor: '#fff3cd',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  noClientsText: {
    color: '#856404',
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    fontWeight: 'bold',
  },
  noClientsSubText: {
    color: '#856404',
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 4,
  },
  selectSecteurMessage: {
    backgroundColor: '#f8d7da',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#f5c6cb',
  },
  selectSecteurText: {
    color: '#721c24',
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Styles pour les informations du client
  clientInfoGroup: {
    backgroundColor: '#e8f5e8',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#28a745',
  },
  clientInfoLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#28a745',
    marginBottom: 10,
  },
  clientInfoContainer: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#d4edda',
  },
  clientInfoName: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6,
  },
  clientInfoAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  clientInfoContact: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  clientInfoSector: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: 'bold',
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 6, // Réduit le rayon des bordures
    padding: 10, // Réduit le padding
    fontSize: 14, // Réduit la taille de police
    backgroundColor: 'white',
    height: 40, // Hauteur fixe plus petite
  },
  readOnlyInput: {
    backgroundColor: '#f9fafb',
    color: '#6b7280',
  },
  message: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  successMessage: {
    backgroundColor: '#d1fae5',
    borderColor: '#a7f3d0',
    borderWidth: 1,
  },
  errorMessage: {
    backgroundColor: '#fee2e2',
    borderColor: '#fecaca',
    borderWidth: 1,
  },
  messageText: {
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    backgroundColor: '#10b981',
    padding: 12, // Réduit le padding
    borderRadius: 6, // Réduit le rayon des bordures
    alignItems: 'center',
    marginTop: 10,
    height: 45, // Hauteur fixe plus petite
  },
  disabledButton: {
    backgroundColor: '#9ca3af',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 14, // Réduit la taille de police
    fontWeight: 'bold',
  },
  // Styles pour la section des clients du secteur
  clientsSection: {
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#007bff',
  },
  clientsSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 10,
    textAlign: 'center',
  },
  clientsList: {
    maxHeight: 200, // Limite la hauteur pour éviter que la liste soit trop longue
    marginBottom: 10,
  },
  clientCard: {
    backgroundColor: 'white',
    borderRadius: 6,
    padding: 10,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  clientName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  clientAddress: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  clientContact: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  clientEmail: {
    fontSize: 12,
    color: '#666',
  },
  // Styles pour la carte Google Maps intégrée
  mapContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#007bff',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  mapTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007bff',
    textAlign: 'center',
    marginBottom: 5,
  },
  mapSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 15,
  },
  mapImageContainer: {
    position: 'relative',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 10,
  },
  mapImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
    cursor: 'pointer',
  },
  mapOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 123, 255, 0.9)',
    padding: 8,
    alignItems: 'center',
  },
  mapOverlayText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  markersInfo: {
    backgroundColor: 'white',
    borderRadius: 6,
    padding: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  markersTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  markerText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
});

export default Consommation;
