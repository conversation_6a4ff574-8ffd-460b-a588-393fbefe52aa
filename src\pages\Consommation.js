import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator
} from 'react-native';

// Composant Picker personnalisé compatible React Native Web
const CustomPicker = ({ selectedValue, onValueChange, children, style }) => {
  return (
    <select
      value={selectedValue}
      onChange={(e) => onValueChange(e.target.value)}
      style={{
        width: '100%',
        padding: 10, 
        fontSize: 14, 
        height: 40, 
        borderWidth: 1,
        borderColor: '#d1d5db',
        borderRadius: 6, 
        backgroundColor: 'white',
        ...style
      }}
    >
      {children}
    </select>
  );
};

const PickerItem = ({ label, value }) => {
  return <option value={value}>{label}</option>;
};

const Consommation = () => {
  const [formData, setFormData] = useState({
    periode: '',
    idClient: '',
    idContract: '',
    idSecteur: '',
    consommationActuelle: '',
    consommationPre: '',
    jours: ''
  });

  const [periodeError, setPeriodeError] = useState('');
  const [consommationError, setConsommationError] = useState('');
  const [showMaps, setShowMaps] = useState(false);
  const [contracts, setContracts] = useState([]);
  const [secteurs, setSecteurs] = useState([]);
  const [clientsDuSecteur, setClientsDuSecteur] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    fetchSecteurs();
  }, []);

  // Fonction de validation de la période
  const validerPeriode = (periode) => {
    console.log('🔍 Validation période:', periode);

    if (!periode) {
      setPeriodeError('');
      return true;
    }

    // Vérifier le format YYYY-MM
    const formatRegex = /^\d{4}-\d{2}$/;
    if (!formatRegex.test(periode)) {
      setPeriodeError('Format invalide. Utilisez YYYY-MM (ex: 2024-12)');
      return false;
    }

    // Obtenir la date actuelle
    const maintenant = new Date();
    const moisActuel = maintenant.getFullYear() + '-' + String(maintenant.getMonth() + 1).padStart(2, '0');

    console.log('📅 Mois actuel:', moisActuel);
    console.log('📅 Période saisie:', periode);

    // Vérifier si la période est antérieure au mois actuel
    if (periode >= moisActuel) {
      const [annee, mois] = periode.split('-');
      const nomsMois = [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
      ];
      const nomMois = nomsMois[parseInt(mois) - 1] || mois;

      if (periode === moisActuel) {
        setPeriodeError(`❌ Impossible de saisir le mois actuel (${nomMois} ${annee}). Seulement les mois précédents sont autorisés.`);
      } else {
        setPeriodeError(`❌ Impossible de saisir un mois futur (${nomMois} ${annee}). Seulement les mois précédents sont autorisés.`);
      }
      return false;
    }

    // Période valide
    setPeriodeError('');
    console.log('✅ Période valide:', periode);
    return true;
  };

  // Gestionnaire de changement de période avec validation
  const handlePeriodeChange = (text) => {
    console.log('🔄 Changement période:', text);

    // Mettre à jour la valeur
    setFormData(prev => ({ ...prev, periode: text }));

    // Valider la période
    validerPeriode(text);

    // Calculer automatiquement le nombre de jours si on a un client sélectionné
    if (text && text.length === 7 && formData.idClient) {
      setTimeout(() => {
        mettreAJourNombreJours(text, formData.idClient);
      }, 100); // Petit délai pour s'assurer que la validation est terminée
    }
  };



  const fetchContracts = async (clientId) => {
    try {
      console.log('🔍 DEBUT fetchContracts - Client ID:', clientId);

      // Données de test pour les contrats par client
      const contratsParClientTest = {
        '1': [ // Benali Fatima
          { idcontract: 1, codeqr: 'QR001', datecontract: '2024-01-15', marquecompteur: 'Sensus', numseriecompteur: 'SN001', posx: 33.5731, posy: -7.5898 }
        ],
        '2': [ // Alami Mohammed
          { idcontract: 2, codeqr: 'QR002', datecontract: '2024-02-10', marquecompteur: 'Itron', numseriecompteur: 'SN002', posx: 33.5735, posy: -7.5895 }
        ],
        '3': [ // Tazi Aicha
          { idcontract: 3, codeqr: 'QR003', datecontract: '2024-01-20', marquecompteur: 'Sensus', numseriecompteur: 'SN003', posx: 33.5740, posy: -7.5890 }
        ],
        '4': [ // Benjelloun Youssef
          { idcontract: 4, codeqr: 'QR004', datecontract: '2024-03-05', marquecompteur: 'Elster', numseriecompteur: 'SN004', posx: 33.5831, posy: -7.5998 },
          { idcontract: 5, codeqr: 'QR005', datecontract: '2024-03-10', marquecompteur: 'Itron', numseriecompteur: 'SN005', posx: 33.5835, posy: -7.5995 }
        ],
        '5': [ // Lahlou Khadija
          { idcontract: 6, codeqr: 'QR006', datecontract: '2024-02-15', marquecompteur: 'Sensus', numseriecompteur: 'SN006', posx: 33.5840, posy: -7.5990 }
        ],
        '6': [ // Fassi Omar
          { idcontract: 7, codeqr: 'QR007', datecontract: '2024-01-25', marquecompteur: 'Elster', numseriecompteur: 'SN007', posx: 33.5931, posy: -7.6098 }
        ],
        '7': [ // Chraibi Salma
          { idcontract: 8, codeqr: 'QR008', datecontract: '2024-02-20', marquecompteur: 'Itron', numseriecompteur: 'SN008', posx: 33.5935, posy: -7.6095 }
        ],
        '8': [ // Idrissi Hassan
          { idcontract: 9, codeqr: 'QR009', datecontract: '2024-03-01', marquecompteur: 'Sensus', numseriecompteur: 'SN009', posx: 33.5940, posy: -7.6090 }
        ],
        '9': [ // Berrada Nadia
          { idcontract: 10, codeqr: 'QR010', datecontract: '2024-01-30', marquecompteur: 'Elster', numseriecompteur: 'SN010', posx: 33.5631, posy: -7.5798 }
        ],
        '10': [ // Kettani Rachid
          { idcontract: 11, codeqr: 'QR011', datecontract: '2024-02-25', marquecompteur: 'Itron', numseriecompteur: 'SN011', posx: 33.5635, posy: -7.5795 }
        ],
        '11': [ // Amrani Leila
          { idcontract: 12, codeqr: 'QR012', datecontract: '2024-03-15', marquecompteur: 'Sensus', numseriecompteur: 'SN012', posx: 33.5531, posy: -7.5698 }
        ],
        '12': [ // Zouaki Karim
          { idcontract: 13, codeqr: 'QR013', datecontract: '2024-02-28', marquecompteur: 'Elster', numseriecompteur: 'SN013', posx: 33.5535, posy: -7.5695 }
        ]
      };

      console.log(`📊 Contrats de test disponibles pour le client ${clientId}:`, contratsParClientTest[clientId]?.length || 0);

      // Essayer d'abord l'API, sinon utiliser les données de test
      try {
        const response = await fetch(`http://localhost:4002/api/clients/${clientId}/contracts`);
        console.log(`📥 Réponse API contrats, status: ${response.status}`);

        if (response.ok) {
          const result = await response.json();
          console.log('📊 Réponse API contrats:', result);

          if (result.success && Array.isArray(result.data) && result.data.length > 0) {
            console.log(`✅ API OK - ${result.data.length} contrats du client ${clientId}`);
            setContracts(result.data);

            // Sélection automatique si un seul contrat
            if (result.data.length === 1) {
              setFormData(prev => ({ ...prev, idContract: result.data[0].idcontract }));
              console.log('✅ Contrat unique sélectionné automatiquement');
            }
            return;
          }
        }
      } catch (apiError) {
        console.log('⚠️ API contrats non accessible, utilisation des données de test');
      }

      // Utiliser les données de test si l'API ne fonctionne pas
      const contratsDeTest = contratsParClientTest[clientId] || [];
      console.log(`✅ Utilisation des données de test: ${contratsDeTest.length} contrats pour le client ${clientId}`);

      setContracts(contratsDeTest);

      if (contratsDeTest.length > 0) {
        console.log(`📄 CONTRATS TROUVÉS - Client ${clientId}:`);
        contratsDeTest.forEach((contrat, index) => {
          console.log(`   ${index + 1}. Contrat ${contrat.idcontract} - QR: ${contrat.codeqr} - ${contrat.marquecompteur}`);
        });

        // Sélection automatique si un seul contrat
        if (contratsDeTest.length === 1) {
          setFormData(prev => ({ ...prev, idContract: contratsDeTest[0].idcontract }));
          console.log('✅ Contrat unique sélectionné automatiquement');
        }

        console.log(`🎯 Le champ Contrat affiche maintenant ${contratsDeTest.length} contrat(s) pour ce client`);
      } else {
        console.log(`ℹ️ Aucun contrat de test pour le client ${clientId}`);
      }

    } catch (err) {
      console.error('❌ ERREUR dans fetchContracts:', err);
      setContracts([]);
    }
  };

  // Fonction pour récupérer la dernière consommation du client
  const fetchDerniereConsommation = async (clientId) => {
    try {
      console.log('🔍 DEBUT fetchDerniereConsommation - Client ID:', clientId);

      // Données de test pour les dernières consommations par client
      const dernieresConsommationsTest = {
        '1': { consommationActuelle: 45, periode: '2024-11', date: '2024-11-15' }, // Benali Fatima
        '2': { consommationActuelle: 38, periode: '2024-11', date: '2024-11-18' }, // Alami Mohammed
        '3': { consommationActuelle: 52, periode: '2024-11', date: '2024-11-20' }, // Tazi Aicha
        '4': { consommationActuelle: 67, periode: '2024-11', date: '2024-11-12' }, // Benjelloun Youssef
        '5': { consommationActuelle: 41, periode: '2024-11', date: '2024-11-25' }, // Lahlou Khadija
        '6': { consommationActuelle: 55, periode: '2024-11', date: '2024-11-10' }, // Fassi Omar
        '7': { consommationActuelle: 33, periode: '2024-11', date: '2024-11-22' }, // Chraibi Salma
        '8': { consommationActuelle: 48, periode: '2024-11', date: '2024-11-14' }, // Idrissi Hassan
        '9': { consommationActuelle: 39, periode: '2024-11', date: '2024-11-28' }, // Berrada Nadia
        '10': { consommationActuelle: 44, periode: '2024-11', date: '2024-11-16' }, // Kettani Rachid
        '11': { consommationActuelle: 51, periode: '2024-11', date: '2024-11-19' }, // Amrani Leila
        '12': { consommationActuelle: 36, periode: '2024-11', date: '2024-11-21' }  // Zouaki Karim
      };

      console.log(`📊 Dernière consommation de test disponible pour le client ${clientId}:`, dernieresConsommationsTest[clientId]);

      // Essayer d'abord l'API, sinon utiliser les données de test
      try {
        const response = await fetch(`http://localhost:4002/api/clients/${clientId}/derniere-consommation`);
        console.log(`📥 Réponse API dernière consommation, status: ${response.status}`);

        if (response.ok) {
          const result = await response.json();
          console.log('📊 Réponse API dernière consommation:', result);

          if (result.success && result.data) {
            console.log(`✅ API OK - Dernière consommation du client ${clientId}: ${result.data.consommationActuelle} m³`);

            // Mettre à jour le champ consommation précédente
            setFormData(prev => ({
              ...prev,
              consommationPre: result.data.consommationActuelle.toString()
            }));

            console.log(`🎯 Consommation précédente mise à jour: ${result.data.consommationActuelle} m³`);
            return;
          }
        }
      } catch (apiError) {
        console.log('⚠️ API dernière consommation non accessible, utilisation des données de test');
      }

      // Utiliser les données de test si l'API ne fonctionne pas
      const derniereConsommation = dernieresConsommationsTest[clientId];

      if (derniereConsommation) {
        console.log(`✅ Utilisation des données de test: ${derniereConsommation.consommationActuelle} m³ (${derniereConsommation.periode})`);

        // Mettre à jour le champ consommation précédente
        setFormData(prev => ({
          ...prev,
          consommationPre: derniereConsommation.consommationActuelle.toString()
        }));

        console.log(`🎯 CONSOMMATION PRÉCÉDENTE AUTOMATIQUE - Client ${clientId}:`);
        console.log(`   📊 Dernière consommation: ${derniereConsommation.consommationActuelle} m³`);
        console.log(`   📅 Période: ${derniereConsommation.periode}`);
        console.log(`   📅 Date: ${derniereConsommation.date}`);
        console.log(`   ✅ Le champ "Consommation Précédente" est maintenant rempli automatiquement`);
      } else {
        console.log(`ℹ️ Aucune consommation précédente trouvée pour le client ${clientId}`);

        // Réinitialiser le champ si aucune donnée
        setFormData(prev => ({
          ...prev,
          consommationPre: '0'
        }));

        console.log('🔄 Champ "Consommation Précédente" réinitialisé à 0');
      }

    } catch (err) {
      console.error('❌ ERREUR dans fetchDerniereConsommation:', err);

      // En cas d'erreur, réinitialiser à 0
      setFormData(prev => ({
        ...prev,
        consommationPre: '0'
      }));
    }
  };

  const fetchSecteurs = async () => {
    try {
      console.log('🔍 DEBUT fetchSecteurs - Récupération des secteurs...');

      // Données de test directes de la table secteur
      const secteursDeTest = [
        { ids: 1, nom: 'Centre-Ville', latitude: 33.5731, longitude: -7.5898 },
        { ids: 2, nom: 'Quartier Industriel', latitude: 33.5831, longitude: -7.5998 },
        { ids: 3, nom: 'Zone Résidentielle Nord', latitude: 33.5931, longitude: -7.6098 },
        { ids: 4, nom: 'Zone Résidentielle Sud', latitude: 33.5631, longitude: -7.5798 },
        { ids: 5, nom: 'Quartier Commercial', latitude: 33.5531, longitude: -7.5698 }
      ];

      console.log('📊 Utilisation des données de test directes');
      console.log('� Secteurs de la table:', secteursDeTest);

      // Essayer d'abord l'API, sinon utiliser les données de test
      try {
        console.log('� Tentative API:', 'http://localhost:4002/api/secteurs');
        const response = await fetch('http://localhost:4002/api/secteurs', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        console.log('� Réponse API, status:', response.status, response.statusText);

        if (response.ok) {
          const result = await response.json();
          console.log('📊 Réponse API secteurs:', result);

          if (result.success && Array.isArray(result.data) && result.data.length > 0) {
            console.log('✅ API OK - Utilisation des données API:', result.data.length, 'secteurs');
            setSecteurs(result.data);
            return;
          }
        }
      } catch (apiError) {
        console.log('⚠️ API non accessible, utilisation des données de test');
      }

      // Utiliser les données de test si l'API ne fonctionne pas
      console.log('✅ Utilisation des données de test de la table secteur');
      setSecteurs(secteursDeTest);
      console.log('✅ Secteurs chargés depuis les données de test:', secteursDeTest.length);

    } catch (err) {
      console.error('❌ ERREUR dans fetchSecteurs:', err);

      // En cas d'erreur, utiliser les données de test par défaut
      const secteursParDefaut = [
        { ids: 1, nom: 'Centre-Ville', latitude: 33.5731, longitude: -7.5898 },
        { ids: 2, nom: 'Quartier Industriel', latitude: 33.5831, longitude: -7.5998 },
        { ids: 3, nom: 'Zone Résidentielle Nord', latitude: 33.5931, longitude: -7.6098 },
        { ids: 4, nom: 'Zone Résidentielle Sud', latitude: 33.5631, longitude: -7.5798 },
        { ids: 5, nom: 'Quartier Commercial', latitude: 33.5531, longitude: -7.5698 }
      ];

      console.log('🔄 Utilisation des secteurs par défaut');
      setSecteurs(secteursParDefaut);
    }
  };

  const fetchClientsDuSecteur = async (secteurId) => {
    try {
      console.log(`🔍 DEBUT fetchClientsDuSecteur - Secteur ID: ${secteurId}`);

      // Données de test pour les clients par secteur (relation secteur-client)
      const clientsParSecteurTest = {
        '1': [ // Centre-Ville
          { idclient: 1, nom: 'Benali', prenom: 'Fatima', adresse: '12 Rue Mohammed V', ville: 'casablanca', tel: '0661234567', email: '<EMAIL>', ids: 1, secteur_nom: 'Centre-Ville' },
          { idclient: 2, nom: 'Alami', prenom: 'Mohammed', adresse: '25 Avenue Hassan II', ville: 'casablanca', tel: '0662345678', email: '<EMAIL>', ids: 1, secteur_nom: 'Centre-Ville' },
          { idclient: 3, nom: 'Tazi', prenom: 'Aicha', adresse: '8 Place de la Liberté', ville: 'casablanca', tel: '0663456789', email: '<EMAIL>', ids: 1, secteur_nom: 'Centre-Ville' }
        ],
        '2': [ // Quartier Industriel
          { idclient: 4, nom: 'Benjelloun', prenom: 'Youssef', adresse: '45 Zone Industrielle', ville: 'Sefrou', tel: '0664567890', email: '<EMAIL>', ids: 2, secteur_nom: 'Quartier Industriel' },
          { idclient: 5, nom: 'Lahlou', prenom: 'Khadija', adresse: '67 Rue de l\'Industrie', ville: 'Sefrou', tel: '0665678901', email: '<EMAIL>', ids: 2, secteur_nom: 'Quartier Industriel' }
        ],
        '3': [ // Zone Résidentielle Nord
          { idclient: 6, nom: 'Fassi', prenom: 'Omar', adresse: '123 Quartier Nord', ville: 'Sefrou', tel: '0666789012', email: '<EMAIL>', ids: 3, secteur_nom: 'Zone Résidentielle Nord' },
          { idclient: 7, nom: 'Chraibi', prenom: 'Salma', adresse: '89 Résidence Al Amal', ville: 'Sefrou', tel: '0667890123', email: '<EMAIL>', ids: 3, secteur_nom: 'Zone Résidentielle Nord' },
          { idclient: 8, nom: 'Idrissi', prenom: 'Hassan', adresse: '34 Villa des Roses', ville: 'Sefrou', tel: '0668901234', email: '<EMAIL>', ids: 3, secteur_nom: 'Zone Résidentielle Nord' }
        ],
        '4': [ // Zone Résidentielle Sud
          { idclient: 9, nom: 'Berrada', prenom: 'Nadia', adresse: '56 Quartier Sud', ville: 'Sefrou', tel: '0669012345', email: '<EMAIL>', ids: 4, secteur_nom: 'Zone Résidentielle Sud' },
          { idclient: 10, nom: 'Kettani', prenom: 'Rachid', adresse: '78 Résidence Al Baraka', ville: 'Sefrou', tel: '0660123456', email: '<EMAIL>', ids: 4, secteur_nom: 'Zone Résidentielle Sud' }
        ],
        '5': [ // Quartier Commercial
          { idclient: 11, nom: 'Amrani', prenom: 'Leila', adresse: '90 Centre Commercial', ville: 'Sefrou', tel: '0661234567', email: '<EMAIL>', ids: 5, secteur_nom: 'Quartier Commercial' },
          { idclient: 12, nom: 'Zouaki', prenom: 'Karim', adresse: '12 Marché Central', ville: 'Sefrou', tel: '0662345678', email: '<EMAIL>', ids: 5, secteur_nom: 'Quartier Commercial' }
        ]
      };

      console.log(`📊 Clients de test disponibles pour le secteur ${secteurId}:`, clientsParSecteurTest[secteurId]?.length || 0);

      // Essayer d'abord l'API, sinon utiliser les données de test
      try {
        const url = `http://localhost:4002/api/secteurs/${secteurId}/clients`;
        console.log(`📡 Tentative API: ${url}`);

        const response = await fetch(url);
        console.log(`📥 Réponse API clients, status: ${response.status}`);

        if (response.ok) {
          const result = await response.json();
          console.log('📊 Réponse API clients du secteur:', result);

          if (result.success && Array.isArray(result.data) && result.data.length > 0) {
            console.log(`✅ API OK - ${result.data.length} clients du secteur ${secteurId}`);
            setClientsDuSecteur(result.data);
            return;
          }
        }
      } catch (apiError) {
        console.log('⚠️ API clients non accessible, utilisation des données de test');
      }

      // Utiliser les données de test si l'API ne fonctionne pas
      const clientsDeTest = clientsParSecteurTest[secteurId] || [];
      console.log(`✅ Utilisation des données de test: ${clientsDeTest.length} clients pour le secteur ${secteurId}`);

      setClientsDuSecteur(clientsDeTest);

      if (clientsDeTest.length > 0) {
        console.log(`👥 FILTRAGE RÉUSSI - Clients du secteur ${secteurId}:`);
        clientsDeTest.forEach((client, index) => {
          console.log(`   ${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`);
        });
        console.log(`🎯 Le champ Client affiche maintenant ${clientsDeTest.length} client(s) de ce secteur uniquement`);
      } else {
        console.log(`ℹ️ Aucun client de test pour le secteur ${secteurId}`);
      }

    } catch (err) {
      console.error('❌ ERREUR dans fetchClientsDuSecteur:', err);
      setClientsDuSecteur([]);
    }
  };



  const handleSecteurChange = (secteurId) => {
    console.log('🔄 Changement de secteur:', secteurId);

    // Réinitialiser les données client
    setFormData(prev => ({
      ...prev,
      idSecteur: secteurId,
      idClient: '', // Réinitialiser le client sélectionné
      idContract: '' // Réinitialiser le contrat
    }));
    setContracts([]); // Vider les contrats

    if (secteurId) {
      // Trouver le secteur sélectionné
      const secteur = secteurs.find(s => s.ids === parseInt(secteurId));

      console.log(`📍 Secteur sélectionné: ${secteur?.nom} (ID: ${secteurId})`);

      // Récupérer les clients de ce secteur spécifique
      fetchClientsDuSecteur(secteurId);
    } else {
      console.log('❌ Aucun secteur sélectionné');
      setClientsDuSecteur([]);
    }
  };

  // Fonction pour gérer la sélection du client
  const handleClientChange = (clientId) => {
    console.log('🔄 Changement de client ID:', clientId);

    // Mettre à jour le formulaire
    setFormData(prev => ({
      ...prev,
      idClient: clientId,
      idContract: '' // Réinitialiser le contrat quand on change de client
    }));

    if (clientId && clientId !== '') {
      // Trouver le client sélectionné dans la liste des clients du secteur
      const client = clientsDuSecteur.find(c => c.idclient.toString() === clientId.toString());

      if (client) {
        console.log(`✅ Client sélectionné: ${client.nom} ${client.prenom} (ID: ${clientId})`);

        // Récupérer les contrats de ce client
        fetchContracts(clientId);

        // Récupérer automatiquement la dernière consommation de ce client
        fetchDerniereConsommation(clientId);

        // Calculer automatiquement le nombre de jours si on a une période sélectionnée
        if (formData.periode && formData.periode.length === 7) {
          setTimeout(() => {
            mettreAJourNombreJours(formData.periode, clientId);
          }, 200); // Délai pour s'assurer que les données sont récupérées
        }
      } else {
        console.log('❌ Client non trouvé dans la liste');
      }
    } else {
      console.log('❌ Aucun client sélectionné');
      setContracts([]);

      // Réinitialiser la consommation précédente si aucun client
      setFormData(prev => ({ ...prev, consommationPre: '' }));
    }
  };

  // Fonction pour valider la consommation actuelle en temps réel
  const validerConsommationActuelle = (consommationActuelle, consommationPre) => {
    console.log('🔍 VALIDATION CONSOMMATION ACTUELLE:');
    console.log(`   📊 Consommation actuelle saisie: ${consommationActuelle}`);
    console.log(`   📊 Consommation précédente: ${consommationPre}`);

    // Réinitialiser l'erreur
    setConsommationError('');

    // Vérifier si les valeurs sont valides
    if (!consommationActuelle || consommationActuelle.trim() === '') {
      console.log('   ℹ️ Consommation actuelle vide, pas de validation');
      return true; // Pas d'erreur si le champ est vide
    }

    if (!consommationPre || consommationPre.trim() === '') {
      console.log('   ℹ️ Consommation précédente vide, pas de validation');
      return true; // Pas d'erreur si la consommation précédente n'est pas définie
    }

    const actuelle = parseFloat(consommationActuelle);
    const precedente = parseFloat(consommationPre);

    console.log(`   🔢 Valeurs numériques: Actuelle=${actuelle}, Précédente=${precedente}`);

    // Vérifier si les valeurs sont des nombres valides
    if (isNaN(actuelle) || isNaN(precedente)) {
      console.log('   ⚠️ Valeurs non numériques détectées');
      return true; // Pas d'erreur de validation si les valeurs ne sont pas numériques
    }

    // Validation principale : consommation actuelle doit être supérieure à la précédente
    if (actuelle <= precedente) {
      const messageErreur = `Consommation invalide ! La consommation actuelle (${actuelle} m³) doit être supérieure à la consommation précédente (${precedente} m³)`;
      console.log(`   ❌ ERREUR DE VALIDATION: ${messageErreur}`);
      setConsommationError(messageErreur);
      return false;
    }

    console.log(`   ✅ VALIDATION RÉUSSIE: ${actuelle} m³ > ${precedente} m³`);
    return true;
  };

  // Fonction pour gérer le changement de consommation actuelle
  const handleConsommationActuelleChange = (text) => {
    console.log('🔄 Changement consommation actuelle:', text);

    // Mettre à jour la valeur
    setFormData(prev => ({ ...prev, consommationActuelle: text }));

    // Valider en temps réel
    setTimeout(() => {
      validerConsommationActuelle(text, formData.consommationPre);
    }, 500); // Délai de 500ms pour éviter la validation à chaque caractère
  };

  // Fonction pour calculer automatiquement le nombre de jours
  const calculerNombreJours = (periodeActuelle, dernierePeriode) => {
    try {
      console.log('🔍 CALCUL NOMBRE DE JOURS:');
      console.log(`   📅 Période actuelle sélectionnée: ${periodeActuelle}`);
      console.log(`   📅 Dernière période du client: ${dernierePeriode}`);

      if (!periodeActuelle || !dernierePeriode) {
        console.log('   ⚠️ Périodes manquantes, pas de calcul possible');
        return null;
      }

      // Convertir les périodes en dates (format YYYY-MM)
      const [anneeActuelle, moisActuel] = periodeActuelle.split('-').map(Number);
      const [anneeDerniere, moisDernier] = dernierePeriode.split('-').map(Number);

      console.log(`   🔢 Période actuelle: ${anneeActuelle}-${moisActuel.toString().padStart(2, '0')}`);
      console.log(`   🔢 Dernière période: ${anneeDerniere}-${moisDernier.toString().padStart(2, '0')}`);

      // Créer des objets Date pour le calcul
      // Utiliser le dernier jour du mois pour un calcul plus précis
      const dateActuelle = new Date(anneeActuelle, moisActuel, 0); // Dernier jour du mois actuel
      const dateDerniere = new Date(anneeDerniere, moisDernier, 0); // Dernier jour du mois dernier

      console.log(`   📅 Date actuelle: ${dateActuelle.toLocaleDateString('fr-FR')}`);
      console.log(`   📅 Date dernière: ${dateDerniere.toLocaleDateString('fr-FR')}`);

      // Calculer la différence en millisecondes puis en jours
      const differenceMs = dateActuelle.getTime() - dateDerniere.getTime();
      const differenceJours = Math.round(differenceMs / (1000 * 60 * 60 * 24));

      console.log(`   🔢 Différence en millisecondes: ${differenceMs}`);
      console.log(`   📊 NOMBRE DE JOURS CALCULÉ: ${differenceJours} jours`);

      // Vérifier que le résultat est logique (positif et raisonnable)
      if (differenceJours <= 0) {
        console.log('   ⚠️ Différence négative ou nulle, période actuelle doit être postérieure');
        return null;
      }

      if (differenceJours > 365) {
        console.log('   ⚠️ Différence trop importante (> 365 jours), vérifiez les périodes');
        return null;
      }

      console.log(`   ✅ CALCUL RÉUSSI: ${differenceJours} jours entre ${dernierePeriode} et ${periodeActuelle}`);
      return differenceJours;

    } catch (error) {
      console.error('❌ ERREUR dans calculerNombreJours:', error);
      return null;
    }
  };

  // Fonction pour mettre à jour automatiquement le nombre de jours
  const mettreAJourNombreJours = (periodeActuelle = null, clientId = null) => {
    const periode = periodeActuelle || formData.periode;
    const client = clientId || formData.idClient;

    console.log('🔄 MISE À JOUR NOMBRE DE JOURS:');
    console.log(`   📅 Période: ${periode}`);
    console.log(`   👤 Client: ${client}`);

    // Vérifier si on a les données nécessaires
    if (!periode || !client) {
      console.log('   ℹ️ Période ou client manquant, pas de calcul');
      return;
    }

    // Récupérer la dernière période du client depuis les données de test
    const dernieresConsommationsTest = {
      '1': { consommationActuelle: 45, periode: '2024-11', date: '2024-11-15' }, // Benali Fatima
      '2': { consommationActuelle: 38, periode: '2024-11', date: '2024-11-18' }, // Alami Mohammed
      '3': { consommationActuelle: 52, periode: '2024-11', date: '2024-11-20' }, // Tazi Aicha
      '4': { consommationActuelle: 67, periode: '2024-11', date: '2024-11-12' }, // Benjelloun Youssef
      '5': { consommationActuelle: 41, periode: '2024-11', date: '2024-11-25' }, // Lahlou Khadija
      '6': { consommationActuelle: 55, periode: '2024-11', date: '2024-11-10' }, // Fassi Omar
      '7': { consommationActuelle: 33, periode: '2024-11', date: '2024-11-22' }, // Chraibi Salma
      '8': { consommationActuelle: 48, periode: '2024-11', date: '2024-11-14' }, // Idrissi Hassan
      '9': { consommationActuelle: 39, periode: '2024-11', date: '2024-11-28' }, // Berrada Nadia
      '10': { consommationActuelle: 44, periode: '2024-11', date: '2024-11-16' }, // Kettani Rachid
      '11': { consommationActuelle: 51, periode: '2024-11', date: '2024-11-19' }, // Amrani Leila
      '12': { consommationActuelle: 36, periode: '2024-11', date: '2024-11-21' }  // Zouaki Karim
    };

    const derniereConsommation = dernieresConsommationsTest[client];

    if (!derniereConsommation) {
      console.log(`   ❌ Aucune donnée trouvée pour le client ${client}`);
      return;
    }

    const dernierePeriode = derniereConsommation.periode;
    console.log(`   📊 Dernière période trouvée: ${dernierePeriode}`);

    // Calculer le nombre de jours
    const nombreJours = calculerNombreJours(periode, dernierePeriode);

    if (nombreJours !== null) {
      // Mettre à jour le champ
      setFormData(prev => ({
        ...prev,
        jours: nombreJours.toString()
      }));

      console.log(`   🎯 NOMBRE DE JOURS MIS À JOUR: ${nombreJours} jours`);
      console.log(`   📅 Calcul: ${periode} - ${dernierePeriode} = ${nombreJours} jours`);
    } else {
      console.log('   ❌ Impossible de calculer le nombre de jours');

      // Réinitialiser à une valeur par défaut
      setFormData(prev => ({
        ...prev,
        jours: '30'
      }));
    }
  };

  const handleSubmit = async () => {
    console.log('💾 DÉBUT ENREGISTREMENT CONSOMMATION');
    console.log('📊 Données du formulaire:', formData);

    setLoading(true);
    setMessage('');

    try {
      // 1. VALIDATION DES CHAMPS OBLIGATOIRES
      console.log('🔍 VALIDATION DES CHAMPS OBLIGATOIRES...');

      if (!formData.idSecteur) {
        Alert.alert('Champ manquant', 'Veuillez sélectionner un secteur');
        setLoading(false);
        return;
      }

      if (!formData.idClient) {
        Alert.alert('Champ manquant', 'Veuillez sélectionner un client');
        setLoading(false);
        return;
      }

      if (!formData.idContract) {
        Alert.alert('Champ manquant', 'Veuillez sélectionner un contrat');
        setLoading(false);
        return;
      }

      if (!formData.periode) {
        Alert.alert('Champ manquant', 'Veuillez saisir une période');
        setLoading(false);
        return;
      }

      if (!formData.consommationActuelle) {
        Alert.alert('Champ manquant', 'Veuillez saisir la consommation actuelle');
        setLoading(false);
        return;
      }

      // 2. VALIDATION DE LA PÉRIODE
      console.log('📅 VALIDATION DE LA PÉRIODE...');
      if (!validerPeriode(formData.periode)) {
        Alert.alert(
          'Période Invalide',
          periodeError || 'Veuillez saisir une période valide (mois précédent uniquement)',
          [{ text: 'OK', style: 'default' }]
        );
        setLoading(false);
        return;
      }

      // 3. VALIDATION DES CONSOMMATIONS
      console.log('⚡ VALIDATION DES CONSOMMATIONS...');
      const consommationActuelle = parseFloat(formData.consommationActuelle);
      const consommationPre = parseFloat(formData.consommationPre || 0);

      if (isNaN(consommationActuelle) || consommationActuelle <= 0) {
        Alert.alert('Erreur de validation', 'La consommation actuelle doit être un nombre positif');
        setLoading(false);
        return;
      }

      if (consommationActuelle <= consommationPre) {
        Alert.alert(
          'Erreur de validation',
          `La consommation actuelle (${consommationActuelle} m³) doit être supérieure à la consommation précédente (${consommationPre} m³)`
        );
        setLoading(false);
        return;
      }

      // 4. PRÉPARATION DES DONNÉES À ENVOYER
      console.log('📦 PRÉPARATION DES DONNÉES...');
      const donneesConsommation = {
        periode: formData.periode,
        consommationPre: parseInt(consommationPre),
        consommationActuelle: parseInt(consommationActuelle),
        jours: parseInt(formData.jours || 30),
        idClient: parseInt(formData.idClient),
        idContract: parseInt(formData.idContract),
        idSecteur: parseInt(formData.idSecteur),
        idTech: 1, // ID du technicien connecté (à récupérer du localStorage)
        idTranch: 1, // ID de la tranche (à définir selon vos besoins)
        status: 'Enregistrée'
      };

      console.log('📡 Données à envoyer:', donneesConsommation);

      // 5. ENVOI À L'API
      console.log('📡 ENVOI À L\'API...');
      const response = await fetch('http://localhost:4002/api/consommations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(donneesConsommation),
      });

      console.log('📥 Réponse API status:', response.status);

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const result = await response.json();
      console.log('📊 Résultat API:', result);

      if (result.success) {
        console.log('✅ ENREGISTREMENT RÉUSSI');

        Alert.alert(
          '✅ Succès',
          `Consommation enregistrée avec succès!\n\n` +
          `📅 Période: ${formData.periode}\n` +
          `👤 Client: ${clientsDuSecteur.find(c => c.idclient.toString() === formData.idClient)?.nom || 'Client'}\n` +
          `📊 Consommation: ${consommationActuelle} m³\n` +
          `📅 Jours: ${formData.jours}`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Réinitialiser le formulaire après succès
                setFormData({
                  periode: '',
                  idClient: '',
                  idContract: '',
                  idSecteur: '',
                  consommationActuelle: '',
                  consommationPre: '',
                  jours: ''
                });
                setContracts([]);
                setClientsDuSecteur([]);
                setConsommationError('');
                setPeriodeError('');
                console.log('🔄 Formulaire réinitialisé');
              }
            }
          ]
        );
      } else {
        console.log('❌ ERREUR API:', result.message);
        Alert.alert('Erreur', result.message || 'Erreur lors de l\'enregistrement');
      }

    } catch (err) {
      console.error('❌ ERREUR ENREGISTREMENT:', err);

      // Essayer d'enregistrer en mode test si l'API n'est pas disponible
      console.log('🧪 TENTATIVE ENREGISTREMENT MODE TEST...');

      Alert.alert(
        '⚠️ Mode Test',
        `Serveur non disponible. Simulation d'enregistrement:\n\n` +
        `📅 Période: ${formData.periode}\n` +
        `👤 Client: ${clientsDuSecteur.find(c => c.idclient.toString() === formData.idClient)?.nom || 'Client'}\n` +
        `📊 Consommation: ${formData.consommationActuelle} m³\n` +
        `📅 Jours: ${formData.jours}\n\n` +
        `✅ Données validées et prêtes pour l'enregistrement`,
        [
          {
            text: 'OK',
            onPress: () => {
              // Réinitialiser le formulaire même en mode test
              setFormData({
                periode: '',
                idClient: '',
                idContract: '',
                idSecteur: '',
                consommationActuelle: '',
                consommationPre: '',
                jours: ''
              });
              setContracts([]);
              setClientsDuSecteur([]);
              setConsommationError('');
              setPeriodeError('');
              console.log('🔄 Formulaire réinitialisé (mode test)');
            }
          }
        ]
      );

    } finally {
      setLoading(false);
      console.log('💾 FIN ENREGISTREMENT CONSOMMATION');
    }
  };

  const goBack = () => {
    // Pour React Native, vous pouvez utiliser la navigation
    // navigation.goBack(); // Si vous utilisez React Navigation
    console.log('Retour demandé');
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={goBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <Text style={styles.title}>💧 Saisie Consommation</Text>
      </View>

      {/* Form Container */}
      <View style={styles.formContainer}>
        {/* Secteur - PREMIER CHAMP */}
        <View style={[styles.formGroup, styles.firstFormGroup]}>
          <Text style={[styles.label, styles.firstLabel]}>📍 Secteur </Text>
          <CustomPicker
            selectedValue={formData.idSecteur}
            onValueChange={handleSecteurChange}
          >
            <PickerItem label="Sélectionner un secteur" value="" />
            {Array.isArray(secteurs) && secteurs.length > 0 ? (
              secteurs.map(secteur => {
                console.log(`🎯 Rendu secteur: ${secteur.nom} (ID: ${secteur.ids})`);
                return (
                  <PickerItem
                    key={secteur.ids}
                    label={secteur.nom}
                    value={secteur.ids}
                  />
                );
              })
            ) : (
              console.log('❌ Aucun secteur à afficher, secteurs:', secteurs) || null
            )}
          </CustomPicker>
        </View>

        {/* Client - DEUXIÈME CHAMP */}
        <View style={[styles.formGroup, styles.secondFormGroup]}>
          <Text style={[styles.label, styles.secondLabel]}>👤 Client </Text>
          {formData.idSecteur ? (
            clientsDuSecteur.length > 0 ? (
              <CustomPicker
                selectedValue={formData.idClient}
                onValueChange={handleClientChange}
              >
                <PickerItem label={`-- Sélectionner un client (${clientsDuSecteur.length} dans ce secteur) --`} value="" />
                {clientsDuSecteur.map((client, index) => (
                  <PickerItem
                    key={client.idclient}
                    label={`${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`}
                    value={client.idclient}
                  />
                ))}
              </CustomPicker>
            ) : (
              <View style={styles.noClientsMessage}>
                <Text style={styles.noClientsText}>
                  ❌ Aucun client dans ce secteur
                </Text>
                <Text style={styles.noClientsSubText}>
                  Essayez un autre secteur
                </Text>
              </View>
            )
          ) : (
            <View style={styles.selectSecteurMessage}>
              <Text style={styles.selectSecteurText}>
                ⬆️ Sélectionnez d'abord un secteur ci-dessus
              </Text>
            </View>
          )}
        </View>

        {/* Contract - TROISIÈME CHAMP */}
        <View style={[styles.formGroup, styles.thirdFormGroup]}>
          <View style={styles.contractHeader}>
            <Text style={[styles.label, styles.thirdLabel]}>📄 Contrat </Text>
            {formData.idClient && contracts.length > 0 && (
              <TouchableOpacity
                style={styles.mapsButton}
                onPress={() => setShowMaps(!showMaps)}
              >
                <Text style={styles.mapsButtonText}>
                  🗺️ {showMaps ? 'Masquer' : 'Voir sur'} Google Maps
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {formData.idClient ? (
            contracts.length > 0 ? (
              contracts.length === 1 ? (
                <View>
                  <TextInput
                    style={[styles.input, styles.readOnlyInput]}
                    value={`Contrat ${contracts[0].idcontract} - QR: ${contracts[0].codeqr || 'N/A'}`}
                    editable={false}
                  />
                  <Text style={styles.contractInfo}>
                    ✅ Contrat unique sélectionné automatiquement
                  </Text>
                </View>
              ) : (
                <CustomPicker
                  selectedValue={formData.idContract}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, idContract: value }))}
                >
                  <PickerItem label={`-- Sélectionner un contrat (${contracts.length} disponibles) --`} value="" />
                  {contracts.map((contract, index) => (
                    <PickerItem
                      key={contract.idcontract}
                      label={`${index + 1}. Contrat ${contract.idcontract} - QR: ${contract.codeqr}`}
                      value={contract.idcontract}
                    />
                  ))}
                </CustomPicker>
              )
            ) : (
              <View style={styles.noContractsMessage}>
                <Text style={styles.noContractsText}>
                  ❌ Aucun contrat pour ce client
                </Text>
                <Text style={styles.noContractsSubText}>
                  Contactez l'administrateur
                </Text>
              </View>
            )
          ) : (
            <View style={styles.selectClientMessage}>
              <Text style={styles.selectClientText}>
                ⬆️ Sélectionnez d'abord un client ci-dessus
              </Text>
            </View>
          )}
        </View>

        {/* Google Maps pour les contrats */}
        {showMaps && formData.idClient && contracts.length > 0 && (
          <View style={styles.mapsContainer}>
            <Text style={styles.mapsTitle}>
              🗺️ Localisation des Contrats - {(() => {
                const clientSelectionne = clientsDuSecteur.find(c => c.idclient.toString() === formData.idClient.toString());
                return clientSelectionne ? `${clientSelectionne.nom} ${clientSelectionne.prenom}` : 'Client';
              })()}
            </Text>

            <View style={styles.mapsWrapper}>
              {/* Google Maps intégré sans clé API */}
              <iframe
                src={`https://maps.google.com/maps?q=${contracts[0]?.posx || 33.5731},${contracts[0]?.posy || -7.5898}&hl=fr&z=16&output=embed`}
                style={styles.mapsIframe}
                allowFullScreen=""
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Localisation des Contrats Google Maps"
                onError={() => {
                  console.log('Erreur Google Maps, tentative avec OpenStreetMap');
                  // Fallback vers OpenStreetMap si Google Maps échoue
                  const iframe = document.querySelector('iframe[title="Localisation des Contrats Google Maps"]');
                  if (iframe) {
                    iframe.src = `https://www.openstreetmap.org/export/embed.html?bbox=${(contracts[0]?.posy || -7.5898) - 0.01},${(contracts[0]?.posx || 33.5731) - 0.01},${(contracts[0]?.posy || -7.5898) + 0.01},${(contracts[0]?.posx || 33.5731) + 0.01}&layer=mapnik&marker=${contracts[0]?.posx || 33.5731},${contracts[0]?.posy || -7.5898}`;
                  }
                }}
              />

              <View style={styles.mapsOverlay}>
                <View style={styles.contractsList}>
                  <Text style={styles.contractsListTitle}>
                    📄 Contrats sur la carte ({contracts.length})
                  </Text>
                  {contracts.map((contract, index) => (
                    <View key={contract.idcontract} style={styles.contractItem}>
                      <Text style={styles.contractItemText}>
                        📍 Contrat {contract.idcontract}
                      </Text>
                      <Text style={styles.contractItemDetails}>
                        QR: {contract.codeqr} | {contract.marquecompteur}
                      </Text>
                      <Text style={styles.contractItemCoords}>
                        📍 {contract.posx}, {contract.posy}
                      </Text>
                    </View>
                  ))}
                </View>

                <TouchableOpacity
                  style={styles.closeMapsButton}
                  onPress={() => setShowMaps(false)}
                >
                  <Text style={styles.closeMapsButtonText}>✕ Fermer</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}

        {/* Informations du client sélectionné */}
        {formData.idClient && clientsDuSecteur.length > 0 && (
          <View style={styles.clientInfoGroup}>
            <Text style={styles.clientInfoLabel}>ℹ️ Informations du Client</Text>
            {(() => {
              const clientSelectionne = clientsDuSecteur.find(c => c.idclient.toString() === formData.idClient.toString());
              return clientSelectionne ? (
                <View style={styles.clientInfoContainer}>
                  <Text style={styles.clientInfoName}>
                    👤 {clientSelectionne.nom} {clientSelectionne.prenom}
                  </Text>
                  <Text style={styles.clientInfoAddress}>
                    🏠 {clientSelectionne.adresse}, {clientSelectionne.ville}
                  </Text>
                  <Text style={styles.clientInfoContact}>
                    📞 {clientSelectionne.tel} | ✉️ {clientSelectionne.email}
                  </Text>
                  <Text style={styles.clientInfoSector}>
                    🗺️ Secteur: {clientSelectionne.secteur_nom}
                  </Text>
                </View>
              ) : null;
            })()}
          </View>
        )}

        {/* Période - QUATRIÈME CHAMP */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>📅 Période (YYYY-MM) *</Text>
          <Text style={styles.periodeInfo}>
            {/* ⚠️ Seulement les mois précédents sont autorisés (pas le mois actuel ni les mois futurs) */}
          </Text>
          <TextInput
            style={[
              styles.input,
              periodeError ? styles.inputError : null
            ]}
            value={formData.periode}
            onChangeText={handlePeriodeChange}
            placeholder=""
            maxLength={7}
          />

          {/* Message d'erreur pour la période */}
          {periodeError ? (
            <View style={styles.errorButtonContainer}>
              <TouchableOpacity style={styles.errorButton} onPress={() => {
                Alert.alert(
                  'Période Invalide',
                  periodeError + '\n\nVeuillez saisir une période antérieure au mois actuel.',
                  [{ text: 'Compris', style: 'default' }]
                );
              }}>
                <Text style={styles.errorButtonText}>❌ {periodeError}</Text>
              </TouchableOpacity>
            </View>
          ) : null}
        </View>



        {/* Consommation Précédente */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>📊 Consommation Précédente (m³)</Text>
          <TextInput
            style={[styles.input, formData.consommationPre && formData.idClient ? styles.autoFilledInput : null]}
            value={formData.consommationPre}
            onChangeText={(text) => setFormData(prev => ({ ...prev, consommationPre: text }))}
            placeholder={formData.idClient ? "Chargement automatique..." : "Sélectionnez d'abord un client"}
            keyboardType="numeric"
          />
          {formData.consommationPre && formData.idClient && (
            <Text style={styles.autoFilledInfo}>
              ✅ Dernière consommation récupérée automatiquement
            </Text>
          )}
        </View>

        {/* Consommation Actuelle */}
        <View style={styles.formGroup}>
          <Text style={styles.label}> Consommation Actuelle (m³)</Text>
          <TextInput
            style={[
              styles.input,
              consommationError ? styles.inputError : null
            ]}
            value={formData.consommationActuelle}
            onChangeText={handleConsommationActuelleChange}
            placeholder={formData.consommationPre ? `Doit être > ${formData.consommationPre} m³` : "Saisir la consommation actuelle"}
            keyboardType="numeric"
          />
          {consommationError && (
            <TouchableOpacity style={styles.errorButton} onPress={() => {
              Alert.alert(
                '❌ Consommation Invalide',
                consommationError,
                [
                  {
                    text: 'Compris',
                    style: 'default'
                  }
                ]
              );
            }}>
              <Text style={styles.errorButtonText}>
                ❌ Consommation Invalide - Cliquez pour plus d'infos
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Nombre de jours */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>📅 Nombre de jours</Text>
          <TextInput
            style={[
              styles.input,
              formData.jours && formData.idClient && formData.periode ? styles.autoFilledInput : null
            ]}
            value={formData.jours}
            onChangeText={(text) => setFormData(prev => ({ ...prev, jours: text }))}
            placeholder={
              formData.idClient && formData.periode
                ? "Calcul automatique en cours..."
                : "Sélectionnez client et période"
            }
            keyboardType="numeric"
          />
          {formData.jours && formData.idClient && formData.periode && (
            <Text style={styles.autoFilledInfo}>
              ✅ Calculé automatiquement : {formData.periode} - dernière période
            </Text>
          )}
        </View>

        {/* Message */}
        {message && (
          <View style={[styles.message, message.includes('succès') ? styles.successMessage : styles.errorMessage]}>
            <Text style={styles.messageText}>{message}</Text>
          </View>
        )}



        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.submitButtonText}>Enregistrer la Consommation</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>

  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f1f5f9',
    minHeight: '100vh',
    paddingBottom: 40,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    padding: 24,
    marginBottom: 24,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    borderRadius: 16,
    border: '1px solid rgba(255,255,255,0.1)',
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    marginRight: 20,
    border: '1px solid rgba(255,255,255,0.3)',
    transition: 'all 0.3s ease',
  },
  backButtonText: {
    color: '#000',
    fontSize: 16,
    fontWeight: '600',
    textShadow: '0 1px 2px rgba(0,0,0,0.2)',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#000',
    textShadow: '0 2px 4px rgba(0,0,0,0.3)',
    letterSpacing: '0.5px',
  },
  formContainer: {
    backgroundColor: '#ffffff',
    margin: 20,
    marginHorizontal: 40,
    maxWidth: 600,
    alignSelf: 'center',
    padding: 28,
    borderRadius: 20,
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    border: '1px solid #e2e8f0',
    transition: 'all 0.3s ease',
  },
  formGroup: {
    marginBottom: 20,
    transition: 'all 0.3s ease',
  },
  firstFormGroup: {
    background: 'linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%)',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#667eea',
    marginBottom: 24,
    boxShadow: '0 4px 12px rgba(102, 126, 234, 0.15)',
  },
  secondFormGroup: {
    background: 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%)',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#10b981',
    marginBottom: 24,
    boxShadow: '0 4px 12px rgba(16, 185, 129, 0.15)',
  },
  label: {
    fontSize: 15,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
    letterSpacing: '0.3px',
  },
  firstLabel: {
    fontSize: 17,
    fontWeight: '700',
    color: '#667eea',
    marginBottom: 10,
    textShadow: '0 1px 2px rgba(0,0,0,0.1)',
  },
  secondLabel: {
    fontSize: 17,
    fontWeight: '700',
    color: '#10b981',
    marginBottom: 10,
    textShadow: '0 1px 2px rgba(0,0,0,0.1)',
  },

  thirdFormGroup: {
    background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#f59e0b',
    marginBottom: 24,
    boxShadow: '0 4px 12px rgba(245, 158, 11, 0.15)',
  },
  thirdLabel: {
    fontSize: 17,
    fontWeight: '700',
    color: '#f59e0b',
    marginBottom: 10,
    textShadow: '0 1px 2px rgba(0,0,0,0.1)',
  },

  contractInfo: {
    fontSize: 13,
    color: '#10b981',
    fontStyle: 'italic',
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '500',
  },

  noContractsMessage: {
    background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#f87171',
    boxShadow: '0 4px 12px rgba(248, 113, 113, 0.15)',
  },
  noContractsText: {
    fontSize: 15,
    color: '#dc2626',
    fontWeight: '700',
    textAlign: 'center',
  },
  noContractsSubText: {
    fontSize: 13,
    color: '#991b1b',
    textAlign: 'center',
    marginTop: 6,
    fontWeight: '500',
  },

  selectClientMessage: {
    background: 'linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%)',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#d1d5db',
    boxShadow: '0 4px 12px rgba(209, 213, 219, 0.15)',
  },
  selectClientText: {
    fontSize: 15,
    color: '#6b7280',
    fontWeight: '600',
    textAlign: 'center',
  },

  contractHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },

  mapsButton: {
    background: 'linear-gradient(135deg, #4285f4 0%, #1976d2 100%)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 10,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    border: '1px solid rgba(255,255,255,0.2)',
    transition: 'all 0.3s ease',
  },

  mapsButtonText: {
    color: '#ffffff',
    fontSize: 13,
    fontWeight: '700',
    textShadow: '0 1px 2px rgba(0,0,0,0.2)',
  },

  mapsContainer: {
    background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    borderWidth: 2,
    borderColor: '#4285f4',
    boxShadow: '0 8px 24px rgba(66, 133, 244, 0.15)',
  },

  mapsTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#4285f4',
    marginBottom: 16,
    textAlign: 'center',
    textShadow: '0 1px 2px rgba(0,0,0,0.1)',
  },

  mapsWrapper: {
    position: 'relative',
    height: 300,
    borderRadius: 8,
    overflow: 'hidden',
  },

  mapsIframe: {
    width: '100%',
    height: '100%',
    border: 'none',
  },

  mapsOverlay: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 8,
    padding: 10,
    maxWidth: 200,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },

  contractsList: {
    marginBottom: 10,
  },

  contractsListTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#4285f4',
    marginBottom: 8,
  },

  contractItem: {
    marginBottom: 8,
    paddingBottom: 6,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },

  contractItemText: {
    fontSize: 11,
    fontWeight: '600',
    color: '#333',
  },

  contractItemDetails: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },

  contractItemCoords: {
    fontSize: 9,
    color: '#888',
    marginTop: 1,
  },

  closeMapsButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-end',
  },

  closeMapsButtonText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
  },

  autoFilledInput: {
    background: 'linear-gradient(135deg, #f0fff4 0%, #dcfce7 100%)',
    borderColor: '#10b981',
    borderWidth: 2,
    boxShadow: '0 4px 12px rgba(16, 185, 129, 0.15)',
  },

  autoFilledInfo: {
    fontSize: 13,
    color: '#10b981',
    fontStyle: 'italic',
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '600',
  },

  inputError: {
    borderColor: '#ef4444',
    borderWidth: 2,
    background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',
    boxShadow: '0 4px 12px rgba(239, 68, 68, 0.15)',
  },

  errorButton: {
    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
    paddingHorizontal: 18,
    paddingVertical: 12,
    borderRadius: 12,
    marginTop: 12,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    border: '1px solid rgba(255,255,255,0.2)',
    transition: 'all 0.3s ease',
  },

  errorButtonText: {
    color: '#ffffff',
    fontSize: 15,
    fontWeight: '700',
    textAlign: 'center',
    textShadow: '0 1px 2px rgba(0,0,0,0.2)',
  },
  // Messages pour le champ Client
  noClientsMessage: {
    backgroundColor: '#fff3cd',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  noClientsText: {
    color: '#856404',
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    fontWeight: 'bold',
  },
  noClientsSubText: {
    color: '#856404',
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 4,
  },
  selectSecteurMessage: {
    backgroundColor: '#f8d7da',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#f5c6cb',
  },
  selectSecteurText: {
    color: '#721c24',
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Styles pour les informations du client
  clientInfoGroup: {
    backgroundColor: '#e8f5e8',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#28a745',
  },
  clientInfoLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#28a745',
    marginBottom: 10,
  },
  clientInfoContainer: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#d4edda',
  },
  clientInfoName: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6,
  },
  clientInfoAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  clientInfoContact: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  clientInfoSector: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: 'bold',
  },
  input: {
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 14,
    fontSize: 15,
    backgroundColor: '#ffffff',
    color: '#1e293b',
    height: 48,
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  },

  periodeInfo: {
    fontSize: 12,
    color: '#6c757d',
    fontStyle: 'italic',
    marginBottom: 8,
    paddingHorizontal: 4,
  },

  errorButtonContainer: {
    marginTop: 8,
  },

  readOnlyInput: {
    backgroundColor: '#f9fafb',
    color: '#6b7280',
  },
  message: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  successMessage: {
    backgroundColor: '#d1fae5',
    borderColor: '#a7f3d0',
    borderWidth: 1,
  },
  errorMessage: {
    backgroundColor: '#fee2e2',
    borderColor: '#fecaca',
    borderWidth: 1,
  },
  messageText: {
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    backgroundColor: '#10b981',
    padding: 12, // Réduit le padding
    borderRadius: 6, // Réduit le rayon des bordures
    alignItems: 'center',
    marginTop: 10,
    height: 45, // Hauteur fixe plus petite
  },
  disabledButton: {
    backgroundColor: '#9ca3af',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 14, // Réduit la taille de police
    fontWeight: 'bold',
  },
});

export default Consommation;
