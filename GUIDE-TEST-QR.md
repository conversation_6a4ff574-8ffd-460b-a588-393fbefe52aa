# 📱 Guide de Test - Scanner QR avec Caméra PC

## 🎯 Fonctionnalité Implémentée

✅ **Scanner QR avec vraie caméra PC**
✅ **Détection automatique en temps réel**
✅ **Recherche client dans la base de données**
✅ **Affichage des informations complètes**

## 🚀 Étapes de Test

### 1. Préparation
```bash
# Terminal 1 - Démarrer le serveur backend
cd server
node server.js

# Terminal 2 - Démarrer l'application React
npm start
```

### 2. Codes QR de Test
Ouvrez le fichier `test-qr-codes.html` dans votre navigateur pour voir les codes QR :
- **QR001** → <PERSON> (Centre Ville)
- **QR002** → <PERSON> (Sfax Nord)

### 3. Test de la Fonctionnalité

1. **Accédez à l'application** : http://localhost:3000
2. **Naviguez vers "Scanner QR"** dans le menu
3. **Cliquez sur "Démarrer le scan"**
4. **Autorisez l'accès caméra** quand le navigateur le demande
5. **Pointez votre caméra** vers un des codes QR du fichier test
6. **Résultat attendu** : 
   - Détection automatique du QR
   - Recherche dans la base de données
   - Affichage des informations client
   - Options : "Consommation" ou "Scanner autre"

## 🔧 Technologies Utilisées

- **API Web Native** : `navigator.mediaDevices.getUserMedia()`
- **Détection QR** : Bibliothèque `jsQR`
- **Éléments HTML5** : `<video>` et `<canvas>`
- **Scan temps réel** : Interval de 100ms

## 🎨 Interface

- **Modal plein écran** avec flux vidéo
- **Cadre de visée** avec coins colorés
- **Instructions claires** 
- **Indicateur de chargement** animé
- **Gestion d'erreurs** complète

## 📊 Données de Test

Le serveur utilise des données de test si la base PostgreSQL n'est pas connectée :

```javascript
// Codes QR disponibles
QR001 → Client Jean Dupont (ID: 1)
QR002 → Client Marie Martin (ID: 2)
```

## 🐛 Dépannage

### Caméra ne s'ouvre pas
- Vérifiez les permissions du navigateur
- Assurez-vous qu'aucune autre application n'utilise la caméra
- Rechargez la page si nécessaire

### QR non détecté
- Assurez-vous que le code QR est bien visible
- Ajustez la distance et l'angle
- Vérifiez l'éclairage

### Erreur de connexion
- Vérifiez que le serveur backend fonctionne (port 4000)
- Contrôlez la console pour les erreurs réseau

## ✨ Fonctionnalités Avancées

- **Auto-stop** du scan après détection
- **Nettoyage automatique** des ressources
- **Fallback** avec données de test
- **Interface responsive** mobile
- **Gestion des permissions** intelligente

## 🎯 Prochaines Étapes

Après validation du scan QR, vous pouvez :
1. Naviguer vers la page Consommation
2. Scanner un autre client
3. Voir les détails complets du client

---

**🔥 La caméra de votre PC va maintenant s'ouvrir et scanner automatiquement les codes QR !**
