# 🔧 Résolution de l'Erreur de Connexion

## ❌ Erreur : "Impossible de se connecter au serveur"

Cette erreur indique que votre application mobile ne peut pas se connecter au serveur backend.

## 🚀 Solution Rapide

### **Étape 1 : Démarrer le serveur backend**

Ouvrez un terminal et exécutez :
```bash
node simple-server.js
```

**OU** double-cliquez sur : `SOLUTION-RAPIDE.bat`

### **Étape 2 : Vérifier que le serveur fonctionne**

Ouvrez votre navigateur et allez sur : http://localhost:4000

Vous devriez voir :
```json
{
  "message": "Serveur AquaTrack fonctionnel",
  "status": "OK",
  "port": 4000
}
```

### **Étape 3 : Démarrer l'application mobile**

Dans un nouveau terminal :
```bash
cd mobile
npx expo start --web
```

### **Étape 4 : Tester la connexion**

1. Ouvrez http://localhost:19006
2. Essayez de vous connecter avec :
   - **Email :** <EMAIL>
   - **Mot de passe :** Tech123

## 🔍 Diagnostic des Problèmes

### **Problème 1 : Port 4000 occupé**
```bash
# Arrêter tous les processus Node.js
taskkill /f /im node.exe

# Ou redémarrer votre PC
```

### **Problème 2 : Modules manquants**
```bash
# Installer les dépendances
npm install

# Dans le dossier mobile
cd mobile
npm install
```

### **Problème 3 : Configuration IP incorrecte**

Vérifiez les fichiers suivants et assurez-vous qu'ils utilisent `localhost:4000` :

- `AuthenticationMobile.js` (ligne 22)
- `mobile/src/screens/LoginScreen.js` (ligne 20)
- `App.js` (ligne 33)

Changez l'URL en :
```javascript
const API_BASE_URL = 'http://localhost:4000';
```

## 🛠️ Solutions Avancées

### **Solution 1 : Serveur de diagnostic**
```bash
node diagnostic-serveur.js
```

### **Solution 2 : Réinstallation complète**
```bash
# Supprimer node_modules
rmdir /s node_modules
rmdir /s mobile\node_modules

# Réinstaller
npm install
cd mobile && npm install
```

### **Solution 3 : Vérifier les ports**
```bash
# Voir quels ports sont utilisés
netstat -an | findstr :4000
netstat -an | findstr :19006
```

## 📋 Checklist de Vérification

- [ ] Node.js est installé (`node --version`)
- [ ] Le serveur backend démarre sans erreur
- [ ] http://localhost:4000 fonctionne dans le navigateur
- [ ] L'application mobile démarre sans erreur
- [ ] Les comptes de test fonctionnent

## 🔑 Comptes de Test

```
Technicien :
- Email : <EMAIL>
- Mot de passe : Tech123

Administrateur :
- Email : <EMAIL>
- Mot de passe : Admin123
```

## 🆘 Si Rien ne Fonctionne

1. **Redémarrez votre PC**
2. **Exécutez :** `SOLUTION-RAPIDE.bat`
3. **Attendez** que les deux services démarrent
4. **Testez** les URLs dans le navigateur
5. **Contactez** le support si le problème persiste

## 📞 Support Technique

Si l'erreur persiste après avoir suivi toutes ces étapes :

1. Notez le message d'erreur exact
2. Vérifiez les logs dans les terminaux
3. Prenez une capture d'écran de l'erreur
4. Contactez l'équipe de développement

---

**Note :** Gardez toujours les deux terminaux (backend + mobile) ouverts pendant l'utilisation de l'application.
