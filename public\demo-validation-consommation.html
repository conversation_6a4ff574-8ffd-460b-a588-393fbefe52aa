<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Validation Consommation Actuelle</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #dc3545;
        }
        
        .form-group {
            margin-bottom: 20px;
            padding: 12px;
            border-radius: 8px;
            border-width: 1px;
            border-style: solid;
        }
        
        .form-group.precedente {
            background: #f0fff4;
            border-color: #28a745;
        }
        
        .form-group.actuelle {
            background: #fff8f0;
            border-color: #ffc107;
        }
        
        .form-group.actuelle.error {
            background: #fff5f5;
            border-color: #dc3545;
        }
        
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .form-label.precedente {
            color: #28a745;
        }
        
        .form-label.actuelle {
            color: #ffc107;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            background: white;
            transition: all 0.3s;
        }
        
        .form-input.auto-filled {
            background: #f0fff4;
            border-color: #28a745;
            border-width: 2px;
        }
        
        .form-input.error {
            background: #fff5f5;
            border-color: #dc3545;
            border-width: 2px;
        }
        
        .auto-filled-info {
            font-size: 12px;
            color: #28a745;
            font-style: italic;
            margin-top: 5px;
            text-align: center;
        }
        
        .error-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 8px;
            width: 100%;
            transition: background 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .error-button:hover {
            background: #c82333;
        }
        
        .validation-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .example-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .example-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .example-card.valid {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .example-card.invalid {
            border-color: #dc3545;
            background: #fff8f8;
        }
        
        .example-card h4 {
            margin-bottom: 15px;
        }
        
        .example-card.valid h4 {
            color: #28a745;
        }
        
        .example-card.invalid h4 {
            color: #dc3545;
        }
        
        .test-scenarios {
            background: #fff3cd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }
        
        .test-scenarios h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .scenario-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid #ffc107;
        }
        
        .scenario-client {
            font-weight: bold;
            color: #ffc107;
            margin-bottom: 10px;
        }
        
        .scenario-test {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        
        .scenario-test:last-child {
            border-bottom: none;
        }
        
        .scenario-test.valid {
            color: #28a745;
        }
        
        .scenario-test.invalid {
            color: #dc3545;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Validation Consommation Actuelle</h1>
            <p>Démonstration de la validation en temps réel avec bouton d'erreur</p>
        </div>
        
        <div class="content">
            <div class="demo-form">
                <h3 style="color: #dc3545; margin-bottom: 20px;">📱 Simulation de la Validation en Temps Réel</h3>
                
                <div class="form-group precedente">
                    <label class="form-label precedente">📊 Consommation Précédente (m³)</label>
                    <input 
                        type="text" 
                        id="consommationPrecedente" 
                        class="form-input auto-filled" 
                        value="45"
                        readonly
                    >
                    <div class="auto-filled-info">
                        ✅ Dernière consommation récupérée automatiquement
                    </div>
                </div>
                
                <div class="form-group actuelle" id="consommationActuelleGroup">
                    <label class="form-label actuelle">⚡ Consommation Actuelle (m³)</label>
                    <input 
                        type="number" 
                        id="consommationActuelle" 
                        class="form-input" 
                        placeholder="Doit être > 45 m³"
                        oninput="validateConsommation()"
                    >
                    <button id="errorButton" class="error-button" style="display: none;" onclick="showErrorDetails()">
                        ❌ Consommation Invalide - Cliquez pour plus d'infos
                    </button>
                </div>
            </div>
            
            <div class="validation-examples">
                <div class="example-card valid">
                    <h4>✅ Validation Réussie</h4>
                    <ul style="line-height: 1.6; color: #155724;">
                        <li><strong>Condition :</strong> Consommation actuelle > précédente</li>
                        <li><strong>Exemple :</strong> 50 m³ > 45 m³</li>
                        <li><strong>Apparence :</strong> Champ normal</li>
                        <li><strong>Bouton erreur :</strong> Pas visible</li>
                    </ul>
                </div>
                
                <div class="example-card invalid">
                    <h4>❌ Validation Échouée</h4>
                    <ul style="line-height: 1.6; color: #721c24;">
                        <li><strong>Condition :</strong> Consommation actuelle ≤ précédente</li>
                        <li><strong>Exemple :</strong> 40 m³ ≤ 45 m³</li>
                        <li><strong>Apparence :</strong> Champ rouge</li>
                        <li><strong>Bouton erreur :</strong> Visible (rouge)</li>
                    </ul>
                </div>
            </div>
            
            <div class="test-scenarios">
                <h3>🧪 Scénarios de Test par Client</h3>
                <div class="scenario-grid">
                    <div class="scenario-card">
                        <div class="scenario-client">👤 Benali Fatima (Précédente: 45 m³)</div>
                        <div class="scenario-test valid">✅ Saisie 50 m³ → VALIDE</div>
                        <div class="scenario-test invalid">❌ Saisie 40 m³ → INVALIDE</div>
                        <div class="scenario-test invalid">❌ Saisie 45 m³ → INVALIDE (égale)</div>
                    </div>
                    
                    <div class="scenario-card">
                        <div class="scenario-client">👤 Alami Mohammed (Précédente: 38 m³)</div>
                        <div class="scenario-test valid">✅ Saisie 42 m³ → VALIDE</div>
                        <div class="scenario-test invalid">❌ Saisie 35 m³ → INVALIDE</div>
                        <div class="scenario-test invalid">❌ Saisie 38 m³ → INVALIDE (égale)</div>
                    </div>
                    
                    <div class="scenario-card">
                        <div class="scenario-client">👤 Benjelloun Youssef (Précédente: 67 m³)</div>
                        <div class="scenario-test valid">✅ Saisie 70 m³ → VALIDE</div>
                        <div class="scenario-test invalid">❌ Saisie 60 m³ → INVALIDE</div>
                        <div class="scenario-test invalid">❌ Saisie 67 m³ → INVALIDE (égale)</div>
                    </div>
                    
                    <div class="scenario-card">
                        <div class="scenario-client">👤 Lahlou Khadija (Précédente: 41 m³)</div>
                        <div class="scenario-test valid">✅ Saisie 45 m³ → VALIDE</div>
                        <div class="scenario-test invalid">❌ Saisie 39 m³ → INVALIDE</div>
                        <div class="scenario-test invalid">❌ Saisie 41 m³ → INVALIDE (égale)</div>
                    </div>
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-primary" target="_blank">
                    📱 Tester l'Application Réelle
                </a>
                <button class="btn btn-danger" onclick="simulateError()">
                    🧪 Simuler une Erreur
                </button>
            </div>
            
            <div style="background: #d4edda; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #28a745;">
                <h3 style="color: #155724; margin-bottom: 15px;">✅ Validation Consommation Actuelle</h3>
                <ul style="color: #155724; line-height: 1.6;">
                    <li>✅ <strong>Validation en temps réel :</strong> Vérification 500ms après la saisie</li>
                    <li>✅ <strong>Condition stricte :</strong> Consommation actuelle doit être > précédente</li>
                    <li>✅ <strong>Bouton d'erreur :</strong> Apparaît si validation échoue</li>
                    <li>✅ <strong>Indication visuelle :</strong> Champ rouge avec fond rose en cas d'erreur</li>
                    <li>✅ <strong>Message détaillé :</strong> Popup avec explication complète</li>
                    <li>✅ <strong>Placeholder dynamique :</strong> Indique la valeur minimale requise</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        let validationTimeout;
        let currentError = '';
        
        function validateConsommation() {
            clearTimeout(validationTimeout);
            
            validationTimeout = setTimeout(() => {
                const precedente = parseFloat(document.getElementById('consommationPrecedente').value);
                const actuelle = parseFloat(document.getElementById('consommationActuelle').value);
                const input = document.getElementById('consommationActuelle');
                const group = document.getElementById('consommationActuelleGroup');
                const errorButton = document.getElementById('errorButton');
                
                // Réinitialiser l'apparence
                input.classList.remove('error');
                group.classList.remove('error');
                errorButton.style.display = 'none';
                currentError = '';
                
                // Vérifier si les valeurs sont valides
                if (isNaN(actuelle) || actuelle === '') {
                    return; // Pas de validation si le champ est vide ou non numérique
                }
                
                if (isNaN(precedente)) {
                    return; // Pas de validation si la consommation précédente n'est pas définie
                }
                
                // Validation principale
                if (actuelle <= precedente) {
                    // Erreur de validation
                    input.classList.add('error');
                    group.classList.add('error');
                    errorButton.style.display = 'block';
                    
                    currentError = `Consommation invalide ! La consommation actuelle (${actuelle} m³) doit être supérieure à la consommation précédente (${precedente} m³)`;
                    
                    console.log('❌ ERREUR DE VALIDATION:', currentError);
                } else {
                    console.log(`✅ VALIDATION RÉUSSIE: ${actuelle} m³ > ${precedente} m³`);
                }
            }, 500);
        }
        
        function showErrorDetails() {
            if (currentError) {
                alert('❌ Consommation Invalide\n\n' + currentError + '\n\nVeuillez saisir une valeur supérieure à la consommation précédente.');
            }
        }
        
        function simulateError() {
            const input = document.getElementById('consommationActuelle');
            input.value = '40';
            validateConsommation();
            
            setTimeout(() => {
                alert('Simulation d\'erreur !\n\nLa valeur 40 m³ est inférieure à la consommation précédente (45 m³).\n\nLe bouton d\'erreur rouge est maintenant visible.');
            }, 600);
        }
        
        // Changer le client pour tester différentes valeurs
        function changeClient(clientName, previousConsumption) {
            const precedenteInput = document.getElementById('consommationPrecedente');
            const actuelleInput = document.getElementById('consommationActuelle');
            
            precedenteInput.value = previousConsumption;
            actuelleInput.placeholder = `Doit être > ${previousConsumption} m³`;
            actuelleInput.value = '';
            
            // Réinitialiser l'apparence
            const group = document.getElementById('consommationActuelleGroup');
            const errorButton = document.getElementById('errorButton');
            actuelleInput.classList.remove('error');
            group.classList.remove('error');
            errorButton.style.display = 'none';
            
            alert(`Client changé : ${clientName}\nConsommation précédente : ${previousConsumption} m³\n\nTestez maintenant différentes valeurs dans le champ "Consommation Actuelle".`);
        }
        
        // Ajouter des boutons pour changer de client
        window.addEventListener('load', function() {
            const buttonsDiv = document.querySelector('.buttons');
            
            const clientButtons = [
                { name: 'Benali Fatima', consumption: 45 },
                { name: 'Alami Mohammed', consumption: 38 },
                { name: 'Benjelloun Youssef', consumption: 67 },
                { name: 'Lahlou Khadija', consumption: 41 }
            ];
            
            clientButtons.forEach(client => {
                const button = document.createElement('button');
                button.className = 'btn btn-primary';
                button.textContent = `👤 ${client.name}`;
                button.onclick = () => changeClient(client.name, client.consumption);
                buttonsDiv.appendChild(button);
            });
        });
    </script>
</body>
</html>
