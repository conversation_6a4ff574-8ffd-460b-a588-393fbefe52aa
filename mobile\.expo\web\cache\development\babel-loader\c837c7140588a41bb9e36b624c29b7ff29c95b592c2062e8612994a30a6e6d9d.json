{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport ActivityIndicator from \"react-native-web/dist/exports/ActivityIndicator\";\nimport SafeAreaView from \"react-native-web/dist/exports/SafeAreaView\";\nimport KeyboardAvoidingView from \"react-native-web/dist/exports/KeyboardAvoidingView\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { jsxs as _jsxs, jsx as _jsx } from \"react/jsx-runtime\";\nvar ConsommationScreen = function ConsommationScreen(_ref) {\n  var navigation = _ref.navigation,\n    route = _ref.route;\n  var _route$params = route.params,\n    client = _route$params.client,\n    user = _route$params.user;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    loading = _useState2[0],\n    setLoading = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    contract = _useState4[0],\n    setContract = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    lastConsommation = _useState6[0],\n    setLastConsommation = _useState6[1];\n  var _useState7 = useState({\n      periode: '',\n      consommationPre: '',\n      consommationActuelle: '',\n      jours: '',\n      idtranch: 1,\n      status: 'En cours'\n    }),\n    _useState8 = _slicedToArray(_useState7, 2),\n    formData = _useState8[0],\n    setFormData = _useState8[1];\n  var API_BASE_URL = 'http://***********:4000';\n  useEffect(function () {\n    fetchClientContract();\n    fetchLastConsommation();\n    generateCurrentPeriod();\n  }, []);\n  var generateCurrentPeriod = function generateCurrentPeriod() {\n    var now = new Date();\n    var periode = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n    setFormData(function (prev) {\n      return _objectSpread(_objectSpread({}, prev), {}, {\n        periode: periode\n      });\n    });\n  };\n  var fetchClientContract = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      try {\n        var response = yield fetch(`${API_BASE_URL}/api/client-contract/${client.idclient}`);\n        var data = yield response.json();\n        if (data.success && data.contract) {\n          setContract(data.contract);\n        }\n      } catch (error) {\n        console.error('Erreur récupération contrat:', error);\n      }\n    });\n    return function fetchClientContract() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var fetchLastConsommation = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      try {\n        var response = yield fetch(`${API_BASE_URL}/api/clients/${client.idclient}/last-consommation`);\n        var data = yield response.json();\n        if (data.success && data.data) {\n          setLastConsommation(data.data);\n          setFormData(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              consommationPre: data.data.consommationactuelle.toString()\n            });\n          });\n        }\n      } catch (error) {\n        console.error('Erreur récupération dernière consommation:', error);\n      }\n    });\n    return function fetchLastConsommation() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var calculateJours = function calculateJours() {\n    if (lastConsommation && formData.periode) {\n      try {\n        var currentPeriod = new Date(formData.periode + '-01');\n        var lastPeriod = new Date(lastConsommation.periode + '-01');\n        var diffTime = Math.abs(currentPeriod - lastPeriod);\n        var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        setFormData(function (prev) {\n          return _objectSpread(_objectSpread({}, prev), {}, {\n            jours: diffDays.toString()\n          });\n        });\n      } catch (error) {\n        console.error('Erreur calcul jours:', error);\n      }\n    }\n  };\n  useEffect(function () {\n    calculateJours();\n  }, [formData.periode, lastConsommation]);\n  var validateForm = function validateForm() {\n    if (!formData.periode) {\n      Alert.alert('Erreur', 'Veuillez saisir la période');\n      return false;\n    }\n    if (!formData.consommationActuelle) {\n      Alert.alert('Erreur', 'Veuillez saisir la consommation actuelle');\n      return false;\n    }\n    if (parseInt(formData.consommationActuelle) <= parseInt(formData.consommationPre || 0)) {\n      Alert.alert('Erreur', 'La consommation actuelle doit être supérieure à la consommation précédente');\n      return false;\n    }\n    if (!contract) {\n      Alert.alert('Erreur', 'Aucun contrat trouvé pour ce client');\n      return false;\n    }\n    return true;\n  };\n  var handleSubmit = function () {\n    var _ref4 = _asyncToGenerator(function* () {\n      if (!validateForm()) return;\n      setLoading(true);\n      try {\n        var consommationData = {\n          consommationpre: parseInt(formData.consommationPre || 0),\n          consommationactuelle: parseInt(formData.consommationActuelle),\n          idcont: contract.idcontract,\n          idtech: (user == null ? void 0 : user.idtech) || 1,\n          idtranch: formData.idtranch,\n          jours: parseInt(formData.jours || 30),\n          periode: formData.periode,\n          status: formData.status\n        };\n        var response = yield fetch(`${API_BASE_URL}/api/consommations`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(consommationData)\n        });\n        var result = yield response.json();\n        if (result.success) {\n          Alert.alert('Succès', 'Consommation enregistrée avec succès !', [{\n            text: 'OK',\n            onPress: function onPress() {\n              return navigation.goBack();\n            }\n          }]);\n        } else {\n          Alert.alert('Erreur', result.message || 'Erreur lors de l\\'enregistrement');\n        }\n      } catch (error) {\n        console.error('Erreur soumission:', error);\n        Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);\n      } finally {\n        setLoading(false);\n      }\n    });\n    return function handleSubmit() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  return _jsx(SafeAreaView, {\n    style: styles.container,\n    children: _jsx(KeyboardAvoidingView, {\n      behavior: Platform.OS === 'ios' ? 'padding' : 'height',\n      style: styles.keyboardView,\n      children: _jsxs(ScrollView, {\n        style: styles.scrollView,\n        showsVerticalScrollIndicator: false,\n        children: [_jsxs(View, {\n          style: styles.clientHeader,\n          children: [_jsxs(Text, {\n            style: styles.clientName,\n            children: [client.nom, \" \", client.prenom]\n          }), _jsxs(Text, {\n            style: styles.clientDetail,\n            children: [\"\\uD83D\\uDCCD \", client.adresse]\n          }), _jsxs(Text, {\n            style: styles.clientDetail,\n            children: [\"\\uD83C\\uDFD9\\uFE0F \", client.ville]\n          }), contract && _jsxs(Text, {\n            style: styles.clientDetail,\n            children: [\"\\uD83D\\uDD27 Compteur: \", contract.marquecompteur]\n          })]\n        }), _jsxs(View, {\n          style: styles.form,\n          children: [_jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"P\\xE9riode (YYYY-MM) *\"\n            }), _jsx(TextInput, {\n              style: styles.input,\n              value: formData.periode,\n              onChangeText: function onChangeText(text) {\n                return setFormData(function (prev) {\n                  return _objectSpread(_objectSpread({}, prev), {}, {\n                    periode: text\n                  });\n                });\n              },\n              placeholder: \"2024-01\",\n              maxLength: 7\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Consommation Pr\\xE9c\\xE9dente (m\\xB3)\"\n            }), _jsx(TextInput, {\n              style: [styles.input, styles.readOnlyInput],\n              value: formData.consommationPre,\n              editable: false,\n              placeholder: \"Automatique\"\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Consommation Actuelle (m\\xB3) *\"\n            }), _jsx(TextInput, {\n              style: styles.input,\n              value: formData.consommationActuelle,\n              onChangeText: function onChangeText(text) {\n                return setFormData(function (prev) {\n                  return _objectSpread(_objectSpread({}, prev), {}, {\n                    consommationActuelle: text\n                  });\n                });\n              },\n              placeholder: \"Saisir la consommation actuelle\",\n              keyboardType: \"numeric\"\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Nombre de jours\"\n            }), _jsx(TextInput, {\n              style: [styles.input, styles.readOnlyInput],\n              value: formData.jours,\n              editable: false,\n              placeholder: \"Calcul\\xE9 automatiquement\"\n            })]\n          }), _jsxs(View, {\n            style: styles.formGroup,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Statut\"\n            }), _jsx(TextInput, {\n              style: styles.input,\n              value: formData.status,\n              onChangeText: function onChangeText(text) {\n                return setFormData(function (prev) {\n                  return _objectSpread(_objectSpread({}, prev), {}, {\n                    status: text\n                  });\n                });\n              },\n              placeholder: \"En cours\"\n            })]\n          })]\n        }), _jsxs(View, {\n          style: styles.buttonContainer,\n          children: [_jsx(TouchableOpacity, {\n            style: styles.cancelButton,\n            onPress: function onPress() {\n              return navigation.goBack();\n            },\n            children: _jsx(Text, {\n              style: styles.cancelButtonText,\n              children: \"Annuler\"\n            })\n          }), _jsx(TouchableOpacity, {\n            style: [styles.submitButton, loading && styles.disabledButton],\n            onPress: handleSubmit,\n            disabled: loading,\n            children: loading ? _jsx(ActivityIndicator, {\n              color: \"#fff\"\n            }) : _jsx(Text, {\n              style: styles.submitButtonText,\n              children: \"Enregistrer\"\n            })\n          })]\n        })]\n      })\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  keyboardView: {\n    flex: 1\n  },\n  scrollView: {\n    flex: 1\n  },\n  clientHeader: {\n    backgroundColor: '#007AFF',\n    padding: 20,\n    alignItems: 'center'\n  },\n  clientName: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginBottom: 5\n  },\n  clientDetail: {\n    fontSize: 14,\n    color: '#fff',\n    opacity: 0.9,\n    marginBottom: 2\n  },\n  form: {\n    padding: 20\n  },\n  formGroup: {\n    marginBottom: 20\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 8\n  },\n  input: {\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    fontSize: 16,\n    backgroundColor: '#fff'\n  },\n  readOnlyInput: {\n    backgroundColor: '#f8f9fa',\n    color: '#666'\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    padding: 20,\n    paddingTop: 0\n  },\n  cancelButton: {\n    flex: 1,\n    backgroundColor: '#f8f9fa',\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginRight: 10\n  },\n  cancelButtonText: {\n    color: '#666',\n    fontWeight: 'bold',\n    fontSize: 16\n  },\n  submitButton: {\n    flex: 1,\n    backgroundColor: '#007AFF',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginLeft: 10\n  },\n  submitButtonText: {\n    color: '#fff',\n    fontWeight: 'bold',\n    fontSize: 16\n  },\n  disabledButton: {\n    backgroundColor: '#ccc'\n  }\n});\nexport default ConsommationScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "TextInput", "TouchableOpacity", "StyleSheet", "<PERSON><PERSON>", "ScrollView", "ActivityIndicator", "SafeAreaView", "KeyboardAvoidingView", "Platform", "jsxs", "_jsxs", "jsx", "_jsx", "ConsommationScreen", "_ref", "navigation", "route", "_route$params", "params", "client", "user", "_useState", "_useState2", "_slicedToArray", "loading", "setLoading", "_useState3", "_useState4", "contract", "setContract", "_useState5", "_useState6", "lastConsommation", "setLastConsommation", "_useState7", "periode", "consommationPre", "consommationActuelle", "jours", "idtranch", "status", "_useState8", "formData", "setFormData", "API_BASE_URL", "fetchClientContract", "fetchLastConsommation", "generateCurrentPeriod", "now", "Date", "getFullYear", "String", "getMonth", "padStart", "prev", "_objectSpread", "_ref2", "_asyncToGenerator", "response", "fetch", "idclient", "data", "json", "success", "error", "console", "apply", "arguments", "_ref3", "consommationactuelle", "toString", "calculateJours", "currentPeriod", "last<PERSON><PERSON><PERSON>", "diffTime", "Math", "abs", "diffDays", "ceil", "validateForm", "alert", "parseInt", "handleSubmit", "_ref4", "consommationData", "consommationpre", "idcont", "idcontract", "idtech", "method", "headers", "body", "JSON", "stringify", "result", "text", "onPress", "goBack", "message", "style", "styles", "container", "children", "behavior", "OS", "keyboard<PERSON>iew", "scrollView", "showsVerticalScrollIndicator", "clientHeader", "clientName", "nom", "prenom", "clientDetail", "adresse", "ville", "marquecompteur", "form", "formGroup", "label", "input", "value", "onChangeText", "placeholder", "max<PERSON><PERSON><PERSON>", "readOnlyInput", "editable", "keyboardType", "buttonContainer", "cancelButton", "cancelButtonText", "submitButton", "disabled<PERSON><PERSON>on", "disabled", "color", "submitButtonText", "create", "flex", "backgroundColor", "padding", "alignItems", "fontSize", "fontWeight", "marginBottom", "opacity", "borderWidth", "borderColor", "borderRadius", "flexDirection", "justifyContent", "paddingTop", "marginRight", "marginLeft"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/mobile/src/screens/ConsommationScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  View,\n  Text,\n  TextInput,\n  TouchableOpacity,\n  StyleSheet,\n  Alert,\n  ScrollView,\n  ActivityIndicator,\n  SafeAreaView,\n  KeyboardAvoidingView,\n  Platform,\n} from 'react-native';\n\nconst ConsommationScreen = ({ navigation, route }) => {\n  const { client, user } = route.params;\n  const [loading, setLoading] = useState(false);\n  const [contract, setContract] = useState(null);\n  const [lastConsommation, setLastConsommation] = useState(null);\n  const [formData, setFormData] = useState({\n    periode: '',\n    consommationPre: '',\n    consommationActuelle: '',\n    jours: '',\n    idtranch: 1,\n    status: 'En cours'\n  });\n\n  const API_BASE_URL = 'http://***********:4000'; // IP locale détectée\n\n  useEffect(() => {\n    fetchClientContract();\n    fetchLastConsommation();\n    generateCurrentPeriod();\n  }, []);\n\n  const generateCurrentPeriod = () => {\n    const now = new Date();\n    const periode = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;\n    setFormData(prev => ({ ...prev, periode }));\n  };\n\n  const fetchClientContract = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/client-contract/${client.idclient}`);\n      const data = await response.json();\n      if (data.success && data.contract) {\n        setContract(data.contract);\n      }\n    } catch (error) {\n      console.error('Erreur récupération contrat:', error);\n    }\n  };\n\n  const fetchLastConsommation = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/clients/${client.idclient}/last-consommation`);\n      const data = await response.json();\n      if (data.success && data.data) {\n        setLastConsommation(data.data);\n        setFormData(prev => ({ \n          ...prev, \n          consommationPre: data.data.consommationactuelle.toString()\n        }));\n      }\n    } catch (error) {\n      console.error('Erreur récupération dernière consommation:', error);\n    }\n  };\n\n  const calculateJours = () => {\n    if (lastConsommation && formData.periode) {\n      try {\n        const currentPeriod = new Date(formData.periode + '-01');\n        const lastPeriod = new Date(lastConsommation.periode + '-01');\n        const diffTime = Math.abs(currentPeriod - lastPeriod);\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        setFormData(prev => ({ ...prev, jours: diffDays.toString() }));\n      } catch (error) {\n        console.error('Erreur calcul jours:', error);\n      }\n    }\n  };\n\n  useEffect(() => {\n    calculateJours();\n  }, [formData.periode, lastConsommation]);\n\n  const validateForm = () => {\n    if (!formData.periode) {\n      Alert.alert('Erreur', 'Veuillez saisir la période');\n      return false;\n    }\n    if (!formData.consommationActuelle) {\n      Alert.alert('Erreur', 'Veuillez saisir la consommation actuelle');\n      return false;\n    }\n    if (parseInt(formData.consommationActuelle) <= parseInt(formData.consommationPre || 0)) {\n      Alert.alert('Erreur', 'La consommation actuelle doit être supérieure à la consommation précédente');\n      return false;\n    }\n    if (!contract) {\n      Alert.alert('Erreur', 'Aucun contrat trouvé pour ce client');\n      return false;\n    }\n    return true;\n  };\n\n  const handleSubmit = async () => {\n    if (!validateForm()) return;\n\n    setLoading(true);\n    try {\n      const consommationData = {\n        consommationpre: parseInt(formData.consommationPre || 0),\n        consommationactuelle: parseInt(formData.consommationActuelle),\n        idcont: contract.idcontract,\n        idtech: user?.idtech || 1,\n        idtranch: formData.idtranch,\n        jours: parseInt(formData.jours || 30),\n        periode: formData.periode,\n        status: formData.status\n      };\n\n      const response = await fetch(`${API_BASE_URL}/api/consommations`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(consommationData),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        Alert.alert(\n          'Succès',\n          'Consommation enregistrée avec succès !',\n          [\n            {\n              text: 'OK',\n              onPress: () => navigation.goBack()\n            }\n          ]\n        );\n      } else {\n        Alert.alert('Erreur', result.message || 'Erreur lors de l\\'enregistrement');\n      }\n    } catch (error) {\n      console.error('Erreur soumission:', error);\n      Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <KeyboardAvoidingView \n        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}\n        style={styles.keyboardView}\n      >\n        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>\n          {/* En-tête Client */}\n          <View style={styles.clientHeader}>\n            <Text style={styles.clientName}>{client.nom} {client.prenom}</Text>\n            <Text style={styles.clientDetail}>📍 {client.adresse}</Text>\n            <Text style={styles.clientDetail}>🏙️ {client.ville}</Text>\n            {contract && (\n              <Text style={styles.clientDetail}>🔧 Compteur: {contract.marquecompteur}</Text>\n            )}\n          </View>\n\n          {/* Formulaire */}\n          <View style={styles.form}>\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Période (YYYY-MM) *</Text>\n              <TextInput\n                style={styles.input}\n                value={formData.periode}\n                onChangeText={(text) => setFormData(prev => ({ ...prev, periode: text }))}\n                placeholder=\"2024-01\"\n                maxLength={7}\n              />\n            </View>\n\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Consommation Précédente (m³)</Text>\n              <TextInput\n                style={[styles.input, styles.readOnlyInput]}\n                value={formData.consommationPre}\n                editable={false}\n                placeholder=\"Automatique\"\n              />\n            </View>\n\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Consommation Actuelle (m³) *</Text>\n              <TextInput\n                style={styles.input}\n                value={formData.consommationActuelle}\n                onChangeText={(text) => setFormData(prev => ({ ...prev, consommationActuelle: text }))}\n                placeholder=\"Saisir la consommation actuelle\"\n                keyboardType=\"numeric\"\n              />\n            </View>\n\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Nombre de jours</Text>\n              <TextInput\n                style={[styles.input, styles.readOnlyInput]}\n                value={formData.jours}\n                editable={false}\n                placeholder=\"Calculé automatiquement\"\n              />\n            </View>\n\n            <View style={styles.formGroup}>\n              <Text style={styles.label}>Statut</Text>\n              <TextInput\n                style={styles.input}\n                value={formData.status}\n                onChangeText={(text) => setFormData(prev => ({ ...prev, status: text }))}\n                placeholder=\"En cours\"\n              />\n            </View>\n          </View>\n\n          {/* Boutons */}\n          <View style={styles.buttonContainer}>\n            <TouchableOpacity\n              style={styles.cancelButton}\n              onPress={() => navigation.goBack()}\n            >\n              <Text style={styles.cancelButtonText}>Annuler</Text>\n            </TouchableOpacity>\n\n            <TouchableOpacity\n              style={[styles.submitButton, loading && styles.disabledButton]}\n              onPress={handleSubmit}\n              disabled={loading}\n            >\n              {loading ? (\n                <ActivityIndicator color=\"#fff\" />\n              ) : (\n                <Text style={styles.submitButtonText}>Enregistrer</Text>\n              )}\n            </TouchableOpacity>\n          </View>\n        </ScrollView>\n      </KeyboardAvoidingView>\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  keyboardView: {\n    flex: 1,\n  },\n  scrollView: {\n    flex: 1,\n  },\n  clientHeader: {\n    backgroundColor: '#007AFF',\n    padding: 20,\n    alignItems: 'center',\n  },\n  clientName: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginBottom: 5,\n  },\n  clientDetail: {\n    fontSize: 14,\n    color: '#fff',\n    opacity: 0.9,\n    marginBottom: 2,\n  },\n  form: {\n    padding: 20,\n  },\n  formGroup: {\n    marginBottom: 20,\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 8,\n  },\n  input: {\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    fontSize: 16,\n    backgroundColor: '#fff',\n  },\n  readOnlyInput: {\n    backgroundColor: '#f8f9fa',\n    color: '#666',\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    padding: 20,\n    paddingTop: 0,\n  },\n  cancelButton: {\n    flex: 1,\n    backgroundColor: '#f8f9fa',\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginRight: 10,\n  },\n  cancelButtonText: {\n    color: '#666',\n    fontWeight: 'bold',\n    fontSize: 16,\n  },\n  submitButton: {\n    flex: 1,\n    backgroundColor: '#007AFF',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginLeft: 10,\n  },\n  submitButtonText: {\n    color: '#fff',\n    fontWeight: 'bold',\n    fontSize: 16,\n  },\n  disabledButton: {\n    backgroundColor: '#ccc',\n  },\n});\n\nexport default ConsommationScreen;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,iBAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,oBAAA;AAAA,OAAAC,QAAA;AAAA,SAAAC,IAAA,IAAAC,KAAA,EAAAC,GAAA,IAAAC,IAAA;AAenD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,IAAA,EAA8B;EAAA,IAAxBC,UAAU,GAAAD,IAAA,CAAVC,UAAU;IAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;EAC7C,IAAAC,aAAA,GAAyBD,KAAK,CAACE,MAAM;IAA7BC,MAAM,GAAAF,aAAA,CAANE,MAAM;IAAEC,IAAI,GAAAH,aAAA,CAAJG,IAAI;EACpB,IAAAC,SAAA,GAA8BzB,QAAQ,CAAC,KAAK,CAAC;IAAA0B,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAtCG,OAAO,GAAAF,UAAA;IAAEG,UAAU,GAAAH,UAAA;EAC1B,IAAAI,UAAA,GAAgC9B,QAAQ,CAAC,IAAI,CAAC;IAAA+B,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAvCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAAgDlC,QAAQ,CAAC,IAAI,CAAC;IAAAmC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAvDE,gBAAgB,GAAAD,UAAA;IAAEE,mBAAmB,GAAAF,UAAA;EAC5C,IAAAG,UAAA,GAAgCtC,QAAQ,CAAC;MACvCuC,OAAO,EAAE,EAAE;MACXC,eAAe,EAAE,EAAE;MACnBC,oBAAoB,EAAE,EAAE;MACxBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACV,CAAC,CAAC;IAAAC,UAAA,GAAAlB,cAAA,CAAAW,UAAA;IAPKQ,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAS5B,IAAMG,YAAY,GAAG,yBAAyB;EAE9C/C,SAAS,CAAC,YAAM;IACdgD,mBAAmB,CAAC,CAAC;IACrBC,qBAAqB,CAAC,CAAC;IACvBC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAClC,IAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,IAAMd,OAAO,GAAG,GAAGa,GAAG,CAACE,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IACrFV,WAAW,CAAC,UAAAW,IAAI;MAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;QAAEnB,OAAO,EAAPA;MAAO;IAAA,CAAG,CAAC;EAC7C,CAAC;EAED,IAAMU,mBAAmB;IAAA,IAAAW,KAAA,GAAAC,iBAAA,CAAG,aAAY;MACtC,IAAI;QACF,IAAMC,QAAQ,SAASC,KAAK,CAAC,GAAGf,YAAY,wBAAwBzB,MAAM,CAACyC,QAAQ,EAAE,CAAC;QACtF,IAAMC,IAAI,SAASH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACjC,QAAQ,EAAE;UACjCC,WAAW,CAACgC,IAAI,CAACjC,QAAQ,CAAC;QAC5B;MACF,CAAC,CAAC,OAAOoC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAAA,gBAVKnB,mBAAmBA,CAAA;MAAA,OAAAW,KAAA,CAAAU,KAAA,OAAAC,SAAA;IAAA;EAAA,GAUxB;EAED,IAAMrB,qBAAqB;IAAA,IAAAsB,KAAA,GAAAX,iBAAA,CAAG,aAAY;MACxC,IAAI;QACF,IAAMC,QAAQ,SAASC,KAAK,CAAC,GAAGf,YAAY,gBAAgBzB,MAAM,CAACyC,QAAQ,oBAAoB,CAAC;QAChG,IAAMC,IAAI,SAASH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;UAC7B5B,mBAAmB,CAAC4B,IAAI,CAACA,IAAI,CAAC;UAC9BlB,WAAW,CAAC,UAAAW,IAAI;YAAA,OAAAC,aAAA,CAAAA,aAAA,KACXD,IAAI;cACPlB,eAAe,EAAEyB,IAAI,CAACA,IAAI,CAACQ,oBAAoB,CAACC,QAAQ,CAAC;YAAC;UAAA,CAC1D,CAAC;QACL;MACF,CAAC,CAAC,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MACpE;IACF,CAAC;IAAA,gBAdKlB,qBAAqBA,CAAA;MAAA,OAAAsB,KAAA,CAAAF,KAAA,OAAAC,SAAA;IAAA;EAAA,GAc1B;EAED,IAAMI,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,IAAIvC,gBAAgB,IAAIU,QAAQ,CAACP,OAAO,EAAE;MACxC,IAAI;QACF,IAAMqC,aAAa,GAAG,IAAIvB,IAAI,CAACP,QAAQ,CAACP,OAAO,GAAG,KAAK,CAAC;QACxD,IAAMsC,UAAU,GAAG,IAAIxB,IAAI,CAACjB,gBAAgB,CAACG,OAAO,GAAG,KAAK,CAAC;QAC7D,IAAMuC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,aAAa,GAAGC,UAAU,CAAC;QACrD,IAAMI,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC5D/B,WAAW,CAAC,UAAAW,IAAI;UAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;YAAEhB,KAAK,EAAEuC,QAAQ,CAACP,QAAQ,CAAC;UAAC;QAAA,CAAG,CAAC;MAChE,CAAC,CAAC,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF;EACF,CAAC;EAEDnE,SAAS,CAAC,YAAM;IACd0E,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC7B,QAAQ,CAACP,OAAO,EAAEH,gBAAgB,CAAC,CAAC;EAExC,IAAM+C,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzB,IAAI,CAACrC,QAAQ,CAACP,OAAO,EAAE;MACrBhC,KAAK,CAAC6E,KAAK,CAAC,QAAQ,EAAE,4BAA4B,CAAC;MACnD,OAAO,KAAK;IACd;IACA,IAAI,CAACtC,QAAQ,CAACL,oBAAoB,EAAE;MAClClC,KAAK,CAAC6E,KAAK,CAAC,QAAQ,EAAE,0CAA0C,CAAC;MACjE,OAAO,KAAK;IACd;IACA,IAAIC,QAAQ,CAACvC,QAAQ,CAACL,oBAAoB,CAAC,IAAI4C,QAAQ,CAACvC,QAAQ,CAACN,eAAe,IAAI,CAAC,CAAC,EAAE;MACtFjC,KAAK,CAAC6E,KAAK,CAAC,QAAQ,EAAE,4EAA4E,CAAC;MACnG,OAAO,KAAK;IACd;IACA,IAAI,CAACpD,QAAQ,EAAE;MACbzB,KAAK,CAAC6E,KAAK,CAAC,QAAQ,EAAE,qCAAqC,CAAC;MAC5D,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,IAAME,YAAY;IAAA,IAAAC,KAAA,GAAA1B,iBAAA,CAAG,aAAY;MAC/B,IAAI,CAACsB,YAAY,CAAC,CAAC,EAAE;MAErBtD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,IAAM2D,gBAAgB,GAAG;UACvBC,eAAe,EAAEJ,QAAQ,CAACvC,QAAQ,CAACN,eAAe,IAAI,CAAC,CAAC;UACxDiC,oBAAoB,EAAEY,QAAQ,CAACvC,QAAQ,CAACL,oBAAoB,CAAC;UAC7DiD,MAAM,EAAE1D,QAAQ,CAAC2D,UAAU;UAC3BC,MAAM,EAAE,CAAApE,IAAI,oBAAJA,IAAI,CAAEoE,MAAM,KAAI,CAAC;UACzBjD,QAAQ,EAAEG,QAAQ,CAACH,QAAQ;UAC3BD,KAAK,EAAE2C,QAAQ,CAACvC,QAAQ,CAACJ,KAAK,IAAI,EAAE,CAAC;UACrCH,OAAO,EAAEO,QAAQ,CAACP,OAAO;UACzBK,MAAM,EAAEE,QAAQ,CAACF;QACnB,CAAC;QAED,IAAMkB,QAAQ,SAASC,KAAK,CAAC,GAAGf,YAAY,oBAAoB,EAAE;UAChE6C,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACT,gBAAgB;QACvC,CAAC,CAAC;QAEF,IAAMU,MAAM,SAASpC,QAAQ,CAACI,IAAI,CAAC,CAAC;QAEpC,IAAIgC,MAAM,CAAC/B,OAAO,EAAE;UAClB5D,KAAK,CAAC6E,KAAK,CACT,QAAQ,EACR,wCAAwC,EACxC,CACE;YACEe,IAAI,EAAE,IAAI;YACVC,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQjF,UAAU,CAACkF,MAAM,CAAC,CAAC;YAAA;UACpC,CAAC,CAEL,CAAC;QACH,CAAC,MAAM;UACL9F,KAAK,CAAC6E,KAAK,CAAC,QAAQ,EAAEc,MAAM,CAACI,OAAO,IAAI,kCAAkC,CAAC;QAC7E;MACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C7D,KAAK,CAAC6E,KAAK,CAAC,QAAQ,EAAE,wBAAwBhB,KAAK,CAACkC,OAAO,EAAE,CAAC;MAChE,CAAC,SAAS;QACRzE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBA9CKyD,YAAYA,CAAA;MAAA,OAAAC,KAAA,CAAAjB,KAAA,OAAAC,SAAA;IAAA;EAAA,GA8CjB;EAED,OACEvD,IAAA,CAACN,YAAY;IAAC6F,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,EACpC1F,IAAA,CAACL,oBAAoB;MACnBgG,QAAQ,EAAE/F,QAAQ,CAACgG,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG,QAAS;MACvDL,KAAK,EAAEC,MAAM,CAACK,YAAa;MAAAH,QAAA,EAE3B5F,KAAA,CAACN,UAAU;QAAC+F,KAAK,EAAEC,MAAM,CAACM,UAAW;QAACC,4BAA4B,EAAE,KAAM;QAAAL,QAAA,GAExE5F,KAAA,CAACZ,IAAI;UAACqG,KAAK,EAAEC,MAAM,CAACQ,YAAa;UAAAN,QAAA,GAC/B5F,KAAA,CAACX,IAAI;YAACoG,KAAK,EAAEC,MAAM,CAACS,UAAW;YAAAP,QAAA,GAAEnF,MAAM,CAAC2F,GAAG,EAAC,GAAC,EAAC3F,MAAM,CAAC4F,MAAM;UAAA,CAAO,CAAC,EACnErG,KAAA,CAACX,IAAI;YAACoG,KAAK,EAAEC,MAAM,CAACY,YAAa;YAAAV,QAAA,GAAC,eAAG,EAACnF,MAAM,CAAC8F,OAAO;UAAA,CAAO,CAAC,EAC5DvG,KAAA,CAACX,IAAI;YAACoG,KAAK,EAAEC,MAAM,CAACY,YAAa;YAAAV,QAAA,GAAC,qBAAI,EAACnF,MAAM,CAAC+F,KAAK;UAAA,CAAO,CAAC,EAC1DtF,QAAQ,IACPlB,KAAA,CAACX,IAAI;YAACoG,KAAK,EAAEC,MAAM,CAACY,YAAa;YAAAV,QAAA,GAAC,yBAAa,EAAC1E,QAAQ,CAACuF,cAAc;UAAA,CAAO,CAC/E;QAAA,CACG,CAAC,EAGPzG,KAAA,CAACZ,IAAI;UAACqG,KAAK,EAAEC,MAAM,CAACgB,IAAK;UAAAd,QAAA,GACvB5F,KAAA,CAACZ,IAAI;YAACqG,KAAK,EAAEC,MAAM,CAACiB,SAAU;YAAAf,QAAA,GAC5B1F,IAAA,CAACb,IAAI;cAACoG,KAAK,EAAEC,MAAM,CAACkB,KAAM;cAAAhB,QAAA,EAAC;YAAmB,CAAM,CAAC,EACrD1F,IAAA,CAACZ,SAAS;cACRmG,KAAK,EAAEC,MAAM,CAACmB,KAAM;cACpBC,KAAK,EAAE9E,QAAQ,CAACP,OAAQ;cACxBsF,YAAY,EAAE,SAAdA,YAAYA,CAAG1B,IAAI;gBAAA,OAAKpD,WAAW,CAAC,UAAAW,IAAI;kBAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;oBAAEnB,OAAO,EAAE4D;kBAAI;gBAAA,CAAG,CAAC;cAAA,CAAC;cAC1E2B,WAAW,EAAC,SAAS;cACrBC,SAAS,EAAE;YAAE,CACd,CAAC;UAAA,CACE,CAAC,EAEPjH,KAAA,CAACZ,IAAI;YAACqG,KAAK,EAAEC,MAAM,CAACiB,SAAU;YAAAf,QAAA,GAC5B1F,IAAA,CAACb,IAAI;cAACoG,KAAK,EAAEC,MAAM,CAACkB,KAAM;cAAAhB,QAAA,EAAC;YAA4B,CAAM,CAAC,EAC9D1F,IAAA,CAACZ,SAAS;cACRmG,KAAK,EAAE,CAACC,MAAM,CAACmB,KAAK,EAAEnB,MAAM,CAACwB,aAAa,CAAE;cAC5CJ,KAAK,EAAE9E,QAAQ,CAACN,eAAgB;cAChCyF,QAAQ,EAAE,KAAM;cAChBH,WAAW,EAAC;YAAa,CAC1B,CAAC;UAAA,CACE,CAAC,EAEPhH,KAAA,CAACZ,IAAI;YAACqG,KAAK,EAAEC,MAAM,CAACiB,SAAU;YAAAf,QAAA,GAC5B1F,IAAA,CAACb,IAAI;cAACoG,KAAK,EAAEC,MAAM,CAACkB,KAAM;cAAAhB,QAAA,EAAC;YAA4B,CAAM,CAAC,EAC9D1F,IAAA,CAACZ,SAAS;cACRmG,KAAK,EAAEC,MAAM,CAACmB,KAAM;cACpBC,KAAK,EAAE9E,QAAQ,CAACL,oBAAqB;cACrCoF,YAAY,EAAE,SAAdA,YAAYA,CAAG1B,IAAI;gBAAA,OAAKpD,WAAW,CAAC,UAAAW,IAAI;kBAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;oBAAEjB,oBAAoB,EAAE0D;kBAAI;gBAAA,CAAG,CAAC;cAAA,CAAC;cACvF2B,WAAW,EAAC,iCAAiC;cAC7CI,YAAY,EAAC;YAAS,CACvB,CAAC;UAAA,CACE,CAAC,EAEPpH,KAAA,CAACZ,IAAI;YAACqG,KAAK,EAAEC,MAAM,CAACiB,SAAU;YAAAf,QAAA,GAC5B1F,IAAA,CAACb,IAAI;cAACoG,KAAK,EAAEC,MAAM,CAACkB,KAAM;cAAAhB,QAAA,EAAC;YAAe,CAAM,CAAC,EACjD1F,IAAA,CAACZ,SAAS;cACRmG,KAAK,EAAE,CAACC,MAAM,CAACmB,KAAK,EAAEnB,MAAM,CAACwB,aAAa,CAAE;cAC5CJ,KAAK,EAAE9E,QAAQ,CAACJ,KAAM;cACtBuF,QAAQ,EAAE,KAAM;cAChBH,WAAW,EAAC;YAAyB,CACtC,CAAC;UAAA,CACE,CAAC,EAEPhH,KAAA,CAACZ,IAAI;YAACqG,KAAK,EAAEC,MAAM,CAACiB,SAAU;YAAAf,QAAA,GAC5B1F,IAAA,CAACb,IAAI;cAACoG,KAAK,EAAEC,MAAM,CAACkB,KAAM;cAAAhB,QAAA,EAAC;YAAM,CAAM,CAAC,EACxC1F,IAAA,CAACZ,SAAS;cACRmG,KAAK,EAAEC,MAAM,CAACmB,KAAM;cACpBC,KAAK,EAAE9E,QAAQ,CAACF,MAAO;cACvBiF,YAAY,EAAE,SAAdA,YAAYA,CAAG1B,IAAI;gBAAA,OAAKpD,WAAW,CAAC,UAAAW,IAAI;kBAAA,OAAAC,aAAA,CAAAA,aAAA,KAAUD,IAAI;oBAAEd,MAAM,EAAEuD;kBAAI;gBAAA,CAAG,CAAC;cAAA,CAAC;cACzE2B,WAAW,EAAC;YAAU,CACvB,CAAC;UAAA,CACE,CAAC;QAAA,CACH,CAAC,EAGPhH,KAAA,CAACZ,IAAI;UAACqG,KAAK,EAAEC,MAAM,CAAC2B,eAAgB;UAAAzB,QAAA,GAClC1F,IAAA,CAACX,gBAAgB;YACfkG,KAAK,EAAEC,MAAM,CAAC4B,YAAa;YAC3BhC,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQjF,UAAU,CAACkF,MAAM,CAAC,CAAC;YAAA,CAAC;YAAAK,QAAA,EAEnC1F,IAAA,CAACb,IAAI;cAACoG,KAAK,EAAEC,MAAM,CAAC6B,gBAAiB;cAAA3B,QAAA,EAAC;YAAO,CAAM;UAAC,CACpC,CAAC,EAEnB1F,IAAA,CAACX,gBAAgB;YACfkG,KAAK,EAAE,CAACC,MAAM,CAAC8B,YAAY,EAAE1G,OAAO,IAAI4E,MAAM,CAAC+B,cAAc,CAAE;YAC/DnC,OAAO,EAAEd,YAAa;YACtBkD,QAAQ,EAAE5G,OAAQ;YAAA8E,QAAA,EAEjB9E,OAAO,GACNZ,IAAA,CAACP,iBAAiB;cAACgI,KAAK,EAAC;YAAM,CAAE,CAAC,GAElCzH,IAAA,CAACb,IAAI;cAACoG,KAAK,EAAEC,MAAM,CAACkC,gBAAiB;cAAAhC,QAAA,EAAC;YAAW,CAAM;UACxD,CACe,CAAC;QAAA,CACf,CAAC;MAAA,CACG;IAAC,CACO;EAAC,CACX,CAAC;AAEnB,CAAC;AAED,IAAMF,MAAM,GAAGlG,UAAU,CAACqI,MAAM,CAAC;EAC/BlC,SAAS,EAAE;IACTmC,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACDhC,YAAY,EAAE;IACZ+B,IAAI,EAAE;EACR,CAAC;EACD9B,UAAU,EAAE;IACV8B,IAAI,EAAE;EACR,CAAC;EACD5B,YAAY,EAAE;IACZ6B,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC;EACD9B,UAAU,EAAE;IACV+B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBR,KAAK,EAAE,MAAM;IACbS,YAAY,EAAE;EAChB,CAAC;EACD9B,YAAY,EAAE;IACZ4B,QAAQ,EAAE,EAAE;IACZP,KAAK,EAAE,MAAM;IACbU,OAAO,EAAE,GAAG;IACZD,YAAY,EAAE;EAChB,CAAC;EACD1B,IAAI,EAAE;IACJsB,OAAO,EAAE;EACX,CAAC;EACDrB,SAAS,EAAE;IACTyB,YAAY,EAAE;EAChB,CAAC;EACDxB,KAAK,EAAE;IACLsB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBR,KAAK,EAAE,MAAM;IACbS,YAAY,EAAE;EAChB,CAAC;EACDvB,KAAK,EAAE;IACLyB,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,EAAE;IAChBR,OAAO,EAAE,EAAE;IACXE,QAAQ,EAAE,EAAE;IACZH,eAAe,EAAE;EACnB,CAAC;EACDb,aAAa,EAAE;IACba,eAAe,EAAE,SAAS;IAC1BJ,KAAK,EAAE;EACT,CAAC;EACDN,eAAe,EAAE;IACfoB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BV,OAAO,EAAE,EAAE;IACXW,UAAU,EAAE;EACd,CAAC;EACDrB,YAAY,EAAE;IACZQ,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,SAAS;IAC1BO,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,EAAE;IAChBR,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,QAAQ;IACpBW,WAAW,EAAE;EACf,CAAC;EACDrB,gBAAgB,EAAE;IAChBI,KAAK,EAAE,MAAM;IACbQ,UAAU,EAAE,MAAM;IAClBD,QAAQ,EAAE;EACZ,CAAC;EACDV,YAAY,EAAE;IACZM,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,SAAS;IAC1BS,YAAY,EAAE,EAAE;IAChBR,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,QAAQ;IACpBY,UAAU,EAAE;EACd,CAAC;EACDjB,gBAAgB,EAAE;IAChBD,KAAK,EAAE,MAAM;IACbQ,UAAU,EAAE,MAAM;IAClBD,QAAQ,EAAE;EACZ,CAAC;EACDT,cAAc,EAAE;IACdM,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AAEF,eAAe5H,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}