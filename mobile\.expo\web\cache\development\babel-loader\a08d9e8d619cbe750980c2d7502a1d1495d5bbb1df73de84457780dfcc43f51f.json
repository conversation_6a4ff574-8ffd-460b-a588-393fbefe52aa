{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport ActivityIndicator from \"react-native-web/dist/exports/ActivityIndicator\";\nimport KeyboardAvoidingView from \"react-native-web/dist/exports/KeyboardAvoidingView\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport SafeAreaView from \"react-native-web/dist/exports/SafeAreaView\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar LoginScreen = function LoginScreen(_ref) {\n  var navigation = _ref.navigation;\n  var _useState = useState(''),\n    _useState2 = _slicedToArray(_useState, 2),\n    email = _useState2[0],\n    setEmail = _useState2[1];\n  var _useState3 = useState(''),\n    _useState4 = _slicedToArray(_useState3, 2),\n    password = _useState4[0],\n    setPassword = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    loading = _useState6[0],\n    setLoading = _useState6[1];\n  var API_BASE_URL = 'http://***********:4000';\n  var handleLogin = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      if (!email || !password) {\n        Alert.alert('Erreur', 'Veuillez saisir votre email et mot de passe');\n        return;\n      }\n      setLoading(true);\n      try {\n        var response = yield fetch(`${API_BASE_URL}/api/login`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            email: email,\n            password: password\n          })\n        });\n        var data = yield response.json();\n        if (data.success) {\n          Alert.alert('Connexion réussie', `Bienvenue ${data.user.nom} ${data.user.prenom}`, [{\n            text: 'OK',\n            onPress: function onPress() {\n              if (data.user.role === 'Tech') {\n                navigation.replace('TechnicianDashboard', {\n                  user: data.user\n                });\n              } else if (data.user.role === 'Admin') {\n                navigation.replace('AdminDashboard', {\n                  user: data.user\n                });\n              } else {\n                Alert.alert('Erreur', 'Rôle utilisateur non reconnu');\n              }\n            }\n          }]);\n        } else {\n          Alert.alert('Erreur de connexion', data.message || 'Email ou mot de passe incorrect');\n        }\n      } catch (error) {\n        console.error('Erreur de connexion:', error);\n        Alert.alert('Erreur', 'Impossible de se connecter au serveur');\n      } finally {\n        setLoading(false);\n      }\n    });\n    return function handleLogin() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  return _jsx(SafeAreaView, {\n    style: styles.container,\n    children: _jsx(KeyboardAvoidingView, {\n      behavior: Platform.OS === 'ios' ? 'padding' : 'height',\n      style: styles.keyboardView,\n      children: _jsxs(View, {\n        style: styles.loginContainer,\n        children: [_jsxs(View, {\n          style: styles.header,\n          children: [_jsx(Text, {\n            style: styles.title,\n            children: \"\\uD83D\\uDCA7 AquaTrack\"\n          }), _jsx(Text, {\n            style: styles.subtitle,\n            children: \"Gestion des Consommations d'Eau\"\n          })]\n        }), _jsxs(View, {\n          style: styles.form,\n          children: [_jsxs(View, {\n            style: styles.inputContainer,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Email\"\n            }), _jsx(TextInput, {\n              style: styles.input,\n              value: email,\n              onChangeText: setEmail,\n              placeholder: \"Entrez votre email\",\n              keyboardType: \"email-address\",\n              autoCapitalize: \"none\",\n              autoCorrect: false,\n              editable: !loading\n            })]\n          }), _jsxs(View, {\n            style: styles.inputContainer,\n            children: [_jsx(Text, {\n              style: styles.label,\n              children: \"Mot de passe\"\n            }), _jsx(TextInput, {\n              style: styles.input,\n              value: password,\n              onChangeText: setPassword,\n              placeholder: \"Entrez votre mot de passe\",\n              secureTextEntry: true,\n              editable: !loading\n            })]\n          }), _jsx(TouchableOpacity, {\n            style: [styles.loginButton, loading && styles.disabledButton],\n            onPress: handleLogin,\n            disabled: loading,\n            children: loading ? _jsx(ActivityIndicator, {\n              color: \"#fff\"\n            }) : _jsx(Text, {\n              style: styles.loginButtonText,\n              children: \"Se connecter\"\n            })\n          })]\n        }), _jsxs(View, {\n          style: styles.footer,\n          children: [_jsx(Text, {\n            style: styles.footerText,\n            children: \"Comptes de test :\"\n          }), _jsx(TouchableOpacity, {\n            onPress: function onPress() {\n              setEmail('<EMAIL>');\n              setPassword('Tech123');\n            },\n            children: _jsx(Text, {\n              style: styles.testAccount,\n              children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDD27 Technicien : <EMAIL> / Tech123\"\n            })\n          }), _jsx(TouchableOpacity, {\n            onPress: function onPress() {\n              setEmail('<EMAIL>');\n              setPassword('Admin123');\n            },\n            children: _jsx(Text, {\n              style: styles.testAccount,\n              children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBC Admin : <EMAIL> / Admin123\"\n            })\n          })]\n        })]\n      })\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  keyboardView: {\n    flex: 1\n  },\n  loginContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    paddingHorizontal: 30\n  },\n  header: {\n    alignItems: 'center',\n    marginBottom: 50\n  },\n  title: {\n    fontSize: 32,\n    fontWeight: 'bold',\n    color: '#007AFF',\n    marginBottom: 10\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#666',\n    textAlign: 'center'\n  },\n  form: {\n    marginBottom: 30\n  },\n  inputContainer: {\n    marginBottom: 20\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 8\n  },\n  input: {\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    fontSize: 16,\n    backgroundColor: '#fff'\n  },\n  loginButton: {\n    backgroundColor: '#007AFF',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginTop: 20\n  },\n  disabledButton: {\n    backgroundColor: '#ccc'\n  },\n  loginButtonText: {\n    color: '#fff',\n    fontSize: 18,\n    fontWeight: 'bold'\n  },\n  footer: {\n    alignItems: 'center'\n  },\n  footerText: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 10\n  },\n  testAccount: {\n    fontSize: 12,\n    color: '#007AFF',\n    marginBottom: 5,\n    textAlign: 'center'\n  }\n});\nexport default LoginScreen;", "map": {"version": 3, "names": ["React", "useState", "View", "Text", "TextInput", "TouchableOpacity", "StyleSheet", "<PERSON><PERSON>", "ActivityIndicator", "KeyboardAvoidingView", "Platform", "SafeAreaView", "jsx", "_jsx", "jsxs", "_jsxs", "LoginScreen", "_ref", "navigation", "_useState", "_useState2", "_slicedToArray", "email", "setEmail", "_useState3", "_useState4", "password", "setPassword", "_useState5", "_useState6", "loading", "setLoading", "API_BASE_URL", "handleLogin", "_ref2", "_asyncToGenerator", "alert", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "success", "user", "nom", "prenom", "text", "onPress", "role", "replace", "message", "error", "console", "apply", "arguments", "style", "styles", "container", "children", "behavior", "OS", "keyboard<PERSON>iew", "login<PERSON><PERSON><PERSON>", "header", "title", "subtitle", "form", "inputContainer", "label", "input", "value", "onChangeText", "placeholder", "keyboardType", "autoCapitalize", "autoCorrect", "editable", "secureTextEntry", "loginButton", "disabled<PERSON><PERSON>on", "disabled", "color", "loginButtonText", "footer", "footerText", "testAccount", "create", "flex", "backgroundColor", "justifyContent", "paddingHorizontal", "alignItems", "marginBottom", "fontSize", "fontWeight", "textAlign", "borderWidth", "borderColor", "borderRadius", "padding", "marginTop"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/mobile/src/screens/LoginScreen.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  View,\n  Text,\n  TextInput,\n  TouchableOpacity,\n  StyleSheet,\n  Alert,\n  ActivityIndicator,\n  KeyboardAvoidingView,\n  Platform,\n  SafeAreaView,\n} from 'react-native';\n\nconst LoginScreen = ({ navigation }) => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const API_BASE_URL = 'http://***********:4000'; // IP locale détectée\n\n  const handleLogin = async () => {\n    if (!email || !password) {\n      Alert.alert('Erreur', 'Veuillez saisir votre email et mot de passe');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        Alert.alert(\n          'Connexion réussie',\n          `Bienvenue ${data.user.nom} ${data.user.prenom}`,\n          [\n            {\n              text: 'OK',\n              onPress: () => {\n                if (data.user.role === 'Tech') {\n                  navigation.replace('TechnicianDashboard', { user: data.user });\n                } else if (data.user.role === 'Admin') {\n                  navigation.replace('AdminDashboard', { user: data.user });\n                } else {\n                  Alert.alert('Erreur', 'Rôle utilisateur non reconnu');\n                }\n              }\n            }\n          ]\n        );\n      } else {\n        Alert.alert('Erreur de connexion', data.message || 'Email ou mot de passe incorrect');\n      }\n    } catch (error) {\n      console.error('Erreur de connexion:', error);\n      Alert.alert('Erreur', 'Impossible de se connecter au serveur');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <KeyboardAvoidingView \n        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}\n        style={styles.keyboardView}\n      >\n        <View style={styles.loginContainer}>\n          <View style={styles.header}>\n            <Text style={styles.title}>💧 AquaTrack</Text>\n            <Text style={styles.subtitle}>Gestion des Consommations d'Eau</Text>\n          </View>\n\n          <View style={styles.form}>\n            <View style={styles.inputContainer}>\n              <Text style={styles.label}>Email</Text>\n              <TextInput\n                style={styles.input}\n                value={email}\n                onChangeText={setEmail}\n                placeholder=\"Entrez votre email\"\n                keyboardType=\"email-address\"\n                autoCapitalize=\"none\"\n                autoCorrect={false}\n                editable={!loading}\n              />\n            </View>\n\n            <View style={styles.inputContainer}>\n              <Text style={styles.label}>Mot de passe</Text>\n              <TextInput\n                style={styles.input}\n                value={password}\n                onChangeText={setPassword}\n                placeholder=\"Entrez votre mot de passe\"\n                secureTextEntry\n                editable={!loading}\n              />\n            </View>\n\n            <TouchableOpacity\n              style={[styles.loginButton, loading && styles.disabledButton]}\n              onPress={handleLogin}\n              disabled={loading}\n            >\n              {loading ? (\n                <ActivityIndicator color=\"#fff\" />\n              ) : (\n                <Text style={styles.loginButtonText}>Se connecter</Text>\n              )}\n            </TouchableOpacity>\n          </View>\n\n          <View style={styles.footer}>\n            <Text style={styles.footerText}>Comptes de test :</Text>\n            <TouchableOpacity onPress={() => {setEmail('<EMAIL>'); setPassword('Tech123');}}>\n              <Text style={styles.testAccount}>👨‍🔧 Technicien : <EMAIL> / Tech123</Text>\n            </TouchableOpacity>\n            <TouchableOpacity onPress={() => {setEmail('<EMAIL>'); setPassword('Admin123');}}>\n              <Text style={styles.testAccount}>👨‍💼 Admin : <EMAIL> / Admin123</Text>\n            </TouchableOpacity>\n          </View>\n        </View>\n      </KeyboardAvoidingView>\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  keyboardView: {\n    flex: 1,\n  },\n  loginContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    paddingHorizontal: 30,\n  },\n  header: {\n    alignItems: 'center',\n    marginBottom: 50,\n  },\n  title: {\n    fontSize: 32,\n    fontWeight: 'bold',\n    color: '#007AFF',\n    marginBottom: 10,\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#666',\n    textAlign: 'center',\n  },\n  form: {\n    marginBottom: 30,\n  },\n  inputContainer: {\n    marginBottom: 20,\n  },\n  label: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 8,\n  },\n  input: {\n    borderWidth: 1,\n    borderColor: '#ddd',\n    borderRadius: 10,\n    padding: 15,\n    fontSize: 16,\n    backgroundColor: '#fff',\n  },\n  loginButton: {\n    backgroundColor: '#007AFF',\n    borderRadius: 10,\n    padding: 15,\n    alignItems: 'center',\n    marginTop: 20,\n  },\n  disabledButton: {\n    backgroundColor: '#ccc',\n  },\n  loginButtonText: {\n    color: '#fff',\n    fontSize: 18,\n    fontWeight: 'bold',\n  },\n  footer: {\n    alignItems: 'center',\n  },\n  footerText: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 10,\n  },\n  testAccount: {\n    fontSize: 12,\n    color: '#007AFF',\n    marginBottom: 5,\n    textAlign: 'center',\n  },\n});\n\nexport default LoginScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,iBAAA;AAAA,OAAAC,oBAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,YAAA;AAAA,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAcxC,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAAuB;EAAA,IAAjBC,UAAU,GAAAD,IAAA,CAAVC,UAAU;EAC/B,IAAAC,SAAA,GAA0BlB,QAAQ,CAAC,EAAE,CAAC;IAAAmB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA/BG,KAAK,GAAAF,UAAA;IAAEG,QAAQ,GAAAH,UAAA;EACtB,IAAAI,UAAA,GAAgCvB,QAAQ,CAAC,EAAE,CAAC;IAAAwB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAArCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAA8B3B,QAAQ,CAAC,KAAK,CAAC;IAAA4B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAtCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAE1B,IAAMG,YAAY,GAAG,yBAAyB;EAE9C,IAAMC,WAAW;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAC9B,IAAI,CAACb,KAAK,IAAI,CAACI,QAAQ,EAAE;QACvBnB,KAAK,CAAC6B,KAAK,CAAC,QAAQ,EAAE,6CAA6C,CAAC;QACpE;MACF;MAEAL,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,IAAMM,QAAQ,SAASC,KAAK,CAAC,GAAGN,YAAY,YAAY,EAAE;UACxDO,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAErB,KAAK,EAALA,KAAK;YAAEI,QAAQ,EAARA;UAAS,CAAC;QAC1C,CAAC,CAAC;QAEF,IAAMkB,IAAI,SAASP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChBvC,KAAK,CAAC6B,KAAK,CACT,mBAAmB,EACnB,aAAaQ,IAAI,CAACG,IAAI,CAACC,GAAG,IAAIJ,IAAI,CAACG,IAAI,CAACE,MAAM,EAAE,EAChD,CACE;YACEC,IAAI,EAAE,IAAI;YACVC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;cACb,IAAIP,IAAI,CAACG,IAAI,CAACK,IAAI,KAAK,MAAM,EAAE;gBAC7BlC,UAAU,CAACmC,OAAO,CAAC,qBAAqB,EAAE;kBAAEN,IAAI,EAAEH,IAAI,CAACG;gBAAK,CAAC,CAAC;cAChE,CAAC,MAAM,IAAIH,IAAI,CAACG,IAAI,CAACK,IAAI,KAAK,OAAO,EAAE;gBACrClC,UAAU,CAACmC,OAAO,CAAC,gBAAgB,EAAE;kBAAEN,IAAI,EAAEH,IAAI,CAACG;gBAAK,CAAC,CAAC;cAC3D,CAAC,MAAM;gBACLxC,KAAK,CAAC6B,KAAK,CAAC,QAAQ,EAAE,8BAA8B,CAAC;cACvD;YACF;UACF,CAAC,CAEL,CAAC;QACH,CAAC,MAAM;UACL7B,KAAK,CAAC6B,KAAK,CAAC,qBAAqB,EAAEQ,IAAI,CAACU,OAAO,IAAI,iCAAiC,CAAC;QACvF;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5ChD,KAAK,CAAC6B,KAAK,CAAC,QAAQ,EAAE,uCAAuC,CAAC;MAChE,CAAC,SAAS;QACRL,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBA9CKE,WAAWA,CAAA;MAAA,OAAAC,KAAA,CAAAuB,KAAA,OAAAC,SAAA;IAAA;EAAA,GA8ChB;EAED,OACE7C,IAAA,CAACF,YAAY;IAACgD,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,EACpCjD,IAAA,CAACJ,oBAAoB;MACnBsD,QAAQ,EAAErD,QAAQ,CAACsD,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG,QAAS;MACvDL,KAAK,EAAEC,MAAM,CAACK,YAAa;MAAAH,QAAA,EAE3B/C,KAAA,CAACb,IAAI;QAACyD,KAAK,EAAEC,MAAM,CAACM,cAAe;QAAAJ,QAAA,GACjC/C,KAAA,CAACb,IAAI;UAACyD,KAAK,EAAEC,MAAM,CAACO,MAAO;UAAAL,QAAA,GACzBjD,IAAA,CAACV,IAAI;YAACwD,KAAK,EAAEC,MAAM,CAACQ,KAAM;YAAAN,QAAA,EAAC;UAAY,CAAM,CAAC,EAC9CjD,IAAA,CAACV,IAAI;YAACwD,KAAK,EAAEC,MAAM,CAACS,QAAS;YAAAP,QAAA,EAAC;UAA+B,CAAM,CAAC;QAAA,CAChE,CAAC,EAEP/C,KAAA,CAACb,IAAI;UAACyD,KAAK,EAAEC,MAAM,CAACU,IAAK;UAAAR,QAAA,GACvB/C,KAAA,CAACb,IAAI;YAACyD,KAAK,EAAEC,MAAM,CAACW,cAAe;YAAAT,QAAA,GACjCjD,IAAA,CAACV,IAAI;cAACwD,KAAK,EAAEC,MAAM,CAACY,KAAM;cAAAV,QAAA,EAAC;YAAK,CAAM,CAAC,EACvCjD,IAAA,CAACT,SAAS;cACRuD,KAAK,EAAEC,MAAM,CAACa,KAAM;cACpBC,KAAK,EAAEpD,KAAM;cACbqD,YAAY,EAAEpD,QAAS;cACvBqD,WAAW,EAAC,oBAAoB;cAChCC,YAAY,EAAC,eAAe;cAC5BC,cAAc,EAAC,MAAM;cACrBC,WAAW,EAAE,KAAM;cACnBC,QAAQ,EAAE,CAAClD;YAAQ,CACpB,CAAC;UAAA,CACE,CAAC,EAEPf,KAAA,CAACb,IAAI;YAACyD,KAAK,EAAEC,MAAM,CAACW,cAAe;YAAAT,QAAA,GACjCjD,IAAA,CAACV,IAAI;cAACwD,KAAK,EAAEC,MAAM,CAACY,KAAM;cAAAV,QAAA,EAAC;YAAY,CAAM,CAAC,EAC9CjD,IAAA,CAACT,SAAS;cACRuD,KAAK,EAAEC,MAAM,CAACa,KAAM;cACpBC,KAAK,EAAEhD,QAAS;cAChBiD,YAAY,EAAEhD,WAAY;cAC1BiD,WAAW,EAAC,2BAA2B;cACvCK,eAAe;cACfD,QAAQ,EAAE,CAAClD;YAAQ,CACpB,CAAC;UAAA,CACE,CAAC,EAEPjB,IAAA,CAACR,gBAAgB;YACfsD,KAAK,EAAE,CAACC,MAAM,CAACsB,WAAW,EAAEpD,OAAO,IAAI8B,MAAM,CAACuB,cAAc,CAAE;YAC9DhC,OAAO,EAAElB,WAAY;YACrBmD,QAAQ,EAAEtD,OAAQ;YAAAgC,QAAA,EAEjBhC,OAAO,GACNjB,IAAA,CAACL,iBAAiB;cAAC6E,KAAK,EAAC;YAAM,CAAE,CAAC,GAElCxE,IAAA,CAACV,IAAI;cAACwD,KAAK,EAAEC,MAAM,CAAC0B,eAAgB;cAAAxB,QAAA,EAAC;YAAY,CAAM;UACxD,CACe,CAAC;QAAA,CACf,CAAC,EAEP/C,KAAA,CAACb,IAAI;UAACyD,KAAK,EAAEC,MAAM,CAAC2B,MAAO;UAAAzB,QAAA,GACzBjD,IAAA,CAACV,IAAI;YAACwD,KAAK,EAAEC,MAAM,CAAC4B,UAAW;YAAA1B,QAAA,EAAC;UAAiB,CAAM,CAAC,EACxDjD,IAAA,CAACR,gBAAgB;YAAC8C,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;cAAC5B,QAAQ,CAAC,sBAAsB,CAAC;cAAEI,WAAW,CAAC,SAAS,CAAC;YAAC,CAAE;YAAAmC,QAAA,EAC3FjD,IAAA,CAACV,IAAI;cAACwD,KAAK,EAAEC,MAAM,CAAC6B,WAAY;cAAA3B,QAAA,EAAC;YAAiD,CAAM;UAAC,CACzE,CAAC,EACnBjD,IAAA,CAACR,gBAAgB;YAAC8C,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;cAAC5B,QAAQ,CAAC,uBAAuB,CAAC;cAAEI,WAAW,CAAC,UAAU,CAAC;YAAC,CAAE;YAAAmC,QAAA,EAC7FjD,IAAA,CAACV,IAAI;cAACwD,KAAK,EAAEC,MAAM,CAAC6B,WAAY;cAAA3B,QAAA,EAAC;YAA8C,CAAM;UAAC,CACtE,CAAC;QAAA,CACf,CAAC;MAAA,CACH;IAAC,CACa;EAAC,CACX,CAAC;AAEnB,CAAC;AAED,IAAMF,MAAM,GAAGtD,UAAU,CAACoF,MAAM,CAAC;EAC/B7B,SAAS,EAAE;IACT8B,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACD3B,YAAY,EAAE;IACZ0B,IAAI,EAAE;EACR,CAAC;EACDzB,cAAc,EAAE;IACdyB,IAAI,EAAE,CAAC;IACPE,cAAc,EAAE,QAAQ;IACxBC,iBAAiB,EAAE;EACrB,CAAC;EACD3B,MAAM,EAAE;IACN4B,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE;EAChB,CAAC;EACD5B,KAAK,EAAE;IACL6B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBb,KAAK,EAAE,SAAS;IAChBW,YAAY,EAAE;EAChB,CAAC;EACD3B,QAAQ,EAAE;IACR4B,QAAQ,EAAE,EAAE;IACZZ,KAAK,EAAE,MAAM;IACbc,SAAS,EAAE;EACb,CAAC;EACD7B,IAAI,EAAE;IACJ0B,YAAY,EAAE;EAChB,CAAC;EACDzB,cAAc,EAAE;IACdyB,YAAY,EAAE;EAChB,CAAC;EACDxB,KAAK,EAAE;IACLyB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBb,KAAK,EAAE,MAAM;IACbW,YAAY,EAAE;EAChB,CAAC;EACDvB,KAAK,EAAE;IACL2B,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXN,QAAQ,EAAE,EAAE;IACZL,eAAe,EAAE;EACnB,CAAC;EACDV,WAAW,EAAE;IACXU,eAAe,EAAE,SAAS;IAC1BU,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXR,UAAU,EAAE,QAAQ;IACpBS,SAAS,EAAE;EACb,CAAC;EACDrB,cAAc,EAAE;IACdS,eAAe,EAAE;EACnB,CAAC;EACDN,eAAe,EAAE;IACfD,KAAK,EAAE,MAAM;IACbY,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDX,MAAM,EAAE;IACNQ,UAAU,EAAE;EACd,CAAC;EACDP,UAAU,EAAE;IACVS,QAAQ,EAAE,EAAE;IACZZ,KAAK,EAAE,MAAM;IACbW,YAAY,EAAE;EAChB,CAAC;EACDP,WAAW,EAAE;IACXQ,QAAQ,EAAE,EAAE;IACZZ,KAAK,EAAE,SAAS;IAChBW,YAAY,EAAE,CAAC;IACfG,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAenF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}