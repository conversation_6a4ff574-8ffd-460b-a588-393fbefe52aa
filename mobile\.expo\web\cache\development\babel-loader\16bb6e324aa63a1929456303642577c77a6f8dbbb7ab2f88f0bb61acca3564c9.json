{"ast": null, "code": "import React from 'react';\nimport UnimplementedView from \"../../modules/UnimplementedView\";\nfunction YellowBox(props) {\n  return React.createElement(UnimplementedView, props);\n}\nYellowBox.ignoreWarnings = function () {};\nexport default YellowBox;", "map": {"version": 3, "names": ["React", "UnimplementedView", "YellowBox", "props", "createElement", "ignoreWarnings"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/YellowBox/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport React from 'react';\nimport UnimplementedView from '../../modules/UnimplementedView';\nfunction YellowBox(props) {\n  return /*#__PURE__*/React.createElement(UnimplementedView, props);\n}\nYellowBox.ignoreWarnings = () => {};\nexport default YellowBox;"], "mappings": "AAUA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,iBAAiB;AACxB,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAoBH,KAAK,CAACI,aAAa,CAACH,iBAAiB,EAAEE,KAAK,CAAC;AACnE;AACAD,SAAS,CAACG,cAAc,GAAG,YAAM,CAAC,CAAC;AACnC,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}