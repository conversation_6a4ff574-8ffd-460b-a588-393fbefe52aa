import React from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';

const { width } = Dimensions.get('window');

const GoogleMapsEmbed = ({ secteur, clients }) => {
  if (!secteur || !clients || clients.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.noDataText}>
          Sélectionnez un secteur pour voir la carte avec tous les clients
        </Text>
      </View>
    );
  }

  // Coordonnées du centre du secteur
  const centerLat = secteur.latitude;
  const centerLng = secteur.longitude;

  // URL avec marqueurs multiples pour tous les clients
  const mapsUrlWithMarkers = `https://www.google.com/maps/dir/${centerLat},${centerLng}/${clients.map(c => `${c.latitude},${c.longitude}`).join('/')}`;

  // Créer une URL Google Maps avec marqueurs personnalisés
  const createGoogleMapsEmbedUrl = () => {
    const baseUrl = 'https://www.google.com/maps/embed/v1/view';
    const apiKey = 'AIzaSyBFw0Qbyq9zTFTd-tUY6dOWTgHz-TK7VgM'; // Clé publique de démonstration

    // Créer les marqueurs pour chaque client
    const markers = clients.map((client, index) => {
      const label = client.nom.charAt(0).toUpperCase();
      return `markers=color:red%7Clabel:${label}%7C${client.latitude},${client.longitude}`;
    }).join('&');

    return `${baseUrl}?key=${apiKey}&center=${centerLat},${centerLng}&zoom=15&${markers}`;
  };

  // Créer une carte HTML personnalisée avec OpenStreetMap (gratuit)
  const createCustomMapHTML = () => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Carte du secteur ${secteur.nom}</title>
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <style>
          body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
          #map { height: 100vh; width: 100%; }
          .custom-popup { font-size: 12px; }
          .sector-marker { background-color: #007AFF; }
          .client-marker { background-color: #FF3B30; }
        </style>
      </head>
      <body>
        <div id="map"></div>
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <script>
          // Initialiser la carte
          const map = L.map('map').setView([${centerLat}, ${centerLng}], 15);

          // Ajouter les tuiles OpenStreetMap
          L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
          }).addTo(map);

          // Marqueur pour le centre du secteur (bleu)
          const sectorIcon = L.divIcon({
            className: 'sector-marker',
            html: '<div style="background-color: #007AFF; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px;">S</div>',
            iconSize: [24, 24],
            iconAnchor: [12, 12]
          });

          L.marker([${centerLat}, ${centerLng}], { icon: sectorIcon })
            .addTo(map)
            .bindPopup('<div class="custom-popup"><b>📍 ${secteur.nom}</b><br>Centre du secteur</div>');

          // Marqueurs pour chaque client (rouge)
          ${clients.map((client, index) => `
            const clientIcon${index} = L.divIcon({
              className: 'client-marker',
              html: '<div style="background-color: #FF3B30; width: 18px; height: 18px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 10px;">${client.nom.charAt(0)}</div>',
              iconSize: [22, 22],
              iconAnchor: [11, 11]
            });

            L.marker([${client.latitude}, ${client.longitude}], { icon: clientIcon${index} })
              .addTo(map)
              .bindPopup('<div class="custom-popup"><b>👤 ${client.nom} ${client.prenom}</b><br>📍 ${client.adresse}<br>📞 ${client.tel || 'N/A'}<br>📧 ${client.email || 'N/A'}</div>');
          `).join('')}

          // Ajuster la vue pour inclure tous les marqueurs
          const group = new L.featureGroup([
            L.marker([${centerLat}, ${centerLng}]),
            ${clients.map((client, index) => `L.marker([${client.latitude}, ${client.longitude}])`).join(',')}
          ]);

          if (group.getLayers().length > 1) {
            map.fitBounds(group.getBounds().pad(0.1));
          }
        </script>
      </body>
      </html>
    `;
  };

  const customMapHTML = createCustomMapHTML();

  // Pour React Native Web, utiliser une iframe
  if (typeof window !== 'undefined') {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>
          🗺️ {secteur.nom} - {clients.length} client(s)
        </Text>

        {/* Carte Interactive Intégrée */}
        <div style={styles.webMapContainer}>
          <iframe
            srcDoc={customMapHTML}
            style={styles.webMap}
            allowFullScreen=""
            loading="lazy"
            title={`Carte interactive du secteur ${secteur.nom}`}
          />
        </div>

        {/* Informations du secteur */}
        <View style={styles.secteurInfo}>
          <Text style={styles.secteurInfoText}>
            📍 Centre du secteur: {centerLat.toFixed(4)}, {centerLng.toFixed(4)}
          </Text>
        </View>

        {/* Liste des clients avec leurs coordonnées */}
        <View style={styles.clientsList}>
          <Text style={styles.clientsListTitle}>👥 Clients dans ce secteur :</Text>
          {clients.map((client, index) => (
            <TouchableOpacity
              key={client.idclient}
              style={styles.clientItem}
              onPress={() => {
                const clientMapUrl = `https://www.google.com/maps?q=${client.latitude},${client.longitude}&z=18`;
                window.open(clientMapUrl, '_blank');
              }}
            >
              <View style={styles.clientHeader}>
                <Text style={styles.clientName}>
                  📍 {client.nom} {client.prenom}
                </Text>
                <Text style={styles.clientNumber}>#{index + 1}</Text>
              </View>
              <Text style={styles.clientAddress}>
                🏠 {client.adresse}, {client.ville}
              </Text>
              <Text style={styles.clientCoords}>
                🗺️ {client.latitude?.toFixed(6)}, {client.longitude?.toFixed(6)}
              </Text>
              <Text style={styles.clickHint}>
                👆 Cliquez pour voir sur Google Maps
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Boutons d'action */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.openAllButton}
            onPress={() => window.open(mapsUrlWithMarkers, '_blank')}
          >
            <Text style={styles.openAllButtonText}>
              🌐 Voir tous les clients sur Google Maps
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.centerButton}
            onPress={() => {
              const centerMapUrl = `https://www.google.com/maps?q=${centerLat},${centerLng}&z=15`;
              window.open(centerMapUrl, '_blank');
            }}
          >
            <Text style={styles.centerButtonText}>
              🎯 Voir le centre du secteur
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Pour React Native mobile, afficher la liste des clients avec coordonnées
  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        🗺️ {secteur.nom} - {clients.length} client(s)
      </Text>

      <View style={styles.clientsList}>
        <Text style={styles.clientsListTitle}>👥 Clients dans ce secteur :</Text>
        {clients.map((client, index) => (
          <View key={client.idclient} style={styles.clientItem}>
            <Text style={styles.clientName}>
              📍 {client.nom} {client.prenom}
            </Text>
            <Text style={styles.clientAddress}>
              🏠 {client.adresse}, {client.ville}
            </Text>
            <Text style={styles.clientCoords}>
              🗺️ {client.latitude?.toFixed(6)}, {client.longitude?.toFixed(6)}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
    backgroundColor: '#007AFF',
    color: '#fff',
    padding: 12,
    borderRadius: 10,
  },
  noDataText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    padding: 30,
    fontSize: 16,
    lineHeight: 24,
  },
  webMapContainer: {
    width: '100%',
    height: 350,
    borderRadius: 15,
    overflow: 'hidden',
    marginBottom: 20,
    borderWidth: 3,
    borderColor: '#007AFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  webMap: {
    width: '100%',
    height: '100%',
    border: 'none',
    borderRadius: '12px',
  },
  secteurInfo: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#28a745',
  },
  secteurInfoText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
  clientsList: {
    marginTop: 10,
  },
  clientsListTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    paddingLeft: 5,
  },
  clientItem: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 12,
    marginBottom: 12,
    borderLeftWidth: 5,
    borderLeftColor: '#007AFF',
    cursor: 'pointer',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  clientName: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  clientNumber: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#007AFF',
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  clientAddress: {
    fontSize: 13,
    color: '#666',
    marginBottom: 6,
    lineHeight: 18,
  },
  clientCoords: {
    fontSize: 11,
    color: '#999',
    fontFamily: 'monospace',
    marginBottom: 6,
  },
  clickHint: {
    fontSize: 10,
    color: '#007AFF',
    fontStyle: 'italic',
    textAlign: 'right',
  },
  actionButtons: {
    marginTop: 20,
    gap: 10,
  },
  openAllButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 10,
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  openAllButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 15,
  },
  centerButton: {
    backgroundColor: '#28a745',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#28a745',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  centerButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 15,
  },
});

export default GoogleMapsEmbed;
