import React from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';

const { width } = Dimensions.get('window');

const GoogleMapsEmbed = ({ secteur, clients }) => {
  if (!secteur || !clients || clients.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.noDataText}>
          Sélectionnez un secteur pour voir la carte
        </Text>
      </View>
    );
  }

  // Construire l'URL pour Google Maps sans clé API
  const centerLat = secteur.latitude;
  const centerLng = secteur.longitude;

  // Créer l'URL Google Maps avec tous les points
  const clientsCoords = clients.map(client =>
    `${client.latitude},${client.longitude}`
  ).join('|');

  // URL Google Maps simple sans API key
  const mapsUrl = `https://www.google.com/maps?q=${centerLat},${centerLng}&z=15`;

  // URL avec marqueurs multiples
  const mapsUrlWithMarkers = `https://www.google.com/maps/dir/${centerLat},${centerLng}/${clients.map(c => `${c.latitude},${c.longitude}`).join('/')}`;

  // Pour React Native Web, utiliser une iframe avec OpenStreetMap ou Google Maps simple
  if (typeof window !== 'undefined') {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>
          📍 {secteur.nom} - {clients.length} client(s)
        </Text>

        {/* Carte intégrée */}
        <div style={styles.webMapContainer}>
          <iframe
            src={`https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d3000!2d${centerLng}!3d${centerLat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sen!2sma!4v1234567890123`}
            style={styles.webMap}
            allowFullScreen=""
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
          />
        </div>

        {/* Liste des clients avec leurs coordonnées */}
        <View style={styles.clientsList}>
          <Text style={styles.clientsListTitle}>👥 Clients dans ce secteur :</Text>
          {clients.map((client, index) => (
            <TouchableOpacity
              key={client.idclient}
              style={styles.clientItem}
              onPress={() => {
                const clientMapUrl = `https://www.google.com/maps?q=${client.latitude},${client.longitude}`;
                window.open(clientMapUrl, '_blank');
              }}
            >
              <Text style={styles.clientName}>
                📍 {client.nom} {client.prenom}
              </Text>
              <Text style={styles.clientAddress}>
                {client.adresse}
              </Text>
              <Text style={styles.clientCoords}>
                🗺️ {client.latitude?.toFixed(4)}, {client.longitude?.toFixed(4)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Bouton pour ouvrir tous les clients sur Google Maps */}
        <TouchableOpacity
          style={styles.openAllButton}
          onPress={() => window.open(mapsUrlWithMarkers, '_blank')}
        >
          <Text style={styles.openAllButtonText}>
            🌐 Voir tous les clients sur Google Maps
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Pour React Native mobile, afficher la liste des clients avec coordonnées
  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        📍 {secteur.nom} - {clients.length} client(s)
      </Text>

      <View style={styles.clientsList}>
        <Text style={styles.clientsListTitle}>👥 Clients dans ce secteur :</Text>
        {clients.map((client, index) => (
          <View key={client.idclient} style={styles.clientItem}>
            <Text style={styles.clientName}>
              📍 {client.nom} {client.prenom}
            </Text>
            <Text style={styles.clientAddress}>
              {client.adresse}
            </Text>
            <Text style={styles.clientCoords}>
              🗺️ {client.latitude?.toFixed(4)}, {client.longitude?.toFixed(4)}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
    backgroundColor: '#f8f9fa',
    padding: 10,
    borderRadius: 8,
  },
  noDataText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    padding: 20,
  },
  webMapContainer: {
    width: '100%',
    height: 300,
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 15,
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  webMap: {
    width: '100%',
    height: '100%',
    border: 'none',
    borderRadius: '8px',
  },
  clientsList: {
    marginTop: 15,
  },
  clientsListTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    paddingLeft: 5,
  },
  clientItem: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
    cursor: 'pointer',
  },
  clientName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  clientAddress: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  clientCoords: {
    fontSize: 11,
    color: '#999',
    fontFamily: 'monospace',
  },
  openAllButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 15,
  },
  openAllButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
});

export default GoogleMapsEmbed;
