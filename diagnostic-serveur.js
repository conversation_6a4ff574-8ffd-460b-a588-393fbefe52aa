// Script de diagnostic pour identifier les problèmes de serveur
console.log('🔍 DIAGNOSTIC DU SERVEUR AQUATRACK');
console.log('=====================================');

// Test 1: Vérifier Node.js
console.log('\n1. Version Node.js:');
console.log('   Version:', process.version);

// Test 2: Vérifier les modules
console.log('\n2. Test des modules requis:');
try {
  const express = require('express');
  console.log('   ✅ Express: OK');
} catch (error) {
  console.log('   ❌ Express: ERREUR -', error.message);
}

try {
  const cors = require('cors');
  console.log('   ✅ CORS: OK');
} catch (error) {
  console.log('   ❌ CORS: ERREUR -', error.message);
}

// Test 3: Créer un serveur simple
console.log('\n3. Test de création d\'un serveur simple:');
try {
  const express = require('express');
  const cors = require('cors');
  
  const app = express();
  const PORT = 4000;
  
  // Middleware
  app.use(cors());
  app.use(express.json());
  
  // Route de test
  app.get('/', (req, res) => {
    res.json({
      message: 'Serveur de diagnostic OK',
      timestamp: new Date().toISOString(),
      status: 'success'
    });
  });
  
  // Route d'authentification de test
  app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;
    
    if (email === '<EMAIL>' && password === 'Tech123') {
      res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          idtech: 1,
          nom: 'Technicien',
          prenom: 'Test',
          email: '<EMAIL>',
          role: 'Tech'
        }
      });
    } else {
      res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }
  });
  
  // Route clients de test
  app.get('/api/clients', (req, res) => {
    res.json({
      success: true,
      data: [
        {
          idclient: 1,
          nom: 'Benali',
          prenom: 'Fatima',
          adresse: '45 Avenue Hassan II',
          ville: 'Setrou',
          tel: '0612345678',
          email: '<EMAIL>',
          statut: 'Actif'
        }
      ]
    });
  });
  
  // Démarrage du serveur
  const server = app.listen(PORT, '0.0.0.0', () => {
    console.log('   ✅ Serveur créé avec succès');
    console.log(`   📡 URL: http://localhost:${PORT}`);
    console.log('   🌐 Accessible sur toutes les interfaces');
    console.log('\n4. Routes disponibles:');
    console.log('   - GET  /                - Test serveur');
    console.log('   - POST /api/auth/login  - Authentification');
    console.log('   - GET  /api/clients     - Liste clients');
    console.log('\n✅ DIAGNOSTIC TERMINÉ - SERVEUR FONCTIONNEL');
    console.log('\n🔑 Comptes de test:');
    console.log('   - <EMAIL> / Tech123');
    console.log('   - <EMAIL> / Admin123');
    console.log('\n📋 Instructions:');
    console.log('   1. Testez http://localhost:4000 dans votre navigateur');
    console.log('   2. Démarrez votre application mobile');
    console.log('   3. Utilisez les comptes de test pour vous connecter');
    console.log('\n⚠️  Appuyez sur Ctrl+C pour arrêter le serveur');
  });
  
  // Gestion des erreurs
  server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.log(`   ❌ Port ${PORT} déjà utilisé`);
      console.log('   💡 Solution: Arrêtez les autres serveurs ou changez de port');
    } else {
      console.log('   ❌ Erreur serveur:', error.message);
    }
  });
  
} catch (error) {
  console.log('   ❌ Erreur lors de la création du serveur:', error.message);
  console.log('\n🔧 Solutions possibles:');
  console.log('   1. Installez les dépendances: npm install');
  console.log('   2. Vérifiez que Node.js est installé correctement');
  console.log('   3. Redémarrez votre terminal');
}
