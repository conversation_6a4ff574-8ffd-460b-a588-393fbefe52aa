@echo off
title Test Enregistrement Consommation
color 0A

echo.
echo ========================================
echo    💾 TEST ENREGISTREMENT CONSOMMATION
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR...
echo.
start "🧪 Serveur Enregistrement" cmd /k "title SERVEUR ENREGISTREMENT && color 0B && echo ========================================== && echo    🧪 SERVEUR ENREGISTREMENT && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ API: /api/consommations && echo ✅ Enregistrement: Actif && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 5. DEMARRAGE DE L'APPLICATION...
echo.
cd mobile
start "📱 App Enregistrement" cmd /k "title APPLICATION ENREGISTREMENT && color 0D && echo ========================================== && echo    📱 APPLICATION ENREGISTREMENT && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Bouton: Enregistrer fonctionnel && echo ✅ Validation: Complète && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 6. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 7. OUVERTURE POUR TEST...
start http://localhost:19006

echo.
echo ========================================
echo    💾 ENREGISTREMENT CONSOMMATION FONCTIONNEL
echo ========================================
echo.
echo 🎯 FONCTIONNALITÉ IMPLÉMENTÉE:
echo.
echo ✅ BOUTON ENREGISTRER:
echo    - Validation complète de tous les champs
echo    - Enregistrement dans la base de données
echo    - Messages de succès/erreur détaillés
echo    - Réinitialisation automatique du formulaire
echo.
echo ✅ VALIDATIONS AJOUTÉES:
echo    - Champs obligatoires (secteur, client, contrat, période, consommation)
echo    - Période valide (mois précédent uniquement)
echo    - Consommation actuelle > consommation précédente
echo    - Valeurs numériques positives
echo.
echo ✅ GESTION D'ERREURS:
echo    - Messages d'erreur spécifiques pour chaque validation
echo    - Mode test si serveur indisponible
echo    - Logs détaillés pour debug
echo.
echo 📊 DONNÉES ENREGISTRÉES:
echo.
echo 📋 Structure de la consommation:
echo    - 📅 Période (YYYY-MM)
echo    - 📊 Consommation précédente (m³)
echo    - ⚡ Consommation actuelle (m³)
echo    - 📅 Nombre de jours
echo    - 👤 ID Client
echo    - 📄 ID Contrat
echo    - 📍 ID Secteur
echo    - 👨‍🔧 ID Technicien
echo    - 📊 ID Tranche
echo    - ✅ Statut: "Enregistrée"
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 TESTEZ L'ENREGISTREMENT COMPLET:
echo.
echo    a) 📍 ÉTAPE 1 - Remplissage du formulaire:
echo       - Sélectionnez "Centre-Ville" (secteur)
echo       - Sélectionnez "Benali Fatima" (client)
echo       - Le contrat se remplit automatiquement
echo       - Saisissez "2024-12" (période)
echo       - Consommation précédente se remplit: "45"
echo       - Nombre de jours se calcule: "31"
echo       - Saisissez "50" (consommation actuelle)
echo.
echo    b) 💾 ÉTAPE 2 - Test enregistrement réussi:
echo       - Cliquez sur "💾 Enregistrer la Consommation"
echo       - Attendez le traitement (indicateur de chargement)
echo       - Popup de succès doit s'afficher:
echo         "✅ Succès
echo          Consommation enregistrée avec succès!
echo          📅 Période: 2024-12
echo          👤 Client: Benali Fatima
echo          📊 Consommation: 50 m³
echo          📅 Jours: 31"
echo       - Cliquez "OK"
echo       - Le formulaire se réinitialise automatiquement
echo.
echo    c) ❌ ÉTAPE 3 - Test validations d'erreur:
echo       - Essayez de cliquer "Enregistrer" sans remplir → "Champ manquant"
echo       - Remplissez secteur et client
echo       - Saisissez "2025-01" (période future) → "Période Invalide"
echo       - Saisissez "2024-12" (période valide)
echo       - Saisissez "40" (consommation < précédente) → "Erreur de validation"
echo       - Saisissez "50" (consommation valide)
echo       - Cliquez "Enregistrer" → Succès
echo.
echo    d) 🧪 ÉTAPE 4 - Test mode serveur indisponible:
echo       - Si le serveur est arrêté, un message "Mode Test" s'affiche
echo       - Simulation d'enregistrement avec validation des données
echo       - Formulaire se réinitialise quand même
echo.
echo 📊 VALIDATIONS TESTÉES:
echo.
echo ✅ CHAMPS OBLIGATOIRES:
echo    - Secteur: "Veuillez sélectionner un secteur"
echo    - Client: "Veuillez sélectionner un client"
echo    - Contrat: "Veuillez sélectionner un contrat"
echo    - Période: "Veuillez saisir une période"
echo    - Consommation actuelle: "Veuillez saisir la consommation actuelle"
echo.
echo ✅ VALIDATIONS MÉTIER:
echo    - Période valide: Mois précédent uniquement
echo    - Consommation positive: Nombre positif requis
echo    - Consommation croissante: Actuelle > Précédente
echo.
echo ✅ GESTION D'ERREURS:
echo    - Erreur réseau: Mode test activé
echo    - Erreur API: Message d'erreur affiché
echo    - Validation échouée: Message spécifique
echo.
echo 🎨 MESSAGES D'INTERFACE:
echo.
echo ✅ SUCCÈS:
echo    Titre: "✅ Succès"
echo    Message: Détails de la consommation enregistrée
echo    Action: Réinitialisation automatique du formulaire
echo.
echo ❌ ERREURS:
echo    - "Champ manquant" → Champ spécifique manquant
echo    - "Période Invalide" → Période future ou invalide
echo    - "Erreur de validation" → Consommation invalide
echo    - "Erreur de connexion" → Serveur indisponible
echo.
echo ⚠️ MODE TEST:
echo    Titre: "⚠️ Mode Test"
echo    Message: Simulation d'enregistrement avec données validées
echo    Action: Réinitialisation du formulaire
echo.
echo 🔧 FONCTIONNALITÉS TECHNIQUES:
echo.
echo ✅ API ENDPOINT:
echo    - URL: http://localhost:4002/api/consommations
echo    - Méthode: POST
echo    - Content-Type: application/json
echo.
echo ✅ DONNÉES ENVOYÉES:
echo    {
echo      "periode": "2024-12",
echo      "consommationPre": 45,
echo      "consommationActuelle": 50,
echo      "jours": 31,
echo      "idClient": 1,
echo      "idContract": 1,
echo      "idSecteur": 1,
echo      "idTech": 1,
echo      "idTranch": 1,
echo      "status": "Enregistrée"
echo    }
echo.
echo ✅ RÉINITIALISATION:
echo    - Tous les champs vidés
echo    - Listes réinitialisées (contrats, clients)
echo    - Erreurs effacées
echo    - État propre pour nouvelle saisie
echo.
echo 🔍 DEBUG ET LOGS:
echo.
echo Pour voir les logs détaillés:
echo 1. Ouvrez la console du navigateur (F12)
echo 2. Onglet "Console"
echo 3. Recherchez les messages:
echo    - "💾 DÉBUT ENREGISTREMENT CONSOMMATION"
echo    - "🔍 VALIDATION DES CHAMPS OBLIGATOIRES"
echo    - "📅 VALIDATION DE LA PÉRIODE"
echo    - "⚡ VALIDATION DES CONSOMMATIONS"
echo    - "📦 PRÉPARATION DES DONNÉES"
echo    - "📡 ENVOI À L'API"
echo    - "✅ ENREGISTREMENT RÉUSSI" ou "❌ ERREUR"
echo    - "💾 FIN ENREGISTREMENT CONSOMMATION"
echo.
echo 🌐 URLS DE TEST:
echo    - Application: http://localhost:19006
echo    - API: http://localhost:4002/api/consommations
echo    - Console logs: F12 → Console
echo.
echo ✅ ENREGISTREMENT CONSOMMATION IMPLÉMENTÉ !
echo    Le bouton "Enregistrer" sauvegarde maintenant
echo    réellement la consommation dans la base de données
echo    avec toutes les validations nécessaires.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
