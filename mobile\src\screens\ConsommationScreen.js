import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';

const ConsommationScreen = ({ navigation, route }) => {
  const { client, user } = route.params;
  const [loading, setLoading] = useState(false);
  const [contract, setContract] = useState(null);
  const [lastConsommation, setLastConsommation] = useState(null);
  const [secteurs, setSecteurs] = useState([]);
  const [selectedSecteur, setSelectedSecteur] = useState('');
  const [clientsBySecteur, setClientsBySecteur] = useState([]);
  const [showMap, setShowMap] = useState(false);
  const [formData, setFormData] = useState({
    periode: '',
    consommationPre: '',
    consommationActuelle: '',
    jours: '',
    idtranch: 1,
    status: 'En cours'
  });

  const API_BASE_URL = 'http://localhost:4000'; // Utiliser localhost pour les tests

  useEffect(() => {
    fetchClientContract();
    fetchLastConsommation();
    generateCurrentPeriod();
    fetchSecteurs();
  }, []);

  const generateCurrentPeriod = () => {
    const now = new Date();
    const periode = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    setFormData(prev => ({ ...prev, periode }));
  };

  const fetchClientContract = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/client-contract/${client.idclient}`);
      const data = await response.json();
      if (data.success && data.contract) {
        setContract(data.contract);
      }
    } catch (error) {
      console.error('Erreur récupération contrat:', error);
    }
  };

  const fetchLastConsommation = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/clients/${client.idclient}/last-consommation`);
      const data = await response.json();
      if (data.success && data.data) {
        setLastConsommation(data.data);
        setFormData(prev => ({
          ...prev,
          consommationPre: data.data.consommationactuelle.toString()
        }));
      }
    } catch (error) {
      console.error('Erreur récupération dernière consommation:', error);
    }
  };

  const fetchSecteurs = async () => {
    try {
      console.log('🔍 Récupération des secteurs...');
      const response = await fetch(`${API_BASE_URL}/api/secteurs`);
      const data = await response.json();

      if (data.success) {
        setSecteurs(data.data);
        console.log(`✅ ${data.data.length} secteurs récupérés`);
      }
    } catch (error) {
      console.error('Erreur récupération secteurs:', error);
    }
  };

  const fetchClientsBySecteur = async (secteurId) => {
    if (!secteurId) return;

    try {
      console.log(`🔍 Récupération des clients du secteur ${secteurId}...`);
      const response = await fetch(`${API_BASE_URL}/api/secteurs/${secteurId}/clients`);
      const data = await response.json();

      if (data.success) {
        setClientsBySecteur(data.data);
        console.log(`✅ ${data.data.length} clients récupérés pour le secteur ${secteurId}`);

        if (data.data.length > 0) {
          setShowMap(true);
        }
      }
    } catch (error) {
      console.error('Erreur récupération clients:', error);
    }
  };

  const handleSecteurChange = (secteurId) => {
    setSelectedSecteur(secteurId);
    setClientsBySecteur([]);
    setShowMap(false);

    if (secteurId) {
      fetchClientsBySecteur(secteurId);
    }
  };

  const openGoogleMaps = () => {
    if (clientsBySecteur.length === 0) {
      Alert.alert('Information', 'Sélectionnez un secteur avec des clients pour voir la carte');
      return;
    }

    const secteurInfo = secteurs.find(s => s.ids.toString() === selectedSecteur);
    if (!secteurInfo) return;

    // Construire l'URL Google Maps avec les marqueurs des clients
    const baseUrl = 'https://www.google.com/maps/dir/';

    // Centre de la carte sur le secteur
    const centerLat = secteurInfo.latitude;
    const centerLng = secteurInfo.longitude;

    // Créer les marqueurs pour chaque client
    const markers = clientsBySecteur.map(client =>
      `${client.latitude},${client.longitude}`
    ).join('/');

    // URL complète avec tous les points
    const mapsUrl = `${baseUrl}${centerLat},${centerLng}/${markers}`;

    // Pour React Native Web, ouvrir dans une nouvelle fenêtre
    if (typeof window !== 'undefined') {
      window.open(mapsUrl, '_blank');
    } else {
      // Pour React Native mobile, utiliser Linking
      import('react-native').then(({ Linking }) => {
        Linking.openURL(mapsUrl);
      });
    }
  };

  const calculateJours = () => {
    if (lastConsommation && formData.periode) {
      try {
        const currentPeriod = new Date(formData.periode + '-01');
        const lastPeriod = new Date(lastConsommation.periode + '-01');
        const diffTime = Math.abs(currentPeriod - lastPeriod);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        setFormData(prev => ({ ...prev, jours: diffDays.toString() }));
      } catch (error) {
        console.error('Erreur calcul jours:', error);
      }
    }
  };

  useEffect(() => {
    calculateJours();
  }, [formData.periode, lastConsommation]);

  const validateForm = () => {
    if (!formData.periode) {
      Alert.alert('Erreur', 'Veuillez saisir la période');
      return false;
    }
    if (!formData.consommationActuelle) {
      Alert.alert('Erreur', 'Veuillez saisir la consommation actuelle');
      return false;
    }
    if (parseInt(formData.consommationActuelle) <= parseInt(formData.consommationPre || 0)) {
      Alert.alert('Erreur', 'La consommation actuelle doit être supérieure à la consommation précédente');
      return false;
    }
    if (!contract) {
      Alert.alert('Erreur', 'Aucun contrat trouvé pour ce client');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const consommationData = {
        consommationpre: parseInt(formData.consommationPre || 0),
        consommationactuelle: parseInt(formData.consommationActuelle),
        idcont: contract.idcontract,
        idtech: user?.idtech || 1,
        idtranch: formData.idtranch,
        jours: parseInt(formData.jours || 30),
        periode: formData.periode,
        status: formData.status
      };

      const response = await fetch(`${API_BASE_URL}/api/consommations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(consommationData),
      });

      const result = await response.json();

      if (result.success) {
        Alert.alert(
          'Succès',
          'Consommation enregistrée avec succès !',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Erreur', result.message || 'Erreur lors de l\'enregistrement');
      }
    } catch (error) {
      console.error('Erreur soumission:', error);
      Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* En-tête Client */}
          <View style={styles.clientHeader}>
            <Text style={styles.clientName}>{client.nom} {client.prenom}</Text>
            <Text style={styles.clientDetail}>📍 {client.adresse}</Text>
            <Text style={styles.clientDetail}>🏙️ {client.ville}</Text>
            {contract && (
              <Text style={styles.clientDetail}>🔧 Compteur: {contract.marquecompteur}</Text>
            )}
          </View>

          {/* Sélection de Secteur */}
          <View style={styles.form}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>🗺️ Secteur *</Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={selectedSecteur}
                  onValueChange={handleSecteurChange}
                  style={styles.picker}
                >
                  <Picker.Item label="-- Sélectionnez un secteur --" value="" />
                  {secteurs.map((secteur) => (
                    <Picker.Item
                      key={secteur.ids}
                      label={secteur.nom}
                      value={secteur.ids.toString()}
                    />
                  ))}
                </Picker>
              </View>
            </View>

            {/* Carte Google Maps */}
            {showMap && clientsBySecteur.length > 0 && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>📍 Clients du Secteur</Text>
                <View style={styles.mapContainer}>
                  <View style={styles.mapPlaceholder}>
                    <Text style={styles.mapPlaceholderText}>
                      🗺️ {clientsBySecteur.length} client(s) dans ce secteur
                    </Text>
                    <TouchableOpacity style={styles.openMapsButton} onPress={openGoogleMaps}>
                      <Text style={styles.openMapsButtonText}>
                        🌐 Voir sur Google Maps
                      </Text>
                    </TouchableOpacity>
                  </View>

                  {/* Liste des clients du secteur */}
                  <ScrollView style={styles.clientsList} nestedScrollEnabled={true}>
                    {clientsBySecteur.map((clientSecteur) => (
                      <View key={clientSecteur.idclient} style={styles.clientCard}>
                        <Text style={styles.clientCardName}>
                          {clientSecteur.nom} {clientSecteur.prenom}
                        </Text>
                        <Text style={styles.clientCardAddress}>
                          📍 {clientSecteur.adresse}
                        </Text>
                      </View>
                    ))}
                  </ScrollView>
                </View>
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={styles.label}>Période (YYYY-MM) *</Text>
              <TextInput
                style={styles.input}
                value={formData.periode}
                onChangeText={(text) => setFormData(prev => ({ ...prev, periode: text }))}
                placeholder="2024-01"
                maxLength={7}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Consommation Précédente (m³)</Text>
              <TextInput
                style={[styles.input, styles.readOnlyInput]}
                value={formData.consommationPre}
                editable={false}
                placeholder="Automatique"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Consommation Actuelle (m³) *</Text>
              <TextInput
                style={styles.input}
                value={formData.consommationActuelle}
                onChangeText={(text) => setFormData(prev => ({ ...prev, consommationActuelle: text }))}
                placeholder="Saisir la consommation actuelle"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Nombre de jours</Text>
              <TextInput
                style={[styles.input, styles.readOnlyInput]}
                value={formData.jours}
                editable={false}
                placeholder="Calculé automatiquement"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Statut</Text>
              <TextInput
                style={styles.input}
                value={formData.status}
                onChangeText={(text) => setFormData(prev => ({ ...prev, status: text }))}
                placeholder="En cours"
              />
            </View>
          </View>

          {/* Boutons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.cancelButtonText}>Annuler</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.submitButton, loading && styles.disabledButton]}
              onPress={handleSubmit}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.submitButtonText}>Enregistrer</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  clientHeader: {
    backgroundColor: '#007AFF',
    padding: 20,
    alignItems: 'center',
  },
  clientName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  clientDetail: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginBottom: 2,
  },
  form: {
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  readOnlyInput: {
    backgroundColor: '#f8f9fa',
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 0,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: 'bold',
    fontSize: 16,
  },
  submitButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginLeft: 10,
  },
  submitButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  // Nouveaux styles pour la sélection de secteur et la carte
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#fff',
  },
  picker: {
    height: 50,
  },
  mapContainer: {
    marginTop: 10,
  },
  mapPlaceholder: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
    marginBottom: 15,
  },
  mapPlaceholderText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 15,
    textAlign: 'center',
  },
  openMapsButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  openMapsButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  clientsList: {
    maxHeight: 200,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 10,
  },
  clientCard: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#007AFF',
  },
  clientCardName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  clientCardAddress: {
    fontSize: 12,
    color: '#666',
  },
});

export default ConsommationScreen;
