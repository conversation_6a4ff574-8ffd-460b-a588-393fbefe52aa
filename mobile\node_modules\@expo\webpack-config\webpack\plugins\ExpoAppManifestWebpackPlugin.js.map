{"version": 3, "file": "ExpoAppManifestWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ExpoAppManifestWebpackPlugin.ts"], "names": [], "mappings": ";;;;;AAEA,0FAA0F;AAS1F,MAAqB,4BAA6B,SAAQ,kCAAwB;IAChF,YAAY,UAAkC,EAAE,MAAkB;QAChE,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAE1B,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;IACpB,CAAC;CACF;AAND,+CAMC", "sourcesContent": ["import type { ExpoConfig } from 'expo/config';\n\nimport PwaManifestWebpackPlugin, { PwaManifestOptions } from './PwaManifestWebpackPlugin';\n\nexport type ExpoPwaManifestOptions = PwaManifestOptions & {\n  /**\n   * The path to a template manifest.json.\n   */\n  template: string;\n};\n\nexport default class ExpoAppManifestWebpackPlugin extends PwaManifestWebpackPlugin {\n  constructor(pwaOptions: ExpoPwaManifestOptions, config: ExpoConfig) {\n    super(pwaOptions, config);\n\n    this.rel = 'expo';\n  }\n}\n"]}