@echo off
title Demarrage Backend AquaTrack - URGENT
color 0C

echo.
echo ========================================
echo    🚨 DEMARRAGE BACKEND URGENT 🚨
echo ========================================
echo.

echo 🛑 1. ARRET TOTAL DES PROCESSUS...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
taskkill /f /im expo.exe 2>nul
echo ✅ Tous les processus Node.js arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 5 /nobreak >nul

echo.
echo 🔍 3. VERIFICATION DES FICHIERS SERVEUR...
if exist "simple-server.js" (
    echo ✅ simple-server.js trouve
    set SERVER_FILE=simple-server.js
) else if exist "serveur-test.js" (
    echo ✅ serveur-test.js trouve
    set SERVER_FILE=serveur-test.js
) else if exist "server\simple-working-server.js" (
    echo ✅ server\simple-working-server.js trouve
    set SERVER_FILE=server\simple-working-server.js
) else if exist "backend\server-simple.js" (
    echo ✅ backend\server-simple.js trouve
    set SERVER_FILE=backend\server-simple.js
) else (
    echo ❌ Aucun serveur trouve !
    echo Creation d'un serveur d'urgence...
    goto CREATE_EMERGENCY_SERVER
)

echo.
echo 🚀 4. DEMARRAGE DU SERVEUR: %SERVER_FILE%
echo.
start "🚀 BACKEND AQUATRACK" cmd /k "title BACKEND AQUATRACK ACTIF && color 0A && echo ========================================== && echo    🚀 SERVEUR BACKEND AQUATRACK && echo ========================================== && echo. && echo ✅ Fichier: %SERVER_FILE% && echo ✅ Port: 4000 && echo ✅ Status: DEMARRAGE... && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo    - <EMAIL> / Admin123 && echo. && echo 📡 Demarrage en cours... && echo. && node %SERVER_FILE%"

goto WAIT_AND_TEST

:CREATE_EMERGENCY_SERVER
echo.
echo 🆘 CREATION D'UN SERVEUR D'URGENCE...
(
echo const express = require('express'^);
echo const cors = require('cors'^);
echo const app = express(^);
echo const PORT = 4000;
echo app.use(cors({origin: '*'}^)^);
echo app.use(express.json(^)^);
echo app.get('/', (req, res^) =^> res.json({message: 'Serveur AquaTrack d urgence', status: 'OK', port: PORT}^)^);
echo app.post('/api/auth/login', (req, res^) =^> {
echo   const {email, password} = req.body;
echo   if (email === '<EMAIL>' ^&^& password === 'Tech123'^) {
echo     res.json({success: true, message: 'Connexion reussie', user: {idtech: 1, nom: 'Technicien', prenom: 'Test', email: '<EMAIL>', role: 'Tech'}, token: 'test-token-123'}^);
echo   } else {
echo     res.status(401^).json({success: false, message: 'Email ou mot de passe incorrect'}^);
echo   }
echo }^);
echo app.get('/api/clients', (req, res^) =^> res.json({success: true, data: [{idclient: 1, nom: 'Test', prenom: 'Client', email: '<EMAIL>'}]}^)^);
echo app.listen(PORT, '0.0.0.0', (^) =^> console.log('Serveur d urgence demarre sur port', PORT^)^);
) > serveur-urgence.js

echo ✅ Serveur d'urgence cree !
set SERVER_FILE=serveur-urgence.js

echo.
echo 🚀 DEMARRAGE DU SERVEUR D'URGENCE...
start "🆘 SERVEUR URGENCE" cmd /k "title SERVEUR URGENCE AQUATRACK && color 0E && echo ========================================== && echo    🆘 SERVEUR D URGENCE AQUATRACK && echo ========================================== && echo. && echo ✅ Fichier: serveur-urgence.js && echo ✅ Port: 4000 && echo ✅ Status: DEMARRAGE... && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo. && echo 📡 Demarrage en cours... && echo. && node serveur-urgence.js"

:WAIT_AND_TEST
echo.
echo ⏳ 5. ATTENTE DU DEMARRAGE (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 6. TEST DE CONNEXION...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000' -TimeoutSec 10; Write-Host '✅ SERVEUR FONCTIONNE:' $response.message } catch { Write-Host '❌ SERVEUR NON ACCESSIBLE' }"

echo.
echo 🌐 7. OUVERTURE DU NAVIGATEUR...
start http://localhost:4000

echo.
echo ========================================
echo    ✅ BACKEND DEMARRE !
echo ========================================
echo.
echo 📡 URL: http://localhost:4000
echo.
echo 🔑 Comptes de test:
echo    - <EMAIL> / Tech123
echo    - <EMAIL> / Admin123
echo.
echo 📋 INSTRUCTIONS IMPORTANTES:
echo    1. Une fenetre avec le serveur s'est ouverte
echo    2. Un navigateur s'est ouvert sur http://localhost:4000
echo    3. Vous devriez voir un message JSON
echo    4. GARDEZ LA FENETRE DU SERVEUR OUVERTE !
echo    5. Retournez a votre application mobile
echo    6. Actualisez la page (F5)
echo    7. Essayez de vous connecter
echo.
echo ⚠️  IMPORTANT: NE FERMEZ PAS LA FENETRE DU SERVEUR !
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
