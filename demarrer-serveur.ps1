# Script PowerShell pour démarrer le serveur AquaTrack
Write-Host "========================================" -ForegroundColor Red
Write-Host "    DEMARRAGE SERVEUR AQUATRACK" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red
Write-Host ""

# Arrêter tous les processus Node.js
Write-Host "1. Arrêt des processus existants..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 3

# Vérifier si le port 4000 est libre
Write-Host "2. Vérification du port 4000..." -ForegroundColor Yellow
$portTest = Test-NetConnection -ComputerName "localhost" -Port 4000 -InformationLevel Quiet -WarningAction SilentlyContinue
if ($portTest) {
    Write-Host "   Port 4000 encore occupé, nouvelle tentative..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

# Démarrer le serveur d'urgence
Write-Host "3. Démarrage du serveur d'urgence..." -ForegroundColor Yellow
$currentPath = Get-Location
Start-Process -FilePath "cmd" -ArgumentList "/k", "title SERVEUR AQUATRACK && color 0A && echo SERVEUR AQUATRACK DEMARRE && node serveur-urgence.js" -WorkingDirectory $currentPath

# Attendre que le serveur démarre
Write-Host "4. Attente du démarrage (10 secondes)..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Tester la connexion
Write-Host "5. Test de connexion..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:4000" -TimeoutSec 5
    Write-Host "   ✅ SERVEUR FONCTIONNE: $($response.message)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ SERVEUR NON ACCESSIBLE" -ForegroundColor Red
    Write-Host "   Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Ouvrir le navigateur
Write-Host "6. Ouverture du navigateur..." -ForegroundColor Yellow
Start-Process "http://localhost:4000"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    ✅ SERVEUR DEMARRE !" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "📡 URL: http://localhost:4000" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔑 Comptes de test:" -ForegroundColor Yellow
Write-Host "   - <EMAIL> / Tech123" -ForegroundColor White
Write-Host "   - <EMAIL> / Admin123" -ForegroundColor White
Write-Host ""
Write-Host "📋 Instructions:" -ForegroundColor Yellow
Write-Host "   1. Une fenêtre avec le serveur s'est ouverte" -ForegroundColor White
Write-Host "   2. Un navigateur s'est ouvert sur http://localhost:4000" -ForegroundColor White
Write-Host "   3. Retournez à votre application mobile" -ForegroundColor White
Write-Host "   4. Actualisez la page (F5)" -ForegroundColor White
Write-Host "   5. Essayez de vous connecter" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  IMPORTANT: Gardez la fenêtre du serveur ouverte !" -ForegroundColor Red
Write-Host ""
Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
