<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Codes QR de Test - AquaTrack</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #8b5cf6, #3b82f6);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .qr-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .qr-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .qr-code {
            margin: 20px 0;
        }
        .client-info {
            background: #f8fafc;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .client-name {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .client-details {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .instructions h3 {
            color: #8b5cf6;
            margin-top: 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f3f4f6;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Codes QR de Test - AquaTrack</h1>
        <p>Scannez ces codes QR avec votre application pour tester la fonctionnalité</p>
    </div>

    <div class="qr-container">
        <div class="qr-card">
            <h3>Client Test 1</h3>
            <div class="qr-code" id="qr1"></div>
            <div class="client-info">
                <div class="client-name">Jean Dupont</div>
                <div class="client-details">
                    📍 123 Rue de la Paix, Tunis<br>
                    📞 71234567<br>
                    📧 <EMAIL><br>
                    🏢 Secteur: Centre Ville
                </div>
            </div>
        </div>

        <div class="qr-card">
            <h3>Client Test 2</h3>
            <div class="qr-code" id="qr2"></div>
            <div class="client-info">
                <div class="client-name">Marie Martin</div>
                <div class="client-details">
                    📍 456 Avenue Habib Bourguiba, Sfax<br>
                    📞 74567890<br>
                    📧 <EMAIL><br>
                    🏢 Secteur: Sfax Nord
                </div>
            </div>
        </div>
    </div>

    <div class="instructions">
        <h3>📱 Instructions de Test</h3>
        <div class="step">
            <strong>1.</strong> Démarrez votre application React (npm start)
        </div>
        <div class="step">
            <strong>2.</strong> Démarrez le serveur backend (cd server && node server.js)
        </div>
        <div class="step">
            <strong>3.</strong> Naviguez vers la page Scanner QR
        </div>
        <div class="step">
            <strong>4.</strong> Cliquez sur "Démarrer le scan"
        </div>
        <div class="step">
            <strong>5.</strong> Autorisez l'accès à la caméra
        </div>
        <div class="step">
            <strong>6.</strong> Pointez votre caméra vers l'un des codes QR ci-dessus
        </div>
        <div class="step">
            <strong>7.</strong> Les informations du client s'afficheront automatiquement !
        </div>
    </div>

    <script>
        // Générer les codes QR
        QRCode.toCanvas(document.getElementById('qr1'), 'QR001', {
            width: 200,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        });

        QRCode.toCanvas(document.getElementById('qr2'), 'QR002', {
            width: 200,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        });

        console.log('✅ Codes QR générés:');
        console.log('- QR001 → Jean Dupont');
        console.log('- QR002 → Marie Martin');
    </script>
</body>
</html>
