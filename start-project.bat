@echo off
echo 🚀 Démarrage du projet AquaTrack...
echo.

echo 📡 Démarrage du serveur backend...
cd server
start "Backend Server" cmd /k "node server.js"
cd ..

echo ⏳ Attente de 3 secondes pour le serveur...
timeout /t 3 /nobreak > nul

echo 📱 Démarrage de l'application React...
start "React App" cmd /k "npm start"

echo.
echo ✅ Projet démarré !
echo 📡 Backend: http://localhost:4000
echo 📱 Frontend: http://localhost:3000
echo.
echo Appuyez sur une touche pour fermer cette fenêtre...
pause > nul
