// SERVEUR D'URGENCE AQUATRACK
console.log('🆘 SERVEUR D\'URGENCE AQUATRACK - DEMARRAGE...');

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors({
  origin: '*',
  credentials: true
}));

app.use(express.json());

console.log('✅ Middleware configuré');

// Route principale
app.get('/', (req, res) => {
  console.log('📥 Requête reçue sur /');
  res.json({
    message: 'Serveur AquaTrack d\'urgence ACTIF',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString(),
    version: 'URGENCE-1.0.0'
  });
});

// Route d'authentification
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Tentative de connexion:', req.body);
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'Tech123') {
    console.log('✅ CONNEXION RÉ<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      },
      token: 'test-token-123'
    });
  } else if (email === '<EMAIL>' && password === 'Admin123') {
    console.log('✅ CONNEXION RÉ<NAME_EMAIL>');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 2,
        nom: 'Admin',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Admin'
      },
      token: 'test-token-456'
    });
  } else {
    console.log('❌ ÉCHEC DE CONNEXION pour:', email);
    res.status(401).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// Route clients
app.get('/api/clients', (req, res) => {
  console.log('📋 Requête clients reçue');

  // Données de test réalistes pour la table client
  const testClients = [
    {
      idclient: 1,
      nom: 'Benali',
      prenom: 'Fatima',
      adresse: '45 Avenue Hassan II, près de l\'école Omar Ibn Al Khattab',
      ville: 'Setrou',
      tel: '0612345678',
      email: '<EMAIL>',
      statut: 'Actif'
    },
    {
      idclient: 2,
      nom: 'Alami',
      prenom: 'Mohammed',
      adresse: '12 Rue des Oliviers, Quartier Administratif',
      ville: 'Setrou',
      tel: '0623456789',
      email: '<EMAIL>',
      statut: 'Actif'
    },
    {
      idclient: 3,
      nom: 'Tazi',
      prenom: 'Aicha',
      adresse: '78 Boulevard Zerktouni, près du marché central',
      ville: 'Setrou',
      tel: '0634567890',
      email: '<EMAIL>',
      statut: 'Actif'
    },
    {
      idclient: 4,
      nom: 'Benjelloun',
      prenom: 'Youssef',
      adresse: '23 Rue Ibn Sina, Résidence Al Andalous',
      ville: 'Setrou',
      tel: '0645678901',
      email: '<EMAIL>',
      statut: 'Actif'
    },
    {
      idclient: 5,
      nom: 'Lahlou',
      prenom: 'Khadija',
      adresse: '56 Avenue Mohammed V, Immeuble Nour',
      ville: 'Setrou',
      tel: '0656789012',
      email: '<EMAIL>',
      statut: 'Actif'
    },
    {
      idclient: 6,
      nom: 'Fassi',
      prenom: 'Omar',
      adresse: '89 Rue Al Massira, Villa 15',
      ville: 'Setrou',
      tel: '0667890123',
      email: '<EMAIL>',
      statut: 'Actif'
    }
  ];

  console.log(`✅ ${testClients.length} clients de test retournés`);

  res.json({
    success: true,
    data: testClients,
    clients: testClients, // Pour compatibilité avec différents formats
    total: testClients.length,
    count: testClients.length,
    message: 'Liste des clients récupérée avec succès'
  });
});

// Route secteurs
app.get('/api/secteurs', (req, res) => {
  console.log('🗺️ Requête secteurs reçue');
  res.json({
    success: true,
    data: [
      {
        ids: 1,
        nom: 'Centre Ville',
        latitude: 33.5731,
        longitude: -7.5898
      }
    ],
    count: 1
  });
});

// Démarrage du serveur
console.log('🚀 Tentative de démarrage du serveur d\'urgence...');

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log('');
  console.log('🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘');
  console.log('🆘                                      🆘');
  console.log('🆘    SERVEUR D\'URGENCE AQUATRACK      🆘');
  console.log('🆘           DÉMARRÉ AVEC SUCCÈS       🆘');
  console.log('🆘                                      🆘');
  console.log('🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘');
  console.log('');
  console.log('📡 URL: http://localhost:' + PORT);
  console.log('🌐 Accessible depuis toutes les interfaces');
  console.log('');
  console.log('🔧 Routes disponibles:');
  console.log('   - GET  /                - Test serveur');
  console.log('   - POST /api/auth/login  - Authentification');
  console.log('   - GET  /api/clients     - Liste clients');
  console.log('   - GET  /api/secteurs    - Liste secteurs');
  console.log('');
  console.log('🔑 Comptes de test:');
  console.log('   - <EMAIL> / Tech123');
  console.log('   - <EMAIL> / Admin123');
  console.log('');
  console.log('✅ SERVEUR PRÊT À RECEVOIR DES CONNEXIONS');
  console.log('⚠️  Appuyez sur Ctrl+C pour arrêter');
  console.log('');
  console.log('🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘🆘');
});

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.log('');
    console.log('❌ ERREUR: Port 4000 déjà utilisé');
    console.log('💡 Solutions:');
    console.log('   1. Arrêtez les autres serveurs: taskkill /f /im node.exe');
    console.log('   2. Ou redémarrez votre PC');
    console.log('   3. Ou changez de port');
  } else {
    console.log('❌ Erreur serveur:', error.message);
  }
});

console.log('📝 Serveur d\'urgence configuré, en attente de démarrage...');
