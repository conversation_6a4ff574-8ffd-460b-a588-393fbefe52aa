{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport AnimatedInterpolation from \"./AnimatedInterpolation\";\nimport AnimatedNode from \"./AnimatedNode\";\nimport AnimatedValue from \"./AnimatedValue\";\nimport AnimatedWithChildren from \"./AnimatedWithChildren\";\nvar AnimatedDivision = function (_AnimatedWithChildren) {\n  function AnimatedDivision(a, b) {\n    var _this;\n    _classCallCheck(this, AnimatedDivision);\n    _this = _callSuper(this, AnimatedDivision);\n    _this._warnedAboutDivideByZero = false;\n    if (b === 0 || b instanceof AnimatedNode && b.__getValue() === 0) {\n      console.error('Detected potential division by zero in AnimatedDivision');\n    }\n    _this._a = typeof a === 'number' ? new AnimatedValue(a) : a;\n    _this._b = typeof b === 'number' ? new AnimatedValue(b) : b;\n    return _this;\n  }\n  _inherits(AnimatedDivision, _AnimatedWithChildren);\n  return _createClass(AnimatedDivision, [{\n    key: \"__makeNative\",\n    value: function __makeNative(platformConfig) {\n      this._a.__makeNative(platformConfig);\n      this._b.__makeNative(platformConfig);\n      _superPropGet(AnimatedDivision, \"__makeNative\", this, 3)([platformConfig]);\n    }\n  }, {\n    key: \"__getValue\",\n    value: function __getValue() {\n      var a = this._a.__getValue();\n      var b = this._b.__getValue();\n      if (b === 0) {\n        if (!this._warnedAboutDivideByZero) {\n          console.error('Detected division by zero in AnimatedDivision');\n          this._warnedAboutDivideByZero = true;\n        }\n        return 0;\n      }\n      this._warnedAboutDivideByZero = false;\n      return a / b;\n    }\n  }, {\n    key: \"interpolate\",\n    value: function interpolate(config) {\n      return new AnimatedInterpolation(this, config);\n    }\n  }, {\n    key: \"__attach\",\n    value: function __attach() {\n      this._a.__addChild(this);\n      this._b.__addChild(this);\n    }\n  }, {\n    key: \"__detach\",\n    value: function __detach() {\n      this._a.__removeChild(this);\n      this._b.__removeChild(this);\n      _superPropGet(AnimatedDivision, \"__detach\", this, 3)([]);\n    }\n  }, {\n    key: \"__getNativeConfig\",\n    value: function __getNativeConfig() {\n      return {\n        type: 'division',\n        input: [this._a.__getNativeTag(), this._b.__getNativeTag()]\n      };\n    }\n  }]);\n}(AnimatedWithChildren);\nexport default AnimatedDivision;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedInterpolation", "AnimatedNode", "AnimatedValue", "AnimatedWithChildren", "AnimatedDivision", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "a", "b", "_this", "_warnedAboutDivideByZero", "__getValue", "console", "error", "_a", "_b", "key", "value", "__makeNative", "platformConfig", "interpolate", "config", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedDivision.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport AnimatedInterpolation from './AnimatedInterpolation';\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedValue from './AnimatedValue';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nclass AnimatedDivision extends AnimatedWithChildren {\n  constructor(a, b) {\n    super();\n    this._warnedAboutDivideByZero = false;\n    if (b === 0 || b instanceof AnimatedNode && b.__getValue() === 0) {\n      console.error('Detected potential division by zero in AnimatedDivision');\n    }\n    this._a = typeof a === 'number' ? new AnimatedValue(a) : a;\n    this._b = typeof b === 'number' ? new AnimatedValue(b) : b;\n  }\n  __makeNative(platformConfig) {\n    this._a.__makeNative(platformConfig);\n    this._b.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n  __getValue() {\n    var a = this._a.__getValue();\n    var b = this._b.__getValue();\n    if (b === 0) {\n      // Prevent spamming the console/LogBox\n      if (!this._warnedAboutDivideByZero) {\n        console.error('Detected division by zero in AnimatedDivision');\n        this._warnedAboutDivideByZero = true;\n      }\n      // Passing infinity/NaN to Fabric will cause a native crash\n      return 0;\n    }\n    this._warnedAboutDivideByZero = false;\n    return a / b;\n  }\n  interpolate(config) {\n    return new AnimatedInterpolation(this, config);\n  }\n  __attach() {\n    this._a.__addChild(this);\n    this._b.__addChild(this);\n  }\n  __detach() {\n    this._a.__removeChild(this);\n    this._b.__removeChild(this);\n    super.__detach();\n  }\n  __getNativeConfig() {\n    return {\n      type: 'division',\n      input: [this._a.__getNativeTag(), this._b.__getNativeTag()]\n    };\n  }\n}\nexport default AnimatedDivision;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,qBAAqB;AAC5B,OAAOC,YAAY;AACnB,OAAOC,aAAa;AACpB,OAAOC,oBAAoB;AAA+B,IACpDC,gBAAgB,aAAAC,qBAAA;EACpB,SAAAD,iBAAYE,CAAC,EAAEC,CAAC,EAAE;IAAA,IAAAC,KAAA;IAAA9B,eAAA,OAAA0B,gBAAA;IAChBI,KAAA,GAAAxB,UAAA,OAAAoB,gBAAA;IACAI,KAAA,CAAKC,wBAAwB,GAAG,KAAK;IACrC,IAAIF,CAAC,KAAK,CAAC,IAAIA,CAAC,YAAYN,YAAY,IAAIM,CAAC,CAACG,UAAU,CAAC,CAAC,KAAK,CAAC,EAAE;MAChEC,OAAO,CAACC,KAAK,CAAC,yDAAyD,CAAC;IAC1E;IACAJ,KAAA,CAAKK,EAAE,GAAG,OAAOP,CAAC,KAAK,QAAQ,GAAG,IAAIJ,aAAa,CAACI,CAAC,CAAC,GAAGA,CAAC;IAC1DE,KAAA,CAAKM,EAAE,GAAG,OAAOP,CAAC,KAAK,QAAQ,GAAG,IAAIL,aAAa,CAACK,CAAC,CAAC,GAAGA,CAAC;IAAC,OAAAC,KAAA;EAC7D;EAACzB,SAAA,CAAAqB,gBAAA,EAAAC,qBAAA;EAAA,OAAA1B,YAAA,CAAAyB,gBAAA;IAAAW,GAAA;IAAAC,KAAA,EACD,SAAAC,YAAYA,CAACC,cAAc,EAAE;MAC3B,IAAI,CAACL,EAAE,CAACI,YAAY,CAACC,cAAc,CAAC;MACpC,IAAI,CAACJ,EAAE,CAACG,YAAY,CAACC,cAAc,CAAC;MACpCrB,aAAA,CAAAO,gBAAA,4BAAmBc,cAAc;IACnC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAN,UAAUA,CAAA,EAAG;MACX,IAAIJ,CAAC,GAAG,IAAI,CAACO,EAAE,CAACH,UAAU,CAAC,CAAC;MAC5B,IAAIH,CAAC,GAAG,IAAI,CAACO,EAAE,CAACJ,UAAU,CAAC,CAAC;MAC5B,IAAIH,CAAC,KAAK,CAAC,EAAE;QAEX,IAAI,CAAC,IAAI,CAACE,wBAAwB,EAAE;UAClCE,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;UAC9D,IAAI,CAACH,wBAAwB,GAAG,IAAI;QACtC;QAEA,OAAO,CAAC;MACV;MACA,IAAI,CAACA,wBAAwB,GAAG,KAAK;MACrC,OAAOH,CAAC,GAAGC,CAAC;IACd;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EACD,SAAAG,WAAWA,CAACC,MAAM,EAAE;MAClB,OAAO,IAAIpB,qBAAqB,CAAC,IAAI,EAAEoB,MAAM,CAAC;IAChD;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAK,QAAQA,CAAA,EAAG;MACT,IAAI,CAACR,EAAE,CAACS,UAAU,CAAC,IAAI,CAAC;MACxB,IAAI,CAACR,EAAE,CAACQ,UAAU,CAAC,IAAI,CAAC;IAC1B;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACD,SAAAO,QAAQA,CAAA,EAAG;MACT,IAAI,CAACV,EAAE,CAACW,aAAa,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACV,EAAE,CAACU,aAAa,CAAC,IAAI,CAAC;MAC3B3B,aAAA,CAAAO,gBAAA;IACF;EAAC;IAAAW,GAAA;IAAAC,KAAA,EACD,SAAAS,iBAAiBA,CAAA,EAAG;MAClB,OAAO;QACLC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,CAAC,IAAI,CAACd,EAAE,CAACe,cAAc,CAAC,CAAC,EAAE,IAAI,CAACd,EAAE,CAACc,cAAc,CAAC,CAAC;MAC5D,CAAC;IACH;EAAC;AAAA,EA/C4BzB,oBAAoB;AAiDnD,eAAeC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}