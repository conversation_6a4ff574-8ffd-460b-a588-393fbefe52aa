{"version": 3, "names": ["NAME_REGEX", "javaKeywords", "reservedNames", "validateProjectName", "name", "String", "match", "InvalidNameError", "lowerCaseName", "toLowerCase", "includes", "ReservedNameError", "HelloWorldError"], "sources": ["../../../src/commands/init/validate.ts"], "sourcesContent": ["import InvalidNameError from './errors/InvalidNameError';\nimport ReservedNameError from './errors/ReservedNameError';\nimport HelloWorldError from './errors/HelloWorldError';\n\nconst NAME_REGEX = /^[$A-Z_][0-9A-Z_$]*$/i;\n\n// ref: https://docs.oracle.com/javase/tutorial/java/nutsandbolts/_keywords.html\nconst javaKeywords = [\n  'abstract',\n  'continue',\n  'for',\n  'new',\n  'switch',\n  'assert',\n  'default',\n  'goto',\n  'package',\n  'synchronized',\n  'boolean',\n  'do',\n  'if',\n  'private',\n  'this',\n  'break',\n  'double',\n  'implements',\n  'protected',\n  'throw',\n  'byte',\n  'else',\n  'import',\n  'public',\n  'throws',\n  'case',\n  'enum',\n  'instanceof',\n  'return',\n  'transient',\n  'catch',\n  'extends',\n  'int',\n  'short',\n  'try',\n  'char',\n  'final',\n  'interface',\n  'static',\n  'void',\n  'class',\n  'finally',\n  'long',\n  'strictfp',\n  'volatile',\n  'const',\n  'float',\n  'native',\n  'super',\n  'while',\n];\n\nconst reservedNames = ['react', 'react-native', ...javaKeywords];\n\nexport function validateProjectName(name: string) {\n  if (!String(name).match(NAME_REGEX)) {\n    throw new InvalidNameError(name);\n  }\n\n  const lowerCaseName = name.toLowerCase();\n  if (reservedNames.includes(lowerCaseName)) {\n    throw new ReservedNameError(lowerCaseName);\n  }\n\n  if (name.match(/helloworld/gi)) {\n    throw new HelloWorldError();\n  }\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AAAuD;AAEvD,MAAMA,UAAU,GAAG,uBAAuB;;AAE1C;AACA,MAAMC,YAAY,GAAG,CACnB,UAAU,EACV,UAAU,EACV,KAAK,EACL,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,MAAM,EACN,SAAS,EACT,cAAc,EACd,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,MAAM,EACN,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,OAAO,EACP,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,KAAK,EACL,MAAM,EACN,OAAO,EACP,WAAW,EACX,QAAQ,EACR,MAAM,EACN,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,UAAU,EACV,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,CACR;AAED,MAAMC,aAAa,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE,GAAGD,YAAY,CAAC;AAEzD,SAASE,mBAAmB,CAACC,IAAY,EAAE;EAChD,IAAI,CAACC,MAAM,CAACD,IAAI,CAAC,CAACE,KAAK,CAACN,UAAU,CAAC,EAAE;IACnC,MAAM,IAAIO,yBAAgB,CAACH,IAAI,CAAC;EAClC;EAEA,MAAMI,aAAa,GAAGJ,IAAI,CAACK,WAAW,EAAE;EACxC,IAAIP,aAAa,CAACQ,QAAQ,CAACF,aAAa,CAAC,EAAE;IACzC,MAAM,IAAIG,0BAAiB,CAACH,aAAa,CAAC;EAC5C;EAEA,IAAIJ,IAAI,CAACE,KAAK,CAAC,cAAc,CAAC,EAAE;IAC9B,MAAM,IAAIM,wBAAe,EAAE;EAC7B;AACF"}