import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

const SimpleGoogleMap = ({ secteur, clients }) => {
  if (!secteur || !clients || clients.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>
            Sélectionnez un secteur pour voir la carte
          </Text>
        </View>
      </View>
    );
  }

  const centerLat = secteur.latitude;
  const centerLng = secteur.longitude;

  // Créer une carte HTML simple avec Google Maps
  const createGoogleMapHTML = () => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Carte ${secteur.nom}</title>
        <style>
          body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
          #map { height: 100vh; width: 100%; }
          .info-window { font-size: 12px; max-width: 200px; }
        </style>
      </head>
      <body>
        <div id="map"></div>
        <script>
          function initMap() {
            // Centre de la carte
            const center = { lat: ${centerLat}, lng: ${centerLng} };
            
            // Créer la carte
            const map = new google.maps.Map(document.getElementById("map"), {
              zoom: 15,
              center: center,
              mapTypeId: 'roadmap'
            });
            
            // Marqueur pour le centre du secteur (bleu)
            const sectorMarker = new google.maps.Marker({
              position: center,
              map: map,
              title: "${secteur.nom}",
              icon: {
                url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
                scaledSize: new google.maps.Size(32, 32)
              }
            });
            
            // Info window pour le secteur
            const sectorInfoWindow = new google.maps.InfoWindow({
              content: '<div class="info-window"><h3>📍 ${secteur.nom}</h3><p>Centre du secteur</p><p>Coordonnées: ${centerLat.toFixed(4)}, ${centerLng.toFixed(4)}</p></div>'
            });
            
            sectorMarker.addListener('click', () => {
              sectorInfoWindow.open(map, sectorMarker);
            });
            
            // Marqueurs pour chaque client (rouge)
            const clientMarkers = [];
            ${clients.map((client, index) => `
              const clientMarker${index} = new google.maps.Marker({
                position: { lat: ${client.latitude}, lng: ${client.longitude} },
                map: map,
                title: "${client.nom} ${client.prenom}",
                icon: {
                  url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
                  scaledSize: new google.maps.Size(28, 28)
                }
              });
              
              const clientInfoWindow${index} = new google.maps.InfoWindow({
                content: '<div class="info-window"><h3>👤 ${client.nom} ${client.prenom}</h3><p>📍 ${client.adresse}</p><p>🏙️ ${client.ville}</p><p>📞 ${client.tel || 'N/A'}</p><p>📧 ${client.email || 'N/A'}</p><p>🗺️ ${client.latitude.toFixed(4)}, ${client.longitude.toFixed(4)}</p></div>'
              });
              
              clientMarker${index}.addListener('click', () => {
                clientInfoWindow${index}.open(map, clientMarker${index});
              });
              
              clientMarkers.push(clientMarker${index});
            `).join('')}
            
            // Ajuster la vue pour inclure tous les marqueurs
            const bounds = new google.maps.LatLngBounds();
            bounds.extend(center);
            ${clients.map((client, index) => `
              bounds.extend({ lat: ${client.latitude}, lng: ${client.longitude} });
            `).join('')}
            
            if (clientMarkers.length > 0) {
              map.fitBounds(bounds);
              // Assurer un zoom minimum
              google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
                if (map.getZoom() > 16) {
                  map.setZoom(16);
                }
              });
            }
          }
          
          // Fonction de fallback si Google Maps ne charge pas
          window.addEventListener('load', function() {
            setTimeout(function() {
              if (typeof google === 'undefined') {
                document.getElementById('map').innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f0f0f0; color: #666; text-align: center; padding: 20px;"><div><h3>🗺️ Carte non disponible</h3><p>Impossible de charger Google Maps</p><p>Vérifiez votre connexion internet</p></div></div>';
              }
            }, 5000);
          });
        </script>
        <script async defer
          src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dOWTgHz-TK7VgM&callback=initMap"
          onerror="document.getElementById('map').innerHTML='<div style=\\'display: flex; align-items: center; justify-content: center; height: 100%; background: #f0f0f0; color: #666; text-align: center; padding: 20px;\\'><div><h3>🗺️ Carte non disponible</h3><p>Erreur de chargement Google Maps</p></div></div>';">
        </script>
      </body>
      </html>
    `;
  };

  // Pour React Native Web
  if (typeof window !== 'undefined') {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>
          🗺️ {secteur.nom} - {clients.length} client(s)
        </Text>
        
        {/* Carte Google Maps */}
        <div style={styles.mapContainer}>
          <iframe
            srcDoc={createGoogleMapHTML()}
            style={styles.map}
            allowFullScreen=""
            loading="lazy"
            title={`Carte Google Maps - ${secteur.nom}`}
          />
        </div>
        
        {/* Bouton pour ouvrir dans Google Maps */}
        <TouchableOpacity 
          style={styles.openButton}
          onPress={() => {
            const mapsUrl = `https://www.google.com/maps/dir/${centerLat},${centerLng}/${clients.map(c => `${c.latitude},${c.longitude}`).join('/')}`;
            window.open(mapsUrl, '_blank');
          }}
        >
          <Text style={styles.openButtonText}>
            🌐 Ouvrir dans Google Maps
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Pour React Native mobile
  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        🗺️ {secteur.nom} - {clients.length} client(s)
      </Text>
      <View style={styles.mobileMapPlaceholder}>
        <Text style={styles.mobileMapText}>
          Carte disponible sur la version web
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
    backgroundColor: '#f8f9fa',
    padding: 10,
    borderRadius: 8,
  },
  noDataContainer: {
    padding: 40,
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  noDataText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    fontSize: 14,
  },
  mapContainer: {
    width: '100%',
    height: 300,
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 15,
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  map: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
  openButton: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  openButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  mobileMapPlaceholder: {
    height: 200,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  mobileMapText: {
    color: '#666',
    fontStyle: 'italic',
  },
});

export default SimpleGoogleMap;
