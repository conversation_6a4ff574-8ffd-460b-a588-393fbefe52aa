<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Champ Secteur utilise la Table Secteur</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .sql-schema {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        
        .sql-schema h3 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        
        .api-flow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .flow-step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .flow-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .flow-step.database {
            border-color: #28a745;
        }
        
        .flow-step.api {
            border-color: #007bff;
        }
        
        .flow-step.frontend {
            border-color: #ffc107;
        }
        
        .flow-step h4 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .flow-step.database h4 {
            color: #28a745;
        }
        
        .flow-step.api h4 {
            color: #007bff;
        }
        
        .flow-step.frontend h4 {
            color: #ffc107;
        }
        
        .secteur-preview {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        
        .secteur-preview h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .secteur-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .secteur-item {
            background: white;
            padding: 10px;
            border-radius: 8px;
            border-left: 3px solid #007bff;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .secteur-item .id {
            font-weight: bold;
            color: #007bff;
        }
        
        .secteur-item .nom {
            font-weight: bold;
            color: #333;
            margin: 5px 0;
        }
        
        .secteur-item .coords {
            color: #666;
            font-size: 0.8em;
        }
        
        .live-demo {
            background: #fff3cd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }
        
        .live-demo h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .api-response {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .json-data {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            white-space: pre-wrap;
            color: #495057;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .api-flow {
                grid-template-columns: 1fr;
            }
            
            .secteur-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📍 Champ Secteur utilise la Table Secteur</h1>
            <p>Démonstration de l'intégration complète Base de Données → API → Interface</p>
        </div>
        
        <div class="content">
            <div class="sql-schema">
                <h3>🗄️ Structure de la Table Secteur</h3>
                <div class="sql-code">CREATE TABLE secteur (
    ids SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);</div>
                <p style="margin-top: 10px; color: #666;">
                    Cette table contient tous les secteurs géographiques avec leurs coordonnées GPS.
                </p>
            </div>
            
            <div class="api-flow">
                <div class="flow-step database">
                    <h4>1️⃣ Base de Données</h4>
                    <p><strong>Table:</strong> secteur</p>
                    <p><strong>Champs:</strong> ids, nom, latitude, longitude</p>
                    <p><strong>Données:</strong> 5 secteurs stockés</p>
                </div>
                
                <div class="flow-step api">
                    <h4>2️⃣ API Backend</h4>
                    <p><strong>Route:</strong> GET /api/secteurs</p>
                    <p><strong>SQL:</strong> SELECT * FROM secteur ORDER BY nom</p>
                    <p><strong>Format:</strong> JSON avec tous les secteurs</p>
                </div>
                
                <div class="flow-step frontend">
                    <h4>3️⃣ Interface Frontend</h4>
                    <p><strong>Fonction:</strong> fetchSecteurs()</p>
                    <p><strong>Affichage:</strong> Menu déroulant</p>
                    <p><strong>Données:</strong> secteur.nom pour chaque option</p>
                </div>
            </div>
            
            <div class="secteur-preview">
                <h3>📋 Secteurs de la Table (Aperçu)</h3>
                <div class="secteur-list">
                    <div class="secteur-item">
                        <div class="id">ID: 1</div>
                        <div class="nom">Centre-Ville</div>
                        <div class="coords">33.5731, -7.5898</div>
                    </div>
                    <div class="secteur-item">
                        <div class="id">ID: 2</div>
                        <div class="nom">Quartier Industriel</div>
                        <div class="coords">33.5831, -7.5998</div>
                    </div>
                    <div class="secteur-item">
                        <div class="id">ID: 3</div>
                        <div class="nom">Zone Résidentielle Nord</div>
                        <div class="coords">33.5931, -7.6098</div>
                    </div>
                    <div class="secteur-item">
                        <div class="id">ID: 4</div>
                        <div class="nom">Zone Résidentielle Sud</div>
                        <div class="coords">33.5631, -7.5798</div>
                    </div>
                    <div class="secteur-item">
                        <div class="id">ID: 5</div>
                        <div class="nom">Quartier Commercial</div>
                        <div class="coords">33.5531, -7.5698</div>
                    </div>
                </div>
            </div>
            
            <div class="live-demo">
                <h3>🔴 Données en Temps Réel</h3>
                <p>Cliquez sur "Charger les Secteurs" pour voir les données actuelles de votre table :</p>
                <button onclick="loadSecteurs()" class="btn btn-warning" style="margin: 10px 0;">
                    🔄 Charger les Secteurs depuis l'API
                </button>
                <div id="api-response" class="api-response" style="display: none;">
                    <div id="json-data" class="json-data"></div>
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:4002/api/secteurs" class="btn btn-primary" target="_blank">
                    📊 Voir l'API Secteurs
                </a>
                <a href="http://localhost:19006" class="btn btn-success" target="_blank">
                    📱 Tester l'Application
                </a>
            </div>
            
            <div style="background: #d4edda; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #28a745;">
                <h3 style="color: #155724; margin-bottom: 15px;">✅ Fonctionnement Confirmé</h3>
                <ul style="color: #155724; line-height: 1.6;">
                    <li>✅ <strong>Table secteur</strong> : Contient tous les secteurs avec coordonnées GPS</li>
                    <li>✅ <strong>API /api/secteurs</strong> : Récupère tous les secteurs de la table</li>
                    <li>✅ <strong>Champ Secteur</strong> : Affiche tous les secteurs dans le menu déroulant</li>
                    <li>✅ <strong>Sélection</strong> : Utilise secteur.ids comme valeur, secteur.nom comme libellé</li>
                    <li>✅ <strong>Filtrage Client</strong> : Basé sur la sélection du secteur</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        async function loadSecteurs() {
            const responseDiv = document.getElementById('api-response');
            const jsonDiv = document.getElementById('json-data');
            
            try {
                responseDiv.style.display = 'block';
                jsonDiv.textContent = 'Chargement des secteurs...';
                
                const response = await fetch('http://localhost:4002/api/secteurs');
                const data = await response.json();
                
                jsonDiv.textContent = JSON.stringify(data, null, 2);
                
                // Mettre en évidence les secteurs
                if (data.success && data.data) {
                    let highlight = '\n\n📍 SECTEURS TROUVÉS DANS VOTRE TABLE:\n';
                    data.data.forEach(secteur => {
                        highlight += `   ${secteur.ids}. ${secteur.nom} (${secteur.latitude}, ${secteur.longitude})\n`;
                    });
                    highlight += '\n✅ Ces secteurs apparaissent dans le champ "📍 Secteur *"';
                    jsonDiv.textContent += highlight;
                }
                
            } catch (error) {
                jsonDiv.textContent = `❌ Erreur: ${error.message}\n\n💡 Vérifiez que le serveur est démarré sur le port 4002`;
            }
        }
        
        // Charger automatiquement au démarrage
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(loadSecteurs, 1000);
        });
    </script>
</body>
</html>
