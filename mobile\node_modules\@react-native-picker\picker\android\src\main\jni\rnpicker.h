#pragma once

#include <ReactCommon/JavaTurboModule.h>
#include <ReactCommon/TurboModule.h>
#include <jsi/jsi.h>

/**
 * Note this imports and that they are not present in autogenerated header file
 * under android/build/generated/source/codegen/jni/rnpicker.h
 *
 * It is added here to make the custom symbols visible in autogenerated file
 * with code responsible for registering component descriptor providers. See
 * that rncli.cpp, located under
 * <App>/android/app/build/generated/rncli/src/main/jni/rncli.cpp, includes
 * autogenerated rnpicker.h header by default. We change this behaviour by
 * appropriate include path configuration so that this header file gets
 * included.
 */
#include <react/renderer/components/rnpicker/RNCAndroidDialogPickerComponentDescriptor.h>
#include <react/renderer/components/rnpicker/RNCAndroidDropdownPickerComponentDescriptor.h>

namespace facebook::react {

JSI_EXPORT
std::shared_ptr<TurboModule> rnpicker_ModuleProvider(
    const std::string& moduleName,
    const JavaTurboModule::InitParams& params);

} // namespace facebook::react
