{"ast": null, "code": "'use strict';\n\nimport * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar UnimplementedView = function UnimplementedView(props) {\n  return React.createElement(View, {\n    style: [styles.unimplementedView, props.style]\n  }, props.children);\n};\nvar styles = StyleSheet.create({\n  unimplementedView: process.env.NODE_ENV !== 'production' ? {\n    alignSelf: 'flex-start',\n    borderColor: 'red',\n    borderWidth: 1\n  } : {}\n});\nexport default UnimplementedView;", "map": {"version": 3, "names": ["React", "View", "StyleSheet", "UnimplementedView", "props", "createElement", "style", "styles", "unimplementedView", "children", "create", "process", "env", "NODE_ENV", "alignSelf", "borderColor", "borderWidth"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-native-picker\\picker\\js\\UnimplementedView.web.js"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport * as React from 'react';\nimport {View, StyleSheet} from 'react-native';\ndeclare var __DEV__: boolean;\n/**\n * Common implementation for a simple stubbed view. Simply applies the view's styles to the inner\n * View component and renders its children.\n */\nconst UnimplementedView = (props: $FlowFixMeProps): React.Node => {\n  return (\n    <View style={[styles.unimplementedView, props.style]}>\n      {props.children}\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  unimplementedView:\n    process.env.NODE_ENV !== 'production'\n      ? {\n          alignSelf: 'flex-start',\n          borderColor: 'red',\n          borderWidth: 1,\n        }\n      : {},\n});\n\nexport default UnimplementedView;\n"], "mappings": "AAUA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAO9B,IAAMC,iBAAiB,GAAI,SAArBA,iBAAiBA,CAAIC,KAAsB,EAAiB;EAChE,OACEJ,KAAA,CAAAK,aAAA,CAACJ,IAAI;IAACK,KAAK,EAAE,CAACC,MAAM,CAACC,iBAAiB,EAAEJ,KAAK,CAACE,KAAK;EAAE,GAClDF,KAAK,CAACK,QACH,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAGL,UAAU,CAACQ,MAAM,CAAC;EAC/BF,iBAAiB,EACfG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACjC;IACEC,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACf,CAAC,GACD,CAAC;AACT,CAAC,CAAC;AAEF,eAAeb,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}