{"ast": null, "code": "import * as CardStyleInterpolators from \"./TransitionConfigs/CardStyleInterpolators\";\nimport * as HeaderStyleInterpolators from \"./TransitionConfigs/HeaderStyleInterpolators\";\nimport * as TransitionPresets from \"./TransitionConfigs/TransitionPresets\";\nimport * as TransitionSpecs from \"./TransitionConfigs/TransitionSpecs\";\nexport { default as createStackNavigator } from \"./navigators/createStackNavigator\";\nexport { default as Header } from \"./views/Header/Header\";\nexport { default as StackView } from \"./views/Stack/StackView\";\nexport { CardStyleInterpolators, HeaderStyleInterpolators, TransitionPresets, TransitionSpecs };\nexport { default as CardAnimationContext } from \"./utils/CardAnimationContext\";\nexport { default as GestureHandlerRefContext } from \"./utils/GestureHandlerRefContext\";\nexport { default as useCardAnimation } from \"./utils/useCardAnimation\";\nexport { default as useGestureHandlerRef } from \"./utils/useGestureHandlerRef\";", "map": {"version": 3, "names": ["CardStyleInterpolators", "HeaderStyleInterpolators", "TransitionPresets", "TransitionSpecs", "default", "createStackNavigator", "Header", "StackView", "CardAnimationContext", "GestureHandlerRefContext", "useCardAnimation", "useGestureHandlerRef"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\stack\\src\\index.tsx"], "sourcesContent": ["import * as CardStyleInterpolators from './TransitionConfigs/CardStyleInterpolators';\nimport * as HeaderStyleInterpolators from './TransitionConfigs/HeaderStyleInterpolators';\nimport * as TransitionPresets from './TransitionConfigs/TransitionPresets';\nimport * as TransitionSpecs from './TransitionConfigs/TransitionSpecs';\n\n/**\n * Navigators\n */\nexport { default as createStackNavigator } from './navigators/createStackNavigator';\n\n/**\n * Views\n */\nexport { default as Header } from './views/Header/Header';\nexport { default as StackView } from './views/Stack/StackView';\n\n/**\n * Transition presets\n */\nexport {\n  CardStyleInterpolators,\n  HeaderStyleInterpolators,\n  TransitionPresets,\n  TransitionSpecs,\n};\n\n/**\n * Utilities\n */\nexport { default as CardAnimationContext } from './utils/CardAnimationContext';\nexport { default as GestureHandlerRefContext } from './utils/GestureHandlerRefContext';\nexport { default as useCardAnimation } from './utils/useCardAnimation';\nexport { default as useGestureHandlerRef } from './utils/useGestureHandlerRef';\n\n/**\n * Types\n */\nexport type {\n  StackCardInterpolatedStyle,\n  StackCardInterpolationProps,\n  StackCardStyleInterpolator,\n  StackHeaderInterpolatedStyle,\n  StackHeaderInterpolationProps,\n  StackHeaderProps,\n  StackHeaderStyleInterpolator,\n  StackNavigationEventMap,\n  StackNavigationOptions,\n  StackNavigationProp,\n  StackScreenProps,\n  TransitionPreset,\n} from './types';\n"], "mappings": "AAAA,OAAO,KAAKA,sBAAsB;AAClC,OAAO,KAAKC,wBAAwB;AACpC,OAAO,KAAKC,iBAAiB;AAC7B,OAAO,KAAKC,eAAe;AAK3B,SAASC,OAAO,IAAIC,oBAAoB;AAKxC,SAASD,OAAO,IAAIE,MAAM;AAC1B,SAASF,OAAO,IAAIG,SAAS;AAK7B,SACEP,sBAAsB,EACtBC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAe;AAMjB,SAASC,OAAO,IAAII,oBAAoB;AACxC,SAASJ,OAAO,IAAIK,wBAAwB;AAC5C,SAASL,OAAO,IAAIM,gBAAgB;AACpC,SAASN,OAAO,IAAIO,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}