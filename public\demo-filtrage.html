<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Filtrage Clients par Secteur</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .sql-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        
        .sql-demo h3 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-table {
            color: #68d391;
        }
        
        .sql-field {
            color: #fbb6ce;
        }
        
        .secteur-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .secteur-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .secteur-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .secteur-card.active {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .secteur-title {
            color: #007bff;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .secteur-id {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .client-list {
            list-style: none;
        }
        
        .client-item {
            background: white;
            padding: 10px;
            margin: 8px 0;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            font-size: 14px;
        }
        
        .client-name {
            font-weight: bold;
            color: #333;
        }
        
        .client-details {
            color: #666;
            font-size: 12px;
            margin-top: 4px;
        }
        
        .no-clients {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-style: italic;
        }
        
        .flow-demo {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }
        
        .step-number {
            background: #28a745;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .step-description {
            color: #666;
            font-size: 14px;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }
        
        .highlight {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        
        .highlight h3 {
            color: #155724;
            margin-bottom: 10px;
        }
        
        .highlight p {
            color: #155724;
            margin: 0;
            line-height: 1.5;
        }
        
        @media (max-width: 768px) {
            .secteur-demo {
                grid-template-columns: 1fr;
            }
            
            .flow-step {
                flex-direction: column;
                text-align: center;
            }
            
            .step-number {
                margin-bottom: 10px;
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Filtrage Clients par Secteur</h1>
            <p>Démonstration du système de filtrage utilisant les tables secteur et client</p>
        </div>
        
        <div class="content">
            <div class="highlight">
                <h3>🎯 Fonctionnalité Implémentée</h3>
                <p>
                    Quand vous sélectionnez un secteur, le champ Client affiche <strong>uniquement les clients de ce secteur</strong> 
                    en utilisant la relation <code>client.ids = secteur.ids</code> dans la base de données.
                </p>
            </div>
            
            <div class="sql-demo">
                <h3>🗄️ Requête SQL de Filtrage</h3>
                <div class="sql-code">
<span class="sql-keyword">SELECT</span><br>
&nbsp;&nbsp;<span class="sql-field">c.idclient</span>,<br>
&nbsp;&nbsp;<span class="sql-field">c.nom</span>,<br>
&nbsp;&nbsp;<span class="sql-field">c.prenom</span>,<br>
&nbsp;&nbsp;<span class="sql-field">c.adresse</span>,<br>
&nbsp;&nbsp;<span class="sql-field">c.ville</span>,<br>
&nbsp;&nbsp;<span class="sql-field">c.tel</span>,<br>
&nbsp;&nbsp;<span class="sql-field">c.email</span>,<br>
&nbsp;&nbsp;<span class="sql-field">c.ids</span>,<br>
&nbsp;&nbsp;<span class="sql-field">s.nom</span> <span class="sql-keyword">as</span> <span class="sql-field">secteur_nom</span><br>
<span class="sql-keyword">FROM</span> <span class="sql-table">client</span> <span class="sql-field">c</span><br>
<span class="sql-keyword">INNER JOIN</span> <span class="sql-table">secteur</span> <span class="sql-field">s</span> <span class="sql-keyword">ON</span> <span class="sql-field">c.ids</span> = <span class="sql-field">s.ids</span><br>
<span class="sql-keyword">WHERE</span> <span class="sql-field">c.ids</span> = <span style="color: #ffd700;">$1</span><br>
<span class="sql-keyword">ORDER BY</span> <span class="sql-field">c.nom</span>, <span class="sql-field">c.prenom</span>
                </div>
                <p style="margin-top: 15px; color: #666; font-size: 14px;">
                    <strong>$1</strong> = ID du secteur sélectionné dans le formulaire
                </p>
            </div>
            
            <div class="flow-demo">
                <h3 style="text-align: center; color: #28a745; margin-bottom: 20px;">📋 Flux de Filtrage</h3>
                
                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">Sélection du Secteur</div>
                        <div class="step-description">L'utilisateur choisit un secteur dans le premier champ (ex: "Centre-Ville")</div>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">Appel API de Filtrage</div>
                        <div class="step-description">L'application appelle <code>/api/secteurs/{id}/clients</code> avec l'ID du secteur</div>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">Exécution de la Requête SQL</div>
                        <div class="step-description">Le serveur exécute un INNER JOIN entre les tables client et secteur</div>
                    </div>
                </div>
                
                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <div class="step-title">Mise à Jour du Champ Client</div>
                        <div class="step-description">Le champ Client affiche uniquement les clients du secteur sélectionné</div>
                    </div>
                </div>
            </div>
            
            <h3 style="text-align: center; color: #007bff; margin: 30px 0 20px;">🗺️ Répartition des Clients par Secteur</h3>
            
            <div class="secteur-demo">
                <div class="secteur-card active">
                    <div class="secteur-title">
                        <div class="secteur-id">1</div>
                        Centre-Ville
                    </div>
                    <ul class="client-list">
                        <li class="client-item">
                            <div class="client-name">👤 Benali Fatima</div>
                            <div class="client-details">📍 45 Avenue Hassan II, Setrou | 📞 0612345678</div>
                        </li>
                        <li class="client-item">
                            <div class="client-name">👤 Alami Mohammed</div>
                            <div class="client-details">📍 12 Rue des Oliviers, Setrou | 📞 0623456789</div>
                        </li>
                    </ul>
                </div>
                
                <div class="secteur-card">
                    <div class="secteur-title">
                        <div class="secteur-id">2</div>
                        Quartier Industriel
                    </div>
                    <ul class="client-list">
                        <li class="client-item">
                            <div class="client-name">👤 Tazi Aicha</div>
                            <div class="client-details">📍 78 Boulevard Zerktouni, Setrou | 📞 0634567890</div>
                        </li>
                    </ul>
                </div>
                
                <div class="secteur-card">
                    <div class="secteur-title">
                        <div class="secteur-id">3</div>
                        Zone Résidentielle Nord
                    </div>
                    <ul class="client-list">
                        <li class="client-item">
                            <div class="client-name">👤 Benjelloun Youssef</div>
                            <div class="client-details">📍 23 Rue Ibn Sina, Setrou | 📞 0645678901</div>
                        </li>
                        <li class="client-item">
                            <div class="client-name">👤 Lahlou Khadija</div>
                            <div class="client-details">📍 56 Avenue Mohammed V, Setrou | 📞 0656789012</div>
                        </li>
                    </ul>
                </div>
                
                <div class="secteur-card">
                    <div class="secteur-title">
                        <div class="secteur-id">4</div>
                        Zone Résidentielle Sud
                    </div>
                    <ul class="client-list">
                        <li class="client-item">
                            <div class="client-name">👤 Fassi Omar</div>
                            <div class="client-details">📍 89 Rue Al Massira, Setrou | 📞 0667890123</div>
                        </li>
                    </ul>
                </div>
                
                <div class="secteur-card">
                    <div class="secteur-title">
                        <div class="secteur-id">5</div>
                        Quartier Commercial
                    </div>
                    <div class="no-clients">
                        ❌ Aucun client dans ce secteur
                    </div>
                </div>
            </div>
            
            <div class="highlight">
                <h3>🧪 Instructions de Test</h3>
                <p>
                    1. Allez sur <strong>http://localhost:19006</strong><br>
                    2. Connectez-vous avec <strong><EMAIL> / Tech123</strong><br>
                    3. Allez dans <strong>"Consommation"</strong><br>
                    4. Sélectionnez différents secteurs et observez que le champ Client ne montre que les clients du secteur choisi<br>
                    5. Vérifiez que le changement de secteur vide et recharge le champ Client
                </p>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-success" target="_blank">
                    📱 Tester l'Application
                </a>
                <a href="http://localhost:4002/api/secteurs" class="btn btn-primary" target="_blank">
                    🗄️ Voir API Secteurs
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Simulation d'interaction avec les secteurs
        document.addEventListener('DOMContentLoaded', function() {
            const secteurCards = document.querySelectorAll('.secteur-card');
            
            secteurCards.forEach((card, index) => {
                card.addEventListener('click', function() {
                    // Retirer la classe active de toutes les cartes
                    secteurCards.forEach(c => c.classList.remove('active'));
                    
                    // Ajouter la classe active à la carte cliquée
                    this.classList.add('active');
                    
                    // Simuler le filtrage
                    console.log(`Secteur ${index + 1} sélectionné - Filtrage des clients...`);
                });
            });
        });
    </script>
</body>
</html>
