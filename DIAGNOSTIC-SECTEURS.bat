@echo off
title Diagnostic Secteurs
color 0A

echo.
echo ========================================
echo    🔍 DIAGNOSTIC CHAMP SECTEURS
echo ========================================
echo.

echo 🔍 1. TEST DIRECT DE L'API SECTEURS...
echo.
powershell -Command "try { Write-Host 'Test de l''API /api/secteurs...'; $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 10; Write-Host '✅ API SECTEURS REPONSE:'; Write-Host '   Success:' $response.success; Write-Host '   Count:' $response.count; Write-Host '   Data type:' $response.data.GetType().Name; Write-Host '   Data length:' $response.data.Count; Write-Host ''; Write-Host '📋 SECTEURS RETOURNES:'; $response.data | ForEach-Object { Write-Host '   ID:' $_.ids '| Nom:' $_.nom '| Type:' $_.GetType().Name } } catch { Write-Host '❌ ERREUR API:' $_.Exception.Message }"

echo.
echo 🔍 2. TEST DE CONNEXION CORS...
echo.
powershell -Command "try { $headers = @{ 'Origin' = 'http://localhost:19006' }; $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -Headers $headers -TimeoutSec 5; Write-Host '✅ CORS OK - Secteurs accessibles depuis le frontend' } catch { Write-Host '❌ PROBLEME CORS ou serveur inaccessible' }"

echo.
echo 🔍 3. VERIFICATION DU SERVEUR BACKEND...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 5; Write-Host '✅ Serveur backend actif:' $response.message } catch { Write-Host '❌ Serveur backend non accessible' }"

echo.
echo 🔍 4. TEST AVEC CURL (simulation frontend)...
echo.
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:4002/api/secteurs' -Method GET -Headers @{'Accept'='application/json'; 'Content-Type'='application/json'} -TimeoutSec 5; Write-Host '✅ Requete HTTP OK:'; Write-Host '   Status:' $response.StatusCode; Write-Host '   Content-Type:' $response.Headers.'Content-Type'; $data = $response.Content | ConvertFrom-Json; Write-Host '   JSON valide:' $data.success; Write-Host '   Secteurs:' $data.count } catch { Write-Host '❌ Erreur requete HTTP:' $_.Exception.Message }"

echo.
echo 🔍 5. VERIFICATION DU CODE FRONTEND...
echo.
if exist "src\pages\Consommation.js" (
    echo ✅ Fichier Consommation.js trouve
    
    findstr /C:"http://localhost:4002/api/secteurs" "src\pages\Consommation.js" >nul
    if !errorlevel! equ 0 (
        echo ✅ URL API correcte dans le code
    ) else (
        echo ❌ URL API incorrecte ou manquante
    )
    
    findstr /C:"setSecteurs" "src\pages\Consommation.js" >nul
    if !errorlevel! equ 0 (
        echo ✅ Fonction setSecteurs presente
    ) else (
        echo ❌ Fonction setSecteurs manquante
    )
    
    findstr /C:"fetchSecteurs" "src\pages\Consommation.js" >nul
    if !errorlevel! equ 0 (
        echo ✅ Fonction fetchSecteurs presente
    ) else (
        echo ❌ Fonction fetchSecteurs manquante
    )
) else (
    echo ❌ Fichier Consommation.js non trouve
)

echo.
echo 🔍 6. SIMULATION DE LA REQUETE FRONTEND...
echo.
powershell -Command "try { Write-Host 'Simulation exacte de la requete frontend...'; $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -Method GET -ContentType 'application/json' -TimeoutSec 10; Write-Host ''; Write-Host '📊 REPONSE COMPLETE:'; Write-Host ($response | ConvertTo-Json -Depth 3); Write-Host ''; Write-Host '🎯 VERIFICATION STRUCTURE:'; Write-Host '   response.success:' $response.success; Write-Host '   response.data existe:' ($response.data -ne $null); Write-Host '   response.data est array:' ($response.data -is [array]); if ($response.data) { Write-Host '   Premier secteur:' $response.data[0].nom '(ID:' $response.data[0].ids ')' } } catch { Write-Host '❌ Erreur simulation:' $_.Exception.Message }"

echo.
echo ========================================
echo    📊 DIAGNOSTIC TERMINE
echo ========================================
echo.
echo 🎯 PROBLEMES POSSIBLES:
echo.
echo 1. ❌ Serveur backend non demarre
echo    💡 Solution: Lancez OUVRIR-PROJET.bat
echo.
echo 2. ❌ API secteurs ne retourne pas de donnees
echo    💡 Solution: Verifiez la base de donnees
echo.
echo 3. ❌ Probleme CORS
echo    💡 Solution: Verifiez la configuration CORS
echo.
echo 4. ❌ Erreur dans le code frontend
echo    💡 Solution: Verifiez la console du navigateur
echo.
echo 5. ❌ URL API incorrecte
echo    💡 Solution: Verifiez l'URL dans fetchSecteurs()
echo.
echo 🧪 PROCHAINES ETAPES:
echo.
echo 1. 🌐 Ouvrez la console du navigateur (F12)
echo 2. 🔍 Regardez les erreurs dans l'onglet Console
echo 3. 📡 Verifiez l'onglet Network pour les requetes
echo 4. 🧪 Testez l'API directement: http://localhost:4002/api/secteurs
echo.
echo 🌐 OUVERTURE DES PAGES DE TEST...
start http://localhost:4002/api/secteurs
timeout /t 2 /nobreak >nul
start http://localhost:19006

echo.
echo ✅ DIAGNOSTIC TERMINE !
echo    Verifiez les resultats ci-dessus pour identifier le probleme.
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
