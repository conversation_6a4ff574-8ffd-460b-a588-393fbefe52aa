@echo off
title Demarrage Serveur AquaTrack
color 0A

echo.
echo ========================================
echo    DEMARRAGE SERVEUR AQUATRACK
echo ========================================
echo.

echo 🛑 Arret des processus existants...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ Liberation des ports...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Demarrage du serveur de test...
echo.
echo Le serveur va demarrer sur http://localhost:4000
echo.

REM Demarrer le serveur dans une nouvelle fenetre
start "Serveur AquaTrack" cmd /k "title Serveur AquaTrack && color 0B && echo ========================================== && echo    🚀 SERVEUR AQUATRACK ACTIF && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Status: FONCTIONNEL && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo    - <EMAIL> / Admin123 && echo. && echo 📡 Serveur en cours de demarrage... && echo. && node serveur-test.js"

echo.
echo ⏳ Attente du demarrage (10 secondes)...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 Test de connexion...
start http://localhost:4000

echo.
echo ========================================
echo    ✅ SERVEUR DEMARRE !
echo ========================================
echo.
echo 📡 URL du serveur: http://localhost:4000
echo.
echo 🔑 Comptes de test:
echo    - <EMAIL> / Tech123
echo    - <EMAIL> / Admin123
echo.
echo 📋 Instructions:
echo    1. Une nouvelle fenetre s'est ouverte avec le serveur
echo    2. Un navigateur s'est ouvert sur http://localhost:4000
echo    3. Vous devriez voir un message JSON
echo    4. Maintenant vous pouvez utiliser votre application mobile
echo.
echo ⚠️  IMPORTANT:
echo    - Gardez la fenetre du serveur ouverte
echo    - Ne fermez pas le terminal du serveur
echo    - Le serveur doit rester actif pour l'application mobile
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
