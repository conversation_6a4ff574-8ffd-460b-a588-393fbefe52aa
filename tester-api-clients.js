// Script de test pour l'API clients
const { Pool } = require('pg');

console.log('🔍 TEST DE L\'API CLIENTS AQUATRACK');
console.log('=====================================');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function testClients() {
  try {
    console.log('\n1. 🔗 Test de connexion à la base de données...');
    
    // Test de connexion
    const testResult = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Connexion réussie:', testResult.rows[0].current_time);
    
    console.log('\n2. 📋 Vérification de la table client...');
    
    // Vérifier si la table existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'client'
      );
    `);
    
    if (!tableExists.rows[0].exists) {
      console.log('❌ La table client n\'existe pas !');
      console.log('💡 Créez la table avec cette commande SQL :');
      console.log(`
CREATE TABLE client (
    idclient SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    adresse VARCHAR(255),
    ville VARCHAR(100),
    tel VARCHAR(20),
    email VARCHAR(100),
    statut VARCHAR(20)
);
      `);
      return;
    }
    
    console.log('✅ Table client existe');
    
    console.log('\n3. 📊 Récupération des clients...');
    
    // Récupérer tous les clients
    const clientsResult = await pool.query(`
      SELECT
        idclient,
        nom,
        prenom,
        adresse,
        ville,
        tel,
        email,
        statut
      FROM client
      ORDER BY nom, prenom
    `);
    
    console.log(`✅ ${clientsResult.rows.length} client(s) trouvé(s)`);
    
    if (clientsResult.rows.length === 0) {
      console.log('\n⚠️  Aucun client dans la table !');
      console.log('💡 Ajoutez des clients avec cette commande SQL :');
      console.log(`
INSERT INTO client (nom, prenom, adresse, ville, tel, email, statut) VALUES
('Benali', 'Fatima', '45 Avenue Hassan II', 'Setrou', '0612345678', '<EMAIL>', 'Actif'),
('Alami', 'Mohammed', '12 Rue des Oliviers', 'Setrou', '0623456789', '<EMAIL>', 'Actif'),
('Tazi', 'Aicha', '78 Boulevard Zerktouni', 'Setrou', '0634567890', '<EMAIL>', 'Actif');
      `);
    } else {
      console.log('\n📋 Liste des clients :');
      clientsResult.rows.forEach((client, index) => {
        console.log(`${index + 1}. ${client.nom} ${client.prenom} - ${client.ville} (${client.statut})`);
      });
      
      console.log('\n🎯 Format JSON pour l\'API :');
      console.log(JSON.stringify({
        success: true,
        data: clientsResult.rows,
        total: clientsResult.rows.length
      }, null, 2));
    }
    
    console.log('\n4. 🌐 Test de l\'API HTTP...');
    
    // Test de l'API HTTP si le serveur est démarré
    try {
      const fetch = require('node-fetch');
      const response = await fetch('http://localhost:4000/api/clients');
      const data = await response.json();
      
      if (response.ok) {
        console.log('✅ API HTTP fonctionne !');
        console.log(`📊 ${data.total || data.count || data.data?.length || 0} client(s) retourné(s) par l'API`);
      } else {
        console.log('❌ Erreur API HTTP:', data.message);
      }
    } catch (error) {
      console.log('⚠️  Serveur HTTP non accessible (normal si pas démarré)');
      console.log('💡 Démarrez le serveur avec: node backend/server-minimal.js');
    }
    
  } catch (error) {
    console.error('\n❌ Erreur lors du test :', error.message);
    console.error('📋 Détails :', {
      code: error.code,
      detail: error.detail,
      hint: error.hint
    });
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Solutions possibles :');
      console.log('1. Démarrez PostgreSQL');
      console.log('2. Vérifiez que la base "Facturation" existe');
      console.log('3. Vérifiez les paramètres de connexion (utilisateur/mot de passe)');
    }
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testClients();
