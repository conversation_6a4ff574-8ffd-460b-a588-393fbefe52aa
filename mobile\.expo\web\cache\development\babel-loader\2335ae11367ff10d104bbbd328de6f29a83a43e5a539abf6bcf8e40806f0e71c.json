{"ast": null, "code": "export { default as Picker } from \"./Picker\";\nexport { default as PickerIOS } from \"./PickerIOS\";", "map": {"version": 3, "names": ["default", "Picker", "PickerIOS"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-native-picker\\picker\\js\\index.js"], "sourcesContent": ["/**\n * @flow\n */\n\nexport {default as Picker} from './Picker';\nexport {default as PickerIOS} from './PickerIOS';\n"], "mappings": "AAIA,SAAQA,OAAO,IAAIC,MAAM;AACzB,SAAQD,OAAO,IAAIE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}