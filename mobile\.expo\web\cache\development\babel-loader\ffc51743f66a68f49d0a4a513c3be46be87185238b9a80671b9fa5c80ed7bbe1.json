{"ast": null, "code": "import * as React from 'react';\nimport * as ReactNativeWeb from \"react-native-web/dist/index\";\nvar Option = function Option(props) {\n  return ReactNativeWeb.unstable_createElement('option', props);\n};\nexport default function PickerItem(_ref) {\n  var color = _ref.color,\n    label = _ref.label,\n    testID = _ref.testID,\n    value = _ref.value,\n    _ref$enabled = _ref.enabled,\n    enabled = _ref$enabled === void 0 ? true : _ref$enabled;\n  return React.createElement(Option, {\n    disabled: enabled === false ? true : undefined,\n    style: {\n      color: color\n    },\n    testID: testID,\n    value: value,\n    label: label\n  }, label);\n}", "map": {"version": 3, "names": ["React", "ReactNativeWeb", "Option", "props", "unstable_createElement", "PickerItem", "_ref", "color", "label", "testID", "value", "_ref$enabled", "enabled", "createElement", "disabled", "undefined", "style"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-native-picker\\picker\\js\\PickerItem.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * @flow\n *\n */\n\nimport type {ColorValue} from 'react-native/Libraries/StyleSheet/StyleSheet';\n\nimport * as React from 'react';\nimport * as ReactNativeWeb from 'react-native-web';\n\ntype Props = {\n  color?: ColorValue,\n  label: string,\n  testID?: string,\n  enabled?: boolean,\n  value?: number | string,\n};\n\nconst Option = (props: any) =>\n  ReactNativeWeb.unstable_createElement('option', props);\n\n/**\n * PickerItem Component for React Native Web\n * @returns\n */\nexport default function PickerItem({\n  color,\n  label,\n  testID,\n  value,\n  enabled = true,\n}: Props): React.Node {\n  return (\n    <Option\n      disabled={enabled === false ? true : undefined}\n      style={{color}}\n      testID={testID}\n      value={value}\n      label={label}>\n      {label}\n    </Option>\n  );\n}\n"], "mappings": "AASA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OACvB,KAAKC,cAAc;AAU1B,IAAMC,MAAM,GAAI,SAAVA,MAAMA,CAAIC,KAAU;EAAA,OACxBF,cAAc,CAACG,sBAAsB,CAAC,QAAQ,EAAED,KAAK,CAAC;AAAA;AAMxD,eAAe,SAASE,UAAUA,CAAAC,IAAA,EAMZ;EAAA,IALpBC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,MAAM,GAAAH,IAAA,CAANG,MAAM;IACNC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IAAAC,YAAA,GAAAL,IAAA,CACLM,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,OAAAA,YAAA;EAEV,OACEX,KAAA,CAAAa,aAAA,CAACX,MAAM;IACLY,QAAQ,EAAEF,OAAO,KAAK,KAAK,GAAG,IAAI,GAAGG,SAAU;IAC/CC,KAAK,EAAE;MAACT,KAAA,EAAAA;IAAK,CAAE;IACfE,MAAM,EAAEA,MAAO;IACfC,KAAK,EAAEA,KAAM;IACbF,KAAK,EAAEA;EAAM,GACZA,KACK,CAAC;AAEb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}