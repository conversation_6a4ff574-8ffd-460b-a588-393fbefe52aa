<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Enregistrement Consommation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #28a745;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        
        .form-select, .form-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 1em;
            transition: border-color 0.3s;
        }
        
        .form-select:focus, .form-input:focus {
            outline: none;
            border-color: #28a745;
        }
        
        .form-input.auto-filled {
            background: #f0fff4;
            border-color: #28a745;
        }
        
        .auto-filled-info {
            font-size: 12px;
            color: #28a745;
            font-style: italic;
            margin-top: 3px;
        }
        
        .save-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            transition: all 0.3s;
        }
        
        .save-button:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .save-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .validation-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .step-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .step-card.validation {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .step-card.error {
            border-color: #dc3545;
            background: #fff8f8;
        }
        
        .step-card h4 {
            margin-bottom: 15px;
        }
        
        .step-card.validation h4 {
            color: #28a745;
        }
        
        .step-card.error h4 {
            color: #dc3545;
        }
        
        .data-structure {
            background: #fff3cd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }
        
        .data-structure h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .data-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
            text-align: center;
            display: none;
        }
        
        .status-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💾 Enregistrement Consommation</h1>
            <p>Démonstration du processus d'enregistrement complet</p>
        </div>
        
        <div class="content">
            <div class="demo-form">
                <h3 style="color: #28a745; margin-bottom: 20px;">📱 Simulation du Formulaire d'Enregistrement</h3>
                
                <div class="form-group">
                    <label class="form-label">📍 Secteur *</label>
                    <select id="secteurSelect" class="form-select" onchange="updateForm()">
                        <option value="">Sélectionner un secteur</option>
                        <option value="1">Centre-Ville</option>
                        <option value="2">Quartier Industriel</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">👤 Client *</label>
                    <select id="clientSelect" class="form-select" onchange="updateForm()" disabled>
                        <option value="">⬆️ Sélectionnez d'abord un secteur</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">📄 Contrat *</label>
                    <input type="text" id="contractInput" class="form-input auto-filled" readonly placeholder="Sélectionnez un client">
                    <div id="contractInfo" class="auto-filled-info" style="display: none;">
                        ✅ Contrat unique sélectionné automatiquement
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">📅 Période *</label>
                    <input type="text" id="periodeInput" class="form-input" placeholder="2024-12" maxlength="7" oninput="updateForm()">
                </div>
                
                <div class="form-group">
                    <label class="form-label">📊 Consommation Précédente (m³)</label>
                    <input type="text" id="consommationPreInput" class="form-input auto-filled" readonly>
                    <div id="consommationPreInfo" class="auto-filled-info" style="display: none;">
                        ✅ Dernière consommation récupérée automatiquement
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">⚡ Consommation Actuelle (m³) *</label>
                    <input type="number" id="consommationActuelleInput" class="form-input" placeholder="Doit être > consommation précédente" oninput="updateForm()">
                </div>
                
                <div class="form-group">
                    <label class="form-label">📅 Nombre de jours</label>
                    <input type="text" id="joursInput" class="form-input auto-filled" readonly>
                    <div id="joursInfo" class="auto-filled-info" style="display: none;">
                        ✅ Calculé automatiquement : période - dernière période
                    </div>
                </div>
                
                <div id="statusMessage" class="status-message"></div>
                
                <button id="saveButton" class="save-button" onclick="simulateEnregistrement()" disabled>
                    💾 Enregistrer la Consommation
                </button>
            </div>
            
            <div class="validation-steps">
                <div class="step-card validation">
                    <h4>✅ Validations Réussies</h4>
                    <ul style="line-height: 1.6; color: #155724;">
                        <li>Tous les champs obligatoires remplis</li>
                        <li>Période valide (mois précédent)</li>
                        <li>Consommation actuelle > précédente</li>
                        <li>Valeurs numériques positives</li>
                    </ul>
                </div>
                
                <div class="step-card error">
                    <h4>❌ Erreurs Possibles</h4>
                    <ul style="line-height: 1.6; color: #721c24;">
                        <li>Champs obligatoires manquants</li>
                        <li>Période future ou invalide</li>
                        <li>Consommation actuelle ≤ précédente</li>
                        <li>Erreur de connexion serveur</li>
                    </ul>
                </div>
            </div>
            
            <div class="data-structure">
                <h3>📊 Structure des Données Enregistrées</h3>
                <div class="data-example">
{
  "periode": "2024-12",
  "consommationPre": 45,
  "consommationActuelle": 50,
  "jours": 31,
  "idClient": 1,
  "idContract": 1,
  "idSecteur": 1,
  "idTech": 1,
  "idTranch": 1,
  "status": "Enregistrée"
}
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-primary" target="_blank">
                    📱 Tester l'Application Réelle
                </a>
                <button class="btn btn-success" onclick="simulateSuccess()">
                    ✅ Simuler Succès
                </button>
                <button class="btn btn-danger" onclick="simulateError()">
                    ❌ Simuler Erreur
                </button>
            </div>
            
            <div style="background: #d4edda; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #28a745;">
                <h3 style="color: #155724; margin-bottom: 15px;">✅ Enregistrement Consommation</h3>
                <ul style="color: #155724; line-height: 1.6;">
                    <li>✅ <strong>Validation complète :</strong> Tous les champs et règles métier</li>
                    <li>✅ <strong>Enregistrement API :</strong> Sauvegarde dans la base de données</li>
                    <li>✅ <strong>Messages détaillés :</strong> Succès et erreurs spécifiques</li>
                    <li>✅ <strong>Réinitialisation :</strong> Formulaire vidé après succès</li>
                    <li>✅ <strong>Mode test :</strong> Simulation si serveur indisponible</li>
                    <li>✅ <strong>Logs complets :</strong> Traçabilité de toutes les étapes</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // Données de test
        const clientsData = {
            '1': [
                { id: 1, nom: 'Benali Fatima', contract: 'Contrat 1 - QR: QR001', lastConsumption: 45 },
                { id: 2, nom: 'Alami Mohammed', contract: 'Contrat 2 - QR: QR002', lastConsumption: 38 }
            ],
            '2': [
                { id: 4, nom: 'Benjelloun Youssef', contract: 'Contrat 4 - QR: QR004', lastConsumption: 67 },
                { id: 5, nom: 'Lahlou Khadija', contract: 'Contrat 5 - QR: QR005', lastConsumption: 41 }
            ]
        };
        
        function updateForm() {
            const secteur = document.getElementById('secteurSelect').value;
            const clientSelect = document.getElementById('clientSelect');
            const periode = document.getElementById('periodeInput').value;
            const consommationActuelle = document.getElementById('consommationActuelleInput').value;
            
            // Mise à jour des clients
            if (secteur) {
                const clients = clientsData[secteur] || [];
                clientSelect.innerHTML = '<option value="">-- Sélectionner un client --</option>';
                clients.forEach(client => {
                    const option = document.createElement('option');
                    option.value = client.id;
                    option.textContent = client.nom;
                    clientSelect.appendChild(option);
                });
                clientSelect.disabled = false;
            } else {
                clientSelect.innerHTML = '<option value="">⬆️ Sélectionnez d\'abord un secteur</option>';
                clientSelect.disabled = true;
            }
            
            // Mise à jour du contrat et consommation précédente
            const clientId = clientSelect.value;
            if (clientId) {
                const client = Object.values(clientsData).flat().find(c => c.id.toString() === clientId);
                if (client) {
                    document.getElementById('contractInput').value = client.contract;
                    document.getElementById('contractInfo').style.display = 'block';
                    
                    document.getElementById('consommationPreInput').value = client.lastConsumption;
                    document.getElementById('consommationPreInfo').style.display = 'block';
                    
                    // Calcul du nombre de jours
                    if (periode && periode.length === 7) {
                        const jours = calculerJours(periode, '2024-11');
                        document.getElementById('joursInput').value = jours;
                        document.getElementById('joursInfo').style.display = 'block';
                    }
                }
            } else {
                document.getElementById('contractInput').value = '';
                document.getElementById('contractInfo').style.display = 'none';
                document.getElementById('consommationPreInput').value = '';
                document.getElementById('consommationPreInfo').style.display = 'none';
                document.getElementById('joursInput').value = '';
                document.getElementById('joursInfo').style.display = 'none';
            }
            
            // Validation du bouton
            const isValid = secteur && clientId && periode && consommationActuelle && 
                           parseFloat(consommationActuelle) > parseFloat(document.getElementById('consommationPreInput').value || 0);
            
            document.getElementById('saveButton').disabled = !isValid;
        }
        
        function calculerJours(periodeActuelle, dernierePeriode) {
            try {
                const [anneeActuelle, moisActuel] = periodeActuelle.split('-').map(Number);
                const [anneeDerniere, moisDernier] = dernierePeriode.split('-').map(Number);
                
                const dateActuelle = new Date(anneeActuelle, moisActuel, 0);
                const dateDerniere = new Date(anneeDerniere, moisDernier, 0);
                
                const differenceMs = dateActuelle.getTime() - dateDerniere.getTime();
                const differenceJours = Math.round(differenceMs / (1000 * 60 * 60 * 24));
                
                return differenceJours > 0 ? differenceJours : 30;
            } catch (error) {
                return 30;
            }
        }
        
        function simulateEnregistrement() {
            const statusMessage = document.getElementById('statusMessage');
            const saveButton = document.getElementById('saveButton');
            
            saveButton.disabled = true;
            saveButton.textContent = '⏳ Enregistrement en cours...';
            
            setTimeout(() => {
                const secteur = document.getElementById('secteurSelect').value;
                const client = document.getElementById('clientSelect').selectedOptions[0]?.textContent;
                const periode = document.getElementById('periodeInput').value;
                const consommation = document.getElementById('consommationActuelleInput').value;
                const jours = document.getElementById('joursInput').value;
                
                statusMessage.className = 'status-message success';
                statusMessage.style.display = 'block';
                statusMessage.innerHTML = `
                    <strong>✅ Succès - Consommation enregistrée avec succès!</strong><br><br>
                    📅 Période: ${periode}<br>
                    👤 Client: ${client}<br>
                    📊 Consommation: ${consommation} m³<br>
                    📅 Jours: ${jours}
                `;
                
                saveButton.textContent = '💾 Enregistrer la Consommation';
                saveButton.disabled = false;
                
                // Réinitialiser le formulaire après 3 secondes
                setTimeout(() => {
                    document.getElementById('secteurSelect').value = '';
                    document.getElementById('clientSelect').value = '';
                    document.getElementById('periodeInput').value = '';
                    document.getElementById('consommationActuelleInput').value = '';
                    updateForm();
                    statusMessage.style.display = 'none';
                }, 3000);
            }, 2000);
        }
        
        function simulateSuccess() {
            // Remplir le formulaire automatiquement
            document.getElementById('secteurSelect').value = '1';
            updateForm();
            setTimeout(() => {
                document.getElementById('clientSelect').value = '1';
                updateForm();
                document.getElementById('periodeInput').value = '2024-12';
                document.getElementById('consommationActuelleInput').value = '50';
                updateForm();
                
                setTimeout(() => {
                    simulateEnregistrement();
                }, 500);
            }, 500);
        }
        
        function simulateError() {
            const statusMessage = document.getElementById('statusMessage');
            
            statusMessage.className = 'status-message error';
            statusMessage.style.display = 'block';
            statusMessage.innerHTML = `
                <strong>❌ Erreur de validation</strong><br><br>
                La consommation actuelle (40 m³) doit être supérieure à la consommation précédente (45 m³)
            `;
            
            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 4000);
        }
    </script>
</body>
</html>
