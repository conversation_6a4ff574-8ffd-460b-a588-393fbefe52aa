{"version": 3, "file": "defaults.js", "sourceRoot": "", "sources": ["../../src/env/defaults.ts"], "names": [], "mappings": ";;;AAAA,mCAAiC;AAEpB,QAAA,UAAU,GAAG,IAAA,gBAAO,EAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAC1C,QAAA,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC;AACrC,QAAA,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;AACpD,sIAAsI;AACzH,QAAA,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,WAAW,CAAC,CAAC,iBAAiB;AACxE,QAAA,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;AAEpD,2FAA2F;AAC9E,QAAA,kBAAkB,GAAG,IAAA,gBAAO,EAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;AACzD,QAAA,IAAI,GAAG,IAAA,gBAAO,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC", "sourcesContent": ["import { boolish } from 'getenv';\n\nexport const EXPO_DEBUG = boolish('EXPO_DEBUG', false);\nexport const host = process.env.HOST || '0.0.0.0';\nexport const sockHost = process.env.WDS_SOCKET_HOST;\n// TODO: /ws throws on native because it expects wds to provide a version number like `2`, to get around this we use a different path.\nexport const sockPath = process.env.WDS_SOCKET_PATH || '/_expo/ws'; // default: '/ws'\nexport const sockPort = process.env.WDS_SOCKET_PORT;\n\n// Source maps are resource heavy and can cause out of memory issue for large source files.\nexport const shouldUseSourceMap = boolish('GENERATE_SOURCEMAP', true);\nexport const isCI = boolish('CI', false);\n"]}