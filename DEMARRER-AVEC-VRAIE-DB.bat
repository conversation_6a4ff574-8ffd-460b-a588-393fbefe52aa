@echo off
title AquaTrack avec Vraie Base de Donnees PostgreSQL
color 0A

echo.
echo ========================================
echo    🗄️ AQUATRACK AVEC VRAIE BASE DE DONNEES
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. ATTENTE (3 secondes)...
timeout /t 3 /nobreak >nul

echo.
echo 🗄️ 3. VERIFICATION DE POSTGRESQL...
echo.
echo Verification que PostgreSQL est demarre...
echo Base de donnees: Facturation
echo Utilisateur: postgres
echo Port: 5432
echo.

echo 🖥️ 4. DEMARRAGE DU SERVEUR AVEC VRAIE BASE DE DONNEES...
echo.
echo Utilisation du serveur avec PostgreSQL et routes secteurs...
echo.
start "🖥️ Backend PostgreSQL" cmd /k "title BACKEND AQUATRACK POSTGRESQL && color 0B && echo ========================================== && echo    🗄️ BACKEND AVEC VRAIE BASE DE DONNEES && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Base: PostgreSQL Facturation && echo ✅ Tables: client, secteur, contract, etc. && echo ✅ Google Maps: Integration complete && echo. && echo 📡 APIs disponibles: && echo    - GET /api/secteurs && echo    - GET /api/secteurs/:id/clients && echo    - GET /api/clients && echo    - POST /api/consommations && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo    - <EMAIL> / Admin123 && echo. && echo 📡 Connexion a PostgreSQL... && echo. && node server.js"

echo.
echo ⏳ 5. ATTENTE DU SERVEUR (12 secondes)...
timeout /t 12 /nobreak >nul

echo.
echo 🌐 6. TEST DES APIs...
echo.
echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs' -TimeoutSec 10; Write-Host '✅ API SECTEURS OK:' $response.count 'secteurs disponibles'; $response.data | ForEach-Object { Write-Host '   -' $_.nom '(ID:' $_.ids ')' } } catch { Write-Host '❌ API SECTEURS ERREUR:' $_.Exception.Message }"

echo.
echo Test API Clients:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/clients' -TimeoutSec 10; Write-Host '✅ API CLIENTS OK:' $response.count 'clients disponibles' } catch { Write-Host '❌ API CLIENTS ERREUR:' $_.Exception.Message }"

echo.
echo 📱 7. DEMARRAGE DE L'APPLICATION MOBILE...
cd mobile
start "📱 Frontend Mobile" cmd /k "title APPLICATION MOBILE AQUATRACK && color 0D && echo ========================================== && echo    📱 APPLICATION MOBILE AVEC VRAIE DB && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4000 && echo ✅ Base de donnees: PostgreSQL Facturation && echo ✅ Fonctionnalites: && echo    - Selection de secteur && echo    - Google Maps integree && echo    - Vrais clients de la base && echo    - Coordonnees GPS automatiques && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 8. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 9. OUVERTURE DES NAVIGATEURS...
start http://localhost:4000/api/secteurs
timeout /t 2 /nobreak >nul
start http://localhost:4000/api/clients
timeout /t 2 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ AQUATRACK AVEC VRAIE DB DEPLOYE !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 📱 Application: http://localhost:19006
echo 🗄️ Base de donnees: PostgreSQL Facturation
echo.
echo 🗺️ FONCTIONNALITES GOOGLE MAPS:
echo.
echo 1. 📍 Selection de Secteur:
echo    - Menu deroulant avec vrais secteurs de la DB
echo    - Coordonnees GPS reelles des secteurs
echo.
echo 2. 🗺️ Carte Google Maps Integree:
echo    - Affichage DANS LA MEME PAGE du formulaire
echo    - Pas de nouvelle fenetre
echo    - Carte centree sur le secteur selectionne
echo    - Iframe responsive et interactive
echo.
echo 3. 👥 Clients du Secteur:
echo    - Vrais clients de votre table client
echo    - Coordonnees GPS generees automatiquement
echo    - Liste detaillee sous la carte
echo    - Clic sur client = ouverture Google Maps
echo.
echo 4. 🌐 Boutons d'Action:
echo    - "Voir tous les clients sur Google Maps"
echo    - "Voir le centre du secteur"
echo    - Ouverture dans Google Maps externe
echo.
echo 📋 INSTRUCTIONS D'UTILISATION:
echo.
echo 1. Allez sur http://localhost:19006
echo 2. Connectez-<NAME_EMAIL> / Tech123
echo 3. Allez dans "Consommation"
echo 4. Selectionnez un secteur dans le menu deroulant
echo 5. La carte Google Maps s'affiche DANS LA MEME PAGE !
echo 6. Tous les clients du secteur apparaissent sur la carte
echo 7. Cliquez sur un client pour voir sa position exacte
echo 8. Utilisez les boutons pour ouvrir Google Maps externe
echo.
echo 🆘 APIs de test:
echo    - http://localhost:4000/api/secteurs
echo    - http://localhost:4000/api/clients
echo    - http://localhost:4000/api/secteurs/1/clients
echo.
echo ⚠️ IMPORTANT:
echo    - PostgreSQL doit etre demarre
echo    - La base 'Facturation' doit exister
echo    - Les tables client et secteur doivent contenir des donnees
echo    - Le champ 'ids' dans client doit referencer secteur.ids
echo.
echo 🔧 Si aucun secteur/client ne s'affiche:
echo    1. Verifiez que PostgreSQL est demarre
echo    2. Verifiez que la base 'Facturation' existe
echo    3. Verifiez que les tables client et secteur existent
echo    4. Verifiez que les clients ont un ids (secteur) valide
echo    5. Consultez les logs dans la fenetre du backend
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
