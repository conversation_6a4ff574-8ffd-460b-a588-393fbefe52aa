@echo off
title Test Google Maps Integrees
color 0A

echo.
echo ========================================
echo    🧪 TEST GOOGLE MAPS INTEGREES
echo ========================================
echo.

echo 🔍 1. TEST DES APIs...
echo.

echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs' -TimeoutSec 5; Write-Host '✅ API SECTEURS OK:' $response.count 'secteurs disponibles'; $response.data | ForEach-Object { Write-Host '   -' $_.nom '(ID:' $_.ids ')' } } catch { Write-Host '❌ API SECTEURS ERREUR' }"

echo.
echo Test API Clients Secteur 1 (Centre-Ville):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs/1/clients' -TimeoutSec 5; Write-Host '✅ API CLIENTS SECTEUR 1 OK:' $response.count 'clients trouves'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom } } catch { Write-Host '❌ API CLIENTS ERREUR' }"

echo.
echo Test API Clients Secteur 3 (Zone Residentielle Nord):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs/3/clients' -TimeoutSec 5; Write-Host '✅ API CLIENTS SECTEUR 3 OK:' $response.count 'clients trouves'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom } } catch { Write-Host '❌ API CLIENTS ERREUR' }"

echo.
echo 🌐 2. OUVERTURE DES TESTS...
start http://localhost:4000/api/secteurs
timeout /t 2 /nobreak >nul
start http://localhost:4000/api/secteurs/1/clients
timeout /t 2 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ TESTS GOOGLE MAPS INTEGREES
echo ========================================
echo.
echo 📋 INSTRUCTIONS DE TEST:
echo.
echo 1. 🔍 Verifiez les APIs dans le navigateur:
echo    - http://localhost:4000/api/secteurs
echo    - http://localhost:4000/api/secteurs/1/clients
echo.
echo 2. 📱 Testez l'application mobile:
echo    - Allez sur http://localhost:19006
echo    - Connectez-<NAME_EMAIL> / Tech123
echo    - Allez dans "Consommation"
echo.
echo 3. 🗺️ Testez la selection de secteur:
echo    - Selectionnez "Centre-Ville" dans le menu deroulant
echo    - La carte Google Maps doit s'afficher DANS LA MEME PAGE
echo    - Vous devriez voir 2 clients sur la carte
echo    - Cliquez sur un client pour voir ses details
echo.
echo 4. 🌐 Testez Google Maps externe:
echo    - Cliquez sur "Voir tous les clients sur Google Maps"
echo    - Google Maps s'ouvre dans un nouvel onglet
echo    - Tous les clients du secteur sont affiches
echo.
echo 📊 SECTEURS DE TEST:
echo    1. Centre-Ville (2 clients)
echo    2. Quartier Industriel (1 client)
echo    3. Zone Residentielle Nord (2 clients)
echo    4. Zone Residentielle Sud (1 client)
echo    5. Quartier Commercial (0 client)
echo.
echo ✅ FONCTIONNALITES IMPLEMENTEES:
echo    - Selection de secteur dans le formulaire
echo    - Carte Google Maps integree dans la meme page
echo    - Liste des clients avec coordonnees GPS
echo    - Bouton pour ouvrir Google Maps externe
echo    - Marqueurs automatiques pour chaque client
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
