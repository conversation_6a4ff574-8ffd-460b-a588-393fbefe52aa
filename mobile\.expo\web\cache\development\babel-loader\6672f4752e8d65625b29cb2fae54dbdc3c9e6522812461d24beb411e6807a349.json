{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nexport default function getActionFromState(state, options) {\n  var _state$index;\n  var _normalizedConfig$scr;\n  var normalizedConfig = options ? createNormalizedConfigItem(options) : {};\n  var routes = state.index != null ? state.routes.slice(0, state.index + 1) : state.routes;\n  if (routes.length === 0) {\n    return undefined;\n  }\n  if (!(routes.length === 1 && routes[0].key === undefined || routes.length === 2 && routes[0].key === undefined && routes[0].name === (normalizedConfig === null || normalizedConfig === void 0 ? void 0 : normalizedConfig.initialRouteName) && routes[1].key === undefined)) {\n    return {\n      type: 'RESET',\n      payload: state\n    };\n  }\n  var route = state.routes[(_state$index = state.index) != null ? _state$index : state.routes.length - 1];\n  var current = route === null || route === void 0 ? void 0 : route.state;\n  var config = normalizedConfig === null || normalizedConfig === void 0 ? void 0 : (_normalizedConfig$scr = normalizedConfig.screens) === null || _normalizedConfig$scr === void 0 ? void 0 : _normalizedConfig$scr[route === null || route === void 0 ? void 0 : route.name];\n  var params = _objectSpread({}, route.params);\n  var payload = route ? {\n    name: route.name,\n    path: route.path,\n    params: params\n  } : undefined;\n  while (current) {\n    var _config, _config2, _config2$screens;\n    if (current.routes.length === 0) {\n      return undefined;\n    }\n    var _routes = current.index != null ? current.routes.slice(0, current.index + 1) : current.routes;\n    var _route = _routes[_routes.length - 1];\n    Object.assign(params, {\n      initial: undefined,\n      screen: undefined,\n      params: undefined,\n      state: undefined\n    });\n    if (_routes.length === 1 && _routes[0].key === undefined) {\n      params.initial = true;\n      params.screen = _route.name;\n    } else if (_routes.length === 2 && _routes[0].key === undefined && _routes[0].name === ((_config = config) === null || _config === void 0 ? void 0 : _config.initialRouteName) && _routes[1].key === undefined) {\n      params.initial = false;\n      params.screen = _route.name;\n    } else {\n      params.state = current;\n      break;\n    }\n    if (_route.state) {\n      params.params = _objectSpread({}, _route.params);\n      params = params.params;\n    } else {\n      params.path = _route.path;\n      params.params = _route.params;\n    }\n    current = _route.state;\n    config = (_config2 = config) === null || _config2 === void 0 ? void 0 : (_config2$screens = _config2.screens) === null || _config2$screens === void 0 ? void 0 : _config2$screens[_route.name];\n  }\n  if (!payload) {\n    return;\n  }\n  return {\n    type: 'NAVIGATE',\n    payload: payload\n  };\n}\nvar createNormalizedConfigItem = function createNormalizedConfigItem(config) {\n  return typeof config === 'object' && config != null ? {\n    initialRouteName: config.initialRouteName,\n    screens: config.screens != null ? createNormalizedConfigs(config.screens) : undefined\n  } : {};\n};\nvar createNormalizedConfigs = function createNormalizedConfigs(options) {\n  return Object.entries(options).reduce(function (acc, _ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      k = _ref2[0],\n      v = _ref2[1];\n    acc[k] = createNormalizedConfigItem(v);\n    return acc;\n  }, {});\n};", "map": {"version": 3, "names": ["getActionFromState", "state", "options", "_state$index", "_normalizedConfig$scr", "normalizedConfig", "createNormalizedConfigItem", "routes", "index", "slice", "length", "undefined", "key", "name", "initialRouteName", "type", "payload", "route", "current", "config", "screens", "params", "_objectSpread", "path", "_config", "_config2", "_config2$screens", "Object", "assign", "initial", "screen", "createNormalizedConfigs", "entries", "reduce", "acc", "_ref", "_ref2", "_slicedToArray", "k", "v"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\core\\src\\getActionFromState.tsx"], "sourcesContent": ["import type {\n  CommonActions,\n  NavigationState,\n  ParamListBase,\n  PartialRoute,\n  PartialState,\n  Route,\n} from '@react-navigation/routers';\n\nimport type { NavigatorScreenParams, PathConfig, PathConfigMap } from './types';\n\ntype ConfigItem = {\n  initialRouteName?: string;\n  screens?: Record<string, ConfigItem>;\n};\n\ntype Options = {\n  initialRouteName?: string;\n  screens: PathConfigMap<object>;\n};\n\ntype NavigateAction<State extends NavigationState> = {\n  type: 'NAVIGATE';\n  payload: {\n    name: string;\n    params?: NavigatorScreenParams<State>;\n    path?: string;\n  };\n};\n\nexport default function getActionFromState(\n  state: PartialState<NavigationState>,\n  options?: Options\n): NavigateAction<NavigationState> | CommonActions.Action | undefined {\n  // Create a normalized configs object which will be easier to use\n  const normalizedConfig = options\n    ? createNormalizedConfigItem(options as PathConfig<object> | string)\n    : {};\n\n  const routes =\n    state.index != null ? state.routes.slice(0, state.index + 1) : state.routes;\n\n  if (routes.length === 0) {\n    return undefined;\n  }\n\n  if (\n    !(\n      (routes.length === 1 && routes[0].key === undefined) ||\n      (routes.length === 2 &&\n        routes[0].key === undefined &&\n        routes[0].name === normalizedConfig?.initialRouteName &&\n        routes[1].key === undefined)\n    )\n  ) {\n    return {\n      type: 'RESET',\n      payload: state,\n    };\n  }\n\n  const route = state.routes[state.index ?? state.routes.length - 1];\n\n  let current: PartialState<NavigationState> | undefined = route?.state;\n  let config: ConfigItem | undefined = normalizedConfig?.screens?.[route?.name];\n  let params = { ...route.params } as NavigatorScreenParams<\n    ParamListBase,\n    NavigationState\n  >;\n\n  let payload = route\n    ? { name: route.name, path: route.path, params }\n    : undefined;\n\n  while (current) {\n    if (current.routes.length === 0) {\n      return undefined;\n    }\n\n    const routes =\n      current.index != null\n        ? current.routes.slice(0, current.index + 1)\n        : current.routes;\n\n    const route: Route<string> | PartialRoute<Route<string>> =\n      routes[routes.length - 1];\n\n    // Explicitly set to override existing value when merging params\n    Object.assign(params, {\n      initial: undefined,\n      screen: undefined,\n      params: undefined,\n      state: undefined,\n    });\n\n    if (routes.length === 1 && routes[0].key === undefined) {\n      params.initial = true;\n      params.screen = route.name;\n    } else if (\n      routes.length === 2 &&\n      routes[0].key === undefined &&\n      routes[0].name === config?.initialRouteName &&\n      routes[1].key === undefined\n    ) {\n      params.initial = false;\n      params.screen = route.name;\n    } else {\n      params.state = current;\n      break;\n    }\n\n    if (route.state) {\n      params.params = { ...route.params };\n      params = params.params as NavigatorScreenParams<\n        ParamListBase,\n        NavigationState\n      >;\n    } else {\n      params.path = route.path;\n      params.params = route.params;\n    }\n\n    current = route.state;\n    config = config?.screens?.[route.name];\n  }\n\n  if (!payload) {\n    return;\n  }\n\n  // Try to construct payload for a `NAVIGATE` action from the state\n  // This lets us preserve the navigation state and not lose it\n  return {\n    type: 'NAVIGATE',\n    payload,\n  };\n}\n\nconst createNormalizedConfigItem = (config: PathConfig<object> | string) =>\n  typeof config === 'object' && config != null\n    ? {\n        initialRouteName: config.initialRouteName,\n        screens:\n          config.screens != null\n            ? createNormalizedConfigs(config.screens)\n            : undefined,\n      }\n    : {};\n\nconst createNormalizedConfigs = (options: PathConfigMap<object>) =>\n  Object.entries(options).reduce<Record<string, ConfigItem>>((acc, [k, v]) => {\n    acc[k] = createNormalizedConfigItem(v);\n    return acc;\n  }, {});\n"], "mappings": ";;;;AA8BA,eAAe,SAASA,kBAAkBA,CACxCC,KAAoC,EACpCC,OAAiB,EACmD;EAAA,IAAAC,YAAA;EAAA,IAAAC,qBAAA;EAEpE,IAAMC,gBAAgB,GAAGH,OAAO,GAC5BI,0BAA0B,CAACJ,OAAO,CAAgC,GAClE,CAAC,CAAC;EAEN,IAAMK,MAAM,GACVN,KAAK,CAACO,KAAK,IAAI,IAAI,GAAGP,KAAK,CAACM,MAAM,CAACE,KAAK,CAAC,CAAC,EAAER,KAAK,CAACO,KAAK,GAAG,CAAC,CAAC,GAAGP,KAAK,CAACM,MAAM;EAE7E,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOC,SAAS;EAClB;EAEA,IACE,EACGJ,MAAM,CAACG,MAAM,KAAK,CAAC,IAAIH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,IAClDJ,MAAM,CAACG,MAAM,KAAK,CAAC,IAClBH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,IAC3BJ,MAAM,CAAC,CAAC,CAAC,CAACM,IAAI,MAAKR,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAES,gBAAgB,KACrDP,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAU,CAC/B,EACD;IACA,OAAO;MACLI,IAAI,EAAE,OAAO;MACbC,OAAO,EAAEf;IACX,CAAC;EACH;EAEA,IAAMgB,KAAK,GAAGhB,KAAK,CAACM,MAAM,EAAAJ,YAAA,GAACF,KAAK,CAACO,KAAK,YAAAL,YAAA,GAAIF,KAAK,CAACM,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAElE,IAAIQ,OAAkD,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEhB,KAAK;EACrE,IAAIkB,MAA8B,GAAGd,gBAAgB,aAAhBA,gBAAgB,wBAAAD,qBAAA,GAAhBC,gBAAgB,CAAEe,OAAO,cAAAhB,qBAAA,uBAAzBA,qBAAA,CAA4Ba,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEJ,IAAI,CAAC;EAC7E,IAAIQ,MAAM,GAAAC,aAAA,KAAQL,KAAK,CAACI,MAAA,CAGvB;EAED,IAAIL,OAAO,GAAGC,KAAK,GACf;IAAEJ,IAAI,EAAEI,KAAK,CAACJ,IAAI;IAAEU,IAAI,EAAEN,KAAK,CAACM,IAAI;IAAEF,MAAA,EAAAA;EAAO,CAAC,GAC9CV,SAAS;EAEb,OAAOO,OAAO,EAAE;IAAA,IAAAM,OAAA,EAAAC,QAAA,EAAAC,gBAAA;IACd,IAAIR,OAAO,CAACX,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;MAC/B,OAAOC,SAAS;IAClB;IAEA,IAAMJ,OAAM,GACVW,OAAO,CAACV,KAAK,IAAI,IAAI,GACjBU,OAAO,CAACX,MAAM,CAACE,KAAK,CAAC,CAAC,EAAES,OAAO,CAACV,KAAK,GAAG,CAAC,CAAC,GAC1CU,OAAO,CAACX,MAAM;IAEpB,IAAMU,MAAkD,GACtDV,OAAM,CAACA,OAAM,CAACG,MAAM,GAAG,CAAC,CAAC;IAG3BiB,MAAM,CAACC,MAAM,CAACP,MAAM,EAAE;MACpBQ,OAAO,EAAElB,SAAS;MAClBmB,MAAM,EAAEnB,SAAS;MACjBU,MAAM,EAAEV,SAAS;MACjBV,KAAK,EAAEU;IACT,CAAC,CAAC;IAEF,IAAIJ,OAAM,CAACG,MAAM,KAAK,CAAC,IAAIH,OAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,EAAE;MACtDU,MAAM,CAACQ,OAAO,GAAG,IAAI;MACrBR,MAAM,CAACS,MAAM,GAAGb,MAAK,CAACJ,IAAI;IAC5B,CAAC,MAAM,IACLN,OAAM,CAACG,MAAM,KAAK,CAAC,IACnBH,OAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,IAC3BJ,OAAM,CAAC,CAAC,CAAC,CAACM,IAAI,OAAAW,OAAA,GAAKL,MAAM,cAAAK,OAAA,uBAANA,OAAA,CAAQV,gBAAgB,KAC3CP,OAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,EAC3B;MACAU,MAAM,CAACQ,OAAO,GAAG,KAAK;MACtBR,MAAM,CAACS,MAAM,GAAGb,MAAK,CAACJ,IAAI;IAC5B,CAAC,MAAM;MACLQ,MAAM,CAACpB,KAAK,GAAGiB,OAAO;MACtB;IACF;IAEA,IAAID,MAAK,CAAChB,KAAK,EAAE;MACfoB,MAAM,CAACA,MAAM,GAAAC,aAAA,KAAQL,MAAK,CAACI,MAAA,CAAQ;MACnCA,MAAM,GAAGA,MAAM,CAACA,MAGf;IACH,CAAC,MAAM;MACLA,MAAM,CAACE,IAAI,GAAGN,MAAK,CAACM,IAAI;MACxBF,MAAM,CAACA,MAAM,GAAGJ,MAAK,CAACI,MAAM;IAC9B;IAEAH,OAAO,GAAGD,MAAK,CAAChB,KAAK;IACrBkB,MAAM,IAAAM,QAAA,GAAGN,MAAM,cAAAM,QAAA,wBAAAC,gBAAA,GAAND,QAAA,CAAQL,OAAO,cAAAM,gBAAA,uBAAfA,gBAAA,CAAkBT,MAAK,CAACJ,IAAI,CAAC;EACxC;EAEA,IAAI,CAACG,OAAO,EAAE;IACZ;EACF;EAIA,OAAO;IACLD,IAAI,EAAE,UAAU;IAChBC,OAAA,EAAAA;EACF,CAAC;AACH;AAEA,IAAMV,0BAA0B,GAAI,SAA9BA,0BAA0BA,CAAIa,MAAmC;EAAA,OACrE,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAI,IAAI,GACxC;IACEL,gBAAgB,EAAEK,MAAM,CAACL,gBAAgB;IACzCM,OAAO,EACLD,MAAM,CAACC,OAAO,IAAI,IAAI,GAClBW,uBAAuB,CAACZ,MAAM,CAACC,OAAO,CAAC,GACvCT;EACR,CAAC,GACD,CAAC,CAAC;AAAA;AAER,IAAMoB,uBAAuB,GAAI,SAA3BA,uBAAuBA,CAAI7B,OAA8B;EAAA,OAC7DyB,MAAM,CAACK,OAAO,CAAC9B,OAAO,CAAC,CAAC+B,MAAM,CAA6B,UAACC,GAAG,EAAAC,IAAA,EAAa;IAAA,IAAAC,KAAA,GAAAC,cAAA,CAALF,IAAA;MAALG,CAAC,GAAAF,KAAA;MAAEG,CAAC,GAAAH,KAAA;IACpEF,GAAG,CAACI,CAAC,CAAC,GAAGhC,0BAA0B,CAACiC,CAAC,CAAC;IACtC,OAAOL,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}