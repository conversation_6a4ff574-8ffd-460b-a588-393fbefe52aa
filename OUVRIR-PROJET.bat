@echo off
title Ouverture Projet AquaTrack
color 0A

echo.
echo ========================================
echo    🚀 OUVERTURE PROJET AQUATRACK
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🖥️ 3. DEMARRA<PERSON> DU SERVEUR BACKEND...
echo.
echo Demarrage du serveur sur port 4002...
start "🎯 Backend AquaTrack" cmd /k "title BACKEND AQUATRACK PORT 4002 && color 0B && echo ========================================== && echo    🎯 BACKEND AQUATRACK PORT 4002 && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ APIs: Secteurs, Clients, Auth && echo ✅ Table secteur: Tous les secteurs && echo ✅ Filtrage: Secteur → Client && echo. && echo 📡 Demarrage du serveur... && echo. && node serveur-port-4002.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (10 secondes)...
timeout /t 10 /nobreak >nul

echo.
echo 🔍 5. TEST DE CONNEXION AU SERVEUR...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 10; Write-Host '✅ SERVEUR OK:' $response.message } catch { Write-Host '❌ SERVEUR NON ACCESSIBLE' }"

echo.
echo 📱 6. DEMARRAGE DE L'APPLICATION MOBILE...
echo.
cd mobile
start "📱 Application Mobile AquaTrack" cmd /k "title APPLICATION MOBILE AQUATRACK && color 0D && echo ========================================== && echo    📱 APPLICATION MOBILE AQUATRACK && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4002 && echo ✅ Champ Secteur: Table secteur && echo ✅ Champ Client: Filtrage par secteur && echo ✅ Cartes Google Maps integrees && echo. && echo 📡 Demarrage de l'application... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 7. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🔍 8. TEST DES APIs PRINCIPALES...
echo.
echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '✅ API Secteurs:' $response.count 'secteurs disponibles' } catch { Write-Host '❌ API Secteurs erreur' }"

echo.
echo Test API Clients:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/clients' -TimeoutSec 5; Write-Host '✅ API Clients:' $response.count 'clients disponibles' } catch { Write-Host '❌ API Clients erreur' }"

echo.
echo 🌐 9. OUVERTURE DES PAGES...
echo.
echo Ouverture de l'application principale...
start http://localhost:19006

echo.
echo Ouverture de la page consommation...
start http://localhost:4002/consommation

echo.
echo ========================================
echo    ✅ PROJET AQUATRACK OUVERT !
echo ========================================
echo.
echo 🎯 URLS PRINCIPALES:
echo.
echo 📱 Application Mobile: http://localhost:19006
echo 📋 Page Consommation: http://localhost:4002/consommation
echo 📊 API Secteurs: http://localhost:4002/api/secteurs
echo 📊 API Clients: http://localhost:4002/api/clients
echo.
echo 🔑 COMPTES DE CONNEXION:
echo.
echo 👤 Technicien:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 👤 Administrateur:
echo    - Email: <EMAIL>
echo    - Mot de passe: Admin123
echo.
echo 📋 FONCTIONNALITES DISPONIBLES:
echo.
echo ✅ Authentification avec redirection par role
echo ✅ Champ Secteur utilise la table secteur
echo ✅ Champ Client filtre par secteur selectionne
echo ✅ Cartes Google Maps integrees
echo ✅ Saisie de consommation complete
echo ✅ Gestion des contrats automatique
echo ✅ Validation des donnees
echo.
echo 🧪 INSTRUCTIONS D'UTILISATION:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous avec un compte de test
echo.
echo 3. 📱 Naviguez dans l'application:
echo    - Dashboard: Vue d'ensemble
echo    - Consommation: Saisie des consommations
echo    - Clients: Gestion des clients
echo    - Factures: Consultation des factures
echo.
echo 4. 🧪 Testez les fonctionnalites:
echo    - Selection de secteur
echo    - Filtrage automatique des clients
echo    - Saisie de consommation
echo    - Visualisation sur carte
echo.
echo ⚠️ NOTES IMPORTANTES:
echo.
echo - Gardez les deux fenetres ouvertes (Backend + Frontend)
echo - Le serveur backend doit rester actif
echo - Les donnees sont simulees en memoire
echo - Toutes les APIs sont fonctionnelles
echo.
echo 🎯 VOTRE PROJET EST MAINTENANT OUVERT ET PRET !
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
