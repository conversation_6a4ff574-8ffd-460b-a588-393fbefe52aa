<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Champ Client en 2ème Position</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .form-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .form-field {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .form-field.first {
            background: #f0f8ff;
            border-color: #007bff;
        }
        
        .form-field.second {
            background: #f0fff0;
            border-color: #28a745;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
        }
        
        .form-field.third {
            background: #fff8f0;
            border-color: #ffc107;
        }
        
        .form-field.other {
            background: #f8f9fa;
            border-color: #6c757d;
        }
        
        .field-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .field-number.second {
            background: #28a745;
        }
        
        .field-number.third {
            background: #ffc107;
            color: #333;
        }
        
        .field-number.other {
            background: #6c757d;
        }
        
        .field-label {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .field-description {
            color: #666;
            font-size: 0.95em;
            line-height: 1.5;
        }
        
        .new-badge {
            background: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }
        
        .feature h4 {
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #155724;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .highlight h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .highlight p {
            color: #856404;
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            .content {
                padding: 20px;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 Champ Client en 2ème Position</h1>
            <p>Démonstration du nouvel ordre des champs dans le formulaire</p>
        </div>
        
        <div class="content">
            <div class="highlight">
                <h3>🎯 Modification Réalisée</h3>
                <p>Le champ <strong>"Client"</strong> est maintenant le <strong>deuxième champ</strong> du formulaire, juste après le champ "Secteur".</p>
            </div>
            
            <div class="form-demo">
                <h3 style="text-align: center; margin-bottom: 30px; color: #333;">📋 Nouvel Ordre des Champs</h3>
                
                <div class="form-field first">
                    <div class="field-label">
                        <span class="field-number">1</span>
                        📍 Secteur <span class="required">*</span>
                    </div>
                    <div class="field-description">
                        Premier champ obligatoire. Sélection du secteur géographique.
                        <br><strong>Exemple:</strong> Centre-Ville, Quartier Industriel, Zone Nord...
                    </div>
                </div>
                
                <div class="form-field second">
                    <div class="field-label">
                        <span class="field-number second">2</span>
                        👤 Client <span class="required">*</span>
                        <span class="new-badge">NOUVEAU</span>
                    </div>
                    <div class="field-description">
                        <strong>Deuxième champ obligatoire</strong> - Se remplit automatiquement selon le secteur sélectionné.
                        <br><strong>Exemple:</strong> "1. Benali Fatima - Setrou", "2. Alami Mohammed - Setrou"
                        <br><strong>Messages d'aide:</strong> "Sélectionnez d'abord un secteur" ou "Aucun client dans ce secteur"
                    </div>
                </div>
                
                <div class="form-field third">
                    <div class="field-label">
                        <span class="field-number third">3</span>
                        ℹ️ Informations du Client
                        <span class="new-badge">NOUVEAU</span>
                    </div>
                    <div class="field-description">
                        Affichage automatique des détails du client sélectionné.
                        <br><strong>Contenu:</strong> Nom, adresse, téléphone, email, secteur, coordonnées GPS
                    </div>
                </div>
                
                <div class="form-field other">
                    <div class="field-label">
                        <span class="field-number other">4</span>
                        📅 Période (YYYY-MM) <span class="required">*</span>
                    </div>
                    <div class="field-description">
                        Période de consommation au format année-mois.
                        <br><strong>Exemple:</strong> 2025-01
                    </div>
                </div>
                
                <div class="form-field other">
                    <div class="field-label">
                        <span class="field-number other">5</span>
                        📋 Contrat
                    </div>
                    <div class="field-description">
                        Contrat associé au client (automatique).
                        <br><strong>Comportement:</strong> Sélection automatique si un seul contrat, menu déroulant si plusieurs
                    </div>
                </div>
                
                <div class="form-field other">
                    <div class="field-label">
                        <span class="field-number other">6</span>
                        💧 Consommation Précédente (m³)
                    </div>
                    <div class="field-description">
                        Dernière consommation enregistrée (automatique).
                    </div>
                </div>
                
                <div class="form-field other">
                    <div class="field-label">
                        <span class="field-number other">7</span>
                        💧 Consommation Actuelle (m³) <span class="required">*</span>
                    </div>
                    <div class="field-description">
                        Nouvelle consommation à saisir (obligatoire).
                        <br><strong>Validation:</strong> Doit être supérieure à la consommation précédente
                    </div>
                </div>
                
                <div class="form-field other">
                    <div class="field-label">
                        <span class="field-number other">8</span>
                        📊 Nombre de jours
                    </div>
                    <div class="field-description">
                        Nombre de jours entre les deux relevés (calculé automatiquement).
                    </div>
                </div>
            </div>
            
            <div class="features">
                <div class="feature">
                    <h4>🎯 Position Stratégique</h4>
                    <p>Le champ Client en 2ème position permet une sélection logique : Secteur → Client → Détails</p>
                </div>
                
                <div class="feature">
                    <h4>🔗 Dépendance Intelligente</h4>
                    <p>Le champ Client se remplit automatiquement selon le secteur sélectionné</p>
                </div>
                
                <div class="feature">
                    <h4>ℹ️ Informations Contextuelles</h4>
                    <p>Affichage immédiat des détails du client sélectionné</p>
                </div>
                
                <div class="feature">
                    <h4>🎨 Design Distinctif</h4>
                    <p>Fond vert clair et bordure verte pour identifier facilement le champ</p>
                </div>
                
                <div class="feature">
                    <h4>💬 Messages d'Aide</h4>
                    <p>Instructions claires selon l'état : aucun secteur, secteur vide, ou clients disponibles</p>
                </div>
                
                <div class="feature">
                    <h4>✅ Validation Intégrée</h4>
                    <p>Champ obligatoire avec vérifications automatiques</p>
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-success" target="_blank">
                    📱 Tester l'Application
                </a>
                <a href="http://localhost:4002/consommation" class="btn btn-primary" target="_blank">
                    🌐 Page Consommation
                </a>
            </div>
            
            <div class="highlight">
                <h3>🧪 Instructions de Test</h3>
                <p>
                    1. Allez sur <strong>http://localhost:19006</strong><br>
                    2. Connectez-vous avec <strong><EMAIL> / Tech123</strong><br>
                    3. Allez dans <strong>"Consommation"</strong><br>
                    4. Observez l'ordre des champs : Secteur → <strong>Client</strong> → Informations → Période...
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // Mettre en évidence le champ Client
        document.addEventListener('DOMContentLoaded', function() {
            const secondField = document.querySelector('.form-field.second');
            
            // Animation d'attention
            setInterval(() => {
                secondField.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    secondField.style.transform = 'scale(1)';
                }, 500);
            }, 3000);
        });
    </script>
</body>
</html>
