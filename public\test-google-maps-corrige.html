<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Google Maps Corrigé</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #28a745;
        }
        
        .maps-test {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin: 20px 0;
            border: 2px solid #4285f4;
        }
        
        .maps-title {
            font-size: 16px;
            font-weight: bold;
            color: #4285f4;
            margin-bottom: 12px;
            text-align: center;
        }
        
        .maps-wrapper {
            position: relative;
            height: 300px;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .maps-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .url-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        
        .comparison-item {
            padding: 20px;
            border-radius: 10px;
            border: 2px solid;
        }
        
        .comparison-item.error {
            border-color: #dc3545;
            background: #fff5f5;
        }
        
        .comparison-item.success {
            border-color: #28a745;
            background: #f0fff4;
        }
        
        .comparison-item h4 {
            margin-bottom: 15px;
        }
        
        .comparison-item.error h4 {
            color: #dc3545;
        }
        
        .comparison-item.success h4 {
            color: #28a745;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }
        
        .btn-maps {
            background: #4285f4;
            color: white;
        }
        
        .btn-maps:hover {
            background: #3367d6;
            transform: translateY(-2px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #28a745;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .status-warning {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Test Google Maps Corrigé</h1>
            <p>Vérification de la correction de l'erreur "API key invalid"</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3 style="color: #28a745; margin-bottom: 20px;">🎯 Test de la Nouvelle URL Google Maps</h3>
                
                <div class="maps-test">
                    <div class="maps-title">
                        🗺️ Google Maps Corrigé - Benali Fatima (33.5731, -7.5898)
                    </div>
                    
                    <div class="maps-wrapper">
                        <iframe
                            class="maps-iframe"
                            src="https://maps.google.com/maps?q=33.5731,-7.5898&hl=fr&z=16&output=embed"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade"
                            title="Test Google Maps Corrigé"
                        ></iframe>
                    </div>
                    
                    <div class="url-display">
                        URL: https://maps.google.com/maps?q=33.5731,-7.5898&hl=fr&z=16&output=embed
                    </div>
                    
                    <div id="mapStatus" style="text-align: center; margin-top: 10px;">
                        <span class="status-indicator status-warning"></span>
                        <span>Chargement de la carte...</span>
                    </div>
                </div>
            </div>
            
            <div class="comparison">
                <div class="comparison-item error">
                    <h4>❌ Avant (Erreur)</h4>
                    <ul style="line-height: 1.6; color: #721c24;">
                        <li><strong>URL :</strong> /embed/v1/view?key=INVALID_KEY</li>
                        <li><strong>Problème :</strong> Clé API invalide</li>
                        <li><strong>Erreur :</strong> "The API key is invalid"</li>
                        <li><strong>Affichage :</strong> Carte noire avec message d'erreur</li>
                        <li><strong>Fonctionnalité :</strong> Non fonctionnelle</li>
                    </ul>
                </div>
                
                <div class="comparison-item success">
                    <h4>✅ Après (Corrigé)</h4>
                    <ul style="line-height: 1.6; color: #155724;">
                        <li><strong>URL :</strong> /maps?q=LAT,LON&output=embed</li>
                        <li><strong>Avantage :</strong> Pas de clé API requise</li>
                        <li><strong>Résultat :</strong> Carte fonctionnelle</li>
                        <li><strong>Affichage :</strong> Google Maps complet</li>
                        <li><strong>Fonctionnalité :</strong> Entièrement fonctionnelle</li>
                    </ul>
                </div>
            </div>
            
            <div class="test-buttons">
                <button class="btn btn-maps" onclick="testDifferentLocation()">
                    🧪 Tester Autre Localisation
                </button>
                <button class="btn btn-primary" onclick="testOpenStreetMap()">
                    🗺️ Tester OpenStreetMap
                </button>
                <a href="http://localhost:19006" class="btn btn-success" target="_blank">
                    📱 Tester l'Application
                </a>
            </div>
            
            <div style="background: #d4edda; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #28a745;">
                <h3 style="color: #155724; margin-bottom: 15px;">✅ Correction Appliquée</h3>
                <ul style="color: #155724; line-height: 1.6;">
                    <li>✅ <strong>URL corrigée :</strong> Utilise l'API publique Google Maps sans clé</li>
                    <li>✅ <strong>Paramètres optimisés :</strong> Langue française, zoom approprié</li>
                    <li>✅ <strong>Fallback ajouté :</strong> OpenStreetMap en cas d'échec</li>
                    <li>✅ <strong>Gestion d'erreur :</strong> Automatique et transparente</li>
                    <li>✅ <strong>Compatibilité :</strong> Fonctionne dans tous les navigateurs</li>
                </ul>
            </div>
            
            <div style="background: #fff3cd; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #ffc107;">
                <h3 style="color: #856404; margin-bottom: 15px;">📋 Instructions de Test</h3>
                <ol style="color: #856404; line-height: 1.8;">
                    <li><strong>Vérifiez la carte ci-dessus :</strong> Elle doit afficher Google Maps sans erreur</li>
                    <li><strong>Testez l'application :</strong> <a href="http://localhost:19006" target="_blank">http://localhost:19006</a></li>
                    <li><strong>Connectez-vous :</strong> <EMAIL> / Tech123</li>
                    <li><strong>Allez dans Consommation :</strong> Sélectionnez secteur et client</li>
                    <li><strong>Cliquez sur le bouton Maps :</strong> La carte doit s'afficher correctement</li>
                </ol>
            </div>
        </div>
    </div>
    
    <script>
        // Vérifier si la carte se charge correctement
        window.addEventListener('load', function() {
            setTimeout(function() {
                const iframe = document.querySelector('iframe[title="Test Google Maps Corrigé"]');
                const statusElement = document.getElementById('mapStatus');
                
                if (iframe) {
                    iframe.onload = function() {
                        statusElement.innerHTML = '<span class="status-indicator status-success"></span><span style="color: #28a745; font-weight: bold;">✅ Carte chargée avec succès !</span>';
                    };
                    
                    iframe.onerror = function() {
                        statusElement.innerHTML = '<span class="status-indicator status-error"></span><span style="color: #dc3545; font-weight: bold;">❌ Erreur de chargement</span>';
                    };
                }
            }, 2000);
        });
        
        // Tester une autre localisation
        function testDifferentLocation() {
            const iframe = document.querySelector('iframe[title="Test Google Maps Corrigé"]');
            const urlDisplay = document.querySelector('.url-display');
            const title = document.querySelector('.maps-title');
            
            // Coordonnées de Benjelloun Youssef (Quartier Industriel)
            const newLat = 33.5831;
            const newLon = -7.5998;
            const newUrl = `https://maps.google.com/maps?q=${newLat},${newLon}&hl=fr&z=16&output=embed`;
            
            iframe.src = newUrl;
            urlDisplay.textContent = `URL: ${newUrl}`;
            title.textContent = `🗺️ Google Maps Corrigé - Benjelloun Youssef (${newLat}, ${newLon})`;
            
            document.getElementById('mapStatus').innerHTML = '<span class="status-indicator status-warning"></span><span>Chargement nouvelle localisation...</span>';
        }
        
        // Tester OpenStreetMap comme alternative
        function testOpenStreetMap() {
            const iframe = document.querySelector('iframe[title="Test Google Maps Corrigé"]');
            const urlDisplay = document.querySelector('.url-display');
            const title = document.querySelector('.maps-title');
            
            const lat = 33.5731;
            const lon = -7.5898;
            const osmUrl = `https://www.openstreetmap.org/export/embed.html?bbox=${lon-0.01},${lat-0.01},${lon+0.01},${lat+0.01}&layer=mapnik&marker=${lat},${lon}`;
            
            iframe.src = osmUrl;
            urlDisplay.textContent = `URL: ${osmUrl}`;
            title.textContent = `🗺️ OpenStreetMap Alternative - Benali Fatima (${lat}, ${lon})`;
            
            document.getElementById('mapStatus').innerHTML = '<span class="status-indicator status-warning"></span><span>Chargement OpenStreetMap...</span>';
        }
    </script>
</body>
</html>
