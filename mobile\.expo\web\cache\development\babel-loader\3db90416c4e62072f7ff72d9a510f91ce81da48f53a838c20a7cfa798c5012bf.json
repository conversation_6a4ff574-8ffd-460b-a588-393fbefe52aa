{"ast": null, "code": "import { NavigationContext, useRoute } from '@react-navigation/core';\nimport * as React from 'react';\nfunction getScrollableNode(ref) {\n  if (ref.current == null) {\n    return null;\n  }\n  if ('scrollToTop' in ref.current || 'scrollTo' in ref.current || 'scrollToOffset' in ref.current || 'scrollResponderScrollTo' in ref.current) {\n    return ref.current;\n  } else if ('getScrollResponder' in ref.current) {\n    return ref.current.getScrollResponder();\n  } else if ('getNode' in ref.current) {\n    return ref.current.getNode();\n  } else {\n    return ref.current;\n  }\n}\nexport default function useScrollToTop(ref) {\n  var navigation = React.useContext(NavigationContext);\n  var route = useRoute();\n  if (navigation === undefined) {\n    throw new Error(\"Couldn't find a navigation object. Is your component inside NavigationContainer?\");\n  }\n  React.useEffect(function () {\n    var tabNavigations = [];\n    var currentNavigation = navigation;\n    while (currentNavigation) {\n      if (currentNavigation.getState().type === 'tab') {\n        tabNavigations.push(currentNavigation);\n      }\n      currentNavigation = currentNavigation.getParent();\n    }\n    if (tabNavigations.length === 0) {\n      return;\n    }\n    var unsubscribers = tabNavigations.map(function (tab) {\n      return tab.addListener('tabPress', function (e) {\n        var isFocused = navigation.isFocused();\n        var isFirst = tabNavigations.includes(navigation) || navigation.getState().routes[0].key === route.key;\n        requestAnimationFrame(function () {\n          var scrollable = getScrollableNode(ref);\n          if (isFocused && isFirst && scrollable && !e.defaultPrevented) {\n            if ('scrollToTop' in scrollable) {\n              scrollable.scrollToTop();\n            } else if ('scrollTo' in scrollable) {\n              scrollable.scrollTo({\n                y: 0,\n                animated: true\n              });\n            } else if ('scrollToOffset' in scrollable) {\n              scrollable.scrollToOffset({\n                offset: 0,\n                animated: true\n              });\n            } else if ('scrollResponderScrollTo' in scrollable) {\n              scrollable.scrollResponderScrollTo({\n                y: 0,\n                animated: true\n              });\n            }\n          }\n        });\n      });\n    });\n    return function () {\n      unsubscribers.forEach(function (unsubscribe) {\n        return unsubscribe();\n      });\n    };\n  }, [navigation, ref, route.key]);\n}", "map": {"version": 3, "names": ["NavigationContext", "useRoute", "React", "getScrollableNode", "ref", "current", "getScrollResponder", "getNode", "useScrollToTop", "navigation", "useContext", "route", "undefined", "Error", "useEffect", "tabNavigations", "currentNavigation", "getState", "type", "push", "getParent", "length", "unsubscribers", "map", "tab", "addListener", "e", "isFocused", "<PERSON><PERSON><PERSON><PERSON>", "includes", "routes", "key", "requestAnimationFrame", "scrollable", "defaultPrevented", "scrollToTop", "scrollTo", "y", "animated", "scrollToOffset", "offset", "scrollResponderScrollTo", "for<PERSON>ach", "unsubscribe"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\native\\src\\useScrollToTop.tsx"], "sourcesContent": ["import {\n  EventArg,\n  NavigationContext,\n  NavigationProp,\n  ParamListBase,\n  useRoute,\n} from '@react-navigation/core';\nimport * as React from 'react';\nimport type { ScrollView } from 'react-native';\n\ntype ScrollOptions = { x?: number; y?: number; animated?: boolean };\n\ntype ScrollableView =\n  | { scrollToTop(): void }\n  | { scrollTo(options: ScrollOptions): void }\n  | { scrollToOffset(options: { offset?: number; animated?: boolean }): void }\n  | { scrollResponderScrollTo(options: ScrollOptions): void };\n\ntype ScrollableWrapper =\n  | { getScrollResponder(): React.ReactNode | ScrollView }\n  | { getNode(): ScrollableView }\n  | ScrollableView;\n\nfunction getScrollableNode(ref: React.RefObject<ScrollableWrapper>) {\n  if (ref.current == null) {\n    return null;\n  }\n\n  if (\n    'scrollToTop' in ref.current ||\n    'scrollTo' in ref.current ||\n    'scrollToOffset' in ref.current ||\n    'scrollResponderScrollTo' in ref.current\n  ) {\n    // This is already a scrollable node.\n    return ref.current;\n  } else if ('getScrollResponder' in ref.current) {\n    // If the view is a wrapper like FlatList, SectionList etc.\n    // We need to use `getScrollResponder` to get access to the scroll responder\n    return ref.current.getScrollResponder();\n  } else if ('getNode' in ref.current) {\n    // When a `ScrollView` is wraped in `Animated.createAnimatedComponent`\n    // we need to use `getNode` to get the ref to the actual scrollview.\n    // Note that `getNode` is deprecated in newer versions of react-native\n    // this is why we check if we already have a scrollable node above.\n    return ref.current.getNode();\n  } else {\n    return ref.current;\n  }\n}\n\nexport default function useScrollToTop(\n  ref: React.RefObject<ScrollableWrapper>\n) {\n  const navigation = React.useContext(NavigationContext);\n  const route = useRoute();\n\n  if (navigation === undefined) {\n    throw new Error(\n      \"Couldn't find a navigation object. Is your component inside NavigationContainer?\"\n    );\n  }\n\n  React.useEffect(() => {\n    const tabNavigations: NavigationProp<ParamListBase>[] = [];\n    let currentNavigation = navigation;\n    // If the screen is nested inside multiple tab navigators, we should scroll to top for any of them\n    // So we need to find all the parent tab navigators and add the listeners there\n    while (currentNavigation) {\n      if (currentNavigation.getState().type === 'tab') {\n        tabNavigations.push(currentNavigation);\n      }\n\n      currentNavigation = currentNavigation.getParent();\n    }\n\n    if (tabNavigations.length === 0) {\n      return;\n    }\n\n    const unsubscribers = tabNavigations.map((tab) => {\n      return tab.addListener(\n        // We don't wanna import tab types here to avoid extra deps\n        // in addition, there are multiple tab implementations\n        // @ts-expect-error\n        'tabPress',\n        (e: EventArg<'tabPress', true>) => {\n          // We should scroll to top only when the screen is focused\n          const isFocused = navigation.isFocused();\n\n          // In a nested stack navigator, tab press resets the stack to first screen\n          // So we should scroll to top only when we are on first screen\n          const isFirst =\n            tabNavigations.includes(navigation) ||\n            navigation.getState().routes[0].key === route.key;\n\n          // Run the operation in the next frame so we're sure all listeners have been run\n          // This is necessary to know if preventDefault() has been called\n          requestAnimationFrame(() => {\n            const scrollable = getScrollableNode(ref) as ScrollableWrapper;\n\n            if (isFocused && isFirst && scrollable && !e.defaultPrevented) {\n              if ('scrollToTop' in scrollable) {\n                scrollable.scrollToTop();\n              } else if ('scrollTo' in scrollable) {\n                scrollable.scrollTo({ y: 0, animated: true });\n              } else if ('scrollToOffset' in scrollable) {\n                scrollable.scrollToOffset({ offset: 0, animated: true });\n              } else if ('scrollResponderScrollTo' in scrollable) {\n                scrollable.scrollResponderScrollTo({ y: 0, animated: true });\n              }\n            }\n          });\n        }\n      );\n    });\n\n    return () => {\n      unsubscribers.forEach((unsubscribe) => unsubscribe());\n    };\n  }, [navigation, ref, route.key]);\n}\n"], "mappings": "AAAA,SAEEA,iBAAiB,EAGjBC,QAAQ,QACH,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAgB9B,SAASC,iBAAiBA,CAACC,GAAuC,EAAE;EAClE,IAAIA,GAAG,CAACC,OAAO,IAAI,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,IACE,aAAa,IAAID,GAAG,CAACC,OAAO,IAC5B,UAAU,IAAID,GAAG,CAACC,OAAO,IACzB,gBAAgB,IAAID,GAAG,CAACC,OAAO,IAC/B,yBAAyB,IAAID,GAAG,CAACC,OAAO,EACxC;IAEA,OAAOD,GAAG,CAACC,OAAO;EACpB,CAAC,MAAM,IAAI,oBAAoB,IAAID,GAAG,CAACC,OAAO,EAAE;IAG9C,OAAOD,GAAG,CAACC,OAAO,CAACC,kBAAkB,EAAE;EACzC,CAAC,MAAM,IAAI,SAAS,IAAIF,GAAG,CAACC,OAAO,EAAE;IAKnC,OAAOD,GAAG,CAACC,OAAO,CAACE,OAAO,EAAE;EAC9B,CAAC,MAAM;IACL,OAAOH,GAAG,CAACC,OAAO;EACpB;AACF;AAEA,eAAe,SAASG,cAAcA,CACpCJ,GAAuC,EACvC;EACA,IAAMK,UAAU,GAAGP,KAAK,CAACQ,UAAU,CAACV,iBAAiB,CAAC;EACtD,IAAMW,KAAK,GAAGV,QAAQ,EAAE;EAExB,IAAIQ,UAAU,KAAKG,SAAS,EAAE;IAC5B,MAAM,IAAIC,KAAK,CACb,kFAAkF,CACnF;EACH;EAEAX,KAAK,CAACY,SAAS,CAAC,YAAM;IACpB,IAAMC,cAA+C,GAAG,EAAE;IAC1D,IAAIC,iBAAiB,GAAGP,UAAU;IAGlC,OAAOO,iBAAiB,EAAE;MACxB,IAAIA,iBAAiB,CAACC,QAAQ,EAAE,CAACC,IAAI,KAAK,KAAK,EAAE;QAC/CH,cAAc,CAACI,IAAI,CAACH,iBAAiB,CAAC;MACxC;MAEAA,iBAAiB,GAAGA,iBAAiB,CAACI,SAAS,EAAE;IACnD;IAEA,IAAIL,cAAc,CAACM,MAAM,KAAK,CAAC,EAAE;MAC/B;IACF;IAEA,IAAMC,aAAa,GAAGP,cAAc,CAACQ,GAAG,CAAE,UAAAC,GAAG,EAAK;MAChD,OAAOA,GAAG,CAACC,WAAW,CAIpB,UAAU,EACT,UAAAC,CAA6B,EAAK;QAEjC,IAAMC,SAAS,GAAGlB,UAAU,CAACkB,SAAS,EAAE;QAIxC,IAAMC,OAAO,GACXb,cAAc,CAACc,QAAQ,CAACpB,UAAU,CAAC,IACnCA,UAAU,CAACQ,QAAQ,EAAE,CAACa,MAAM,CAAC,CAAC,CAAC,CAACC,GAAG,KAAKpB,KAAK,CAACoB,GAAG;QAInDC,qBAAqB,CAAC,YAAM;UAC1B,IAAMC,UAAU,GAAG9B,iBAAiB,CAACC,GAAG,CAAsB;UAE9D,IAAIuB,SAAS,IAAIC,OAAO,IAAIK,UAAU,IAAI,CAACP,CAAC,CAACQ,gBAAgB,EAAE;YAC7D,IAAI,aAAa,IAAID,UAAU,EAAE;cAC/BA,UAAU,CAACE,WAAW,EAAE;YAC1B,CAAC,MAAM,IAAI,UAAU,IAAIF,UAAU,EAAE;cACnCA,UAAU,CAACG,QAAQ,CAAC;gBAAEC,CAAC,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAK,CAAC,CAAC;YAC/C,CAAC,MAAM,IAAI,gBAAgB,IAAIL,UAAU,EAAE;cACzCA,UAAU,CAACM,cAAc,CAAC;gBAAEC,MAAM,EAAE,CAAC;gBAAEF,QAAQ,EAAE;cAAK,CAAC,CAAC;YAC1D,CAAC,MAAM,IAAI,yBAAyB,IAAIL,UAAU,EAAE;cAClDA,UAAU,CAACQ,uBAAuB,CAAC;gBAAEJ,CAAC,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAK,CAAC,CAAC;YAC9D;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CACF;IACH,CAAC,CAAC;IAEF,OAAO,YAAM;MACXhB,aAAa,CAACoB,OAAO,CAAE,UAAAC,WAAW;QAAA,OAAKA,WAAW,EAAE;MAAA,EAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAAClC,UAAU,EAAEL,GAAG,EAAEO,KAAK,CAACoB,GAAG,CAAC,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}