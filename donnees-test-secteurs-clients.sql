-- Script SQL pour insérer des données de test dans les tables secteur et client
-- Base de données: Facturation

-- ========================================
-- 1. INSERTION DES SECTEURS
-- ========================================

-- Vérifier si la table secteur existe et la créer si nécessaire
CREATE TABLE IF NOT EXISTS secteur (
    ids SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

-- Insérer des secteurs de test avec coordonnées GPS réelles (région de Casablanca, Maroc)
INSERT INTO secteur (nom, latitude, longitude) VALUES
('Centre-Ville', 33.5731, -7.5898),
('Quartier Industriel', 33.5831, -7.5998),
('Zone Résidentielle Nord', 33.5931, -7.6098),
('Zone Résidentielle Sud', 33.5631, -7.5798),
('Quartier Commercial', 33.5531, -7.5698),
('Zone Universitaire', 33.5431, -7.5598),
('Quartier Administratif', 33.5331, -7.5498)
ON CONFLICT (ids) DO NOTHING;

-- ========================================
-- 2. MISE À JOUR DE LA TABLE CLIENT
-- ========================================

-- Vérifier si la table client existe et ajouter la colonne ids si nécessaire
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='client' AND column_name='ids') THEN
        ALTER TABLE client ADD COLUMN ids INTEGER REFERENCES secteur(ids);
    END IF;
END $$;

-- ========================================
-- 3. INSERTION DES CLIENTS DE TEST
-- ========================================

-- Insérer des clients de test répartis dans différents secteurs
INSERT INTO client (nom, prenom, adresse, ville, tel, email, statut, ids) VALUES
-- Clients du Centre-Ville (secteur 1)
('Benali', 'Fatima', '45 Avenue Hassan II, près de l''école Omar Ibn Al Khattab', 'Setrou', '0612345678', '<EMAIL>', 'Actif', 1),
('Alami', 'Mohammed', '12 Rue des Oliviers, Quartier Administratif', 'Setrou', '0623456789', '<EMAIL>', 'Actif', 1),
('Idrissi', 'Aicha', '78 Boulevard Mohammed V, Immeuble Al Baraka', 'Setrou', '0634567890', '<EMAIL>', 'Actif', 1),

-- Clients du Quartier Industriel (secteur 2)
('Tazi', 'Youssef', '23 Zone Industrielle, Lot 15', 'Setrou', '0645678901', '<EMAIL>', 'Actif', 2),
('Benjelloun', 'Khadija', '56 Rue de l''Industrie, Bloc C', 'Setrou', '0656789012', '<EMAIL>', 'Actif', 2),

-- Clients de la Zone Résidentielle Nord (secteur 3)
('Fassi', 'Omar', '89 Résidence Al Andalous, Villa 15', 'Setrou', '0667890123', '<EMAIL>', 'Actif', 3),
('Lahlou', 'Zineb', '34 Lotissement Nour, Maison 8', 'Setrou', '0678901234', '<EMAIL>', 'Actif', 3),
('Chraibi', 'Hassan', '67 Cité Al Wifaq, Appartement 12', 'Setrou', '0689012345', '<EMAIL>', 'Actif', 3),

-- Clients de la Zone Résidentielle Sud (secteur 4)
('Kettani', 'Malika', '12 Hay Al Massira, Rue 5', 'Setrou', '0690123456', '<EMAIL>', 'Actif', 4),
('Berrada', 'Abdelkader', '45 Douar Al Baraka, Maison 23', 'Setrou', '0601234567', '<EMAIL>', 'Actif', 4),

-- Clients du Quartier Commercial (secteur 5)
('Amrani', 'Souad', '78 Avenue du Commerce, Local 45', 'Setrou', '0612345679', '<EMAIL>', 'Actif', 5),
('Ziani', 'Rachid', '23 Marché Central, Boutique 12', 'Setrou', '0623456780', '<EMAIL>', 'Actif', 5),

-- Clients de la Zone Universitaire (secteur 6)
('Mansouri', 'Leila', '56 Cité Universitaire, Bloc A', 'Setrou', '0634567891', '<EMAIL>', 'Actif', 6),
('Tahiri', 'Karim', '89 Résidence Étudiants, Chambre 234', 'Setrou', '0645678902', '<EMAIL>', 'Actif', 6),

-- Clients du Quartier Administratif (secteur 7)
('Benkirane', 'Nadia', '12 Avenue des Ministères, Immeuble 5', 'Setrou', '0656789013', '<EMAIL>', 'Actif', 7),
('Ouali', 'Mustapha', '34 Rue de l''Administration, Bureau 8', 'Setrou', '0667890124', '<EMAIL>', 'Actif', 7)

ON CONFLICT (idclient) DO NOTHING;

-- ========================================
-- 4. VÉRIFICATION DES DONNÉES INSÉRÉES
-- ========================================

-- Afficher le nombre de secteurs
SELECT 'SECTEURS INSÉRÉS:' as info, COUNT(*) as nombre FROM secteur;

-- Afficher le nombre de clients par secteur
SELECT 
    s.nom as secteur,
    COUNT(c.idclient) as nombre_clients
FROM secteur s
LEFT JOIN client c ON s.ids = c.ids
GROUP BY s.ids, s.nom
ORDER BY s.ids;

-- Afficher tous les secteurs avec leurs coordonnées
SELECT 
    ids,
    nom,
    latitude,
    longitude
FROM secteur
ORDER BY ids;

-- Afficher quelques clients avec leur secteur
SELECT 
    c.nom,
    c.prenom,
    c.adresse,
    s.nom as secteur
FROM client c
LEFT JOIN secteur s ON c.ids = s.ids
ORDER BY s.nom, c.nom
LIMIT 10;

-- ========================================
-- 5. REQUÊTES DE TEST POUR L'APPLICATION
-- ========================================

-- Test: Récupérer tous les secteurs (pour l'API /api/secteurs)
-- SELECT ids, nom, latitude, longitude FROM secteur ORDER BY nom;

-- Test: Récupérer les clients d'un secteur spécifique (pour l'API /api/secteurs/:id/clients)
-- SELECT 
--     c.idclient,
--     c.nom,
--     c.prenom,
--     c.adresse,
--     c.ville,
--     c.tel,
--     c.email,
--     c.statut,
--     c.ids,
--     s.nom as secteur_nom,
--     s.latitude as secteur_latitude,
--     s.longitude as secteur_longitude,
--     -- Générer des coordonnées aléatoires autour du centre du secteur
--     s.latitude + (RANDOM() - 0.5) * 0.01 as latitude,
--     s.longitude + (RANDOM() - 0.5) * 0.01 as longitude
-- FROM client c
-- INNER JOIN secteur s ON c.ids = s.ids
-- WHERE c.ids = 1
-- ORDER BY c.nom, c.prenom;

COMMIT;
