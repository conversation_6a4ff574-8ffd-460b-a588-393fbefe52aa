{"ast": null, "code": "import UnimplementedView from \"../../modules/UnimplementedView\";\nexport default UnimplementedView;", "map": {"version": 3, "names": ["UnimplementedView"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/TouchableNativeFeedback/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport UnimplementedView from '../../modules/UnimplementedView';\nexport default UnimplementedView;"], "mappings": "AASA,OAAOA,iBAAiB;AACxB,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}