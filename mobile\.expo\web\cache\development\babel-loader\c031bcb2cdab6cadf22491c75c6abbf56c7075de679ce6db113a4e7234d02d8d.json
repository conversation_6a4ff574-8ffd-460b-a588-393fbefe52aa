{"ast": null, "code": "'use client';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport invariant from 'fbjs/lib/invariant';\nimport canUseDOM from \"../../modules/canUseDom\";\nvar dimensions = {\n  window: {\n    fontScale: 1,\n    height: 0,\n    scale: 1,\n    width: 0\n  },\n  screen: {\n    fontScale: 1,\n    height: 0,\n    scale: 1,\n    width: 0\n  }\n};\nvar listeners = {};\nvar shouldInit = canUseDOM;\nfunction update() {\n  if (!canUseDOM) {\n    return;\n  }\n  var win = window;\n  var height;\n  var width;\n  if (win.visualViewport) {\n    var visualViewport = win.visualViewport;\n    height = Math.round(visualViewport.height * visualViewport.scale);\n    width = Math.round(visualViewport.width * visualViewport.scale);\n  } else {\n    var docEl = win.document.documentElement;\n    height = docEl.clientHeight;\n    width = docEl.clientWidth;\n  }\n  dimensions.window = {\n    fontScale: 1,\n    height: height,\n    scale: win.devicePixelRatio || 1,\n    width: width\n  };\n  dimensions.screen = {\n    fontScale: 1,\n    height: win.screen.height,\n    scale: win.devicePixelRatio || 1,\n    width: win.screen.width\n  };\n}\nfunction handleResize() {\n  update();\n  if (Array.isArray(listeners['change'])) {\n    listeners['change'].forEach(function (handler) {\n      return handler(dimensions);\n    });\n  }\n}\nvar Dimensions = function () {\n  function Dimensions() {\n    _classCallCheck(this, Dimensions);\n  }\n  return _createClass(Dimensions, null, [{\n    key: \"get\",\n    value: function get(dimension) {\n      if (shouldInit) {\n        shouldInit = false;\n        update();\n      }\n      invariant(dimensions[dimension], \"No dimension set for key \" + dimension);\n      return dimensions[dimension];\n    }\n  }, {\n    key: \"set\",\n    value: function set(initialDimensions) {\n      if (initialDimensions) {\n        if (canUseDOM) {\n          invariant(false, 'Dimensions cannot be set in the browser');\n        } else {\n          if (initialDimensions.screen != null) {\n            dimensions.screen = initialDimensions.screen;\n          }\n          if (initialDimensions.window != null) {\n            dimensions.window = initialDimensions.window;\n          }\n        }\n      }\n    }\n  }, {\n    key: \"addEventListener\",\n    value: function addEventListener(type, handler) {\n      var _this = this;\n      listeners[type] = listeners[type] || [];\n      listeners[type].push(handler);\n      return {\n        remove: function remove() {\n          _this.removeEventListener(type, handler);\n        }\n      };\n    }\n  }, {\n    key: \"removeEventListener\",\n    value: function removeEventListener(type, handler) {\n      if (Array.isArray(listeners[type])) {\n        listeners[type] = listeners[type].filter(function (_handler) {\n          return _handler !== handler;\n        });\n      }\n    }\n  }]);\n}();\nexport { Dimensions as default };\nif (canUseDOM) {\n  if (window.visualViewport) {\n    window.visualViewport.addEventListener('resize', handleResize, false);\n  } else {\n    window.addEventListener('resize', handleResize, false);\n  }\n}", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "invariant", "canUseDOM", "dimensions", "window", "fontScale", "height", "scale", "width", "screen", "listeners", "shouldInit", "update", "win", "visualViewport", "Math", "round", "docEl", "document", "documentElement", "clientHeight", "clientWidth", "devicePixelRatio", "handleResize", "Array", "isArray", "for<PERSON>ach", "handler", "Dimensions", "key", "value", "get", "dimension", "set", "initialDimensions", "addEventListener", "type", "_this", "push", "remove", "removeEventListener", "filter", "_handler", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/Dimensions/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport invariant from 'fbjs/lib/invariant';\nimport canUseDOM from '../../modules/canUseDom';\nvar dimensions = {\n  window: {\n    fontScale: 1,\n    height: 0,\n    scale: 1,\n    width: 0\n  },\n  screen: {\n    fontScale: 1,\n    height: 0,\n    scale: 1,\n    width: 0\n  }\n};\nvar listeners = {};\nvar shouldInit = canUseDOM;\nfunction update() {\n  if (!canUseDOM) {\n    return;\n  }\n  var win = window;\n  var height;\n  var width;\n\n  /**\n   * iOS does not update viewport dimensions on keyboard open/close.\n   * window.visualViewport(https://developer.mozilla.org/en-US/docs/Web/API/VisualViewport)\n   * is used instead of document.documentElement.clientHeight (which remains as a fallback)\n   */\n  if (win.visualViewport) {\n    var visualViewport = win.visualViewport;\n    /**\n     * We are multiplying by scale because height and width from visual viewport\n     * also react to pinch zoom, and become smaller when zoomed. But it is not desired\n     * behaviour, since originally documentElement client height and width were used,\n     * and they do not react to pinch zoom.\n     */\n    height = Math.round(visualViewport.height * visualViewport.scale);\n    width = Math.round(visualViewport.width * visualViewport.scale);\n  } else {\n    var docEl = win.document.documentElement;\n    height = docEl.clientHeight;\n    width = docEl.clientWidth;\n  }\n  dimensions.window = {\n    fontScale: 1,\n    height,\n    scale: win.devicePixelRatio || 1,\n    width\n  };\n  dimensions.screen = {\n    fontScale: 1,\n    height: win.screen.height,\n    scale: win.devicePixelRatio || 1,\n    width: win.screen.width\n  };\n}\nfunction handleResize() {\n  update();\n  if (Array.isArray(listeners['change'])) {\n    listeners['change'].forEach(handler => handler(dimensions));\n  }\n}\nexport default class Dimensions {\n  static get(dimension) {\n    if (shouldInit) {\n      shouldInit = false;\n      update();\n    }\n    invariant(dimensions[dimension], \"No dimension set for key \" + dimension);\n    return dimensions[dimension];\n  }\n  static set(initialDimensions) {\n    if (initialDimensions) {\n      if (canUseDOM) {\n        invariant(false, 'Dimensions cannot be set in the browser');\n      } else {\n        if (initialDimensions.screen != null) {\n          dimensions.screen = initialDimensions.screen;\n        }\n        if (initialDimensions.window != null) {\n          dimensions.window = initialDimensions.window;\n        }\n      }\n    }\n  }\n  static addEventListener(type, handler) {\n    listeners[type] = listeners[type] || [];\n    listeners[type].push(handler);\n    return {\n      remove: () => {\n        this.removeEventListener(type, handler);\n      }\n    };\n  }\n  static removeEventListener(type, handler) {\n    if (Array.isArray(listeners[type])) {\n      listeners[type] = listeners[type].filter(_handler => _handler !== handler);\n    }\n  }\n}\nif (canUseDOM) {\n  if (window.visualViewport) {\n    window.visualViewport.addEventListener('resize', handleResize, false);\n  } else {\n    window.addEventListener('resize', handleResize, false);\n  }\n}"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAEb,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,SAAS;AAChB,IAAIC,UAAU,GAAG;EACfC,MAAM,EAAE;IACNC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE;EACT,CAAC;EACDC,MAAM,EAAE;IACNJ,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,UAAU,GAAGT,SAAS;AAC1B,SAASU,MAAMA,CAAA,EAAG;EAChB,IAAI,CAACV,SAAS,EAAE;IACd;EACF;EACA,IAAIW,GAAG,GAAGT,MAAM;EAChB,IAAIE,MAAM;EACV,IAAIE,KAAK;EAOT,IAAIK,GAAG,CAACC,cAAc,EAAE;IACtB,IAAIA,cAAc,GAAGD,GAAG,CAACC,cAAc;IAOvCR,MAAM,GAAGS,IAAI,CAACC,KAAK,CAACF,cAAc,CAACR,MAAM,GAAGQ,cAAc,CAACP,KAAK,CAAC;IACjEC,KAAK,GAAGO,IAAI,CAACC,KAAK,CAACF,cAAc,CAACN,KAAK,GAAGM,cAAc,CAACP,KAAK,CAAC;EACjE,CAAC,MAAM;IACL,IAAIU,KAAK,GAAGJ,GAAG,CAACK,QAAQ,CAACC,eAAe;IACxCb,MAAM,GAAGW,KAAK,CAACG,YAAY;IAC3BZ,KAAK,GAAGS,KAAK,CAACI,WAAW;EAC3B;EACAlB,UAAU,CAACC,MAAM,GAAG;IAClBC,SAAS,EAAE,CAAC;IACZC,MAAM,EAANA,MAAM;IACNC,KAAK,EAAEM,GAAG,CAACS,gBAAgB,IAAI,CAAC;IAChCd,KAAK,EAALA;EACF,CAAC;EACDL,UAAU,CAACM,MAAM,GAAG;IAClBJ,SAAS,EAAE,CAAC;IACZC,MAAM,EAAEO,GAAG,CAACJ,MAAM,CAACH,MAAM;IACzBC,KAAK,EAAEM,GAAG,CAACS,gBAAgB,IAAI,CAAC;IAChCd,KAAK,EAAEK,GAAG,CAACJ,MAAM,CAACD;EACpB,CAAC;AACH;AACA,SAASe,YAAYA,CAAA,EAAG;EACtBX,MAAM,CAAC,CAAC;EACR,IAAIY,KAAK,CAACC,OAAO,CAACf,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE;IACtCA,SAAS,CAAC,QAAQ,CAAC,CAACgB,OAAO,CAAC,UAAAC,OAAO;MAAA,OAAIA,OAAO,CAACxB,UAAU,CAAC;IAAA,EAAC;EAC7D;AACF;AAAC,IACoByB,UAAU;EAAA,SAAAA,WAAA;IAAA7B,eAAA,OAAA6B,UAAA;EAAA;EAAA,OAAA5B,YAAA,CAAA4B,UAAA;IAAAC,GAAA;IAAAC,KAAA,EAC7B,SAAOC,GAAGA,CAACC,SAAS,EAAE;MACpB,IAAIrB,UAAU,EAAE;QACdA,UAAU,GAAG,KAAK;QAClBC,MAAM,CAAC,CAAC;MACV;MACAX,SAAS,CAACE,UAAU,CAAC6B,SAAS,CAAC,EAAE,2BAA2B,GAAGA,SAAS,CAAC;MACzE,OAAO7B,UAAU,CAAC6B,SAAS,CAAC;IAC9B;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAOG,GAAGA,CAACC,iBAAiB,EAAE;MAC5B,IAAIA,iBAAiB,EAAE;QACrB,IAAIhC,SAAS,EAAE;UACbD,SAAS,CAAC,KAAK,EAAE,yCAAyC,CAAC;QAC7D,CAAC,MAAM;UACL,IAAIiC,iBAAiB,CAACzB,MAAM,IAAI,IAAI,EAAE;YACpCN,UAAU,CAACM,MAAM,GAAGyB,iBAAiB,CAACzB,MAAM;UAC9C;UACA,IAAIyB,iBAAiB,CAAC9B,MAAM,IAAI,IAAI,EAAE;YACpCD,UAAU,CAACC,MAAM,GAAG8B,iBAAiB,CAAC9B,MAAM;UAC9C;QACF;MACF;IACF;EAAC;IAAAyB,GAAA;IAAAC,KAAA,EACD,SAAOK,gBAAgBA,CAACC,IAAI,EAAET,OAAO,EAAE;MAAA,IAAAU,KAAA;MACrC3B,SAAS,CAAC0B,IAAI,CAAC,GAAG1B,SAAS,CAAC0B,IAAI,CAAC,IAAI,EAAE;MACvC1B,SAAS,CAAC0B,IAAI,CAAC,CAACE,IAAI,CAACX,OAAO,CAAC;MAC7B,OAAO;QACLY,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;UACZF,KAAI,CAACG,mBAAmB,CAACJ,IAAI,EAAET,OAAO,CAAC;QACzC;MACF,CAAC;IACH;EAAC;IAAAE,GAAA;IAAAC,KAAA,EACD,SAAOU,mBAAmBA,CAACJ,IAAI,EAAET,OAAO,EAAE;MACxC,IAAIH,KAAK,CAACC,OAAO,CAACf,SAAS,CAAC0B,IAAI,CAAC,CAAC,EAAE;QAClC1B,SAAS,CAAC0B,IAAI,CAAC,GAAG1B,SAAS,CAAC0B,IAAI,CAAC,CAACK,MAAM,CAAC,UAAAC,QAAQ;UAAA,OAAIA,QAAQ,KAAKf,OAAO;QAAA,EAAC;MAC5E;IACF;EAAC;AAAA;AAAA,SApCkBC,UAAU,IAAAe,OAAA;AAsC/B,IAAIzC,SAAS,EAAE;EACb,IAAIE,MAAM,CAACU,cAAc,EAAE;IACzBV,MAAM,CAACU,cAAc,CAACqB,gBAAgB,CAAC,QAAQ,EAAEZ,YAAY,EAAE,KAAK,CAAC;EACvE,CAAC,MAAM;IACLnB,MAAM,CAAC+B,gBAAgB,CAAC,QAAQ,EAAEZ,YAAY,EAAE,KAAK,CAAC;EACxD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}