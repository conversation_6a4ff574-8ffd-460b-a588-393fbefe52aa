@echo off
title Test Champ Client Deuxieme Position
color 0A

echo.
echo ========================================
echo    👤 TEST CHAMP CLIENT - 2EME POSITION
echo ========================================
echo.

echo 🔍 1. VERIFICATION DU SERVEUR PORT 4002...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 5; Write-Host '✅ Serveur OK:' $response.message } catch { Write-Host '❌ Serveur non accessible - Demarrez le serveur d''abord' }"

echo.
echo 🔍 2. TEST DES APIs NECESSAIRES...
echo.
echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '✅ API Secteurs:' $response.count 'secteurs disponibles' } catch { Write-Host '❌ API Secteurs erreur' }"

echo.
echo Test API Clients par Secteur (Centre-Ville):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/1/clients' -TimeoutSec 5; Write-Host '✅ API Secteur 1:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom } } catch { Write-Host '❌ API Secteur 1 erreur' }"

echo.
echo Test API Clients par Secteur (Zone Nord):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/3/clients' -TimeoutSec 5; Write-Host '✅ API Secteur 3:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom } } catch { Write-Host '❌ API Secteur 3 erreur' }"

echo.
echo 🚀 3. DEMARRAGE DE L'APPLICATION...
echo.
echo Arret des processus existants...
taskkill /f /im node.exe 2>nul

echo.
echo Demarrage du serveur backend (port 4002)...
start "🎯 Backend Port 4002" cmd /k "title BACKEND AQUATRACK PORT 4002 && color 0B && echo ========================================== && echo    🎯 BACKEND AQUATRACK PORT 4002 && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ APIs: Secteurs, Clients, Auth && echo ✅ Champ Client: Deuxieme position && echo. && echo 📡 Demarrage... && echo. && node serveur-port-4002.js"

echo.
echo Attente du backend (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo Demarrage de l'application React...
cd mobile
start "📱 Application React" cmd /k "title APPLICATION REACT AQUATRACK && color 0D && echo ========================================== && echo    📱 APPLICATION REACT AQUATRACK && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4002 && echo ✅ Champ Client: Deuxieme champ du formulaire && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo Attente de l'application (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 4. OUVERTURE DES PAGES...
start http://localhost:4002/consommation
timeout /t 3 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ CHAMP CLIENT EN 2EME POSITION !
echo ========================================
echo.
echo 📋 NOUVEL ORDRE DES CHAMPS:
echo.
echo    1️⃣ 📍 Secteur * (obligatoire)
echo    2️⃣ 👤 Client * (NOUVEAU - deuxieme position)
echo    3️⃣ ℹ️ Informations du Client (automatique)
echo    4️⃣ 📅 Periode (YYYY-MM) *
echo    5️⃣ 📋 Contrat (automatique selon client)
echo    6️⃣ 💧 Consommation Precedente (m³)
echo    7️⃣ 💧 Consommation Actuelle (m³) *
echo    8️⃣ 📊 Nombre de jours
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous avec:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🧪 TESTEZ LA SEQUENCE:
echo    a) 1er champ: Selectionnez "Centre-Ville" dans Secteur
echo    b) 2eme champ: Le champ Client apparait automatiquement
echo    c) Selectionnez "1. Benali Fatima - Setrou" dans Client
echo    d) 3eme champ: Les informations du client s'affichent
echo    e) 4eme champ: Saisissez la periode (ex: 2025-01)
echo    f) Les autres champs se remplissent automatiquement
echo.
echo 📊 SECTEURS ET CLIENTS A TESTER:
echo.
echo    1. Centre-Ville:
echo       - 1. Benali Fatima - Setrou
echo       - 2. Alami Mohammed - Setrou
echo.
echo    2. Quartier Industriel:
echo       - 1. Tazi Aicha - Setrou
echo.
echo    3. Zone Residentielle Nord:
echo       - 1. Benjelloun Youssef - Setrou
echo       - 2. Lahlou Khadija - Setrou
echo.
echo    4. Zone Residentielle Sud:
echo       - 1. Fassi Omar - Setrou
echo.
echo    5. Quartier Commercial:
echo       - Message: "Aucun client dans ce secteur"
echo.
echo ✅ FONCTIONNALITES DU CHAMP CLIENT:
echo.
echo    ✅ Deuxieme position dans le formulaire
echo    ✅ Depend du secteur selectionne
echo    ✅ Menu deroulant avec numeros et villes
echo    ✅ Affichage automatique des informations
echo    ✅ Messages d'aide contextuels
echo    ✅ Style visuel distinct (fond vert)
echo    ✅ Validation obligatoire
echo.
echo 🎯 VERIFICATION VISUELLE:
echo.
echo    - Le champ Client doit apparaitre en 2eme position
echo    - Il doit avoir un fond vert clair
echo    - Il doit etre marque avec une etoile (*)
echo    - Il doit afficher "Selectionnez d'abord un secteur" si aucun secteur
echo    - Il doit afficher "Aucun client dans ce secteur" si secteur vide
echo    - Il doit lister les clients avec numeros si secteur avec clients
echo.
echo 🌐 URLs de test:
echo    - Page consommation: http://localhost:4002/consommation
echo    - Application React: http://localhost:19006
echo    - API Secteurs: http://localhost:4002/api/secteurs
echo    - API Clients Secteur 1: http://localhost:4002/api/secteurs/1/clients
echo.
echo 🎯 RESULTAT ATTENDU:
echo    Le champ Client est maintenant le DEUXIEME CHAMP
echo    du formulaire, juste apres le Secteur !
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
