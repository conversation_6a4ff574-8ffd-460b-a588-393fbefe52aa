{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport { Platform } from 'expo-modules-core';\nimport { v4 as uuidv4 } from 'uuid';\nimport { ExecutionEnvironment } from \"./Constants.types\";\nvar ID_KEY = 'EXPO_CONSTANTS_INSTALLATION_ID';\nvar _sessionId = uuidv4();\nfunction getBrowserName() {\n  if (Platform.isDOMAvailable) {\n    var agent = navigator.userAgent.toLowerCase();\n    if (agent.includes('edge')) {\n      return 'Edge';\n    } else if (agent.includes('edg')) {\n      return 'Chromium Edge';\n    } else if (agent.includes('opr') && !!window['opr']) {\n      return 'Opera';\n    } else if (agent.includes('chrome') && !!window['chrome']) {\n      return 'Chrome';\n    } else if (agent.includes('trident')) {\n      return 'IE';\n    } else if (agent.includes('firefox')) {\n      return 'Firefox';\n    } else if (agent.includes('safari')) {\n      return 'Safari';\n    }\n  }\n  return undefined;\n}\nexport default {\n  get name() {\n    return 'ExponentConstants';\n  },\n  get appOwnership() {\n    return null;\n  },\n  get executionEnvironment() {\n    return ExecutionEnvironment.Bare;\n  },\n  get installationId() {\n    var installationId;\n    try {\n      installationId = localStorage.getItem(ID_KEY);\n      if (installationId == null || typeof installationId !== 'string') {\n        installationId = uuidv4();\n        localStorage.setItem(ID_KEY, installationId);\n      }\n    } catch (_unused) {\n      installationId = _sessionId;\n    } finally {\n      return installationId;\n    }\n  },\n  get sessionId() {\n    return _sessionId;\n  },\n  get platform() {\n    return {\n      web: Platform.isDOMAvailable ? {\n        ua: navigator.userAgent\n      } : undefined\n    };\n  },\n  get isHeadless() {\n    if (!Platform.isDOMAvailable) return true;\n    return /\\bHeadlessChrome\\//.test(navigator.userAgent);\n  },\n  get isDevice() {\n    return true;\n  },\n  get expoVersion() {\n    return this.manifest.sdkVersion || null;\n  },\n  get linkingUri() {\n    if (Platform.isDOMAvailable) {\n      return location.origin;\n    } else {\n      return '';\n    }\n  },\n  get expoRuntimeVersion() {\n    return this.expoVersion;\n  },\n  get deviceName() {\n    return getBrowserName();\n  },\n  get nativeAppVersion() {\n    return null;\n  },\n  get nativeBuildVersion() {\n    return null;\n  },\n  get systemFonts() {\n    return [];\n  },\n  get statusBarHeight() {\n    return 0;\n  },\n  get deviceYearClass() {\n    return null;\n  },\n  get manifest() {\n    return process.env.APP_MANIFEST || {};\n  },\n  get manifest2() {\n    return null;\n  },\n  get experienceUrl() {\n    if (Platform.isDOMAvailable) {\n      return location.origin;\n    } else {\n      return '';\n    }\n  },\n  get debugMode() {\n    return __DEV__;\n  },\n  getWebViewUserAgentAsync: function () {\n    var _getWebViewUserAgentAsync = _asyncToGenerator(function* () {\n      if (Platform.isDOMAvailable) {\n        return navigator.userAgent;\n      } else {\n        return null;\n      }\n    });\n    function getWebViewUserAgentAsync() {\n      return _getWebViewUserAgentAsync.apply(this, arguments);\n    }\n    return getWebViewUserAgentAsync;\n  }()\n};", "map": {"version": 3, "names": ["Platform", "v4", "uuidv4", "ExecutionEnvironment", "ID_KEY", "_sessionId", "getBrowserName", "isDOMAvailable", "agent", "navigator", "userAgent", "toLowerCase", "includes", "window", "undefined", "name", "appOwnership", "executionEnvironment", "<PERSON><PERSON>", "installationId", "localStorage", "getItem", "setItem", "_unused", "sessionId", "platform", "web", "ua", "isHeadless", "test", "isDevice", "expoVersion", "manifest", "sdkVersion", "linkingUri", "location", "origin", "expoRuntimeVersion", "deviceName", "nativeAppVersion", "nativeBuildVersion", "systemFonts", "statusBarHeight", "deviceYearClass", "process", "env", "APP_MANIFEST", "manifest2", "experienceUrl", "debugMode", "__DEV__", "getWebViewUserAgentAsync", "_getWebViewUserAgentAsync", "_asyncToGenerator", "apply", "arguments"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\expo-constants\\src\\ExponentConstants.web.ts"], "sourcesContent": ["import { Platform } from 'expo-modules-core';\nimport { v4 as uuidv4 } from 'uuid';\n\nimport {\n  ExecutionEnvironment,\n  NativeConstants,\n  PlatformManifest,\n  WebManifest,\n} from './Constants.types';\n\nconst ID_KEY = 'EXPO_CONSTANTS_INSTALLATION_ID';\n\ndeclare let __DEV__: boolean;\ndeclare let process: { env: any };\ndeclare let navigator: Navigator;\ndeclare let location: Location;\ndeclare let localStorage: Storage;\n\nconst _sessionId = uuidv4();\n\nfunction getBrowserName(): string | undefined {\n  if (Platform.isDOMAvailable) {\n    const agent = navigator.userAgent.toLowerCase();\n    if (agent.includes('edge')) {\n      return 'Edge';\n    } else if (agent.includes('edg')) {\n      return 'Chromium Edge';\n    } else if (agent.includes('opr') && !!window['opr']) {\n      return 'Opera';\n    } else if (agent.includes('chrome') && !!window['chrome']) {\n      return 'Chrome';\n    } else if (agent.includes('trident')) {\n      return 'IE';\n    } else if (agent.includes('firefox')) {\n      return 'Firefox';\n    } else if (agent.includes('safari')) {\n      return 'Safari';\n    }\n  }\n\n  return undefined;\n}\n\nexport default {\n  get name(): string {\n    return 'ExponentConstants';\n  },\n  get appOwnership() {\n    return null;\n  },\n  get executionEnvironment() {\n    return ExecutionEnvironment.Bare;\n  },\n  get installationId(): string {\n    let installationId;\n    try {\n      installationId = localStorage.getItem(ID_KEY);\n      if (installationId == null || typeof installationId !== 'string') {\n        installationId = uuidv4();\n        localStorage.setItem(ID_KEY, installationId as string);\n      }\n    } catch {\n      installationId = _sessionId;\n    } finally {\n      return installationId;\n    }\n  },\n  get sessionId(): string {\n    return _sessionId;\n  },\n  get platform(): PlatformManifest {\n    return { web: Platform.isDOMAvailable ? { ua: navigator.userAgent } : undefined };\n  },\n  get isHeadless(): boolean {\n    if (!Platform.isDOMAvailable) return true;\n\n    return /\\bHeadlessChrome\\//.test(navigator.userAgent);\n  },\n  get isDevice(): true {\n    // TODO: Bacon: Possibly want to add information regarding simulators\n    return true;\n  },\n  get expoVersion(): string | null {\n    return this.manifest!.sdkVersion || null;\n  },\n  get linkingUri(): string {\n    if (Platform.isDOMAvailable) {\n      // On native this is `exp://`\n      // On web we should use the protocol and hostname (location.origin)\n      return location.origin;\n    } else {\n      return '';\n    }\n  },\n  get expoRuntimeVersion(): string | null {\n    return this.expoVersion;\n  },\n  get deviceName(): string | undefined {\n    return getBrowserName();\n  },\n  get nativeAppVersion(): null {\n    return null;\n  },\n  get nativeBuildVersion(): null {\n    return null;\n  },\n  get systemFonts(): string[] {\n    // TODO: Bacon: Maybe possible.\n    return [];\n  },\n  get statusBarHeight(): number {\n    return 0;\n  },\n  get deviceYearClass(): number | null {\n    // TODO: Bacon: The android version isn't very accurate either, maybe we could try and guess this value.\n    return null;\n  },\n  get manifest(): WebManifest {\n    // This is defined by @expo/webpack-config.\n    // If your site is bundled with a different config then you may not have access to the app.json automatically.\n    return process.env.APP_MANIFEST || {};\n  },\n  get manifest2(): null {\n    return null;\n  },\n  get experienceUrl(): string {\n    if (Platform.isDOMAvailable) {\n      return location.origin;\n    } else {\n      return '';\n    }\n  },\n  get debugMode(): boolean {\n    return __DEV__;\n  },\n  async getWebViewUserAgentAsync(): Promise<string | null> {\n    if (Platform.isDOMAvailable) {\n      return navigator.userAgent;\n    } else {\n      return null;\n    }\n  },\n} as NativeConstants;\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAEnC,SACEC,oBAAoB;AAMtB,IAAMC,MAAM,GAAG,gCAAgC;AAQ/C,IAAMC,UAAU,GAAGH,MAAM,EAAE;AAE3B,SAASI,cAAcA,CAAA;EACrB,IAAIN,QAAQ,CAACO,cAAc,EAAE;IAC3B,IAAMC,KAAK,GAAGC,SAAS,CAACC,SAAS,CAACC,WAAW,EAAE;IAC/C,IAAIH,KAAK,CAACI,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC1B,OAAO,MAAM;KACd,MAAM,IAAIJ,KAAK,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE;MAChC,OAAO,eAAe;KACvB,MAAM,IAAIJ,KAAK,CAACI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,KAAK,CAAC,EAAE;MACnD,OAAO,OAAO;KACf,MAAM,IAAIL,KAAK,CAACI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,QAAQ,CAAC,EAAE;MACzD,OAAO,QAAQ;KAChB,MAAM,IAAIL,KAAK,CAACI,QAAQ,CAAC,SAAS,CAAC,EAAE;MACpC,OAAO,IAAI;KACZ,MAAM,IAAIJ,KAAK,CAACI,QAAQ,CAAC,SAAS,CAAC,EAAE;MACpC,OAAO,SAAS;KACjB,MAAM,IAAIJ,KAAK,CAACI,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACnC,OAAO,QAAQ;;;EAInB,OAAOE,SAAS;AAClB;AAEA,eAAe;EACb,IAAIC,IAAIA,CAAA;IACN,OAAO,mBAAmB;EAC5B,CAAC;EACD,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,oBAAoBA,CAAA;IACtB,OAAOd,oBAAoB,CAACe,IAAI;EAClC,CAAC;EACD,IAAIC,cAAcA,CAAA;IAChB,IAAIA,cAAc;IAClB,IAAI;MACFA,cAAc,GAAGC,YAAY,CAACC,OAAO,CAACjB,MAAM,CAAC;MAC7C,IAAIe,cAAc,IAAI,IAAI,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;QAChEA,cAAc,GAAGjB,MAAM,EAAE;QACzBkB,YAAY,CAACE,OAAO,CAAClB,MAAM,EAAEe,cAAwB,CAAC;;KAEzD,CAAC,OAAAI,OAAA,EAAM;MACNJ,cAAc,GAAGd,UAAU;KAC5B,SAAS;MACR,OAAOc,cAAc;;EAEzB,CAAC;EACD,IAAIK,SAASA,CAAA;IACX,OAAOnB,UAAU;EACnB,CAAC;EACD,IAAIoB,QAAQA,CAAA;IACV,OAAO;MAAEC,GAAG,EAAE1B,QAAQ,CAACO,cAAc,GAAG;QAAEoB,EAAE,EAAElB,SAAS,CAACC;MAAS,CAAE,GAAGI;IAAS,CAAE;EACnF,CAAC;EACD,IAAIc,UAAUA,CAAA;IACZ,IAAI,CAAC5B,QAAQ,CAACO,cAAc,EAAE,OAAO,IAAI;IAEzC,OAAO,oBAAoB,CAACsB,IAAI,CAACpB,SAAS,CAACC,SAAS,CAAC;EACvD,CAAC;EACD,IAAIoB,QAAQA,CAAA;IAEV,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,WAAWA,CAAA;IACb,OAAO,IAAI,CAACC,QAAS,CAACC,UAAU,IAAI,IAAI;EAC1C,CAAC;EACD,IAAIC,UAAUA,CAAA;IACZ,IAAIlC,QAAQ,CAACO,cAAc,EAAE;MAG3B,OAAO4B,QAAQ,CAACC,MAAM;KACvB,MAAM;MACL,OAAO,EAAE;;EAEb,CAAC;EACD,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACN,WAAW;EACzB,CAAC;EACD,IAAIO,UAAUA,CAAA;IACZ,OAAOhC,cAAc,EAAE;EACzB,CAAC;EACD,IAAIiC,gBAAgBA,CAAA;IAClB,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,WAAWA,CAAA;IAEb,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,eAAeA,CAAA;IACjB,OAAO,CAAC;EACV,CAAC;EACD,IAAIC,eAAeA,CAAA;IAEjB,OAAO,IAAI;EACb,CAAC;EACD,IAAIX,QAAQA,CAAA;IAGV,OAAOY,OAAO,CAACC,GAAG,CAACC,YAAY,IAAI,EAAE;EACvC,CAAC;EACD,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,aAAaA,CAAA;IACf,IAAIhD,QAAQ,CAACO,cAAc,EAAE;MAC3B,OAAO4B,QAAQ,CAACC,MAAM;KACvB,MAAM;MACL,OAAO,EAAE;;EAEb,CAAC;EACD,IAAIa,SAASA,CAAA;IACX,OAAOC,OAAO;EAChB,CAAC;EACKC,wBAAwB;IAAA,IAAAC,yBAAA,GAAAC,iBAAA;MAC5B,IAAIrD,QAAQ,CAACO,cAAc,EAAE;QAC3B,OAAOE,SAAS,CAACC,SAAS;OAC3B,MAAM;QACL,OAAO,IAAI;;IAEf,CAAC;IAAA,SANKyC,wBAAwBA,CAAA;MAAA,OAAAC,yBAAA,CAAAE,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAxBJ,wBAAwB;EAAA;CAOZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}