{"ast": null, "code": "var findNodeHandle = function findNodeHandle(component) {\n  throw new Error('findNodeHandle is not supported on web. ' + 'Use the ref property on the component instead.');\n};\nexport default findNodeHandle;", "map": {"version": 3, "names": ["findNodeHandle", "component", "Error"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/findNodeHandle/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar findNodeHandle = component => {\n  throw new Error('findNodeHandle is not supported on web. ' + 'Use the ref property on the component instead.');\n};\nexport default findNodeHandle;"], "mappings": "AAUA,IAAIA,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,SAAS,EAAI;EAChC,MAAM,IAAIC,KAAK,CAAC,0CAA0C,GAAG,gDAAgD,CAAC;AAChH,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}