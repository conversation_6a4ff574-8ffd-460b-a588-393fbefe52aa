# COMMANDES TERMINAL POUR DÉMARRER AQUATRACK
# Co<PERSON>z et collez ces commandes dans votre terminal

# ========================================
# ÉTAPE 1: NAVIGATION VERS LE PROJET
# ========================================
cd "c:\Users\<USER>\OneDrive\Desktop\Facturation stage\samle-react-app"

# ========================================
# ÉTAPE 2: ARRÊT DES PROCESSUS EXISTANTS
# ========================================
taskkill /f /im node.exe

# ========================================
# ÉTAPE 3: DÉMAR<PERSON>GE DU BACKEND
# ========================================

# Option A - Serveur d'urgence (RECOMMANDÉ pour résoudre l'erreur)
node serveur-urgence.js

# Option B - Serveur avec PostgreSQL
# node backend/server-db.js

# Option C - Serveur simple
# node simple-server.js

# ========================================
# ÉTAPE 4: NOUVEAU TERMINAL POUR FRONTEND
# ========================================
# Ouvrez un NOUVEAU terminal et exécutez:

cd "c:\Users\<USER>\OneDrive\Desktop\Facturation stage\samle-react-app"

# Option A - Application Mobile (RECOMMANDÉ)
cd mobile
npx expo start --web

# Option B - Application React Web
# npm start

# Option C - Application React Native
# cd react-native
# npx expo start

# ========================================
# ÉTAPE 5: VÉRIFICATION
# ========================================
# Ouvrez votre navigateur et testez:
# - Backend: http://localhost:4000
# - Frontend: http://localhost:19006 (ou 3000 selon votre choix)

# ========================================
# COMPTES DE TEST
# ========================================
# Email: <EMAIL>
# Mot de passe: Tech123

# ========================================
# RÉSOLUTION DE L'ERREUR
# ========================================
# Une fois les deux services démarrés:
# 1. Allez sur http://localhost:19006
# 2. Actualisez la page (F5)
# 3. Essayez de vous connecter
# 4. L'erreur "Impossible de se connecter au serveur" devrait disparaître
