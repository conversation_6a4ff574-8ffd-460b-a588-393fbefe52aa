require('dotenv').config();
const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');
const { router: factureRouter, generateFactureAutomatique } = require('./facture');
const clientsRouter = require('./clients');
const app = express();
app.use(express.json());
app.use(cors());

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Utilisation des routes de factures
app.use('/', factureRouter);
// Utilisation des routes de clients
app.use('/', clientsRouter);

// Configuration de la base de données avec gestion d'erreur
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration', // Nom correct de la base
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Variable pour suivre l'état de la connexion DB
let dbConnected = false;

// Test de connexion à la base de données
async function testDatabaseConnection() {
  try {
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    console.log('✅ Connexion à la base de données "Facutration" réussie');
    dbConnected = true;
    return true;
  } catch (error) {
    console.log('❌ Erreur de connexion à la base de données:', error.message);
    console.log('🔄 Utilisation des données de test à la place...');
    dbConnected = false;
    return false;
  }
}

// Données de test
const testClients = [
  {
    idclient: 1,
    nom: 'Dupont',
    prenom: 'Jean',
    adresse: '123 Rue de la Paix',
    ville: 'Tunis',
    tel: '71234567',
    email: '<EMAIL>',
    ids: 1,
    secteur_nom: 'Centre Ville'
  },
  {
    idclient: 2,
    nom: 'Martin',
    prenom: 'Marie',
    adresse: '456 Avenue Habib Bourguiba',
    ville: 'Sfax',
    tel: '74567890',
    email: '<EMAIL>',
    ids: 2,
    secteur_nom: 'Sfax Nord'
  }
];

const testSecteurs = [
  {
    ids: 1,
    nom: 'Centre Ville',
    latitude: 36.8065,
    longitude: 10.1815
  },
  {
    ids: 2,
    nom: 'Sfax Nord',
    latitude: 34.7406,
    longitude: 10.7603
  }
];

const testContracts = {
  1: [{
    idcontract: 1,
    codeqr: 'QR001',
    datecontract: '2024-01-15',
    marquecompteur: 'Sensus',
    numseriecompteur: 'SN123456',
    posx: '36.8075',
    posy: '10.1825'
  }],
  2: [{
    idcontract: 2,
    codeqr: 'QR002',
    datecontract: '2024-02-20',
    marquecompteur: 'Itron',
    numseriecompteur: 'IT789012',
    posx: '34.7416',
    posy: '10.7613'
  }]
};

// Route de test
app.get('/', (req, res) => {
  res.send('Serveur Facutration fonctionnel - ' + (dbConnected ? 'Base de données connectée' : 'Mode test'));
});

// Route de connexion adaptée à votre table Utilisateur
app.post('/login', async (req, res) => {
  console.log('Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body;

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    // Recherche de l'utilisateur dans la table Utilisateur
    const result = await pool.query(
      'SELECT * FROM Utilisateur WHERE email = $1',
      [email]
    );

    if (result.rows.length === 0) {
      console.log('Aucun utilisateur trouvé avec cet email:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('Utilisateur trouvé:', { email: user.email, role: user.role });

    // Comparaison directe du mot de passe (en clair dans votre table)
    if (motDepass !== user.motdepass) {
      console.log('Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    // Connexion réussie
    console.log('Connexion réussie pour:', email);
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });

  } catch (err) {
    console.error('Erreur lors de la connexion:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: err.message
    });
  }
});

// Route pour récupérer tous les clients (avec fallback données de test)
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Récupération de tous les clients');

    if (dbConnected) {
      const query = `
        SELECT
          c.idclient,
          c.nom,
          c.prenom,
          c.adresse,
          c.ville,
          c.tel,
          c.email,
          COALESCE(s.nom, 'Non défini') as secteur_nom
        FROM client c
        LEFT JOIN secteur s ON c.ids = s.ids
        ORDER BY c.nom, c.prenom
      `;

      const result = await pool.query(query);
      console.log(`✅ ${result.rows.length} clients récupérés depuis la base de données`);

      res.json({
        success: true,
        data: result.rows,
        count: result.rows.length,
        message: `${result.rows.length} client(s) trouvé(s)`
      });
    } else {
      console.log(`✅ ${testClients.length} clients récupérés depuis les données de test`);
      res.json({
        success: true,
        data: testClients,
        count: testClients.length,
        message: `${testClients.length} client(s) trouvé(s) (données de test)`
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    // En cas d'erreur, utiliser les données de test
    console.log('🔄 Utilisation des données de test en fallback');
    res.json({
      success: true,
      data: testClients,
      count: testClients.length,
      message: `${testClients.length} client(s) trouvé(s) (données de test - fallback)`
    });
  }
});

// 🎯 ROUTE CRITIQUE : Récupérer les contrats d'un client spécifique (avec fallback données de test)
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`\n🎯 ROUTE CRITIQUE: GET /api/clients/${id}/contracts`);
    console.log(`🔍 Recherche des contrats pour le client ID: ${id}`);

    if (dbConnected) {
      // Vérifier d'abord si le client existe
      const clientCheck = await pool.query('SELECT nom, prenom FROM client WHERE idclient = $1', [id]);
      if (clientCheck.rows.length === 0) {
        console.log(`❌ Client ID ${id} n'existe pas dans la base`);
        return res.status(404).json({
          success: false,
          message: `Client ID ${id} non trouvé`,
          client_id: parseInt(id)
        });
      }

      const clientInfo = clientCheck.rows[0];
      console.log(`✅ Client trouvé: ${clientInfo.nom} ${clientInfo.prenom}`);

      // Requête pour récupérer les contrats
      const query = `
        SELECT
          c.idcontract,
          c.codeqr,
          c.datecontract,
          c.idclient,
          c.marquecompteur,
          c.numseriecompteur,
          c.posx,
          c.posy,
          cl.nom,
          cl.prenom,
          cl.adresse,
          cl.ville
        FROM contract c
        INNER JOIN client cl ON c.idclient = cl.idclient
        WHERE c.idclient = $1
        ORDER BY c.datecontract DESC
      `;

      console.log('📡 Exécution de la requête SQL...');
      const result = await pool.query(query, [id]);

      console.log(`📊 RÉSULTAT: ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);

      const response = {
        success: true,
        data: result.rows,
        count: result.rows.length,
        message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`,
        client_id: parseInt(id),
        client_name: `${clientInfo.nom} ${clientInfo.prenom}`
      };

      console.log('📤 ENVOI DE LA RÉPONSE CONTRATS');
      res.json(response);
    } else {
      // Mode test - utiliser les données de test
      const clientId = parseInt(id);
      const client = testClients.find(c => c.idclient === clientId);

      if (!client) {
        console.log(`❌ Client ID ${id} non trouvé dans les données de test`);
        return res.status(404).json({
          success: false,
          message: `Client ID ${id} non trouvé`,
          client_id: clientId
        });
      }

      const contracts = testContracts[clientId] || [];
      console.log(`✅ Client trouvé: ${client.nom} ${client.prenom}`);
      console.log(`📊 ${contracts.length} contrat(s) trouvé(s) (données de test)`);

      res.json({
        success: true,
        data: contracts,
        count: contracts.length,
        message: `${contracts.length} contrat(s) trouvé(s) pour le client ${id} (données de test)`,
        client_id: clientId,
        client_name: `${client.nom} ${client.prenom}`
      });
    }

  } catch (error) {
    console.error('❌ ERREUR CRITIQUE lors de la récupération des contrats:', error.message);
    // En cas d'erreur, utiliser les données de test
    const clientId = parseInt(req.params.id);
    const contracts = testContracts[clientId] || [];

    res.json({
      success: true,
      data: contracts,
      count: contracts.length,
      message: `${contracts.length} contrat(s) trouvé(s) (données de test - fallback)`,
      client_id: clientId
    });
  }
});

// 🎯 NOUVELLE ROUTE : Rechercher un client par code QR
app.get('/api/clients/qr/:codeqr', async (req, res) => {
  try {
    const { codeqr } = req.params;
    console.log(`\n🔍 RECHERCHE CLIENT PAR QR: ${codeqr}`);

    if (dbConnected) {
      // Requête pour trouver le client via le code QR du contrat
      const query = `
        SELECT
          c.idclient,
          c.nom,
          c.prenom,
          c.adresse,
          c.ville,
          c.tel,
          c.email,
          c.statut,
          s.nom as secteur,
          cont.codeqr,
          cont.idcontract,
          cont.marquecompteur,
          cont.numseriecompteur,
          cont.posx,
          cont.posy
        FROM client c
        INNER JOIN contract cont ON c.idclient = cont.idclient
        LEFT JOIN secteur s ON c.ids = s.ids
        WHERE cont.codeqr = $1
        LIMIT 1
      `;

      const result = await pool.query(query, [codeqr]);

      if (result.rows.length === 0) {
        console.log(`❌ Aucun client trouvé pour le code QR: ${codeqr}`);
        return res.json({
          success: false,
          message: `Aucun client trouvé pour le code QR: ${codeqr}`,
          data: null
        });
      }

      const clientData = result.rows[0];
      console.log(`✅ Client trouvé: ${clientData.nom} ${clientData.prenom}`);
      console.log(`📍 Contrat: ${clientData.codeqr} - ${clientData.marquecompteur}`);

      res.json({
        success: true,
        data: clientData,
        message: `Client trouvé: ${clientData.nom} ${clientData.prenom}`
      });

    } else {
      // Mode test - rechercher dans les données de test
      console.log('🔄 Mode test - recherche dans les données de test');

      // Trouver le contrat avec ce code QR
      let clientTrouve = null;
      let contractTrouve = null;

      for (const [clientId, contracts] of Object.entries(testContracts)) {
        const contract = contracts.find(c => c.codeqr === codeqr);
        if (contract) {
          contractTrouve = contract;
          clientTrouve = testClients.find(c => c.idclient === parseInt(clientId));
          break;
        }
      }

      if (!clientTrouve || !contractTrouve) {
        console.log(`❌ Aucun client trouvé pour le code QR: ${codeqr} (données de test)`);
        return res.json({
          success: false,
          message: `Aucun client trouvé pour le code QR: ${codeqr}`,
          data: null
        });
      }

      console.log(`✅ Client trouvé: ${clientTrouve.nom} ${clientTrouve.prenom} (données de test)`);

      const clientData = {
        ...clientTrouve,
        secteur: clientTrouve.secteur_nom,
        codeqr: contractTrouve.codeqr,
        idcontract: contractTrouve.idcontract,
        marquecompteur: contractTrouve.marquecompteur,
        numseriecompteur: contractTrouve.numseriecompteur,
        posx: contractTrouve.posx,
        posy: contractTrouve.posy
      };

      res.json({
        success: true,
        data: clientData,
        message: `Client trouvé: ${clientTrouve.nom} ${clientTrouve.prenom} (données de test)`
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la recherche par QR:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la recherche par code QR',
      error: error.message,
      data: null
    });
  }
});

// Route pour récupérer tous les secteurs
app.get('/api/secteurs', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/secteurs - Récupération de tous les secteurs');

    const query = 'SELECT ids, nom FROM secteur ORDER BY nom';
    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} secteur(s) récupéré(s)`);
    res.json({
      success: true,
      data: result.rows,
      message: `${result.rows.length} secteur(s) récupéré(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des secteurs',
      error: error.message
    });
  }
});

// Route pour récupérer un secteur spécifique par ID avec coordonnées (avec fallback données de test)
app.get('/api/secteurs/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/secteurs/${id}`);

    if (dbConnected) {
      const query = 'SELECT ids, nom, latitude, longitude FROM secteur WHERE ids = $1';
      const result = await pool.query(query, [id]);

      if (result.rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Secteur non trouvé'
        });
      }

      console.log(`✅ Secteur ${id} récupéré: ${result.rows[0].nom}`);
      console.log(`📍 Coordonnées: ${result.rows[0].latitude}, ${result.rows[0].longitude}`);

      res.json({
        success: true,
        data: result.rows[0],
        message: 'Secteur trouvé'
      });
    } else {
      // Mode test - utiliser les données de test
      const secteurId = parseInt(id);
      const secteur = testSecteurs.find(s => s.ids === secteurId);

      if (!secteur) {
        console.log(`❌ Secteur ID ${id} non trouvé dans les données de test`);
        return res.status(404).json({
          success: false,
          message: 'Secteur non trouvé'
        });
      }

      console.log(`✅ Secteur ${id} récupéré: ${secteur.nom} (données de test)`);
      console.log(`📍 Coordonnées: ${secteur.latitude}, ${secteur.longitude}`);

      res.json({
        success: true,
        data: secteur,
        message: 'Secteur trouvé (données de test)'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du secteur:', error);
    // En cas d'erreur, utiliser les données de test
    const secteurId = parseInt(req.params.id);
    const secteur = testSecteurs.find(s => s.ids === secteurId);

    if (secteur) {
      res.json({
        success: true,
        data: secteur,
        message: 'Secteur trouvé (données de test - fallback)'
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Secteur non trouvé',
        error: error.message
      });
    }
  }
});

// Route pour récupérer tous les contrats
app.get('/api/contracts', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        ct.idContract,
        ct.idClient,
        ct.codeQr,
        ct.dateContract,
        ct.marqueCompteur,
        ct.numSerieCompteur,
        ct.posX,
        ct.posY,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville,
        cl.tel,
        cl.email
      FROM Contract ct
      LEFT JOIN Client cl ON ct.idClient = cl.idClient
      ORDER BY cl.nom, cl.prenom
    `);

    res.json({
      success: true,
      data: result.rows
    });
  } catch (err) {
    console.error('Erreur lors de la récupération des contrats:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer toutes les consommations
app.get('/api/consommations', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        c.idCons,
        c.idCont,
        c.consommationPre,
        c.consommationActuelle,
        c.jours,
        c.periode,
        c.idTech,
        c.status,
        ct.codeQr,
        ct.marqueCompteur,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville
      FROM Consommation c
      LEFT JOIN Contract ct ON c.idCont = ct.idContract
      LEFT JOIN Client cl ON ct.idClient = cl.idClient
      ORDER BY c.idCons DESC
    `);

    res.json({
      success: true,
      data: result.rows
    });
  } catch (err) {
    console.error('Erreur lors de la récupération des consommations:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 GET /api/contracts/${id}/last-consommation`);

    const query = `
      SELECT
        consommationactuelle,
        periode,
        jours
      FROM consommation
      WHERE idcont = $1
      ORDER BY periode DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length > 0) {
      console.log(`✅ Dernière consommation trouvée: ${result.rows[0].consommationactuelle} m³`);
      res.json({
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation trouvée'
      });
    } else {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${id}`);
      res.json({
        success: false,
        message: 'Aucune consommation précédente trouvée'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Route pour ajouter une nouvelle consommation avec génération automatique de facture
app.post('/api/consommations', async (req, res) => {
  try {
    const { idcont, consommationpre, consommationactuelle, jours, periode, idtech, idtranch } = req.body;

    console.log('📝 Ajout d\'une nouvelle consommation:', req.body);

    // 1. Insérer la consommation dans la base de données
    const result = await pool.query(`
      INSERT INTO Consommation (idCont, consommationPre, consommationActuelle, jours, periode, idTech, idTranch, status)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [idcont, consommationpre || 0, consommationactuelle, jours || 30, periode, idtech || 1, idtranch || 1, 'nouveau']);

    const nouvelleConsommation = result.rows[0];
    console.log('✅ Consommation enregistrée:', nouvelleConsommation);

    // 2. Générer automatiquement la facture
    const factureResult = await generateFactureAutomatique(nouvelleConsommation);

    if (factureResult.success) {
      console.log('🧾 Facture générée automatiquement:', factureResult.data);

      // Retourner la consommation ET la facture générée
      res.json({
        success: true,
        data: nouvelleConsommation,
        factureGeneree: factureResult,
        message: 'Consommation enregistrée et facture générée automatiquement'
      });
    } else {
      console.log('⚠️ Erreur lors de la génération de facture:', factureResult.message);

      // Retourner la consommation même si la facture n'a pas pu être générée
      res.json({
        success: true,
        data: nouvelleConsommation,
        factureGeneree: factureResult,
        message: 'Consommation enregistrée mais erreur lors de la génération de facture'
      });
    }

  } catch (err) {
    console.error('❌ Erreur lors de l\'ajout de la consommation:', err);
    res.status(500).json({
      success: false,
      error: err.message,
      message: 'Erreur lors de l\'enregistrement de la consommation'
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat spécifique
app.get('/api/contracts/:idcontract/last-consommation', async (req, res) => {
  try {
    const { idcontract } = req.params;

    const result = await pool.query(`
      SELECT
        c.idCons,
        c.consommationActuelle,
        c.consommationPre,
        c.periode,
        c.jours,
        c.status
      FROM Consommation c
      WHERE c.idCont = $1
      ORDER BY c.idCons DESC
      LIMIT 1
    `, [idcontract]);

    if (result.rows.length > 0) {
      res.json({
        success: true,
        data: result.rows[0],
        message: `Dernière consommation trouvée pour le contrat ${idcontract}`
      });
    } else {
      res.json({
        success: true,
        data: null,
        message: 'Aucune consommation précédente trouvée pour ce contrat'
      });
    }
  } catch (err) {
    console.error('Erreur lors de la récupération de la dernière consommation:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer la toute dernière consommation de la base de données (globale)
app.get('/api/last-consommation-global', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        c.idCons,
        c.consommationActuelle,
        c.consommationPre,
        c.periode,
        c.jours,
        c.status,
        ct.codeQr,
        cl.nom,
        cl.prenom
      FROM Consommation c
      LEFT JOIN Contract ct ON c.idCont = ct.idContract
      LEFT JOIN Client cl ON ct.idClient = cl.idClient
      ORDER BY c.idCons DESC
      LIMIT 1
    `);

    if (result.rows.length > 0) {
      res.json({
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation globale trouvée'
      });
    } else {
      res.json({
        success: true,
        data: null,
        message: 'Aucune consommation trouvée dans la base de données'
      });
    }
  } catch (err) {
    console.error('Erreur lors de la récupération de la dernière consommation globale:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un client spécifique (tous ses contrats)
app.get('/api/clients/:idclient/last-consommation', async (req, res) => {
  try {
    const { idclient } = req.params;
    console.log(`📥 Requête GET /api/clients/${idclient}/last-consommation`);

    const query = `
      SELECT
        cons.idcons,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.periode,
        cons.jours,
        cons.status,
        cont.codeqr as contrat_reference,
        cont.idcontract
      FROM consommation cons
      INNER JOIN contract cont ON cons.idcont = cont.idcontract
      WHERE cont.idclient = $1
      ORDER BY cons.idcons DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [idclient]);

    if (result.rows.length === 0) {
      console.log(`ℹ️ Aucune consommation trouvée pour le client ${idclient}`);
      return res.json({
        success: true,
        data: null,
        message: 'Aucune consommation précédente trouvée pour ce client'
      });
    }

    const lastConsommation = result.rows[0];
    console.log(`✅ Dernière consommation trouvée pour le client ${idclient}:`, lastConsommation);

    res.json({
      success: true,
      data: {
        idcons: lastConsommation.idcons,
        consommationactuelle: lastConsommation.consommationactuelle,
        consommationpre: lastConsommation.consommationpre,
        periode: lastConsommation.periode,
        jours: lastConsommation.jours,
        status: lastConsommation.status,
        contrat_reference: lastConsommation.contrat_reference,
        idcontract: lastConsommation.idcontract
      },
      message: `Dernière consommation du client: ${lastConsommation.consommationactuelle} m³ (période: ${lastConsommation.periode})`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation du client',
      error: error.message
    });
  }
});

// Démarrage du serveur avec test de connexion
const PORT = process.env.PORT || 4000; // Changé à 4000 pour correspondre au frontend

async function startServer() {
  console.log('🔄 Démarrage du serveur AquaTrack...');

  // Tester la connexion à la base de données
  await testDatabaseConnection();

  app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Serveur Facturation démarré sur http://localhost:${PORT}`);
    console.log(`📱 Accessible depuis le réseau sur http://***********:${PORT}`);
    console.log(`🗄️ Mode: ${dbConnected ? 'Base de données connectée' : 'Données de test'}`);
    console.log('📡 Routes disponibles:');
    console.log('  - GET  /api/clients (tous les clients)');
    console.log('  - GET  /api/clients/:id/contracts (contrats du client) ⭐ CRITIQUE');
    console.log('  - GET  /api/clients/qr/:codeqr (recherche client par QR) 📱 NOUVEAU');
    console.log('  - GET  /api/secteurs/:id (secteur par ID avec coordonnées) 📍');
    console.log('  - GET  /api/clients/:id/last-consommation (dernière consommation du client)');
    console.log('  - GET  /api/contracts/:id/last-consommation (dernière consommation)');
    console.log('  - GET  /api/contracts (tous les contrats)');
    console.log('  - GET  /api/consommations (toutes les consommations)');
    console.log('  - POST /api/consommations (ajouter consommation)');
    console.log('🧾 Routes de factures:');
    console.log('  - GET  /api/factures (toutes les factures)');
    console.log('  - GET  /api/factures/:id (détails facture)');
    console.log('  - GET  /api/factures/:id/pdf (télécharger PDF)');
    console.log('  - PUT  /api/factures/:id/status (changer statut)');
    console.log('  - POST /api/factures (créer facture manuelle)');
    console.log('  - DELETE /api/factures/:id (supprimer facture)');
    console.log('✅ PRÊT À RECEVOIR LES REQUÊTES !');
  });
}

// Démarrer le serveur
startServer().catch(console.error);