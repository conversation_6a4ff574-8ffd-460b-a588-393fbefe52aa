@echo off
title Verification Finale - Port 4002
color 0A

echo.
echo ========================================
echo    🎯 VERIFICATION FINALE PORT 4002
echo ========================================
echo.

echo 🔍 1. TEST DE CONNEXION AU SERVEUR...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 10; Write-Host '✅ SERVEUR ACCESSIBLE:' $response.message; Write-Host '📡 Port confirme:' $response.port } catch { Write-Host '❌ SERVEUR INACCESSIBLE - Verifiez que le serveur est demarre' }"

echo.
echo 🔍 2. TEST DE LA PAGE CONSOMMATION...
echo.
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:4002/consommation' -TimeoutSec 10; Write-Host '✅ PAGE CONSOMMATION ACCESSIBLE - Status:' $response.StatusCode; if ($response.StatusCode -eq 200) { Write-Host '🎯 URL DEMANDEE FONCTIONNELLE !' } } catch { Write-Host '❌ PAGE CONSOMMATION INACCESSIBLE:' $_.Exception.Message }"

echo.
echo 🔍 3. VERIFICATION DES APIs ESSENTIELLES...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '✅ API Secteurs:' $response.count 'secteurs' } catch { Write-Host '❌ API Secteurs erreur' }"

powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/clients' -TimeoutSec 5; Write-Host '✅ API Clients:' $response.count 'clients' } catch { Write-Host '❌ API Clients erreur' }"

echo.
echo 🔍 4. TEST D'AUTHENTIFICATION...
echo.
powershell -Command "try { $body = @{ email='<EMAIL>'; password='Tech123' } | ConvertTo-Json; $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/auth/login' -Method POST -Body $body -ContentType 'application/json' -TimeoutSec 5; Write-Host '✅ Authentification OK:' $response.user.nom $response.user.role } catch { Write-Host '❌ Authentification erreur' }"

echo.
echo 🔍 5. VERIFICATION DE L'APPLICATION MOBILE...
echo.
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:19006' -TimeoutSec 3; Write-Host '✅ Application mobile accessible - Status:' $response.StatusCode } catch { Write-Host '⚠️ Application mobile non demarree (optionnel)' }"

echo.
echo ========================================
echo    📊 RAPPORT FINAL
echo ========================================
echo.
echo 🎯 URL DEMANDEE: http://localhost:4002/consommation
echo.
echo ✅ STATUT:
if exist "serveur-port-4002.js" (
    echo    - Serveur configure: OUI
) else (
    echo    - Serveur configure: NON
)

echo    - Port 4002: Teste ci-dessus
echo    - Page /consommation: Testee ci-dessus
echo    - APIs fonctionnelles: Testees ci-dessus
echo    - Authentification: Testee ci-dessus
echo.
echo 🔧 CONFIGURATION:
echo    - Serveur backend: Port 4002
echo    - Application mobile: Port 19006 (optionnel)
echo    - Base de donnees: Simulee en memoire
echo    - APIs REST: Toutes disponibles
echo.
echo 📋 PAGES DISPONIBLES:
echo    - Accueil: http://localhost:4002/
echo    - Consommation: http://localhost:4002/consommation
echo    - API Secteurs: http://localhost:4002/api/secteurs
echo    - API Clients: http://localhost:4002/api/clients
echo.
echo 🔑 COMPTES DE TEST:
echo    - Technicien: <EMAIL> / Tech123
echo    - Admin: <EMAIL> / Admin123
echo.
echo 📱 APPLICATION MOBILE (optionnelle):
echo    - URL: http://localhost:19006
echo    - Utilise automatiquement le port 4002
echo    - Interface complete avec cartes Google Maps
echo.
echo ⚠️ INSTRUCTIONS:
echo.
echo 1. Si le serveur n'est pas accessible:
echo    - Lancez: DEMARRER-PORT-4002.bat
echo    - Attendez 10 secondes
echo    - Retestez: http://localhost:4002/consommation
echo.
echo 2. Si vous voulez l'application mobile complete:
echo    - Lancez: DEMARRER-PORT-4002.bat
echo    - Attendez le demarrage complet
echo    - Allez sur: http://localhost:19006
echo    - Connectez-vous avec les comptes de test
echo.
echo 3. Pour utiliser uniquement la page web:
echo    - Allez directement sur: http://localhost:4002/consommation
echo    - Cette page affiche l'interface de consommation
echo    - Toutes les APIs sont disponibles
echo.
echo 🎯 VOTRE URL EST PRETE:
echo    http://localhost:4002/consommation
echo.
echo 🌐 OUVERTURE DE L'URL...
start http://localhost:4002/consommation

echo.
echo ✅ VERIFICATION TERMINEE !
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
