{"ast": null, "code": "import _createClass from \"@babel/runtime/helpers/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _wrapNativeSuper from \"@babel/runtime/helpers/wrapNativeSuper\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nexport var CodedError = function (_Error) {\n  function CodedError(code, message) {\n    var _this;\n    _classCallCheck(this, CodedError);\n    _this = _callSuper(this, CodedError, [message]);\n    _this.code = code;\n    return _this;\n  }\n  _inherits(CodedError, _Error);\n  return _createClass(CodedError);\n}(_wrapNativeSuper(Error));", "map": {"version": 3, "names": ["CodedError", "_Error", "code", "message", "_this", "_classCallCheck", "_callSuper", "_inherits", "_createClass", "_wrapNativeSuper", "Error"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\expo-modules-core\\src\\errors\\CodedError.ts"], "sourcesContent": ["/**\n * A general error class that should be used for all errors in Expo modules.\n * Guarantees a `code` field that can be used to differentiate between different\n * types of errors without further subclassing Error.\n */\nexport class CodedError extends Error {\n  code: string;\n  info?: any;\n\n  constructor(code: string, message: string) {\n    super(message);\n    this.code = code;\n  }\n}\n"], "mappings": ";;;;;;;;AAKA,WAAaA,UAAW,aAAAC,MAAA;EAItB,SAAAD,WAAYE,IAAY,EAAEC,OAAe;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAL,UAAA;IACvCI,KAAA,GAAAE,UAAA,OAAAN,UAAA,GAAMG,OAAO;IACbC,KAAA,CAAKF,IAAI,GAAGA,IAAI;IAAC,OAAAE,KAAA;EACnB;EAACG,SAAA,CAAAP,UAAA,EAAAC,MAAA;EAAA,OAAAO,YAAA,CAAAR,UAAA;AAAA,EAAAS,gBAAA,CAP6BC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}