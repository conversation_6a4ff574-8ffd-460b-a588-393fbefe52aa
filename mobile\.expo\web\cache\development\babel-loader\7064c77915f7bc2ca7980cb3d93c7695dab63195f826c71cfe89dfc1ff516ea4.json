{"ast": null, "code": "var getBoundingClientRect = function getBoundingClientRect(node) {\n  if (node != null) {\n    var isElement = node.nodeType === 1;\n    if (isElement && typeof node.getBoundingClientRect === 'function') {\n      return node.getBoundingClientRect();\n    }\n  }\n};\nexport default getBoundingClientRect;", "map": {"version": 3, "names": ["getBoundingClientRect", "node", "isElement", "nodeType"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/modules/getBoundingClientRect/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar getBoundingClientRect = node => {\n  if (node != null) {\n    var isElement = node.nodeType === 1; /* Node.ELEMENT_NODE */\n    if (isElement && typeof node.getBoundingClientRect === 'function') {\n      return node.getBoundingClientRect();\n    }\n  }\n};\nexport default getBoundingClientRect;"], "mappings": "AASA,IAAIA,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAGC,IAAI,EAAI;EAClC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,IAAIC,SAAS,GAAGD,IAAI,CAACE,QAAQ,KAAK,CAAC;IACnC,IAAID,SAAS,IAAI,OAAOD,IAAI,CAACD,qBAAqB,KAAK,UAAU,EAAE;MACjE,OAAOC,IAAI,CAACD,qBAAqB,CAAC,CAAC;IACrC;EACF;AACF,CAAC;AACD,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}