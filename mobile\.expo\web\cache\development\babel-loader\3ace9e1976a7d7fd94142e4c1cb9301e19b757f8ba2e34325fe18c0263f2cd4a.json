{"ast": null, "code": "import Easing from \"react-native-web/dist/exports/Easing\";\nexport var TransitionIOSSpec = {\n  animation: 'spring',\n  config: {\n    stiffness: 1000,\n    damping: 500,\n    mass: 3,\n    overshootClamping: true,\n    restDisplacementThreshold: 10,\n    restSpeedThreshold: 10\n  }\n};\nexport var FadeInFromBottomAndroidSpec = {\n  animation: 'timing',\n  config: {\n    duration: 350,\n    easing: Easing.out(Easing.poly(5))\n  }\n};\nexport var FadeOutToBottomAndroidSpec = {\n  animation: 'timing',\n  config: {\n    duration: 150,\n    easing: Easing.in(Easing.linear)\n  }\n};\nexport var RevealFromBottomAndroidSpec = {\n  animation: 'timing',\n  config: {\n    duration: 425,\n    easing: Easing.bezier(0.35, 0.45, 0, 1)\n  }\n};\nexport var ScaleFromCenterAndroidSpec = {\n  animation: 'timing',\n  config: {\n    duration: 400,\n    easing: Easing.bezier(0.35, 0.45, 0, 1)\n  }\n};\nexport var BottomSheetSlideInSpec = {\n  animation: 'timing',\n  config: {\n    duration: 250,\n    easing: function easing(t) {\n      return Math.cos((t + 1) * Math.PI) / 2.0 + 0.5;\n    }\n  }\n};\nexport var BottomSheetSlideOutSpec = {\n  animation: 'timing',\n  config: {\n    duration: 200,\n    easing: function easing(t) {\n      return t === 1.0 ? 1 : Math.pow(t, 2);\n    }\n  }\n};", "map": {"version": 3, "names": ["TransitionIOSSpec", "animation", "config", "stiffness", "damping", "mass", "overshootClamping", "restDisplacementThreshold", "restSpeedThreshold", "FadeInFromBottomAndroidSpec", "duration", "easing", "Easing", "out", "poly", "FadeOutToBottomAndroidSpec", "in", "linear", "RevealFromBottomAndroidSpec", "bezier", "ScaleFromCenterAndroidSpec", "BottomSheetSlideInSpec", "t", "Math", "cos", "PI", "BottomSheetSlideOutSpec", "pow"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\stack\\src\\TransitionConfigs\\TransitionSpecs.tsx"], "sourcesContent": ["import { Easing } from 'react-native';\n\nimport type { TransitionSpec } from '../types';\n\n/**\n * Exact values from UINavigationController's animation configuration.\n */\nexport const TransitionIOSSpec: TransitionSpec = {\n  animation: 'spring',\n  config: {\n    stiffness: 1000,\n    damping: 500,\n    mass: 3,\n    overshootClamping: true,\n    restDisplacementThreshold: 10,\n    restSpeedThreshold: 10,\n  },\n};\n\n/**\n * Configuration for activity open animation from Android Nougat.\n * See http://aosp.opersys.com/xref/android-7.1.2_r37/xref/frameworks/base/core/res/res/anim/activity_open_enter.xml\n */\nexport const FadeInFromBottomAndroidSpec: TransitionSpec = {\n  animation: 'timing',\n  config: {\n    duration: 350,\n    easing: Easing.out(Easing.poly(5)),\n  },\n};\n\n/**\n * Configuration for activity close animation from Android Nougat.\n * See http://aosp.opersys.com/xref/android-7.1.2_r37/xref/frameworks/base/core/res/res/anim/activity_close_exit.xml\n */\nexport const FadeOutToBottomAndroidSpec: TransitionSpec = {\n  animation: 'timing',\n  config: {\n    duration: 150,\n    easing: Easing.in(Easing.linear),\n  },\n};\n\n/**\n * Approximate configuration for activity open animation from Android Pie.\n * See http://aosp.opersys.com/xref/android-9.0.0_r47/xref/frameworks/base/core/res/res/anim/activity_open_enter.xml\n */\nexport const RevealFromBottomAndroidSpec: TransitionSpec = {\n  animation: 'timing',\n  config: {\n    duration: 425,\n    // This is super rough approximation of the path used for the curve by android\n    // See http://aosp.opersys.com/xref/android-9.0.0_r47/xref/frameworks/base/core/res/res/interpolator/fast_out_extra_slow_in.xml\n    easing: Easing.bezier(0.35, 0.45, 0, 1),\n  },\n};\n\n/**\n * Approximate configuration for activity open animation from Android Q.\n * See http://aosp.opersys.com/xref/android-10.0.0_r2/xref/frameworks/base/core/res/res/anim/activity_open_enter.xml\n */\nexport const ScaleFromCenterAndroidSpec: TransitionSpec = {\n  animation: 'timing',\n  config: {\n    duration: 400,\n    // This is super rough approximation of the path used for the curve by android\n    // See http://aosp.opersys.com/xref/android-10.0.0_r2/xref/frameworks/base/core/res/res/interpolator/fast_out_extra_slow_in.xml\n    easing: Easing.bezier(0.35, 0.45, 0, 1),\n  },\n};\n\n/**\n * Configuration for bottom sheet slide in animation from Material Design.\n * See https://github.com/material-components/material-components-android/blob/fd3639092e1ffef9dc11bcedf79f32801d85e898/lib/java/com/google/android/material/bottomsheet/res/anim/mtrl_bottom_sheet_slide_in.xml\n */\nexport const BottomSheetSlideInSpec: TransitionSpec = {\n  animation: 'timing',\n  config: {\n    duration: 250,\n    // See https://android.googlesource.com/platform/frameworks/base/+/master/core/java/android/view/animation/AccelerateDecelerateInterpolator.java\n    easing: (t) => Math.cos((t + 1) * Math.PI) / 2.0 + 0.5,\n  },\n};\n\n/**\n * Configuration for bottom sheet slide out animation from Material Design.\n * See https://github.com/material-components/material-components-android/blob/fd3639092e1ffef9dc11bcedf79f32801d85e898/lib/java/com/google/android/material/bottomsheet/res/anim/mtrl_bottom_sheet_slide_in.xml\n */\nexport const BottomSheetSlideOutSpec: TransitionSpec = {\n  animation: 'timing',\n  config: {\n    duration: 200,\n    // See https://android.googlesource.com/platform/frameworks/base/+/master/core/java/android/view/animation/AccelerateInterpolator.java\n    easing: (t) => (t === 1.0 ? 1 : Math.pow(t, 2)),\n  },\n};\n"], "mappings": ";AAOA,OAAO,IAAMA,iBAAiC,GAAG;EAC/CC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACNC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,IAAI,EAAE,CAAC;IACPC,iBAAiB,EAAE,IAAI;IACvBC,yBAAyB,EAAE,EAAE;IAC7BC,kBAAkB,EAAE;EACtB;AACF,CAAC;AAMD,OAAO,IAAMC,2BAA2C,GAAG;EACzDR,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACNQ,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAEC,MAAM,CAACC,GAAG,CAACD,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;EACnC;AACF,CAAC;AAMD,OAAO,IAAMC,0BAA0C,GAAG;EACxDd,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACNQ,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAEC,MAAM,CAACI,EAAE,CAACJ,MAAM,CAACK,MAAM;EACjC;AACF,CAAC;AAMD,OAAO,IAAMC,2BAA2C,GAAG;EACzDjB,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACNQ,QAAQ,EAAE,GAAG;IAGbC,MAAM,EAAEC,MAAM,CAACO,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;EACxC;AACF,CAAC;AAMD,OAAO,IAAMC,0BAA0C,GAAG;EACxDnB,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACNQ,QAAQ,EAAE,GAAG;IAGbC,MAAM,EAAEC,MAAM,CAACO,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;EACxC;AACF,CAAC;AAMD,OAAO,IAAME,sBAAsC,GAAG;EACpDpB,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACNQ,QAAQ,EAAE,GAAG;IAEbC,MAAM,EAAG,SAATA,MAAMA,CAAGW,CAAC;MAAA,OAAKC,IAAI,CAACC,GAAG,CAAC,CAACF,CAAC,GAAG,CAAC,IAAIC,IAAI,CAACE,EAAE,CAAC,GAAG,GAAG,GAAG;IAAA;EACrD;AACF,CAAC;AAMD,OAAO,IAAMC,uBAAuC,GAAG;EACrDzB,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACNQ,QAAQ,EAAE,GAAG;IAEbC,MAAM,EAAG,SAATA,MAAMA,CAAGW,CAAC;MAAA,OAAMA,CAAC,KAAK,GAAG,GAAG,CAAC,GAAGC,IAAI,CAACI,GAAG,CAACL,CAAC,EAAE,CAAC;IAAA;EAC/C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}