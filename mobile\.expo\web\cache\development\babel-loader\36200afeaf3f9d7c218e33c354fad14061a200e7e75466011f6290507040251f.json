{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"children\", \"enabled\", \"onValueChange\", \"selectedValue\", \"style\", \"testID\", \"itemStyle\", \"mode\", \"prompt\"];\nimport * as React from 'react';\nimport createElement from \"../createElement\";\nimport useMergeRefs from \"../../modules/useMergeRefs\";\nimport usePlatformMethods from \"../../modules/usePlatformMethods\";\nimport PickerItem from \"./PickerItem\";\nimport StyleSheet from \"../StyleSheet\";\nvar Picker = React.forwardRef(function (props, forwardedRef) {\n  var children = props.children,\n    enabled = props.enabled,\n    onValueChange = props.onValueChange,\n    selectedValue = props.selectedValue,\n    style = props.style,\n    testID = props.testID,\n    itemStyle = props.itemStyle,\n    mode = props.mode,\n    prompt = props.prompt,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  var hostRef = React.useRef(null);\n  function handleChange(e) {\n    var _e$target = e.target,\n      selectedIndex = _e$target.selectedIndex,\n      value = _e$target.value;\n    if (onValueChange) {\n      onValueChange(value, selectedIndex);\n    }\n  }\n  var supportedProps = _objectSpread({\n    children: children,\n    disabled: enabled === false ? true : undefined,\n    onChange: handleChange,\n    style: [styles.initial, style],\n    testID: testID,\n    value: selectedValue\n  }, other);\n  var platformMethodsRef = usePlatformMethods(supportedProps);\n  var setRef = useMergeRefs(hostRef, platformMethodsRef, forwardedRef);\n  supportedProps.ref = setRef;\n  return createElement('select', supportedProps);\n});\nPicker.Item = PickerItem;\nvar styles = StyleSheet.create({\n  initial: {\n    fontFamily: 'System',\n    fontSize: 'inherit',\n    margin: 0\n  }\n});\nexport default Picker;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutPropertiesLoose", "_excluded", "React", "createElement", "useMergeRefs", "usePlatformMethods", "PickerItem", "StyleSheet", "Picker", "forwardRef", "props", "forwardedRef", "children", "enabled", "onValueChange", "selected<PERSON><PERSON><PERSON>", "style", "testID", "itemStyle", "mode", "prompt", "other", "hostRef", "useRef", "handleChange", "e", "_e$target", "target", "selectedIndex", "value", "supportedProps", "disabled", "undefined", "onChange", "styles", "initial", "platformMethodsRef", "setRef", "ref", "<PERSON><PERSON>", "create", "fontFamily", "fontSize", "margin"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/Picker/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"children\", \"enabled\", \"onValueChange\", \"selectedValue\", \"style\", \"testID\", \"itemStyle\", \"mode\", \"prompt\"];\nimport * as React from 'react';\nimport createElement from '../createElement';\nimport useMergeRefs from '../../modules/useMergeRefs';\nimport usePlatformMethods from '../../modules/usePlatformMethods';\nimport PickerItem from './PickerItem';\nimport StyleSheet from '../StyleSheet';\nvar Picker = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var children = props.children,\n    enabled = props.enabled,\n    onValueChange = props.onValueChange,\n    selectedValue = props.selectedValue,\n    style = props.style,\n    testID = props.testID,\n    itemStyle = props.itemStyle,\n    mode = props.mode,\n    prompt = props.prompt,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  var hostRef = React.useRef(null);\n  function handleChange(e) {\n    var _e$target = e.target,\n      selectedIndex = _e$target.selectedIndex,\n      value = _e$target.value;\n    if (onValueChange) {\n      onValueChange(value, selectedIndex);\n    }\n  }\n\n  // $FlowFixMe\n  var supportedProps = _objectSpread({\n    children,\n    disabled: enabled === false ? true : undefined,\n    onChange: handleChange,\n    style: [styles.initial, style],\n    testID,\n    value: selectedValue\n  }, other);\n  var platformMethodsRef = usePlatformMethods(supportedProps);\n  var setRef = useMergeRefs(hostRef, platformMethodsRef, forwardedRef);\n  supportedProps.ref = setRef;\n  return createElement('select', supportedProps);\n});\n\n// $FlowFixMe\nPicker.Item = PickerItem;\nvar styles = StyleSheet.create({\n  initial: {\n    fontFamily: 'System',\n    fontSize: 'inherit',\n    margin: 0\n  }\n});\nexport default Picker;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,sCAAsC;AAChE,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC3H,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa;AACpB,OAAOC,YAAY;AACnB,OAAOC,kBAAkB;AACzB,OAAOC,UAAU;AACjB,OAAOC,UAAU;AACjB,IAAIC,MAAM,GAAgBN,KAAK,CAACO,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EAClE,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,KAAK,GAAGrB,6BAA6B,CAACU,KAAK,EAAET,SAAS,CAAC;EACzD,IAAIqB,OAAO,GAAGpB,KAAK,CAACqB,MAAM,CAAC,IAAI,CAAC;EAChC,SAASC,YAAYA,CAACC,CAAC,EAAE;IACvB,IAAIC,SAAS,GAAGD,CAAC,CAACE,MAAM;MACtBC,aAAa,GAAGF,SAAS,CAACE,aAAa;MACvCC,KAAK,GAAGH,SAAS,CAACG,KAAK;IACzB,IAAIf,aAAa,EAAE;MACjBA,aAAa,CAACe,KAAK,EAAED,aAAa,CAAC;IACrC;EACF;EAGA,IAAIE,cAAc,GAAG/B,aAAa,CAAC;IACjCa,QAAQ,EAARA,QAAQ;IACRmB,QAAQ,EAAElB,OAAO,KAAK,KAAK,GAAG,IAAI,GAAGmB,SAAS;IAC9CC,QAAQ,EAAET,YAAY;IACtBR,KAAK,EAAE,CAACkB,MAAM,CAACC,OAAO,EAAEnB,KAAK,CAAC;IAC9BC,MAAM,EAANA,MAAM;IACNY,KAAK,EAAEd;EACT,CAAC,EAAEM,KAAK,CAAC;EACT,IAAIe,kBAAkB,GAAG/B,kBAAkB,CAACyB,cAAc,CAAC;EAC3D,IAAIO,MAAM,GAAGjC,YAAY,CAACkB,OAAO,EAAEc,kBAAkB,EAAEzB,YAAY,CAAC;EACpEmB,cAAc,CAACQ,GAAG,GAAGD,MAAM;EAC3B,OAAOlC,aAAa,CAAC,QAAQ,EAAE2B,cAAc,CAAC;AAChD,CAAC,CAAC;AAGFtB,MAAM,CAAC+B,IAAI,GAAGjC,UAAU;AACxB,IAAI4B,MAAM,GAAG3B,UAAU,CAACiC,MAAM,CAAC;EAC7BL,OAAO,EAAE;IACPM,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AACF,eAAenC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}