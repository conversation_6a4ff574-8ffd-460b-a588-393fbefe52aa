@echo off
title Debug Secteurs avec Logs
color 0A

echo.
echo ========================================
echo    🐛 DEBUG SECTEURS AVEC LOGS
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR DE TEST...
echo.
start "🧪 Serveur Debug" cmd /k "title SERVEUR DEBUG SECTEURS && color 0B && echo ========================================== && echo    🧪 SERVEUR DEBUG SECTEURS && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Logs: Detailles && echo ✅ CORS: Permissif && echo ✅ Debug: Actif && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 🔍 5. TEST RAPIDE DE L'API...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '✅ API OK:' $response.count 'secteurs' } catch { Write-Host '❌ API erreur' }"

echo.
echo 📱 6. DEMARRAGE DE L'APPLICATION AVEC DEBUG...
echo.
cd mobile
start "📱 App Debug" cmd /k "title APPLICATION DEBUG && color 0D && echo ========================================== && echo    📱 APPLICATION DEBUG && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Debug: Logs detailles && echo ✅ Console: Ouvrez F12 pour voir les logs && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 7. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 8. OUVERTURE AVEC INSTRUCTIONS DEBUG...
start http://localhost:19006

echo.
echo ========================================
echo    🐛 DEBUG SECTEURS ACTIF !
echo ========================================
echo.
echo 🎯 LOGS DE DEBUG AJOUTES:
echo.
echo ✅ SERVEUR (Console serveur):
echo    - Logs detailles pour chaque requete
echo    - Affichage des donnees retournees
echo    - Verification CORS
echo.
echo ✅ FRONTEND (Console navigateur):
echo    - Logs de fetchSecteurs() detailles
echo    - Verification des donnees recues
echo    - Logs de rendu des secteurs
echo    - Etat des variables React
echo.
echo 🧪 INSTRUCTIONS DE DEBUG:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔧 Ouvrez la console du navigateur:
echo    - Appuyez sur F12
echo    - Allez dans l'onglet "Console"
echo    - Vous verrez tous les logs de debug
echo.
echo 3. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 4. 📱 Allez dans "Consommation":
echo    - Regardez les logs dans la console
echo    - Cherchez les messages commencant par:
echo      * 🔍 DEBUT fetchSecteurs
echo      * 📊 Reponse API secteurs
echo      * 🎯 RENDU SECTEURS
echo.
echo 5. 🔍 Analysez les logs:
echo    - fetchSecteurs() est-il appele ?
echo    - L'API retourne-t-elle des donnees ?
echo    - setSecteurs() recoit-il les donnees ?
echo    - Le rendu affiche-t-il les secteurs ?
echo.
echo 📊 LOGS A SURVEILLER:
echo.
echo ✅ LOGS NORMAUX:
echo    🔍 DEBUT fetchSecteurs
echo    📥 Reponse recue, status: 200 OK
echo    📊 Reponse API secteurs COMPLETE: {success: true, data: [...]}
echo    ✅ AVANT setSecteurs, donnees valides: 5 secteurs
echo    ✅ APRES setSecteurs - Secteurs charges: 5
echo    🎯 RENDU SECTEURS - Etat actuel: [5 secteurs]
echo    ✅ Rendu de 5 secteurs
echo.
echo ❌ LOGS D'ERREUR POSSIBLES:
echo    ❌ Erreur HTTP: 404 ou 500
echo    ❌ ERREUR CATCH dans fetchSecteurs
echo    ❌ Format de reponse inattendu
echo    ❌ secteurs n'est pas un array
echo.
echo 🎯 SOLUTIONS SELON LES LOGS:
echo.
echo 1. Si fetchSecteurs() n'est pas appele:
echo    → Probleme dans useEffect()
echo.
echo 2. Si API retourne erreur:
echo    → Probleme serveur ou URL
echo.
echo 3. Si donnees recues mais pas affichees:
echo    → Probleme dans setSecteurs() ou rendu
echo.
echo 4. Si secteurs vide dans le rendu:
echo    → Probleme de state React
echo.
echo 🌐 PAGES OUVERTES:
echo    - Application: http://localhost:19006
echo    - API Test: http://localhost:4002/api/secteurs
echo.
echo ✅ DEBUG PRET !
echo    Suivez les instructions ci-dessus pour identifier le probleme.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
