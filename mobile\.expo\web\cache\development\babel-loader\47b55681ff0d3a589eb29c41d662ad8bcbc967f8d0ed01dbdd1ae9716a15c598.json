{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport SafeAreaView from \"react-native-web/dist/exports/SafeAreaView\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport { BarCodeScanner } from 'expo-barcode-scanner';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar QRScannerScreen = function QRScannerScreen(_ref) {\n  var _route$params;\n  var navigation = _ref.navigation,\n    route = _ref.route;\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    hasPermission = _useState2[0],\n    setHasPermission = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    scanned = _useState4[0],\n    setScanned = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    scanning = _useState6[0],\n    setScanning = _useState6[1];\n  var API_BASE_URL = 'http://***********:4000';\n  var user = (_route$params = route.params) == null ? void 0 : _route$params.user;\n  useEffect(function () {\n    getBarCodeScannerPermissions();\n  }, []);\n  var getBarCodeScannerPermissions = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      var _yield$BarCodeScanner = yield BarCodeScanner.requestPermissionsAsync(),\n        status = _yield$BarCodeScanner.status;\n      setHasPermission(status === 'granted');\n    });\n    return function getBarCodeScannerPermissions() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var handleBarCodeScanned = function () {\n    var _ref4 = _asyncToGenerator(function* (_ref3) {\n      var type = _ref3.type,\n        data = _ref3.data;\n      if (scanned) return;\n      setScanned(true);\n      setScanning(false);\n      console.log('QR Code scanné:', data);\n      try {\n        var response = yield fetch(`${API_BASE_URL}/api/scanner/qr/${data}`);\n        var result = yield response.json();\n        if (result.success && result.client) {\n          var client = result.client;\n          Alert.alert('✅ Client Trouvé', `${client.nom} ${client.prenom}\\n📍 ${client.adresse}\\n🏙️ ${client.ville}`, [{\n            text: 'Fermer',\n            style: 'cancel'\n          }, {\n            text: 'Voir Consommation',\n            onPress: function onPress() {\n              navigation.navigate('Consommation', {\n                client: client,\n                user: user\n              });\n            }\n          }, {\n            text: 'Voir Profil',\n            onPress: function onPress() {\n              navigation.navigate('Clients', {\n                user: user,\n                selectedClient: client\n              });\n            }\n          }]);\n        } else {\n          Alert.alert('❌ Client Non Trouvé', 'Ce code QR ne correspond à aucun client dans la base de données.', [{\n            text: 'OK',\n            onPress: function onPress() {\n              return setScanned(false);\n            }\n          }]);\n        }\n      } catch (error) {\n        console.error('Erreur lors de la recherche du client:', error);\n        Alert.alert('Erreur', 'Impossible de vérifier le code QR. Vérifiez votre connexion.', [{\n          text: 'OK',\n          onPress: function onPress() {\n            return setScanned(false);\n          }\n        }]);\n      }\n    });\n    return function handleBarCodeScanned(_x) {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var startScanning = function startScanning() {\n    setScanned(false);\n    setScanning(true);\n  };\n  var stopScanning = function stopScanning() {\n    setScanning(false);\n    setScanned(false);\n  };\n  if (hasPermission === null) {\n    return _jsx(View, {\n      style: styles.permissionContainer,\n      children: _jsx(Text, {\n        style: styles.permissionText,\n        children: \"Demande d'autorisation cam\\xE9ra...\"\n      })\n    });\n  }\n  if (hasPermission === false) {\n    return _jsxs(View, {\n      style: styles.permissionContainer,\n      children: [_jsx(Text, {\n        style: styles.permissionText,\n        children: \"\\u274C Acc\\xE8s \\xE0 la cam\\xE9ra refus\\xE9\"\n      }), _jsx(Text, {\n        style: styles.permissionSubText,\n        children: \"Veuillez autoriser l'acc\\xE8s \\xE0 la cam\\xE9ra dans les param\\xE8tres de l'application\"\n      }), _jsx(TouchableOpacity, {\n        style: styles.settingsButton,\n        onPress: getBarCodeScannerPermissions,\n        children: _jsx(Text, {\n          style: styles.settingsButtonText,\n          children: \"R\\xE9essayer\"\n        })\n      })]\n    });\n  }\n  return _jsxs(SafeAreaView, {\n    style: styles.container,\n    children: [_jsxs(View, {\n      style: styles.header,\n      children: [_jsx(Text, {\n        style: styles.title,\n        children: \"\\uD83D\\uDCF1 Scanner QR\"\n      }), _jsx(Text, {\n        style: styles.subtitle,\n        children: \"Scannez le code QR d'un client\"\n      })]\n    }), scanning ? _jsxs(View, {\n      style: styles.scannerContainer,\n      children: [_jsx(BarCodeScanner, {\n        onBarCodeScanned: scanned ? undefined : handleBarCodeScanned,\n        style: styles.scanner\n      }), _jsxs(View, {\n        style: styles.scannerOverlay,\n        children: [_jsx(View, {\n          style: styles.scannerFrame\n        }), _jsx(Text, {\n          style: styles.scannerText,\n          children: \"Placez le code QR dans le cadre\"\n        })]\n      }), _jsx(View, {\n        style: styles.scannerControls,\n        children: _jsx(TouchableOpacity, {\n          style: styles.stopButton,\n          onPress: stopScanning,\n          children: _jsx(Text, {\n            style: styles.stopButtonText,\n            children: \"Arr\\xEAter\"\n          })\n        })\n      })]\n    }) : _jsx(View, {\n      style: styles.instructionsContainer,\n      children: _jsxs(View, {\n        style: styles.instructionsContent,\n        children: [_jsx(Text, {\n          style: styles.instructionsIcon,\n          children: \"\\uD83D\\uDCF1\"\n        }), _jsx(Text, {\n          style: styles.instructionsTitle,\n          children: \"Scanner un Code QR\"\n        }), _jsx(Text, {\n          style: styles.instructionsText,\n          children: \"Appuyez sur \\\"D\\xE9marrer le scan\\\" pour ouvrir la cam\\xE9ra et scanner le code QR d'un client.\"\n        }), _jsxs(View, {\n          style: styles.featuresContainer,\n          children: [_jsx(Text, {\n            style: styles.featuresTitle,\n            children: \"Fonctionnalit\\xE9s :\"\n          }), _jsx(Text, {\n            style: styles.featureItem,\n            children: \"\\u2022 \\uD83D\\uDD0D Identification automatique du client\"\n          }), _jsx(Text, {\n            style: styles.featureItem,\n            children: \"\\u2022 \\uD83D\\uDCA7 Acc\\xE8s direct \\xE0 la consommation\"\n          }), _jsx(Text, {\n            style: styles.featureItem,\n            children: \"\\u2022 \\uD83D\\uDC64 Consultation du profil client\"\n          })]\n        }), _jsx(TouchableOpacity, {\n          style: styles.startButton,\n          onPress: startScanning,\n          children: _jsx(Text, {\n            style: styles.startButtonText,\n            children: \"\\uD83D\\uDCF7 D\\xE9marrer le scan\"\n          })\n        })]\n      })\n    })]\n  });\n};\nvar _Dimensions$get = Dimensions.get('window'),\n  width = _Dimensions$get.width,\n  height = _Dimensions$get.height;\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  permissionContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20\n  },\n  permissionText: {\n    fontSize: 18,\n    textAlign: 'center',\n    marginBottom: 10\n  },\n  permissionSubText: {\n    fontSize: 14,\n    color: '#666',\n    textAlign: 'center',\n    marginBottom: 20\n  },\n  settingsButton: {\n    backgroundColor: '#007AFF',\n    borderRadius: 8,\n    padding: 12,\n    paddingHorizontal: 20\n  },\n  settingsButtonText: {\n    color: '#fff',\n    fontWeight: 'bold'\n  },\n  header: {\n    backgroundColor: '#28a745',\n    padding: 20,\n    alignItems: 'center'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#fff'\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#fff',\n    opacity: 0.9\n  },\n  scannerContainer: {\n    flex: 1,\n    position: 'relative'\n  },\n  scanner: {\n    flex: 1\n  },\n  scannerOverlay: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  scannerFrame: {\n    width: 250,\n    height: 250,\n    borderWidth: 2,\n    borderColor: '#fff',\n    borderRadius: 10,\n    backgroundColor: 'transparent'\n  },\n  scannerText: {\n    color: '#fff',\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginTop: 20,\n    textAlign: 'center',\n    backgroundColor: 'rgba(0,0,0,0.5)',\n    padding: 10,\n    borderRadius: 5\n  },\n  scannerControls: {\n    position: 'absolute',\n    bottom: 50,\n    left: 0,\n    right: 0,\n    alignItems: 'center'\n  },\n  stopButton: {\n    backgroundColor: '#dc3545',\n    borderRadius: 25,\n    paddingVertical: 15,\n    paddingHorizontal: 30\n  },\n  stopButtonText: {\n    color: '#fff',\n    fontSize: 16,\n    fontWeight: 'bold'\n  },\n  instructionsContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    padding: 20\n  },\n  instructionsContent: {\n    backgroundColor: '#fff',\n    borderRadius: 15,\n    padding: 30,\n    alignItems: 'center',\n    elevation: 3,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 3\n  },\n  instructionsIcon: {\n    fontSize: 64,\n    marginBottom: 20\n  },\n  instructionsTitle: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 15,\n    textAlign: 'center'\n  },\n  instructionsText: {\n    fontSize: 16,\n    color: '#666',\n    textAlign: 'center',\n    lineHeight: 24,\n    marginBottom: 25\n  },\n  featuresContainer: {\n    alignSelf: 'stretch',\n    marginBottom: 30\n  },\n  featuresTitle: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 10\n  },\n  featureItem: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 5\n  },\n  startButton: {\n    backgroundColor: '#28a745',\n    borderRadius: 25,\n    paddingVertical: 15,\n    paddingHorizontal: 30\n  },\n  startButtonText: {\n    color: '#fff',\n    fontSize: 18,\n    fontWeight: 'bold'\n  }\n});\nexport default QRScannerScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "TouchableOpacity", "StyleSheet", "<PERSON><PERSON>", "SafeAreaView", "Dimensions", "BarCodeScanner", "jsx", "_jsx", "jsxs", "_jsxs", "QRScannerScreen", "_ref", "_route$params", "navigation", "route", "_useState", "_useState2", "_slicedToArray", "hasPermission", "setHasPermission", "_useState3", "_useState4", "scanned", "setScanned", "_useState5", "_useState6", "scanning", "setScanning", "API_BASE_URL", "user", "params", "getBarCodeScannerPermissions", "_ref2", "_asyncToGenerator", "_yield$BarCodeScanner", "requestPermissionsAsync", "status", "apply", "arguments", "handleBarCodeScanned", "_ref4", "_ref3", "type", "data", "console", "log", "response", "fetch", "result", "json", "success", "client", "alert", "nom", "prenom", "adresse", "ville", "text", "style", "onPress", "navigate", "selectedClient", "error", "_x", "startScanning", "stopScanning", "styles", "permissionContainer", "children", "permissionText", "permissionSubText", "settingsButton", "settingsButtonText", "container", "header", "title", "subtitle", "scannerContainer", "onBarCodeScanned", "undefined", "scanner", "scannerOverlay", "scannerFrame", "scannerText", "scannerControls", "stopButton", "stopButtonText", "instructionsContainer", "instructionsContent", "instructionsIcon", "instructionsTitle", "instructionsText", "featuresContainer", "featuresTitle", "featureItem", "startButton", "startButtonText", "_Dimensions$get", "get", "width", "height", "create", "flex", "backgroundColor", "justifyContent", "alignItems", "padding", "fontSize", "textAlign", "marginBottom", "color", "borderRadius", "paddingHorizontal", "fontWeight", "opacity", "position", "top", "left", "right", "bottom", "borderWidth", "borderColor", "marginTop", "paddingVertical", "elevation", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "lineHeight", "alignSelf"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/mobile/src/screens/QRScannerScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  View,\n  Text,\n  TouchableOpacity,\n  StyleSheet,\n  Alert,\n  SafeAreaView,\n  Dimensions,\n} from 'react-native';\nimport { BarCodeScanner } from 'expo-barcode-scanner';\n\nconst QRScannerScreen = ({ navigation, route }) => {\n  const [hasPermission, setHasPermission] = useState(null);\n  const [scanned, setScanned] = useState(false);\n  const [scanning, setScanning] = useState(false);\n\n  const API_BASE_URL = 'http://***********:4000'; // IP locale détectée\n  const user = route.params?.user;\n\n  useEffect(() => {\n    getBarCodeScannerPermissions();\n  }, []);\n\n  const getBarCodeScannerPermissions = async () => {\n    const { status } = await BarCodeScanner.requestPermissionsAsync();\n    setHasPermission(status === 'granted');\n  };\n\n  const handleBarCodeScanned = async ({ type, data }) => {\n    if (scanned) return;\n    \n    setScanned(true);\n    setScanning(false);\n    \n    console.log('QR Code scanné:', data);\n    \n    try {\n      // Rechercher le client par code QR\n      const response = await fetch(`${API_BASE_URL}/api/scanner/qr/${data}`);\n      const result = await response.json();\n\n      if (result.success && result.client) {\n        const client = result.client;\n        \n        Alert.alert(\n          '✅ Client Trouvé',\n          `${client.nom} ${client.prenom}\\n📍 ${client.adresse}\\n🏙️ ${client.ville}`,\n          [\n            { text: 'Fermer', style: 'cancel' },\n            {\n              text: 'Voir Consommation',\n              onPress: () => {\n                navigation.navigate('Consommation', { client, user });\n              }\n            },\n            {\n              text: 'Voir Profil',\n              onPress: () => {\n                navigation.navigate('Clients', { user, selectedClient: client });\n              }\n            }\n          ]\n        );\n      } else {\n        Alert.alert(\n          '❌ Client Non Trouvé',\n          'Ce code QR ne correspond à aucun client dans la base de données.',\n          [\n            { text: 'OK', onPress: () => setScanned(false) }\n          ]\n        );\n      }\n    } catch (error) {\n      console.error('Erreur lors de la recherche du client:', error);\n      Alert.alert(\n        'Erreur',\n        'Impossible de vérifier le code QR. Vérifiez votre connexion.',\n        [\n          { text: 'OK', onPress: () => setScanned(false) }\n        ]\n      );\n    }\n  };\n\n  const startScanning = () => {\n    setScanned(false);\n    setScanning(true);\n  };\n\n  const stopScanning = () => {\n    setScanning(false);\n    setScanned(false);\n  };\n\n  if (hasPermission === null) {\n    return (\n      <View style={styles.permissionContainer}>\n        <Text style={styles.permissionText}>Demande d'autorisation caméra...</Text>\n      </View>\n    );\n  }\n\n  if (hasPermission === false) {\n    return (\n      <View style={styles.permissionContainer}>\n        <Text style={styles.permissionText}>❌ Accès à la caméra refusé</Text>\n        <Text style={styles.permissionSubText}>\n          Veuillez autoriser l'accès à la caméra dans les paramètres de l'application\n        </Text>\n        <TouchableOpacity \n          style={styles.settingsButton}\n          onPress={getBarCodeScannerPermissions}\n        >\n          <Text style={styles.settingsButtonText}>Réessayer</Text>\n        </TouchableOpacity>\n      </View>\n    );\n  }\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <View style={styles.header}>\n        <Text style={styles.title}>📱 Scanner QR</Text>\n        <Text style={styles.subtitle}>\n          Scannez le code QR d'un client\n        </Text>\n      </View>\n\n      {scanning ? (\n        <View style={styles.scannerContainer}>\n          <BarCodeScanner\n            onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}\n            style={styles.scanner}\n          />\n          \n          <View style={styles.scannerOverlay}>\n            <View style={styles.scannerFrame} />\n            <Text style={styles.scannerText}>\n              Placez le code QR dans le cadre\n            </Text>\n          </View>\n\n          <View style={styles.scannerControls}>\n            <TouchableOpacity\n              style={styles.stopButton}\n              onPress={stopScanning}\n            >\n              <Text style={styles.stopButtonText}>Arrêter</Text>\n            </TouchableOpacity>\n          </View>\n        </View>\n      ) : (\n        <View style={styles.instructionsContainer}>\n          <View style={styles.instructionsContent}>\n            <Text style={styles.instructionsIcon}>📱</Text>\n            <Text style={styles.instructionsTitle}>Scanner un Code QR</Text>\n            <Text style={styles.instructionsText}>\n              Appuyez sur \"Démarrer le scan\" pour ouvrir la caméra et scanner le code QR d'un client.\n            </Text>\n            \n            <View style={styles.featuresContainer}>\n              <Text style={styles.featuresTitle}>Fonctionnalités :</Text>\n              <Text style={styles.featureItem}>• 🔍 Identification automatique du client</Text>\n              <Text style={styles.featureItem}>• 💧 Accès direct à la consommation</Text>\n              <Text style={styles.featureItem}>• 👤 Consultation du profil client</Text>\n            </View>\n\n            <TouchableOpacity\n              style={styles.startButton}\n              onPress={startScanning}\n            >\n              <Text style={styles.startButtonText}>📷 Démarrer le scan</Text>\n            </TouchableOpacity>\n          </View>\n        </View>\n      )}\n    </SafeAreaView>\n  );\n};\n\nconst { width, height } = Dimensions.get('window');\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  permissionContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n  },\n  permissionText: {\n    fontSize: 18,\n    textAlign: 'center',\n    marginBottom: 10,\n  },\n  permissionSubText: {\n    fontSize: 14,\n    color: '#666',\n    textAlign: 'center',\n    marginBottom: 20,\n  },\n  settingsButton: {\n    backgroundColor: '#007AFF',\n    borderRadius: 8,\n    padding: 12,\n    paddingHorizontal: 20,\n  },\n  settingsButtonText: {\n    color: '#fff',\n    fontWeight: 'bold',\n  },\n  header: {\n    backgroundColor: '#28a745',\n    padding: 20,\n    alignItems: 'center',\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#fff',\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#fff',\n    opacity: 0.9,\n  },\n  scannerContainer: {\n    flex: 1,\n    position: 'relative',\n  },\n  scanner: {\n    flex: 1,\n  },\n  scannerOverlay: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  scannerFrame: {\n    width: 250,\n    height: 250,\n    borderWidth: 2,\n    borderColor: '#fff',\n    borderRadius: 10,\n    backgroundColor: 'transparent',\n  },\n  scannerText: {\n    color: '#fff',\n    fontSize: 16,\n    fontWeight: 'bold',\n    marginTop: 20,\n    textAlign: 'center',\n    backgroundColor: 'rgba(0,0,0,0.5)',\n    padding: 10,\n    borderRadius: 5,\n  },\n  scannerControls: {\n    position: 'absolute',\n    bottom: 50,\n    left: 0,\n    right: 0,\n    alignItems: 'center',\n  },\n  stopButton: {\n    backgroundColor: '#dc3545',\n    borderRadius: 25,\n    paddingVertical: 15,\n    paddingHorizontal: 30,\n  },\n  stopButtonText: {\n    color: '#fff',\n    fontSize: 16,\n    fontWeight: 'bold',\n  },\n  instructionsContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    padding: 20,\n  },\n  instructionsContent: {\n    backgroundColor: '#fff',\n    borderRadius: 15,\n    padding: 30,\n    alignItems: 'center',\n    elevation: 3,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 2 },\n    shadowOpacity: 0.1,\n    shadowRadius: 3,\n  },\n  instructionsIcon: {\n    fontSize: 64,\n    marginBottom: 20,\n  },\n  instructionsTitle: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 15,\n    textAlign: 'center',\n  },\n  instructionsText: {\n    fontSize: 16,\n    color: '#666',\n    textAlign: 'center',\n    lineHeight: 24,\n    marginBottom: 25,\n  },\n  featuresContainer: {\n    alignSelf: 'stretch',\n    marginBottom: 30,\n  },\n  featuresTitle: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 10,\n  },\n  featureItem: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 5,\n  },\n  startButton: {\n    backgroundColor: '#28a745',\n    borderRadius: 25,\n    paddingVertical: 15,\n    paddingHorizontal: 30,\n  },\n  startButtonText: {\n    color: '#fff',\n    fontSize: 18,\n    fontWeight: 'bold',\n  },\n});\n\nexport default QRScannerScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,UAAA;AAUnD,SAASC,cAAc,QAAQ,sBAAsB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEtD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,IAAA,EAA8B;EAAA,IAAAC,aAAA;EAAA,IAAxBC,UAAU,GAAAF,IAAA,CAAVE,UAAU;IAAEC,KAAK,GAAAH,IAAA,CAALG,KAAK;EAC1C,IAAAC,SAAA,GAA0CnB,QAAQ,CAAC,IAAI,CAAC;IAAAoB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAjDG,aAAa,GAAAF,UAAA;IAAEG,gBAAgB,GAAAH,UAAA;EACtC,IAAAI,UAAA,GAA8BxB,QAAQ,CAAC,KAAK,CAAC;IAAAyB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAtCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAgC5B,QAAQ,CAAC,KAAK,CAAC;IAAA6B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAxCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAE5B,IAAMG,YAAY,GAAG,yBAAyB;EAC9C,IAAMC,IAAI,IAAAjB,aAAA,GAAGE,KAAK,CAACgB,MAAM,qBAAZlB,aAAA,CAAciB,IAAI;EAE/BhC,SAAS,CAAC,YAAM;IACdkC,4BAA4B,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,4BAA4B;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAC/C,IAAAC,qBAAA,SAAyB7B,cAAc,CAAC8B,uBAAuB,CAAC,CAAC;QAAzDC,MAAM,GAAAF,qBAAA,CAANE,MAAM;MACdjB,gBAAgB,CAACiB,MAAM,KAAK,SAAS,CAAC;IACxC,CAAC;IAAA,gBAHKL,4BAA4BA,CAAA;MAAA,OAAAC,KAAA,CAAAK,KAAA,OAAAC,SAAA;IAAA;EAAA,GAGjC;EAED,IAAMC,oBAAoB;IAAA,IAAAC,KAAA,GAAAP,iBAAA,CAAG,WAAAQ,KAAA,EAA0B;MAAA,IAAjBC,IAAI,GAAAD,KAAA,CAAJC,IAAI;QAAEC,IAAI,GAAAF,KAAA,CAAJE,IAAI;MAC9C,IAAIrB,OAAO,EAAE;MAEbC,UAAU,CAAC,IAAI,CAAC;MAChBI,WAAW,CAAC,KAAK,CAAC;MAElBiB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;MAEpC,IAAI;QAEF,IAAMG,QAAQ,SAASC,KAAK,CAAC,GAAGnB,YAAY,mBAAmBe,IAAI,EAAE,CAAC;QACtE,IAAMK,MAAM,SAASF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAEpC,IAAID,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACG,MAAM,EAAE;UACnC,IAAMA,MAAM,GAAGH,MAAM,CAACG,MAAM;UAE5BjD,KAAK,CAACkD,KAAK,CACT,iBAAiB,EACjB,GAAGD,MAAM,CAACE,GAAG,IAAIF,MAAM,CAACG,MAAM,QAAQH,MAAM,CAACI,OAAO,SAASJ,MAAM,CAACK,KAAK,EAAE,EAC3E,CACE;YAAEC,IAAI,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAS,CAAC,EACnC;YACED,IAAI,EAAE,mBAAmB;YACzBE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;cACb9C,UAAU,CAAC+C,QAAQ,CAAC,cAAc,EAAE;gBAAET,MAAM,EAANA,MAAM;gBAAEtB,IAAI,EAAJA;cAAK,CAAC,CAAC;YACvD;UACF,CAAC,EACD;YACE4B,IAAI,EAAE,aAAa;YACnBE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;cACb9C,UAAU,CAAC+C,QAAQ,CAAC,SAAS,EAAE;gBAAE/B,IAAI,EAAJA,IAAI;gBAAEgC,cAAc,EAAEV;cAAO,CAAC,CAAC;YAClE;UACF,CAAC,CAEL,CAAC;QACH,CAAC,MAAM;UACLjD,KAAK,CAACkD,KAAK,CACT,qBAAqB,EACrB,kEAAkE,EAClE,CACE;YAAEK,IAAI,EAAE,IAAI;YAAEE,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQpC,UAAU,CAAC,KAAK,CAAC;YAAA;UAAC,CAAC,CAEpD,CAAC;QACH;MACF,CAAC,CAAC,OAAOuC,KAAK,EAAE;QACdlB,OAAO,CAACkB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D5D,KAAK,CAACkD,KAAK,CACT,QAAQ,EACR,8DAA8D,EAC9D,CACE;UAAEK,IAAI,EAAE,IAAI;UAAEE,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQpC,UAAU,CAAC,KAAK,CAAC;UAAA;QAAC,CAAC,CAEpD,CAAC;MACH;IACF,CAAC;IAAA,gBAtDKgB,oBAAoBA,CAAAwB,EAAA;MAAA,OAAAvB,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,GAsDzB;EAED,IAAM0B,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1BzC,UAAU,CAAC,KAAK,CAAC;IACjBI,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,IAAMsC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;IACzBtC,WAAW,CAAC,KAAK,CAAC;IAClBJ,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,IAAIL,aAAa,KAAK,IAAI,EAAE;IAC1B,OACEX,IAAA,CAACT,IAAI;MAAC4D,KAAK,EAAEQ,MAAM,CAACC,mBAAoB;MAAAC,QAAA,EACtC7D,IAAA,CAACR,IAAI;QAAC2D,KAAK,EAAEQ,MAAM,CAACG,cAAe;QAAAD,QAAA,EAAC;MAAgC,CAAM;IAAC,CACvE,CAAC;EAEX;EAEA,IAAIlD,aAAa,KAAK,KAAK,EAAE;IAC3B,OACET,KAAA,CAACX,IAAI;MAAC4D,KAAK,EAAEQ,MAAM,CAACC,mBAAoB;MAAAC,QAAA,GACtC7D,IAAA,CAACR,IAAI;QAAC2D,KAAK,EAAEQ,MAAM,CAACG,cAAe;QAAAD,QAAA,EAAC;MAA0B,CAAM,CAAC,EACrE7D,IAAA,CAACR,IAAI;QAAC2D,KAAK,EAAEQ,MAAM,CAACI,iBAAkB;QAAAF,QAAA,EAAC;MAEvC,CAAM,CAAC,EACP7D,IAAA,CAACP,gBAAgB;QACf0D,KAAK,EAAEQ,MAAM,CAACK,cAAe;QAC7BZ,OAAO,EAAE5B,4BAA6B;QAAAqC,QAAA,EAEtC7D,IAAA,CAACR,IAAI;UAAC2D,KAAK,EAAEQ,MAAM,CAACM,kBAAmB;UAAAJ,QAAA,EAAC;QAAS,CAAM;MAAC,CACxC,CAAC;IAAA,CACf,CAAC;EAEX;EAEA,OACE3D,KAAA,CAACN,YAAY;IAACuD,KAAK,EAAEQ,MAAM,CAACO,SAAU;IAAAL,QAAA,GACpC3D,KAAA,CAACX,IAAI;MAAC4D,KAAK,EAAEQ,MAAM,CAACQ,MAAO;MAAAN,QAAA,GACzB7D,IAAA,CAACR,IAAI;QAAC2D,KAAK,EAAEQ,MAAM,CAACS,KAAM;QAAAP,QAAA,EAAC;MAAa,CAAM,CAAC,EAC/C7D,IAAA,CAACR,IAAI;QAAC2D,KAAK,EAAEQ,MAAM,CAACU,QAAS;QAAAR,QAAA,EAAC;MAE9B,CAAM,CAAC;IAAA,CACH,CAAC,EAEN1C,QAAQ,GACPjB,KAAA,CAACX,IAAI;MAAC4D,KAAK,EAAEQ,MAAM,CAACW,gBAAiB;MAAAT,QAAA,GACnC7D,IAAA,CAACF,cAAc;QACbyE,gBAAgB,EAAExD,OAAO,GAAGyD,SAAS,GAAGxC,oBAAqB;QAC7DmB,KAAK,EAAEQ,MAAM,CAACc;MAAQ,CACvB,CAAC,EAEFvE,KAAA,CAACX,IAAI;QAAC4D,KAAK,EAAEQ,MAAM,CAACe,cAAe;QAAAb,QAAA,GACjC7D,IAAA,CAACT,IAAI;UAAC4D,KAAK,EAAEQ,MAAM,CAACgB;QAAa,CAAE,CAAC,EACpC3E,IAAA,CAACR,IAAI;UAAC2D,KAAK,EAAEQ,MAAM,CAACiB,WAAY;UAAAf,QAAA,EAAC;QAEjC,CAAM,CAAC;MAAA,CACH,CAAC,EAEP7D,IAAA,CAACT,IAAI;QAAC4D,KAAK,EAAEQ,MAAM,CAACkB,eAAgB;QAAAhB,QAAA,EAClC7D,IAAA,CAACP,gBAAgB;UACf0D,KAAK,EAAEQ,MAAM,CAACmB,UAAW;UACzB1B,OAAO,EAAEM,YAAa;UAAAG,QAAA,EAEtB7D,IAAA,CAACR,IAAI;YAAC2D,KAAK,EAAEQ,MAAM,CAACoB,cAAe;YAAAlB,QAAA,EAAC;UAAO,CAAM;QAAC,CAClC;MAAC,CACf,CAAC;IAAA,CACH,CAAC,GAEP7D,IAAA,CAACT,IAAI;MAAC4D,KAAK,EAAEQ,MAAM,CAACqB,qBAAsB;MAAAnB,QAAA,EACxC3D,KAAA,CAACX,IAAI;QAAC4D,KAAK,EAAEQ,MAAM,CAACsB,mBAAoB;QAAApB,QAAA,GACtC7D,IAAA,CAACR,IAAI;UAAC2D,KAAK,EAAEQ,MAAM,CAACuB,gBAAiB;UAAArB,QAAA,EAAC;QAAE,CAAM,CAAC,EAC/C7D,IAAA,CAACR,IAAI;UAAC2D,KAAK,EAAEQ,MAAM,CAACwB,iBAAkB;UAAAtB,QAAA,EAAC;QAAkB,CAAM,CAAC,EAChE7D,IAAA,CAACR,IAAI;UAAC2D,KAAK,EAAEQ,MAAM,CAACyB,gBAAiB;UAAAvB,QAAA,EAAC;QAEtC,CAAM,CAAC,EAEP3D,KAAA,CAACX,IAAI;UAAC4D,KAAK,EAAEQ,MAAM,CAAC0B,iBAAkB;UAAAxB,QAAA,GACpC7D,IAAA,CAACR,IAAI;YAAC2D,KAAK,EAAEQ,MAAM,CAAC2B,aAAc;YAAAzB,QAAA,EAAC;UAAiB,CAAM,CAAC,EAC3D7D,IAAA,CAACR,IAAI;YAAC2D,KAAK,EAAEQ,MAAM,CAAC4B,WAAY;YAAA1B,QAAA,EAAC;UAAyC,CAAM,CAAC,EACjF7D,IAAA,CAACR,IAAI;YAAC2D,KAAK,EAAEQ,MAAM,CAAC4B,WAAY;YAAA1B,QAAA,EAAC;UAAmC,CAAM,CAAC,EAC3E7D,IAAA,CAACR,IAAI;YAAC2D,KAAK,EAAEQ,MAAM,CAAC4B,WAAY;YAAA1B,QAAA,EAAC;UAAkC,CAAM,CAAC;QAAA,CACtE,CAAC,EAEP7D,IAAA,CAACP,gBAAgB;UACf0D,KAAK,EAAEQ,MAAM,CAAC6B,WAAY;UAC1BpC,OAAO,EAAEK,aAAc;UAAAI,QAAA,EAEvB7D,IAAA,CAACR,IAAI;YAAC2D,KAAK,EAAEQ,MAAM,CAAC8B,eAAgB;YAAA5B,QAAA,EAAC;UAAmB,CAAM;QAAC,CAC/C,CAAC;MAAA,CACf;IAAC,CACH,CACP;EAAA,CACW,CAAC;AAEnB,CAAC;AAED,IAAA6B,eAAA,GAA0B7F,UAAU,CAAC8F,GAAG,CAAC,QAAQ,CAAC;EAA1CC,KAAK,GAAAF,eAAA,CAALE,KAAK;EAAEC,MAAM,GAAAH,eAAA,CAANG,MAAM;AAErB,IAAMlC,MAAM,GAAGjE,UAAU,CAACoG,MAAM,CAAC;EAC/B5B,SAAS,EAAE;IACT6B,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACDpC,mBAAmB,EAAE;IACnBmC,IAAI,EAAE,CAAC;IACPE,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDrC,cAAc,EAAE;IACdsC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE;EAChB,CAAC;EACDvC,iBAAiB,EAAE;IACjBqC,QAAQ,EAAE,EAAE;IACZG,KAAK,EAAE,MAAM;IACbF,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE;EAChB,CAAC;EACDtC,cAAc,EAAE;IACdgC,eAAe,EAAE,SAAS;IAC1BQ,YAAY,EAAE,CAAC;IACfL,OAAO,EAAE,EAAE;IACXM,iBAAiB,EAAE;EACrB,CAAC;EACDxC,kBAAkB,EAAE;IAClBsC,KAAK,EAAE,MAAM;IACbG,UAAU,EAAE;EACd,CAAC;EACDvC,MAAM,EAAE;IACN6B,eAAe,EAAE,SAAS;IAC1BG,OAAO,EAAE,EAAE;IACXD,UAAU,EAAE;EACd,CAAC;EACD9B,KAAK,EAAE;IACLgC,QAAQ,EAAE,EAAE;IACZM,UAAU,EAAE,MAAM;IAClBH,KAAK,EAAE;EACT,CAAC;EACDlC,QAAQ,EAAE;IACR+B,QAAQ,EAAE,EAAE;IACZG,KAAK,EAAE,MAAM;IACbI,OAAO,EAAE;EACX,CAAC;EACDrC,gBAAgB,EAAE;IAChByB,IAAI,EAAE,CAAC;IACPa,QAAQ,EAAE;EACZ,CAAC;EACDnC,OAAO,EAAE;IACPsB,IAAI,EAAE;EACR,CAAC;EACDrB,cAAc,EAAE;IACdkC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTf,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDvB,YAAY,EAAE;IACZiB,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXoB,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,MAAM;IACnBV,YAAY,EAAE,EAAE;IAChBR,eAAe,EAAE;EACnB,CAAC;EACDpB,WAAW,EAAE;IACX2B,KAAK,EAAE,MAAM;IACbH,QAAQ,EAAE,EAAE;IACZM,UAAU,EAAE,MAAM;IAClBS,SAAS,EAAE,EAAE;IACbd,SAAS,EAAE,QAAQ;IACnBL,eAAe,EAAE,iBAAiB;IAClCG,OAAO,EAAE,EAAE;IACXK,YAAY,EAAE;EAChB,CAAC;EACD3B,eAAe,EAAE;IACf+B,QAAQ,EAAE,UAAU;IACpBI,MAAM,EAAE,EAAE;IACVF,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRb,UAAU,EAAE;EACd,CAAC;EACDpB,UAAU,EAAE;IACVkB,eAAe,EAAE,SAAS;IAC1BQ,YAAY,EAAE,EAAE;IAChBY,eAAe,EAAE,EAAE;IACnBX,iBAAiB,EAAE;EACrB,CAAC;EACD1B,cAAc,EAAE;IACdwB,KAAK,EAAE,MAAM;IACbH,QAAQ,EAAE,EAAE;IACZM,UAAU,EAAE;EACd,CAAC;EACD1B,qBAAqB,EAAE;IACrBe,IAAI,EAAE,CAAC;IACPE,cAAc,EAAE,QAAQ;IACxBE,OAAO,EAAE;EACX,CAAC;EACDlB,mBAAmB,EAAE;IACnBe,eAAe,EAAE,MAAM;IACvBQ,YAAY,EAAE,EAAE;IAChBL,OAAO,EAAE,EAAE;IACXD,UAAU,EAAE,QAAQ;IACpBmB,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MAAE3B,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrC2B,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE;EAChB,CAAC;EACDvC,gBAAgB,EAAE;IAChBkB,QAAQ,EAAE,EAAE;IACZE,YAAY,EAAE;EAChB,CAAC;EACDnB,iBAAiB,EAAE;IACjBiB,QAAQ,EAAE,EAAE;IACZM,UAAU,EAAE,MAAM;IAClBH,KAAK,EAAE,MAAM;IACbD,YAAY,EAAE,EAAE;IAChBD,SAAS,EAAE;EACb,CAAC;EACDjB,gBAAgB,EAAE;IAChBgB,QAAQ,EAAE,EAAE;IACZG,KAAK,EAAE,MAAM;IACbF,SAAS,EAAE,QAAQ;IACnBqB,UAAU,EAAE,EAAE;IACdpB,YAAY,EAAE;EAChB,CAAC;EACDjB,iBAAiB,EAAE;IACjBsC,SAAS,EAAE,SAAS;IACpBrB,YAAY,EAAE;EAChB,CAAC;EACDhB,aAAa,EAAE;IACbc,QAAQ,EAAE,EAAE;IACZM,UAAU,EAAE,MAAM;IAClBH,KAAK,EAAE,MAAM;IACbD,YAAY,EAAE;EAChB,CAAC;EACDf,WAAW,EAAE;IACXa,QAAQ,EAAE,EAAE;IACZG,KAAK,EAAE,MAAM;IACbD,YAAY,EAAE;EAChB,CAAC;EACDd,WAAW,EAAE;IACXQ,eAAe,EAAE,SAAS;IAC1BQ,YAAY,EAAE,EAAE;IAChBY,eAAe,EAAE,EAAE;IACnBX,iBAAiB,EAAE;EACrB,CAAC;EACDhB,eAAe,EAAE;IACfc,KAAK,EAAE,MAAM;IACbH,QAAQ,EAAE,EAAE;IACZM,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAevG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}