{"name": "aquatrack-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/webpack-config": "^19.0.0", "@react-native-picker/picker": "^2.11.1", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "expo": "~49.0.15", "expo-barcode-scanner": "~12.5.3", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}