{"ast": null, "code": "import * as React from 'react';\nimport StackGestureRefContext from \"./GestureHandlerRefContext\";\nexport default function useGestureHandlerRef() {\n  var ref = React.useContext(StackGestureRefContext);\n  if (ref === undefined) {\n    throw new Error(\"Couldn't find a ref for gesture handler. Are you inside a screen in Stack?\");\n  }\n  return ref;\n}", "map": {"version": 3, "names": ["React", "StackGestureRefContext", "useGestureHandlerRef", "ref", "useContext", "undefined", "Error"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\stack\\src\\utils\\useGestureHandlerRef.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport StackGestureRefContext from './GestureHandlerRefContext';\n\nexport default function useGestureHandlerRef() {\n  const ref = React.useContext(StackGestureRefContext);\n\n  if (ref === undefined) {\n    throw new Error(\n      \"Couldn't find a ref for gesture handler. Are you inside a screen in Stack?\"\n    );\n  }\n\n  return ref;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,sBAAsB;AAE7B,eAAe,SAASC,oBAAoBA,CAAA,EAAG;EAC7C,IAAMC,GAAG,GAAGH,KAAK,CAACI,UAAU,CAACH,sBAAsB,CAAC;EAEpD,IAAIE,GAAG,KAAKE,SAAS,EAAE;IACrB,MAAM,IAAIC,KAAK,CACb,4EAA4E,CAC7E;EACH;EAEA,OAAOH,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}