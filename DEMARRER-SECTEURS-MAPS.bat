@echo off
title AquaTrack - Secteurs et Google Maps
color 0A

echo.
echo ========================================
echo    🗺️ AQUATRACK - SECTEURS ET GOOGLE MAPS
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. ATTENTE (3 secondes)...
timeout /t 3 /nobreak >nul

echo.
echo 🖥️ 3. DEMARRAGE DU BACKEND AVEC SECTEURS...
start "Backend Secteurs" cmd /k "title BACKEND SECTEURS AQUATRACK && color 0B && echo ========================================== && echo    🗺️ BACKEND AVEC SECTEURS ET GOOGLE MAPS && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Secteurs: 5 secteurs disponibles && echo ✅ Clients: Repartis par secteur && echo ✅ Google Maps: Integration complete && echo. && echo 📡 APIs disponibles: && echo    - GET /api/secteurs && echo    - GET /api/secteurs/:id/clients && echo    - GET /api/clients && echo. && echo 🔑 Comptes de test: && echo    - <EMAIL> / Tech123 && echo. && echo 📡 Demarrage... && echo. && node serveur-urgence.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 🌐 5. TEST DES APIs...
echo.
echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs' -TimeoutSec 5; Write-Host '✅ API SECTEURS OK:' $response.count 'secteurs disponibles' } catch { Write-Host '❌ API SECTEURS ERREUR' }"

echo.
echo Test API Clients Secteur 1:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs/1/clients' -TimeoutSec 5; Write-Host '✅ API CLIENTS SECTEUR 1 OK:' $response.count 'clients trouves' } catch { Write-Host '❌ API CLIENTS ERREUR' }"

echo.
echo 📱 6. DEMARRAGE DE L'APPLICATION MOBILE...
cd mobile
start "Frontend Mobile" cmd /k "title APPLICATION MOBILE SECTEURS && color 0D && echo ========================================== && echo    📱 APPLICATION MOBILE AVEC SECTEURS && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4000 && echo ✅ Fonctionnalites: && echo    - Selection de secteur && echo    - Affichage clients par secteur && echo    - Integration Google Maps && echo    - Coordonnees GPS automatiques && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 7. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 8. OUVERTURE DES NAVIGATEURS...
start http://localhost:4000/api/secteurs
timeout /t 2 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ SECTEURS ET GOOGLE MAPS DEPLOYES !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 📱 Application: http://localhost:19006
echo.
echo 🗺️ NOUVELLES FONCTIONNALITES:
echo.
echo 1. 📍 Selection de Secteur:
echo    - 5 secteurs disponibles
echo    - Centre-Ville, Quartier Industriel, etc.
echo.
echo 2. 👥 Clients par Secteur:
echo    - Affichage automatique des clients
echo    - Coordonnees GPS generees
echo.
echo 3. 🌐 Integration Google Maps:
echo    - Bouton "Voir sur Google Maps"
echo    - Marqueurs pour chaque client
echo    - Ouverture directe dans Google Maps
echo.
echo 📋 INSTRUCTIONS D'UTILISATION:
echo.
echo 1. Allez sur http://localhost:19006
echo 2. Connectez-<NAME_EMAIL> / Tech123
echo 3. Allez dans "Consommation" ou "Mes Clients"
echo 4. Selectionnez un secteur dans le menu deroulant
echo 5. Les clients du secteur s'affichent automatiquement
echo 6. Cliquez sur "Voir sur Google Maps" pour ouvrir la carte
echo 7. Google Maps s'ouvre avec tous les clients du secteur !
echo.
echo 🆘 APIs de test:
echo    - http://localhost:4000/api/secteurs
echo    - http://localhost:4000/api/secteurs/1/clients
echo    - http://localhost:4000/api/secteurs/2/clients
echo    - http://localhost:4000/api/secteurs/3/clients
echo.
echo ⚠️ IMPORTANT:
echo    - Gardez les deux fenetres ouvertes
echo    - Testez d'abord les APIs dans le navigateur
echo    - Puis testez l'application mobile
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
