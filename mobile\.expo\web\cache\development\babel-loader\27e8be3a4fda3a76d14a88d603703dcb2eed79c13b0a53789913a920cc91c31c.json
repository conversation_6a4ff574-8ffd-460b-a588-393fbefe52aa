{"ast": null, "code": "import * as React from 'react';\nvar NavigationContext = React.createContext(undefined);\nexport default NavigationContext;", "map": {"version": 3, "names": ["React", "NavigationContext", "createContext", "undefined"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\core\\src\\NavigationContext.tsx"], "sourcesContent": ["import type { ParamListBase } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport type { NavigationProp } from './types';\n\n/**\n * Context which holds the navigation prop for a screen.\n */\nconst NavigationContext = React.createContext<\n  NavigationProp<ParamListBase> | undefined\n>(undefined);\n\nexport default NavigationContext;\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAO9B,IAAMC,iBAAiB,GAAGD,KAAK,CAACE,aAAa,CAE3CC,SAAS,CAAC;AAEZ,eAAeF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}