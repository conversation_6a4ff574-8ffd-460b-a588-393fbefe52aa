@echo off
title Test Port 4002 - AquaTrack
color 0A

echo.
echo ========================================
echo    🎯 TEST PORT 4002 - AQUATRACK
echo ========================================
echo.

echo 🔍 1. VERIFICATION DU PORT 4002...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002' -TimeoutSec 10; Write-Host '✅ SERVEUR PORT 4002 OK:' $response.message; Write-Host '📡 Port:' $response.port; Write-Host '⏰ Timestamp:' $response.timestamp } catch { Write-Host '❌ SERVEUR PORT 4002 NON ACCESSIBLE' }"

echo.
echo 🔍 2. TEST DE LA PAGE CONSOMMATION...
echo.
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:4002/consommation' -TimeoutSec 10; Write-Host '✅ PAGE CONSOMMATION OK - Status:' $response.StatusCode; Write-Host '📄 Content-Type:' $response.Headers.'Content-Type' } catch { Write-Host '❌ PAGE CONSOMMATION ERREUR:' $_.Exception.Message }"

echo.
echo 🔍 3. TEST DES APIs...
echo.
echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 10; Write-Host '✅ API Secteurs OK:' $response.count 'secteurs disponibles' } catch { Write-Host '❌ API Secteurs erreur' }"

echo.
echo Test API Clients:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/clients' -TimeoutSec 10; Write-Host '✅ API Clients OK:' $response.count 'clients disponibles' } catch { Write-Host '❌ API Clients erreur' }"

echo.
echo Test API Clients par Secteur (Centre-Ville):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs/1/clients' -TimeoutSec 10; Write-Host '✅ API Secteur 1 OK:' $response.count 'clients dans Centre-Ville'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom } } catch { Write-Host '❌ API Secteur 1 erreur' }"

echo.
echo 🔍 4. TEST DE L'AUTHENTIFICATION...
echo.
powershell -Command "try { $body = @{ email='<EMAIL>'; password='Tech123' } | ConvertTo-Json; $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/auth/login' -Method POST -Body $body -ContentType 'application/json' -TimeoutSec 10; Write-Host '✅ AUTH OK:' $response.message; Write-Host '👤 Utilisateur:' $response.user.nom $response.user.prenom '(' $response.user.role ')' } catch { Write-Host '❌ AUTH erreur' }"

echo.
echo 🌐 5. OUVERTURE DES PAGES...
echo.
echo Ouverture de la page d'accueil...
start http://localhost:4002

echo.
echo Ouverture de la page consommation...
start http://localhost:4002/consommation

echo.
echo 📱 6. VERIFICATION DE L'APPLICATION MOBILE...
echo.
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:19006' -TimeoutSec 5; Write-Host '✅ APPLICATION MOBILE OK - Status:' $response.StatusCode } catch { Write-Host '⚠️ APPLICATION MOBILE NON DEMARREE (normal si pas encore lancee)' }"

echo.
echo ========================================
echo    ✅ TESTS PORT 4002 TERMINES !
echo ========================================
echo.
echo 📊 RESULTATS:
echo.
echo 🎯 URL DEMANDEE: http://localhost:4002/consommation
echo.
echo 📡 URLs Disponibles:
echo    - Accueil: http://localhost:4002/
echo    - Consommation: http://localhost:4002/consommation
echo    - API Secteurs: http://localhost:4002/api/secteurs
echo    - API Clients: http://localhost:4002/api/clients
echo    - API Auth: http://localhost:4002/api/auth/login
echo.
echo 🔑 Comptes de test:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo    - Role: Technicien
echo.
echo    - Email: <EMAIL>
echo    - Mot de passe: Admin123
echo    - Role: Administrateur
echo.
echo 📋 Instructions d'utilisation:
echo.
echo 1. 🌐 SERVEUR BACKEND (Port 4002):
echo    - Accedez a: http://localhost:4002/consommation
echo    - Cette page affiche l'interface de consommation
echo    - Toutes les APIs sont disponibles
echo.
echo 2. 📱 APPLICATION MOBILE (Port 19006):
echo    - Si pas encore demarree, lancez: DEMARRER-PORT-4002.bat
echo    - Accedez a: http://localhost:19006
echo    - Connectez-vous avec les comptes de test
echo    - L'app utilise automatiquement le port 4002
echo.
echo 3. 🔧 CONFIGURATION:
echo    - Le serveur fonctionne sur le port 4002
echo    - L'application mobile est configuree pour utiliser ce port
echo    - Toutes les APIs sont fonctionnelles
echo.
echo 🎯 VOTRE URL EST PRETE:
echo    http://localhost:4002/consommation
echo.
echo ⚠️ NOTES IMPORTANTES:
echo    - Gardez la fenetre du serveur ouverte
echo    - Le serveur doit rester actif pour que l'URL fonctionne
echo    - Si erreur, relancez: DEMARRER-PORT-4002.bat
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
