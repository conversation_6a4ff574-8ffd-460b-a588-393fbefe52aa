"use strict";

// Generated by CoffeeScript 2.5.1
var cloneDeep, htmlparser, isPlainObject, merge, _objectToDom, self;

htmlparser = require('htmlparser2');

var _require = require('dom-converter');

_objectToDom = _require.objectToDom;
merge = require('lodash/merge');
cloneDeep = require('lodash/cloneDeep');
isPlainObject = require('lodash/isPlainObject');
module.exports = self = {
  repeatString: function repeatString(str, times) {
    var i, j, output, ref;
    output = '';

    for (i = j = 0, ref = times; 0 <= ref ? j < ref : j > ref; i = 0 <= ref ? ++j : --j) {
      output += str;
    }

    return output;
  },
  cloneAndMergeDeep: function cloneAndMergeDeep(base, toAppend) {
    return merge(cloneDeep(base), toAppend);
  },
  toDom: function toDom(subject) {
    if (typeof subject === 'string') {
      return self.stringToDom(subject);
    } else if (isPlainObject(subject)) {
      return self._objectToDom(subject);
    } else {
      throw Error("tools.toDom() only supports strings and objects");
    }
  },
  stringToDom: function stringToDom(string) {
    var handler, parser;
    handler = new htmlparser.DomHandler();
    parser = new htmlparser.Parser(handler);
    parser.write(string);
    parser.end();
    return handler.dom;
  },
  _fixQuotesInDom: function _fixQuotesInDom(input) {
    var j, len, node;

    if (Array.isArray(input)) {
      for (j = 0, len = input.length; j < len; j++) {
        node = input[j];

        self._fixQuotesInDom(node);
      }

      return input;
    }

    node = input;

    if (node.type === 'text') {
      return node.data = self._quoteNodeText(node.data);
    } else {
      return self._fixQuotesInDom(node.children);
    }
  },
  objectToDom: function objectToDom(o) {
    if (!Array.isArray(o)) {
      if (!isPlainObject(o)) {
        throw Error("objectToDom() only accepts a bare object or an array");
      }
    }

    return self._fixQuotesInDom(_objectToDom(o));
  },
  quote: function quote(str) {
    return String(str).replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;').replace(/\ /g, '&sp;').replace(/\n/g, '<br />');
  },
  _quoteNodeText: function _quoteNodeText(text) {
    return String(text).replace(/\&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;').replace(/\ /g, '&sp;').replace(/\n/g, "&nl;");
  },
  getCols: function getCols() {
    var cols, tty; // Based on https://github.com/jonschlinkert/window-size

    tty = require('tty');

    cols = function () {
      try {
        if (tty.isatty(1) && tty.isatty(2)) {
          if (process.stdout.getWindowSize) {
            return process.stdout.getWindowSize(1)[0];
          } else if (tty.getWindowSize) {
            return tty.getWindowSize()[1];
          } else if (process.stdout.columns) {
            return process.stdout.columns;
          }
        }
      } catch (error) {}
    }();

    if (typeof cols === 'number' && cols > 30) {
      return cols;
    } else {
      return 80;
    }
  }
};