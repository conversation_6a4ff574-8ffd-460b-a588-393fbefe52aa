@echo off
title Test Secteurs - Resolution Probleme
color 0A

echo.
echo ========================================
echo    🧪 TEST SECTEURS - RESOLUTION
echo ========================================
echo.

echo 🛑 1. ARRET DES SERVEURS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Serveurs arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR DE TEST...
echo.
start "🧪 Serveur Test Secteurs" cmd /k "title SERVEUR TEST SECTEURS PORT 4002 && color 0B && echo ========================================== && echo    🧪 SERVEUR TEST SECTEURS PORT 4002 && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ API: /api/secteurs && echo ✅ Donnees: 5 secteurs de test && echo ✅ CORS: Permissif pour debug && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 🔍 5. TEST DE L'API SECTEURS...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 10; Write-Host '✅ API SECTEURS FONCTIONNE !'; Write-Host '   Success:' $response.success; Write-Host '   Count:' $response.count; Write-Host '   Message:' $response.message; Write-Host ''; Write-Host '📋 SECTEURS RETOURNES:'; $response.data | ForEach-Object { Write-Host '   ' $_.ids ':' $_.nom } } catch { Write-Host '❌ API SECTEURS ERREUR:' $_.Exception.Message }"

echo.
echo 📱 6. DEMARRAGE DE L'APPLICATION MOBILE...
echo.
cd mobile
start "📱 Application Mobile Test" cmd /k "title APPLICATION MOBILE TEST && color 0D && echo ========================================== && echo    📱 APPLICATION MOBILE TEST && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4002 && echo ✅ Test: Serveur secteurs simplifie && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 7. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 8. OUVERTURE DES PAGES DE TEST...
echo.
echo Ouverture de l'API secteurs...
start http://localhost:4002/api/secteurs

echo.
echo Ouverture de l'application...
start http://localhost:19006

echo.
echo ========================================
echo    🧪 TEST SECTEURS EN COURS
echo ========================================
echo.
echo 🎯 SERVEUR DE TEST DEMARRE:
echo.
echo ✅ URL API: http://localhost:4002/api/secteurs
echo ✅ Donnees: 5 secteurs de test
echo ✅ CORS: Configuration permissive
echo ✅ Logs: Detailles dans la console serveur
echo.
echo 📋 SECTEURS DE TEST:
echo    1. Centre-Ville
echo    2. Quartier Industriel
echo    3. Zone Residentielle Nord
echo    4. Zone Residentielle Sud
echo    5. Quartier Commercial
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Verifiez l'API:
echo    - Allez sur: http://localhost:4002/api/secteurs
echo    - Vous devez voir 5 secteurs en JSON
echo.
echo 2. 📱 Testez l'application:
echo    - Allez sur: http://localhost:19006
echo    - Connectez-vous: <EMAIL> / Tech123
echo    - Allez dans "Consommation"
echo    - Verifiez si le champ Secteur affiche les 5 secteurs
echo.
echo 3. 🔍 Debuggez si necessaire:
echo    - Ouvrez la console du navigateur (F12)
echo    - Regardez l'onglet Console pour les erreurs
echo    - Regardez l'onglet Network pour les requetes
echo.
echo 🎯 RESULTATS ATTENDUS:
echo.
echo ✅ Le champ "📍 Secteur *" doit maintenant afficher:
echo    - Centre-Ville
echo    - Quartier Industriel
echo    - Zone Residentielle Nord
echo    - Zone Residentielle Sud
echo    - Quartier Commercial
echo.
echo ❌ SI LE PROBLEME PERSISTE:
echo    1. Verifiez la console du navigateur
echo    2. Verifiez que fetchSecteurs() est appelee
echo    3. Verifiez que setSecteurs() recoit les donnees
echo    4. Verifiez que le composant se re-rend
echo.
echo 💡 CAUSES POSSIBLES:
echo    - Probleme CORS (resolu avec ce serveur test)
echo    - Erreur dans fetchSecteurs()
echo    - Probleme de state React
echo    - Erreur de rendu du composant
echo.
echo ✅ SERVEUR DE TEST ACTIF !
echo    Testez maintenant l'application.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
