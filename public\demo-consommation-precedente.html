<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Consommation Précédente Automatique</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #17a2b8;
        }
        
        .form-group {
            margin-bottom: 20px;
            padding: 12px;
            border-radius: 8px;
            border-width: 1px;
            border-style: solid;
        }
        
        .form-group.secteur {
            background: #e3f2fd;
            border-color: #007bff;
        }
        
        .form-group.client {
            background: #f0fff0;
            border-color: #28a745;
        }
        
        .form-group.consommation {
            background: #f0fff4;
            border-color: #28a745;
        }
        
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .form-label.secteur {
            color: #007bff;
        }
        
        .form-label.client {
            color: #28a745;
        }
        
        .form-label.consommation {
            color: #17a2b8;
        }
        
        .form-select {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            background: white;
            transition: border-color 0.3s;
        }
        
        .form-select:focus {
            outline: none;
            border-color: #17a2b8;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            background: white;
            transition: all 0.3s;
        }
        
        .form-input.auto-filled {
            background: #f0fff4;
            border-color: #28a745;
            border-width: 2px;
        }
        
        .auto-filled-info {
            font-size: 12px;
            color: #28a745;
            font-style: italic;
            margin-top: 5px;
            text-align: center;
        }
        
        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .step {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .step.active {
            border-color: #17a2b8;
            background: #e6f7ff;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: #17a2b8;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 15px;
        }
        
        .consumption-data {
            background: #fff3cd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }
        
        .consumption-data h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .client-consumptions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .client-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid #17a2b8;
        }
        
        .client-name {
            font-weight: bold;
            color: #17a2b8;
            margin-bottom: 10px;
        }
        
        .consumption-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        
        .consumption-item:last-child {
            border-bottom: none;
        }
        
        .consumption-value {
            font-weight: bold;
            color: #28a745;
            font-size: 1.1em;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Consommation Précédente Automatique</h1>
            <p>Démonstration du remplissage automatique de la dernière consommation</p>
        </div>
        
        <div class="content">
            <div class="demo-form">
                <h3 style="color: #17a2b8; margin-bottom: 20px;">📱 Simulation du Remplissage Automatique</h3>
                
                <div class="form-group secteur">
                    <label class="form-label secteur">📍 Secteur *</label>
                    <select id="secteurSelect" class="form-select">
                        <option value="">Sélectionner un secteur</option>
                        <option value="1">Centre-Ville</option>
                        <option value="2">Quartier Industriel</option>
                        <option value="3">Zone Résidentielle Nord</option>
                    </select>
                </div>
                
                <div class="form-group client">
                    <label class="form-label client">👤 Client *</label>
                    <select id="clientSelect" class="form-select" disabled>
                        <option value="">⬆️ Sélectionnez d'abord un secteur ci-dessus</option>
                    </select>
                </div>
                
                <div class="form-group consommation">
                    <label class="form-label consommation">📊 Consommation Précédente (m³)</label>
                    <input 
                        type="text" 
                        id="consommationInput" 
                        class="form-input" 
                        placeholder="Sélectionnez d'abord un client"
                        readonly
                    >
                    <div id="autoFilledMessage" class="auto-filled-info" style="display: none;">
                        ✅ Dernière consommation récupérée automatiquement
                    </div>
                </div>
            </div>
            
            <div class="flow-steps">
                <div class="step" id="step1">
                    <div class="step-number">1</div>
                    <h4>Sélection Secteur</h4>
                    <p>L'utilisateur choisit un secteur</p>
                </div>
                
                <div class="step" id="step2">
                    <div class="step-number">2</div>
                    <h4>Sélection Client</h4>
                    <p>L'utilisateur choisit un client</p>
                </div>
                
                <div class="step" id="step3">
                    <div class="step-number">3</div>
                    <h4>Récupération Auto</h4>
                    <p>Dernière consommation récupérée</p>
                </div>
                
                <div class="step" id="step4">
                    <div class="step-number">4</div>
                    <h4>Remplissage Auto</h4>
                    <p>Champ rempli automatiquement</p>
                </div>
            </div>
            
            <div class="consumption-data">
                <h3>📊 Dernières Consommations par Client</h3>
                <div class="client-consumptions">
                    <div class="client-card">
                        <div class="client-name">👤 Benali Fatima</div>
                        <div class="consumption-item">📊 Dernière: <span class="consumption-value">45 m³</span></div>
                        <div class="consumption-item">📅 Période: Novembre 2024</div>
                        <div class="consumption-item">📅 Date: 15/11/2024</div>
                    </div>
                    
                    <div class="client-card">
                        <div class="client-name">👤 Alami Mohammed</div>
                        <div class="consumption-item">📊 Dernière: <span class="consumption-value">38 m³</span></div>
                        <div class="consumption-item">📅 Période: Novembre 2024</div>
                        <div class="consumption-item">📅 Date: 18/11/2024</div>
                    </div>
                    
                    <div class="client-card">
                        <div class="client-name">👤 Benjelloun Youssef</div>
                        <div class="consumption-item">📊 Dernière: <span class="consumption-value">67 m³</span></div>
                        <div class="consumption-item">📅 Période: Novembre 2024</div>
                        <div class="consumption-item">📅 Date: 12/11/2024</div>
                    </div>
                    
                    <div class="client-card">
                        <div class="client-name">👤 Lahlou Khadija</div>
                        <div class="consumption-item">📊 Dernière: <span class="consumption-value">41 m³</span></div>
                        <div class="consumption-item">📅 Période: Novembre 2024</div>
                        <div class="consumption-item">📅 Date: 25/11/2024</div>
                    </div>
                </div>
            </div>
            
            <div class="buttons">
                <a href="http://localhost:19006" class="btn btn-primary" target="_blank">
                    📱 Tester l'Application Réelle
                </a>
                <button class="btn btn-info" onclick="simulateAutoFill()">
                    🧪 Simuler le Remplissage Auto
                </button>
            </div>
            
            <div style="background: #d4edda; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #28a745;">
                <h3 style="color: #155724; margin-bottom: 15px;">✅ Consommation Précédente Automatique</h3>
                <ul style="color: #155724; line-height: 1.6;">
                    <li>✅ <strong>Remplissage automatique :</strong> Récupère la dernière consommation du client</li>
                    <li>✅ <strong>Indication visuelle :</strong> Fond vert et bordure verte quand auto-rempli</li>
                    <li>✅ <strong>Message informatif :</strong> Confirme le remplissage automatique</li>
                    <li>✅ <strong>Éditable :</strong> Le technicien peut modifier la valeur si nécessaire</li>
                    <li>✅ <strong>Données de test :</strong> Dernières consommations pour tous les clients</li>
                    <li>✅ <strong>Gestion d'erreur :</strong> Valeur par défaut si aucune donnée trouvée</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // Données des clients par secteur
        const clientsParSecteur = {
            '1': [
                { id: 1, nom: 'Benali Fatima', lastConsumption: 45 },
                { id: 2, nom: 'Alami Mohammed', lastConsumption: 38 },
                { id: 3, nom: 'Tazi Aicha', lastConsumption: 52 }
            ],
            '2': [
                { id: 4, nom: 'Benjelloun Youssef', lastConsumption: 67 },
                { id: 5, nom: 'Lahlou Khadija', lastConsumption: 41 }
            ],
            '3': [
                { id: 6, nom: 'Fassi Omar', lastConsumption: 55 },
                { id: 7, nom: 'Chraibi Salma', lastConsumption: 33 }
            ]
        };
        
        // Gestion du changement de secteur
        document.getElementById('secteurSelect').addEventListener('change', function() {
            const secteurId = this.value;
            const clientSelect = document.getElementById('clientSelect');
            const consommationInput = document.getElementById('consommationInput');
            const autoFilledMessage = document.getElementById('autoFilledMessage');
            
            // Réinitialiser les étapes
            document.querySelectorAll('.step').forEach(step => step.classList.remove('active'));
            
            if (secteurId) {
                // Étape 1 : Secteur sélectionné
                document.getElementById('step1').classList.add('active');
                
                // Remplir les clients
                const clients = clientsParSecteur[secteurId] || [];
                clientSelect.innerHTML = `<option value="">-- Sélectionner un client (${clients.length} dans ce secteur) --</option>`;
                
                clients.forEach((client, index) => {
                    const option = document.createElement('option');
                    option.value = client.id;
                    option.textContent = `${index + 1}. ${client.nom}`;
                    clientSelect.appendChild(option);
                });
                
                clientSelect.disabled = false;
                
                // Réinitialiser la consommation
                consommationInput.value = '';
                consommationInput.placeholder = 'Sélectionnez un client ci-dessus';
                consommationInput.classList.remove('auto-filled');
                autoFilledMessage.style.display = 'none';
                
            } else {
                // Réinitialiser tout
                clientSelect.innerHTML = '<option value="">⬆️ Sélectionnez d\'abord un secteur ci-dessus</option>';
                clientSelect.disabled = true;
                consommationInput.value = '';
                consommationInput.placeholder = 'Sélectionnez d\'abord un client';
                consommationInput.classList.remove('auto-filled');
                autoFilledMessage.style.display = 'none';
            }
        });
        
        // Gestion du changement de client
        document.getElementById('clientSelect').addEventListener('change', function() {
            const clientId = this.value;
            const consommationInput = document.getElementById('consommationInput');
            const autoFilledMessage = document.getElementById('autoFilledMessage');
            
            if (clientId) {
                // Étape 2 : Client sélectionné
                document.getElementById('step2').classList.add('active');
                
                // Simuler le chargement
                consommationInput.placeholder = 'Chargement automatique...';
                consommationInput.value = '';
                consommationInput.classList.remove('auto-filled');
                autoFilledMessage.style.display = 'none';
                
                setTimeout(() => {
                    // Étape 3 : Récupération
                    document.getElementById('step3').classList.add('active');
                    
                    // Trouver le client et sa dernière consommation
                    let clientTrouve = null;
                    Object.values(clientsParSecteur).forEach(clients => {
                        const client = clients.find(c => c.id.toString() === clientId);
                        if (client) clientTrouve = client;
                    });
                    
                    setTimeout(() => {
                        // Étape 4 : Remplissage
                        document.getElementById('step4').classList.add('active');
                        
                        if (clientTrouve && clientTrouve.lastConsumption) {
                            consommationInput.value = clientTrouve.lastConsumption;
                            consommationInput.classList.add('auto-filled');
                            autoFilledMessage.style.display = 'block';
                            consommationInput.placeholder = '';
                        } else {
                            consommationInput.value = '0';
                            consommationInput.placeholder = 'Aucune donnée trouvée';
                        }
                    }, 500);
                }, 1000);
                
            } else {
                consommationInput.value = '';
                consommationInput.placeholder = 'Sélectionnez un client ci-dessus';
                consommationInput.classList.remove('auto-filled');
                autoFilledMessage.style.display = 'none';
                document.getElementById('step2').classList.remove('active');
                document.getElementById('step3').classList.remove('active');
                document.getElementById('step4').classList.remove('active');
            }
        });
        
        // Simulation du remplissage automatique
        function simulateAutoFill() {
            // Réinitialiser
            document.getElementById('secteurSelect').value = '';
            document.getElementById('clientSelect').value = '';
            document.getElementById('consommationInput').value = '';
            
            setTimeout(() => {
                document.getElementById('secteurSelect').value = '1';
                document.getElementById('secteurSelect').dispatchEvent(new Event('change'));
                
                setTimeout(() => {
                    document.getElementById('clientSelect').value = '1';
                    document.getElementById('clientSelect').dispatchEvent(new Event('change'));
                }, 1000);
            }, 500);
        }
    </script>
</body>
</html>
