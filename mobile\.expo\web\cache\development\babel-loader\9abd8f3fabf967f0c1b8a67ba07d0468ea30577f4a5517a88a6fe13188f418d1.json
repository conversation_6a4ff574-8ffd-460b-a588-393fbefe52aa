{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\expo-modules-core\\src\\NativeModulesProxy.ts"], "sourcesContent": ["import { ProxyNativeModule } from './NativeModulesProxy.types';\n\n// We default to an empty object shim wherever we don't have an environment-specific implementation\nexport default {} as { [moduleName: string]: ProxyNativeModule };\n"], "mappings": "AAGA,eAAe,EAAiD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}