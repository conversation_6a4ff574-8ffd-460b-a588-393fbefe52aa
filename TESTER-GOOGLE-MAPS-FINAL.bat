@echo off
title Test Final Google Maps avec Vraie Base de Donnees
color 0A

echo.
echo ========================================
echo    🧪 TEST FINAL GOOGLE MAPS INTEGREE
echo ========================================
echo.

echo 🗄️ 1. PREPARATION DE LA BASE DE DONNEES...
echo.
echo Si votre base de donnees est vide, executez d'abord:
echo psql -U postgres -d Facturation -f donnees-test-secteurs-clients.sql
echo.
echo Appuyez sur une touche pour continuer avec le test...
pause > nul

echo.
echo 🛑 2. ARRET DES PROCESSUS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 3. ATTENTE (3 secondes)...
timeout /t 3 /nobreak >nul

echo.
echo 🖥️ 4. DEMARRAGE DU SERVEUR AVEC VRAIE BASE DE DONNEES...
start "🖥️ Backend PostgreSQL" cmd /k "title BACKEND AQUATRACK POSTGRESQL && color 0B && echo ========================================== && echo    🗄️ BACKEND AVEC VRAIE BASE DE DONNEES && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Base: PostgreSQL Facturation && echo ✅ Google Maps: Integration complete && echo ✅ Cartes: Affichage dans le formulaire && echo. && echo 📡 Demarrage... && echo. && node server.js"

echo.
echo ⏳ 5. ATTENTE DU SERVEUR (10 secondes)...
timeout /t 10 /nobreak >nul

echo.
echo 🔍 6. TEST DES APIs AVEC VRAIE BASE DE DONNEES...
echo.

echo Test API Secteurs:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs' -TimeoutSec 10; Write-Host '✅ API SECTEURS OK:' $response.count 'secteurs disponibles'; $response.data | ForEach-Object { Write-Host '   -' $_.nom '(ID:' $_.ids ', GPS:' $_.latitude ',' $_.longitude ')' } } catch { Write-Host '❌ API SECTEURS ERREUR:' $_.Exception.Message }"

echo.
echo Test API Clients Secteur 1:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs/1/clients' -TimeoutSec 10; Write-Host '✅ API CLIENTS SECTEUR 1 OK:' $response.count 'clients trouves'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom '(' $_.adresse ')' } } catch { Write-Host '❌ API CLIENTS ERREUR:' $_.Exception.Message }"

echo.
echo Test API Clients Secteur 3:
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs/3/clients' -TimeoutSec 10; Write-Host '✅ API CLIENTS SECTEUR 3 OK:' $response.count 'clients trouves'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom '(' $_.adresse ')' } } catch { Write-Host '❌ API CLIENTS ERREUR:' $_.Exception.Message }"

echo.
echo 📱 7. DEMARRAGE DE L'APPLICATION MOBILE...
cd mobile
start "📱 Frontend Mobile" cmd /k "title APPLICATION MOBILE GOOGLE MAPS && color 0D && echo ========================================== && echo    📱 APPLICATION AVEC GOOGLE MAPS INTEGREE && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4000 && echo ✅ Base de donnees: PostgreSQL Facturation && echo ✅ Google Maps: Integration complete && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 8. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 9. OUVERTURE DES TESTS...
start http://localhost:4000/api/secteurs
timeout /t 2 /nobreak >nul
start http://localhost:4000/api/secteurs/1/clients
timeout /t 2 /nobreak >nul
start http://localhost:19006

echo.
echo ========================================
echo    ✅ TEST FINAL GOOGLE MAPS DEPLOYE !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 📱 Application: http://localhost:19006
echo 🗄️ Base de donnees: PostgreSQL Facturation
echo.
echo 🎯 OBJECTIF ATTEINT:
echo    ✅ Selection de secteur dans le formulaire
echo    ✅ Google Maps affichee DANS LA MEME PAGE
echo    ✅ Tous les clients du secteur affiches sur la carte
echo    ✅ Coordonnees GPS automatiques
echo    ✅ Interface integree (pas de nouvelle fenetre)
echo.
echo 📋 INSTRUCTIONS DE TEST FINAL:
echo.
echo 1. 🔍 Verifiez les APIs dans le navigateur:
echo    - http://localhost:4000/api/secteurs
echo    - http://localhost:4000/api/secteurs/1/clients
echo.
echo 2. 📱 Testez l'application mobile:
echo    - Allez sur http://localhost:19006
echo    - Connectez-<NAME_EMAIL> / Tech123
echo    - Allez dans "Consommation"
echo.
echo 3. 🗺️ Testez la fonctionnalite complete:
echo    a) Selectionnez "Centre-Ville" dans le menu deroulant
echo    b) La carte Google Maps s'affiche DANS LA MEME PAGE !
echo    c) Vous voyez tous les clients du secteur sur la carte
echo    d) Liste detaillee des clients sous la carte
echo    e) Cliquez sur un client pour voir sa position
echo    f) Utilisez les boutons pour Google Maps externe
echo.
echo 4. 🔄 Testez d'autres secteurs:
echo    - "Zone Residentielle Nord" (3+ clients)
echo    - "Quartier Industriel" (2+ clients)
echo    - "Zone Residentielle Sud" (2+ clients)
echo.
echo 📊 SECTEURS DISPONIBLES (avec vrais clients):
echo    1. Centre-Ville (3+ clients)
echo    2. Quartier Industriel (2+ clients)
echo    3. Zone Residentielle Nord (3+ clients)
echo    4. Zone Residentielle Sud (2+ clients)
echo    5. Quartier Commercial (2+ clients)
echo    6. Zone Universitaire (2+ clients)
echo    7. Quartier Administratif (2+ clients)
echo.
echo ✅ FONCTIONNALITES IMPLEMENTEES:
echo    - Menu deroulant secteurs (vrais secteurs de la DB)
echo    - Carte Google Maps integree dans le formulaire
echo    - Affichage de tous les clients du secteur
echo    - Coordonnees GPS generees automatiquement
echo    - Liste cliquable des clients
echo    - Boutons pour Google Maps externe
echo    - Interface responsive et interactive
echo.
echo 🆘 Si probleme:
echo    1. Verifiez que PostgreSQL est demarre
echo    2. Verifiez que la base 'Facturation' existe
echo    3. Executez le script SQL: donnees-test-secteurs-clients.sql
echo    4. Consultez les logs dans les fenetres du backend
echo.
echo 🎉 RESULTAT FINAL:
echo    Vous avez maintenant une carte Google Maps integree
echo    qui affiche tous les clients d'un secteur selectionne
echo    directement dans la meme page du formulaire !
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
