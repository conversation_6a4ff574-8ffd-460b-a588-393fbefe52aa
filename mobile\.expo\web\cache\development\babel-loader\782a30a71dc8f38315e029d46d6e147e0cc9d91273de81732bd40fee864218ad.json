{"ast": null, "code": "import UIManager from \"../UIManager\";\nvar NativeModules = {\n  UIManager: UIManager\n};\nexport default NativeModules;", "map": {"version": 3, "names": ["UIManager", "NativeModules"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/NativeModules/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport UIManager from '../UIManager';\n\n// NativeModules shim\nvar NativeModules = {\n  UIManager\n};\nexport default NativeModules;"], "mappings": "AASA,OAAOA,SAAS;AAGhB,IAAIC,aAAa,GAAG;EAClBD,SAAS,EAATA;AACF,CAAC;AACD,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}