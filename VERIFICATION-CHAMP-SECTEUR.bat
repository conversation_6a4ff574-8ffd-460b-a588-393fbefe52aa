@echo off
title Verification Champ Secteur
color 0A

echo.
echo ========================================
echo    📍 VERIFICATION CHAMP SECTEUR
echo ========================================
echo.

echo 🔍 1. TEST DIRECT DE L'API SECTEURS...
echo.
powershell -Command "try { Write-Host 'Test de l''API /api/secteurs...'; $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 10; Write-Host '✅ API SECTEURS FONCTIONNELLE'; Write-Host ''; Write-Host '📊 DONNEES DE LA TABLE SECTEUR:'; Write-Host '   Nombre total:' $response.count; Write-Host '   Success:' $response.success; Write-Host '   Message:' $response.message; Write-Host ''; Write-Host '📋 LISTE COMPLETE DES SECTEURS:'; $response.data | ForEach-Object { Write-Host '   🏢 ID:' $_.ids '| Nom:' $_.nom; Write-Host '      📍 Coordonnees: (' $_.latitude ',' $_.longitude ')'; Write-Host '' }; Write-Host '🎯 CES SECTEURS DOIVENT APPARAITRE DANS LE CHAMP SECTEUR' } catch { Write-Host '❌ ERREUR API SECTEURS:' $_.Exception.Message; Write-Host '💡 Verifiez que le serveur est demarre sur le port 4002' }"

echo.
echo 🔍 2. VERIFICATION DE LA STRUCTURE JSON...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '✅ STRUCTURE JSON CORRECTE:'; Write-Host '   - response.success:' $response.success; Write-Host '   - response.data: array de' $response.data.Count 'elements'; Write-Host '   - response.count:' $response.count; Write-Host ''; Write-Host '📋 STRUCTURE DE CHAQUE SECTEUR:'; $response.data[0] | Get-Member -MemberType NoteProperty | ForEach-Object { Write-Host '   -' $_.Name ':' $response.data[0].($_.Name) } } catch { Write-Host '❌ Erreur structure JSON' }"

echo.
echo 🔍 3. SIMULATION DU CHAMP SECTEUR...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4002/api/secteurs' -TimeoutSec 5; Write-Host '🎯 SIMULATION DU MENU DEROULANT SECTEUR:'; Write-Host ''; Write-Host '   [Menu deroulant ouvert]'; Write-Host '   📍 Selectionner un secteur'; Write-Host '   --------------------------------'; $response.data | ForEach-Object { Write-Host '   📍' $_.nom '(ID:' $_.ids ')' }; Write-Host '   --------------------------------'; Write-Host ''; Write-Host '✅ Tous les secteurs de la table sont affiches !' } catch { Write-Host '❌ Erreur simulation' }"

echo.
echo 🔍 4. VERIFICATION DU CODE FRONTEND...
echo.
echo Verification que le code utilise bien l'API:
if exist "src\pages\Consommation.js" (
    echo ✅ Fichier Consommation.js trouve
    findstr /C:"api/secteurs" "src\pages\Consommation.js" >nul
    if !errorlevel! equ 0 (
        echo ✅ Code utilise bien l'API /api/secteurs
    ) else (
        echo ❌ Code n'utilise pas l'API /api/secteurs
    )
    
    findstr /C:"secteur.nom" "src\pages\Consommation.js" >nul
    if !errorlevel! equ 0 (
        echo ✅ Code affiche bien secteur.nom
    ) else (
        echo ❌ Code n'affiche pas secteur.nom
    )
    
    findstr /C:"secteur.ids" "src\pages\Consommation.js" >nul
    if !errorlevel! equ 0 (
        echo ✅ Code utilise bien secteur.ids comme valeur
    ) else (
        echo ❌ Code n'utilise pas secteur.ids
    )
) else (
    echo ❌ Fichier Consommation.js non trouve
)

echo.
echo 🌐 5. OUVERTURE POUR TEST VISUEL...
echo.
echo Ouverture de l'API dans le navigateur:
start http://localhost:4002/api/secteurs

echo.
echo Ouverture de l'application:
start http://localhost:19006

echo.
echo ========================================
echo    📊 RAPPORT DE VERIFICATION
echo ========================================
echo.
echo 🎯 CONFIGURATION DU CHAMP SECTEUR:
echo.
echo ✅ API BACKEND:
echo    - URL: http://localhost:4002/api/secteurs
echo    - Methode: GET
echo    - Source: Table secteur de la base de donnees
echo    - Requete SQL: SELECT ids, nom, latitude, longitude FROM secteur ORDER BY nom
echo.
echo ✅ CODE FRONTEND:
echo    - Fichier: src/pages/Consommation.js
echo    - Fonction: fetchSecteurs()
echo    - Affichage: secteurs.map(secteur => PickerItem)
echo    - Label: secteur.nom
echo    - Value: secteur.ids
echo.
echo ✅ STRUCTURE ATTENDUE:
echo    - Champ: "📍 Secteur *"
echo    - Placeholder: "Selectionner un secteur"
echo    - Options: Tous les secteurs de la table
echo    - Format: Nom du secteur (ex: "Centre-Ville")
echo.
echo 🧪 INSTRUCTIONS DE VERIFICATION:
echo.
echo 1. 🌐 Verifiez l'API:
echo    - Allez sur: http://localhost:4002/api/secteurs
echo    - Vous devez voir tous les secteurs en JSON
echo.
echo 2. 📱 Testez l'application:
echo    - Allez sur: http://localhost:19006
echo    - Connectez-vous: <EMAIL> / Tech123
echo    - Allez dans "Consommation"
echo    - Cliquez sur le champ "📍 Secteur *"
echo    - Verifiez que tous les secteurs apparaissent
echo.
echo 📋 SECTEURS ATTENDUS:
echo    1. Centre-Ville
echo    2. Quartier Industriel  
echo    3. Zone Residentielle Nord
echo    4. Zone Residentielle Sud
echo    5. Quartier Commercial
echo.
echo 🎯 SI LE CHAMP EST VIDE:
echo    1. Verifiez que le serveur est demarre
echo    2. Verifiez l'URL de l'API dans la console
echo    3. Verifiez la structure de la reponse JSON
echo    4. Verifiez que secteurs.map() fonctionne
echo.
echo ✅ RESULTAT ATTENDU:
echo    Le champ "📍 Secteur *" affiche tous les secteurs
echo    de votre table secteur avec leurs noms exacts !
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
