@echo off
title Correction Google Maps
color 0A

echo.
echo ========================================
echo    🔧 CORRECTION GOOGLE MAPS
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR CORRIGÉ...
echo.
start "🧪 Serveur Maps Corrigé" cmd /k "title SERVEUR MAPS CORRIGÉ && color 0B && echo ========================================== && echo    🧪 SERVEUR MAPS CORRIGÉ && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Google Maps: Sans cle API && echo ✅ Fallback: OpenStreetMap && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 5. DEMARRAGE DE L'APPLICATION CORRIGÉE...
echo.
cd mobile
start "📱 App Maps Corrigée" cmd /k "title APPLICATION MAPS CORRIGÉE && color 0D && echo ========================================== && echo    📱 APPLICATION MAPS CORRIGÉE && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Google Maps: URL corrigée && echo ✅ Erreur API: Resolue && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 6. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 7. OUVERTURE POUR TEST...
start http://localhost:19006

echo.
echo ========================================
echo    🔧 CORRECTION GOOGLE MAPS APPLIQUÉE
echo ========================================
echo.
echo 🎯 PROBLÈME RÉSOLU:
echo.
echo ❌ AVANT (Erreur):
echo    - URL: https://www.google.com/maps/embed/v1/view?key=INVALID_KEY
echo    - Erreur: "The API key is invalid"
echo    - Affichage: Carte noire avec message d'erreur
echo.
echo ✅ APRÈS (Corrigé):
echo    - URL: https://maps.google.com/maps?q=LAT,LON&output=embed
echo    - Pas de clé API requise
echo    - Affichage: Carte Google Maps fonctionnelle
echo.
echo 🔧 CORRECTIONS APPLIQUÉES:
echo.
echo ✅ 1. URL GOOGLE MAPS CORRIGÉE:
echo    - Ancienne: /embed/v1/view?key=INVALID_KEY
echo    - Nouvelle: /maps?q=LAT,LON&output=embed
echo    - Avantage: Pas de clé API nécessaire
echo.
echo ✅ 2. PARAMÈTRES OPTIMISÉS:
echo    - q=LAT,LON : Coordonnées du contrat
echo    - hl=fr : Interface en français
echo    - z=16 : Niveau de zoom approprié
echo    - output=embed : Mode intégration
echo.
echo ✅ 3. FALLBACK AJOUTÉ:
echo    - Si Google Maps échoue → OpenStreetMap
echo    - Gestion d'erreur automatique
echo    - Continuité de service garantie
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 TESTEZ LA CARTE CORRIGÉE:
echo.
echo    a) 📍 Sélection:
echo       - Secteur: "Centre-Ville"
echo       - Client: "Benali Fatima"
echo       - Le champ Contract se remplit
echo.
echo    b) 🗺️ Clic sur le bouton:
echo       - Cliquez sur "🗺️ Voir sur Google Maps"
echo       - La carte doit maintenant s'afficher correctement
echo       - Plus de message d'erreur "API key invalid"
echo.
echo    c) ✅ Vérification:
echo       - Carte Google Maps visible
echo       - Centrée sur les coordonnées du contrat
echo       - Overlay avec informations du contrat
echo       - Bouton "✕ Fermer" fonctionnel
echo.
echo 📊 RÉSULTATS ATTENDUS:
echo.
echo ✅ CARTE FONCTIONNELLE:
echo    🗺️ Google Maps s'affiche correctement
echo    📍 Centrée sur: 33.5731, -7.5898 (Benali Fatima)
echo    🔍 Zoom: Niveau 16 (vue détaillée)
echo    🇫🇷 Interface: En français
echo.
echo ✅ OVERLAY INFORMATIF:
echo    📄 Titre: "Contrats sur la carte (1)"
echo    📍 Contrat: "Contrat 1"
echo    🔖 QR: "QR001 | Sensus"
echo    📍 Coordonnées: "33.5731, -7.5898"
echo    ✕ Bouton fermer: Fonctionnel
echo.
echo 🔧 SI LA CARTE NE S'AFFICHE TOUJOURS PAS:
echo.
echo 1. 🔍 Vérifiez la console du navigateur (F12):
echo    - Recherchez les erreurs JavaScript
echo    - Vérifiez les requêtes réseau
echo.
echo 2. 🌐 Testez l'URL directement:
echo    - https://maps.google.com/maps?q=33.5731,-7.5898&output=embed
echo    - Doit afficher la carte dans le navigateur
echo.
echo 3. 🔄 Rechargez la page:
echo    - Ctrl+F5 pour forcer le rechargement
echo    - Videz le cache si nécessaire
echo.
echo 🎯 ALTERNATIVES DISPONIBLES:
echo.
echo Si Google Maps ne fonctionne toujours pas:
echo ✅ OpenStreetMap: Fallback automatique
echo ✅ Coordonnées GPS: Toujours affichées dans l'overlay
echo ✅ Informations contrat: Complètes et accessibles
echo.
echo ✅ CORRECTION GOOGLE MAPS TERMINÉE !
echo    La carte doit maintenant s'afficher sans erreur.
echo    Testez avec différents clients pour vérifier.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
