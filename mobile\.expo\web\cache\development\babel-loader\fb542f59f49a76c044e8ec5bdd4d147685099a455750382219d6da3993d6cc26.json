{"ast": null, "code": "import * as CommonActions from \"./CommonActions\";\nexport { CommonActions };\nexport { default as BaseRouter } from \"./BaseRouter\";\nexport { DrawerActions, default as DrawerRouter } from \"./DrawerRouter\";\nexport { StackActions, default as StackRouter } from \"./StackRouter\";\nexport { TabActions, default as TabRouter } from \"./TabRouter\";\nexport * from \"./types\";", "map": {"version": 3, "names": ["CommonActions", "default", "BaseRouter", "DrawerActions", "DrawerRouter", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\routers\\src\\index.tsx"], "sourcesContent": ["import * as CommonActions from './CommonActions';\n\nexport { CommonActions };\n\nexport { default as BaseRouter } from './BaseRouter';\nexport type {\n  DrawerActionHelpers,\n  DrawerActionType,\n  DrawerNavigationState,\n  DrawerRouterOptions,\n  DrawerStatus,\n} from './DrawerRouter';\nexport { DrawerActions, default as DrawerRouter } from './DrawerRouter';\nexport type {\n  StackActionHelpers,\n  StackActionType,\n  StackNavigationState,\n  StackRouterOptions,\n} from './StackRouter';\nexport { StackActions, default as StackRouter } from './StackRouter';\nexport type {\n  TabActionHelpers,\n  TabActionType,\n  TabNavigationState,\n  TabRouterOptions,\n} from './TabRouter';\nexport { TabActions, default as TabRouter } from './TabRouter';\nexport * from './types';\n"], "mappings": "AAAA,OAAO,KAAKA,aAAa;AAEzB,SAASA,aAAa;AAEtB,SAASC,OAAO,IAAIC,UAAU;AAQ9B,SAASC,aAAa,EAAEF,OAAO,IAAIG,YAAY;AAO/C,SAASC,YAAY,EAAEJ,OAAO,IAAIK,WAAW;AAO7C,SAASC,UAAU,EAAEN,OAAO,IAAIO,SAAS;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}