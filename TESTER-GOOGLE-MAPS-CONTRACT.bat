@echo off
title Test Google Maps Contract
color 0A

echo.
echo ========================================
echo    🗺️ TEST GOOGLE MAPS CONTRACT
echo ========================================
echo.

echo 🛑 1. ARRET DES PROCESSUS EXISTANTS...
taskkill /f /im node.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🧪 3. DEMARRAGE DU SERVEUR...
echo.
start "🧪 Serveur Maps" cmd /k "title SERVEUR GOOGLE MAPS && color 0B && echo ========================================== && echo    🧪 SERVEUR GOOGLE MAPS && echo ========================================== && echo. && echo ✅ Port: 4002 && echo ✅ Google Maps: Integre && echo ✅ Contrats: Avec coordonnees GPS && echo. && echo 📡 Demarrage... && echo. && node serveur-test-secteurs.js"

echo.
echo ⏳ 4. ATTENTE DU SERVEUR (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 5. DEMARRAGE DE L'APPLICATION...
echo.
cd mobile
start "📱 App Maps" cmd /k "title APPLICATION GOOGLE MAPS && color 0D && echo ========================================== && echo    📱 APPLICATION GOOGLE MAPS && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Google Maps: Bouton integre && echo ✅ Affichage: Dans la meme page && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo ⏳ 6. ATTENTE DE L'APPLICATION (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 7. OUVERTURE POUR TEST...
start http://localhost:19006

echo.
echo ========================================
echo    🗺️ TEST GOOGLE MAPS CONTRACT
echo ========================================
echo.
echo 🎯 BOUTON GOOGLE MAPS AJOUTÉ:
echo.
echo ✅ POSITION DU BOUTON:
echo    - A cote du champ "📄 Contrat *"
echo    - Apparait seulement quand un client avec contrats est selectionne
echo    - Texte: "🗺️ Voir sur Google Maps" / "🗺️ Masquer Google Maps"
echo.
echo ✅ FONCTIONNALITÉ:
echo    - Clic → Affiche Google Maps dans la meme page
echo    - Affiche les contrats du client avec leurs coordonnees
echo    - Vue satellite avec zoom sur la zone
echo    - Overlay avec details des contrats
echo    - Bouton fermer pour masquer la carte
echo.
echo 📊 COORDONNÉES GPS DES CONTRATS:
echo.
echo 👤 Benali Fatima (Contrat 1):
echo    📍 33.5731, -7.5898 (Centre-Ville)
echo.
echo 👤 Alami Mohammed (Contrat 2):
echo    📍 33.5735, -7.5895 (Centre-Ville)
echo.
echo 👤 Benjelloun Youssef (Contrats 4 et 5):
echo    📍 33.5831, -7.5998 (Quartier Industriel)
echo    📍 33.5835, -7.5995 (Quartier Industriel)
echo.
echo 👤 Lahlou Khadija (Contrat 6):
echo    📍 33.5840, -7.5990 (Quartier Industriel)
echo.
echo 🧪 INSTRUCTIONS DE TEST:
echo.
echo 1. 🌐 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🔍 TESTEZ LE BOUTON GOOGLE MAPS:
echo.
echo    a) 📍 ÉTAPE 1 - Sélection:
echo       - Sélectionnez "Centre-Ville" (secteur)
echo       - Sélectionnez "Benali Fatima" (client)
echo       - Le champ Contract se remplit automatiquement
echo       - Le bouton "🗺️ Voir sur Google Maps" apparait
echo.
echo    b) 🗺️ ÉTAPE 2 - Affichage Maps:
echo       - Cliquez sur "🗺️ Voir sur Google Maps"
echo       - Google Maps s'affiche dans la meme page
echo       - Vue satellite centrée sur le contrat
echo       - Overlay avec details du contrat
echo.
echo    c) 📄 ÉTAPE 3 - Informations affichées:
echo       - Titre: "🗺️ Localisation des Contrats - Benali Fatima"
echo       - Carte: Vue satellite avec zoom 16
echo       - Overlay: "📄 Contrats sur la carte (1)"
echo       - Details: "📍 Contrat 1, QR: QR001, Sensus"
echo       - Coordonnées: "📍 33.5731, -7.5898"
echo.
echo    d) 🔄 ÉTAPE 4 - Test client multiple contrats:
echo       - Changez pour "Benjelloun Youssef"
echo       - Cliquez sur "🗺️ Voir sur Google Maps"
echo       - Doit afficher "📄 Contrats sur la carte (2)"
echo       - Deux contrats dans l'overlay
echo.
echo    e) ✕ ÉTAPE 5 - Fermeture:
echo       - Cliquez sur "✕ Fermer" dans l'overlay
echo       - La carte se masque
echo       - Le bouton redevient "🗺️ Voir sur Google Maps"
echo.
echo 📊 COMPORTEMENTS ATTENDUS:
echo.
echo ✅ AUCUN CLIENT SÉLECTIONNÉ:
echo    📄 Contract: [⬆️ Sélectionnez d'abord un client...]
echo    🗺️ Bouton: Pas visible
echo.
echo ✅ CLIENT SANS CONTRAT:
echo    📄 Contract: [❌ Aucun contrat pour ce client]
echo    🗺️ Bouton: Pas visible
echo.
echo ✅ CLIENT AVEC CONTRAT(S):
echo    📄 Contract: [Contrat X - QR: QRXXX]
echo    🗺️ Bouton: "🗺️ Voir sur Google Maps" (visible)
echo.
echo ✅ MAPS AFFICHÉ:
echo    📄 Contract: [Contrat X - QR: QRXXX]
echo    🗺️ Bouton: "🗺️ Masquer Google Maps"
echo    🗺️ Carte: Affichée avec overlay
echo.
echo 🎨 APPARENCE DE LA CARTE:
echo.
echo ✅ Conteneur: Fond gris clair, bordure bleue
echo ✅ Titre: "🗺️ Localisation des Contrats - [Nom Client]"
echo ✅ Carte: 300px de hauteur, vue satellite, zoom 16
echo ✅ Overlay: Fond blanc semi-transparent, coin supérieur droit
echo ✅ Contrats: Liste avec QR, marque, coordonnées
echo ✅ Bouton fermer: Rouge, coin inférieur droit de l'overlay
echo.
echo 🔧 FONCTIONNALITÉS TESTÉES:
echo.
echo ✅ Bouton conditionnel (visible seulement si contrats)
echo ✅ Affichage/masquage de la carte
echo ✅ Intégration Google Maps dans la même page
echo ✅ Centrage automatique sur les contrats
echo ✅ Overlay avec informations détaillées
echo ✅ Gestion des clients avec plusieurs contrats
echo ✅ Bouton fermer fonctionnel
echo.
echo 🌐 URLS DE TEST:
echo    - Application: http://localhost:19006
echo    - Console logs: F12 → Console (pour voir les coordonnées)
echo.
echo ✅ GOOGLE MAPS INTÉGRÉ AVEC BOUTON !
echo    Testez maintenant la sélection de client et
echo    cliquez sur le bouton pour voir les contrats sur la carte.
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
