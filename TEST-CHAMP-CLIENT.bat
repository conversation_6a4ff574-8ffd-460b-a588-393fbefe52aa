@echo off
title Test Champ Client - Deuxieme Champ
color 0A

echo.
echo ========================================
echo    👤 TEST CHAMP CLIENT - 2EME CHAMP
echo ========================================
echo.

echo 🔍 1. VERIFICATION DU BACKEND...
echo.
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs' -TimeoutSec 5; Write-Host '✅ Backend OK:' $response.count 'secteurs disponibles' } catch { Write-Host '❌ Backend non accessible' }"

echo.
echo 🔍 2. TEST DES CLIENTS PAR SECTEUR...
echo.
echo Test Secteur 1 (Centre-Ville):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs/1/clients' -TimeoutSec 5; Write-Host '✅ Secteur 1:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom } } catch { Write-Host '❌ Erreur secteur 1' }"

echo.
echo Test Secteur 3 (Zone Nord):
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs/3/clients' -TimeoutSec 5; Write-Host '✅ Secteur 3:' $response.count 'clients'; $response.data | ForEach-Object { Write-Host '   -' $_.nom $_.prenom } } catch { Write-Host '❌ Erreur secteur 3' }"

echo.
echo 🚀 3. REDEMARRAGE DE L'APPLICATION...
echo.
echo Arret des processus...
taskkill /f /im node.exe 2>nul

echo.
echo Demarrage du backend...
start "Backend" cmd /k "title Backend AquaTrack && color 0B && echo ========================================== && echo    🖥️ BACKEND AVEC CHAMP CLIENT && echo ========================================== && echo. && echo ✅ Port: 4000 && echo ✅ Secteurs: 5 secteurs disponibles && echo ✅ Clients: Repartis par secteur && echo ✅ Champ Client: Deuxieme champ du formulaire && echo. && echo 📡 Demarrage... && echo. && timeout /t 3 /nobreak >nul && node serveur-urgence.js"

echo.
echo Attente du backend (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo Demarrage du frontend...
cd mobile
start "Frontend" cmd /k "title Frontend Mobile && color 0D && echo ========================================== && echo    📱 APPLICATION MOBILE AVEC CHAMP CLIENT && echo ========================================== && echo. && echo ✅ Port: 19006 && echo ✅ Backend: http://localhost:4000 && echo ✅ Champ Client: Deuxieme champ apres Secteur && echo ✅ Selection automatique des clients par secteur && echo. && echo 📡 Demarrage... && echo. && npx expo start --web"
cd ..

echo.
echo Attente du frontend (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 4. OUVERTURE DE L'APPLICATION...
start http://localhost:19006

echo.
echo ========================================
echo    ✅ CHAMP CLIENT AJOUTE !
echo ========================================
echo.
echo 📋 INSTRUCTIONS DE TEST:
echo.
echo 1. 🔗 Allez sur: http://localhost:19006
echo.
echo 2. 🔐 Connectez-vous avec:
echo    - Email: <EMAIL>
echo    - Mot de passe: Tech123
echo.
echo 3. 📱 Allez dans "Consommation"
echo.
echo 4. 🗺️ ORDRE DES CHAMPS (NOUVEAU):
echo    1er champ: 🗺️ Secteur *
echo    2eme champ: 👤 Client * (NOUVEAU !)
echo    3eme champ: ℹ️ Informations du Client (NOUVEAU !)
echo    4eme champ: 🗺️ Carte du Secteur
echo    5eme champ: Periode (YYYY-MM) *
echo    etc...
echo.
echo 5. 🧪 TESTEZ LA SEQUENCE:
echo    a) Selectionnez "Centre-Ville" dans Secteur
echo    b) Le champ Client se remplit automatiquement
echo    c) Selectionnez "Benali Fatima" dans Client
echo    d) Les informations du client s'affichent
echo    e) La carte montre le client selectionne
echo.
echo 📊 SECTEURS ET CLIENTS A TESTER:
echo.
echo    1. Centre-Ville:
echo       - Benali Fatima
echo       - Alami Mohammed
echo.
echo    2. Quartier Industriel:
echo       - Tazi Aicha
echo.
echo    3. Zone Residentielle Nord:
echo       - Benjelloun Youssef
echo       - Lahlou Khadija
echo.
echo    4. Zone Residentielle Sud:
echo       - Fassi Omar
echo.
echo ✅ FONCTIONNALITES DU CHAMP CLIENT:
echo.
echo    ✅ Deuxieme champ du formulaire
echo    ✅ Selection automatique selon le secteur
echo    ✅ Menu deroulant avec nom + adresse
echo    ✅ Affichage des informations detaillees
echo    ✅ Coordonnees GPS du client
echo    ✅ Integration avec la carte
echo    ✅ Messages d'aide si aucun secteur/client
echo.
echo 🎯 RESULTAT:
echo    Le champ Client est maintenant le DEUXIEME CHAMP
echo    du formulaire, juste apres le Secteur !
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
