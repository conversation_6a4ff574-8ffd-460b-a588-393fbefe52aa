{"ast": null, "code": "import * as React from 'react';\nvar MULTIPLE_NAVIGATOR_ERROR = `Another navigator is already registered for this container. You likely have multiple navigators under a single \"NavigationContainer\" or \"Screen\". Make sure each navigator is under a separate \"Screen\" container. See https://reactnavigation.org/docs/nesting-navigators for a guide on nesting.`;\nexport var SingleNavigatorContext = React.createContext(undefined);\nexport default function EnsureSingleNavigator(_ref) {\n  var children = _ref.children;\n  var navigatorKeyRef = React.useRef();\n  var value = React.useMemo(function () {\n    return {\n      register: function register(key) {\n        var currentKey = navigatorKeyRef.current;\n        if (currentKey !== undefined && key !== currentKey) {\n          throw new Error(MULTIPLE_NAVIGATOR_ERROR);\n        }\n        navigatorKeyRef.current = key;\n      },\n      unregister: function unregister(key) {\n        var currentKey = navigatorKeyRef.current;\n        if (key !== currentKey) {\n          return;\n        }\n        navigatorKeyRef.current = undefined;\n      }\n    };\n  }, []);\n  return React.createElement(SingleNavigatorContext.Provider, {\n    value: value\n  }, children);\n}", "map": {"version": 3, "names": ["React", "MULTIPLE_NAVIGATOR_ERROR", "SingleNavigatorContext", "createContext", "undefined", "EnsureSingleNavigator", "_ref", "children", "navigator<PERSON><PERSON><PERSON><PERSON>", "useRef", "value", "useMemo", "register", "key", "current<PERSON><PERSON>", "current", "Error", "unregister", "createElement", "Provider"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\Facturation stage\\samle-react-app\\mobile\\node_modules\\@react-navigation\\core\\src\\EnsureSingleNavigator.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype Props = {\n  children: React.ReactNode;\n};\n\nconst MULTIPLE_NAVIGATOR_ERROR = `Another navigator is already registered for this container. You likely have multiple navigators under a single \"NavigationContainer\" or \"Screen\". Make sure each navigator is under a separate \"Screen\" container. See https://reactnavigation.org/docs/nesting-navigators for a guide on nesting.`;\n\nexport const SingleNavigatorContext = React.createContext<\n  | {\n      register(key: string): void;\n      unregister(key: string): void;\n    }\n  | undefined\n>(undefined);\n\n/**\n * Component which ensures that there's only one navigator nested under it.\n */\nexport default function EnsureSingleNavigator({ children }: Props) {\n  const navigatorKeyRef = React.useRef<string | undefined>();\n\n  const value = React.useMemo(\n    () => ({\n      register(key: string) {\n        const currentKey = navigatorKeyRef.current;\n\n        if (currentKey !== undefined && key !== currentKey) {\n          throw new Error(MULTIPLE_NAVIGATOR_ERROR);\n        }\n\n        navigatorKeyRef.current = key;\n      },\n      unregister(key: string) {\n        const currentKey = navigatorKeyRef.current;\n\n        if (key !== currentKey) {\n          return;\n        }\n\n        navigatorKeyRef.current = undefined;\n      },\n    }),\n    []\n  );\n\n  return (\n    <SingleNavigatorContext.Provider value={value}>\n      {children}\n    </SingleNavigatorContext.Provider>\n  );\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAM9B,IAAMC,wBAAwB,GAAI,oSAAmS;AAErU,OAAO,IAAMC,sBAAsB,GAAGF,KAAK,CAACG,aAAa,CAMvDC,SAAS,CAAC;AAKZ,eAAe,SAASC,qBAAqBA,CAAAC,IAAA,EAAsB;EAAA,IAAnBC,QAAA,GAAiBD,IAAA,CAAjBC,QAAA;EAC9C,IAAMC,eAAe,GAAGR,KAAK,CAACS,MAAM,EAAsB;EAE1D,IAAMC,KAAK,GAAGV,KAAK,CAACW,OAAO,CACzB;IAAA,OAAO;MACLC,QAAQ,WAARA,QAAQA,CAACC,GAAW,EAAE;QACpB,IAAMC,UAAU,GAAGN,eAAe,CAACO,OAAO;QAE1C,IAAID,UAAU,KAAKV,SAAS,IAAIS,GAAG,KAAKC,UAAU,EAAE;UAClD,MAAM,IAAIE,KAAK,CAACf,wBAAwB,CAAC;QAC3C;QAEAO,eAAe,CAACO,OAAO,GAAGF,GAAG;MAC/B,CAAC;MACDI,UAAU,WAAVA,UAAUA,CAACJ,GAAW,EAAE;QACtB,IAAMC,UAAU,GAAGN,eAAe,CAACO,OAAO;QAE1C,IAAIF,GAAG,KAAKC,UAAU,EAAE;UACtB;QACF;QAEAN,eAAe,CAACO,OAAO,GAAGX,SAAS;MACrC;IACF,CAAC;EAAA,CAAC,EACF,EAAE,CACH;EAED,OACEJ,KAAA,CAAAkB,aAAA,CAAChB,sBAAsB,CAACiB,QAAQ;IAACT,KAAK,EAAEA;EAAM,GAC3CH,QAAQ,CACuB;AAEtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}