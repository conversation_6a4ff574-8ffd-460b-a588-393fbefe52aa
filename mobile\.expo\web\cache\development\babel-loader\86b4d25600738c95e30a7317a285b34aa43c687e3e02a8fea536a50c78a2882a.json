{"ast": null, "code": "var LogBox = {\n  ignoreLogs: function ignoreLogs() {},\n  ignoreAllLogs: function ignoreAllLogs() {},\n  uninstall: function uninstall() {},\n  install: function install() {}\n};\nexport default LogBox;", "map": {"version": 3, "names": ["LogBox", "ignoreLogs", "ignoreAllLogs", "uninstall", "install"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/node_modules/react-native-web/dist/exports/LogBox/index.js"], "sourcesContent": ["/**\n * Copyright (c) 2016-present, <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar LogBox = {\n  ignoreLogs() {},\n  ignoreAllLogs() {},\n  uninstall() {},\n  install() {}\n};\nexport default LogBox;"], "mappings": "AASA,IAAIA,MAAM,GAAG;EACXC,UAAU,WAAVA,UAAUA,CAAA,EAAG,CAAC,CAAC;EACfC,aAAa,WAAbA,aAAaA,CAAA,EAAG,CAAC,CAAC;EAClBC,SAAS,WAATA,SAASA,CAAA,EAAG,CAAC,CAAC;EACdC,OAAO,WAAPA,OAAOA,CAAA,EAAG,CAAC;AACb,CAAC;AACD,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}