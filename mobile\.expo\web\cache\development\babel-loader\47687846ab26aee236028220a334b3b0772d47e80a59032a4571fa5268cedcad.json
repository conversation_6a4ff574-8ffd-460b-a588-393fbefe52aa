{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport ActivityIndicator from \"react-native-web/dist/exports/ActivityIndicator\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport SafeAreaView from \"react-native-web/dist/exports/SafeAreaView\";\nimport Linking from \"react-native-web/dist/exports/Linking\";\nimport { jsxs as _jsxs, jsx as _jsx } from \"react/jsx-runtime\";\nvar ClientsScreen = function ClientsScreen(_ref) {\n  var _route$params;\n  var navigation = _ref.navigation,\n    route = _ref.route;\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    clients = _useState2[0],\n    setClients = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    filteredClients = _useState4[0],\n    setFilteredClients = _useState4[1];\n  var _useState5 = useState(true),\n    _useState6 = _slicedToArray(_useState5, 2),\n    loading = _useState6[0],\n    setLoading = _useState6[1];\n  var _useState7 = useState(''),\n    _useState8 = _slicedToArray(_useState7, 2),\n    error = _useState8[0],\n    setError = _useState8[1];\n  var _useState9 = useState(''),\n    _useState0 = _slicedToArray(_useState9, 2),\n    searchText = _useState0[0],\n    setSearchText = _useState0[1];\n  var API_BASE_URL = 'http://***********:4000';\n  var user = (_route$params = route.params) == null ? void 0 : _route$params.user;\n  useEffect(function () {\n    fetchClients();\n  }, []);\n  useEffect(function () {\n    filterClients();\n  }, [clients, searchText]);\n  var fetchClients = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      try {\n        setLoading(true);\n        var response = yield fetch(`${API_BASE_URL}/api/clients`);\n        var data = yield response.json();\n        if (data.success && data.data) {\n          setClients(data.data);\n          setError('');\n        } else {\n          setError('Aucun client trouvé');\n        }\n      } catch (err) {\n        console.error('Erreur lors de la récupération des clients:', err);\n        setError('Erreur de connexion au serveur');\n      } finally {\n        setLoading(false);\n      }\n    });\n    return function fetchClients() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var filterClients = function filterClients() {\n    if (!searchText) {\n      setFilteredClients(clients);\n    } else {\n      var filtered = clients.filter(function (client) {\n        return client.nom.toLowerCase().includes(searchText.toLowerCase()) || client.prenom.toLowerCase().includes(searchText.toLowerCase()) || client.ville.toLowerCase().includes(searchText.toLowerCase());\n      });\n      setFilteredClients(filtered);\n    }\n  };\n  var handleConsommationPress = function handleConsommationPress(client) {\n    console.log('Navigation vers consommation pour:', client.nom, client.prenom);\n    navigation.navigate('Consommation', {\n      client: client,\n      user: user\n    });\n  };\n  var handleLocationPress = function () {\n    var _ref3 = _asyncToGenerator(function* (client) {\n      try {\n        console.log('🗺️ Localisation demandée pour:', client.nom, client.prenom);\n        var response = yield fetch(`${API_BASE_URL}/api/client-contract/${client.idclient}`);\n        var data = yield response.json();\n        if (!response.ok || !data.success || !data.contract) {\n          Alert.alert('Aucun Contrat', `Aucun contrat trouvé pour ${client.nom} ${client.prenom}.\\n\\nImpossible d'afficher la localisation.`);\n          return;\n        }\n        var contract = data.contract;\n        console.log('✅ Contrat trouvé:', contract);\n        yield redirectToGoogleMaps(client, contract);\n      } catch (error) {\n        console.error('❌ Erreur localisation:', error);\n        Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);\n      }\n    });\n    return function handleLocationPress(_x) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var redirectToGoogleMaps = function () {\n    var _ref4 = _asyncToGenerator(function* (client, contract) {\n      try {\n        if (contract.secteur_latitude && contract.secteur_longitude) {\n          var secteurUrl = `https://www.google.com/maps?q=${contract.secteur_latitude},${contract.secteur_longitude}&z=15`;\n          Alert.alert('🗺️ Localisation - Secteur', `Client: ${client.nom} ${client.prenom}\\n\\nSecteur: ${contract.secteur_nom || 'Non défini'}\\n\\nOuvrir la vue du secteur dans Google Maps ?`, [{\n            text: 'Annuler',\n            style: 'cancel'\n          }, {\n            text: 'Ouvrir Secteur',\n            onPress: function () {\n              var _onPress = _asyncToGenerator(function* () {\n                yield Linking.openURL(secteurUrl);\n                setTimeout(function () {\n                  if (contract.posx && contract.posy) {\n                    showExactLocationOption(client, contract);\n                  } else {\n                    Alert.alert('Secteur Ouvert', `Vue du secteur ouverte dans Google Maps.\\n\\nPosition exacte du compteur non disponible.`);\n                  }\n                }, 2000);\n              });\n              function onPress() {\n                return _onPress.apply(this, arguments);\n              }\n              return onPress;\n            }()\n          }]);\n        } else if (contract.posx && contract.posy) {\n          var exactUrl = `https://www.google.com/maps?q=${contract.posx},${contract.posy}&z=18`;\n          yield Linking.openURL(exactUrl);\n          Alert.alert('Position Exacte Ouverte', `Position du compteur ouverte dans Google Maps.\\n\\nInformations du secteur non disponibles.`);\n        } else {\n          Alert.alert('Localisation Non Disponible', `Aucune coordonnée disponible pour ${client.nom} ${client.prenom}.\\n\\nVeuillez contacter l'administrateur.`);\n        }\n      } catch (error) {\n        console.error('Erreur ouverture Google Maps:', error);\n        Alert.alert('Erreur', 'Impossible d\\'ouvrir Google Maps');\n      }\n    });\n    return function redirectToGoogleMaps(_x2, _x3) {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var showExactLocationOption = function showExactLocationOption(client, contract) {\n    Alert.alert('🏠 Position Exacte', `Voulez-vous maintenant voir la position exacte du compteur de ${client.nom} ${client.prenom} ?`, [{\n      text: 'Non',\n      style: 'cancel'\n    }, {\n      text: 'Ouvrir Position Exacte',\n      onPress: function () {\n        var _onPress2 = _asyncToGenerator(function* () {\n          var exactUrl = `https://www.google.com/maps?q=${contract.posx},${contract.posy}&z=18`;\n          yield Linking.openURL(exactUrl);\n          Alert.alert('Position Exacte Ouverte', `Position exacte du compteur ouverte dans Google Maps.`);\n        });\n        function onPress() {\n          return _onPress2.apply(this, arguments);\n        }\n        return onPress;\n      }()\n    }]);\n  };\n  var renderClientItem = function renderClientItem(_ref5) {\n    var item = _ref5.item;\n    return _jsxs(View, {\n      style: styles.clientCard,\n      children: [_jsxs(View, {\n        style: styles.clientInfo,\n        children: [_jsxs(Text, {\n          style: styles.clientName,\n          children: [item.nom, \" \", item.prenom]\n        }), _jsxs(Text, {\n          style: styles.clientDetail,\n          children: [\"\\uD83D\\uDCCD \", item.adresse]\n        }), _jsxs(Text, {\n          style: styles.clientDetail,\n          children: [\"\\uD83C\\uDFD9\\uFE0F \", item.ville]\n        }), _jsxs(Text, {\n          style: styles.clientDetail,\n          children: [\"\\uD83D\\uDCDE \", item.tel]\n        }), _jsxs(Text, {\n          style: styles.clientDetail,\n          children: [\"\\uD83D\\uDCE7 \", item.email]\n        }), item.statut && _jsx(View, {\n          style: styles.statusContainer,\n          children: _jsx(Text, {\n            style: [styles.statusText, item.statut === 'Actif' ? styles.statusActive : styles.statusInactive],\n            children: item.statut\n          })\n        })]\n      }), _jsxs(View, {\n        style: styles.buttonContainer,\n        children: [_jsxs(TouchableOpacity, {\n          style: styles.actionButton,\n          onPress: function onPress() {\n            return handleConsommationPress(item);\n          },\n          children: [_jsx(Text, {\n            style: styles.buttonIcon,\n            children: \"\\uD83D\\uDCA7\"\n          }), _jsx(Text, {\n            style: styles.buttonText,\n            children: \"Consommation\"\n          })]\n        }), _jsxs(TouchableOpacity, {\n          style: [styles.actionButton, styles.locationButton],\n          onPress: function onPress() {\n            return handleLocationPress(item);\n          },\n          children: [_jsx(Text, {\n            style: styles.buttonIcon,\n            children: \"\\uD83D\\uDCCD\"\n          }), _jsx(Text, {\n            style: styles.buttonText,\n            children: \"Localisation\"\n          })]\n        })]\n      })]\n    });\n  };\n  if (loading) {\n    return _jsxs(View, {\n      style: styles.loadingContainer,\n      children: [_jsx(ActivityIndicator, {\n        size: \"large\",\n        color: \"#007AFF\"\n      }), _jsx(Text, {\n        style: styles.loadingText,\n        children: \"Chargement des clients...\"\n      })]\n    });\n  }\n  return _jsxs(SafeAreaView, {\n    style: styles.container,\n    children: [_jsxs(View, {\n      style: styles.header,\n      children: [_jsx(Text, {\n        style: styles.title,\n        children: \"\\uD83D\\uDC65 Mes Clients\"\n      }), _jsxs(Text, {\n        style: styles.subtitle,\n        children: [\"Bonjour \", user == null ? void 0 : user.nom, \" \", user == null ? void 0 : user.prenom]\n      })]\n    }), _jsx(View, {\n      style: styles.searchContainer,\n      children: _jsx(TextInput, {\n        style: styles.searchInput,\n        placeholder: \"\\uD83D\\uDD0D Rechercher un client...\",\n        value: searchText,\n        onChangeText: setSearchText\n      })\n    }), error ? _jsxs(View, {\n      style: styles.errorContainer,\n      children: [_jsxs(Text, {\n        style: styles.errorText,\n        children: [\"\\u274C \", error]\n      }), _jsx(TouchableOpacity, {\n        style: styles.retryButton,\n        onPress: fetchClients,\n        children: _jsx(Text, {\n          style: styles.retryText,\n          children: \"R\\xE9essayer\"\n        })\n      })]\n    }) : _jsx(FlatList, {\n      data: filteredClients,\n      renderItem: renderClientItem,\n      keyExtractor: function keyExtractor(item) {\n        return item.idclient.toString();\n      },\n      contentContainerStyle: styles.listContainer,\n      showsVerticalScrollIndicator: false,\n      refreshing: loading,\n      onRefresh: fetchClients\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  loadingText: {\n    marginTop: 10,\n    fontSize: 16,\n    color: '#666'\n  },\n  header: {\n    backgroundColor: '#007AFF',\n    padding: 20,\n    alignItems: 'center'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#fff'\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#fff',\n    opacity: 0.9\n  },\n  searchContainer: {\n    padding: 15\n  },\n  searchInput: {\n    backgroundColor: '#fff',\n    borderRadius: 10,\n    padding: 15,\n    fontSize: 16,\n    borderWidth: 1,\n    borderColor: '#ddd'\n  },\n  listContainer: {\n    padding: 15\n  },\n  clientCard: {\n    backgroundColor: '#fff',\n    borderRadius: 10,\n    padding: 15,\n    marginBottom: 15,\n    elevation: 2,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 2\n  },\n  clientInfo: {\n    marginBottom: 15\n  },\n  clientName: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 5\n  },\n  clientDetail: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 2\n  },\n  statusContainer: {\n    marginTop: 5\n  },\n  statusText: {\n    fontSize: 12,\n    fontWeight: 'bold',\n    paddingHorizontal: 8,\n    paddingVertical: 4,\n    borderRadius: 10,\n    alignSelf: 'flex-start'\n  },\n  statusActive: {\n    backgroundColor: '#d4edda',\n    color: '#155724'\n  },\n  statusInactive: {\n    backgroundColor: '#f8d7da',\n    color: '#721c24'\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between'\n  },\n  actionButton: {\n    flex: 1,\n    backgroundColor: '#007AFF',\n    borderRadius: 8,\n    padding: 12,\n    alignItems: 'center',\n    marginHorizontal: 5\n  },\n  locationButton: {\n    backgroundColor: '#28a745'\n  },\n  buttonIcon: {\n    fontSize: 20,\n    marginBottom: 5\n  },\n  buttonText: {\n    color: '#fff',\n    fontSize: 12,\n    fontWeight: 'bold'\n  },\n  errorContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20\n  },\n  errorText: {\n    fontSize: 16,\n    color: '#dc3545',\n    textAlign: 'center',\n    marginBottom: 20\n  },\n  retryButton: {\n    backgroundColor: '#007AFF',\n    borderRadius: 8,\n    padding: 12,\n    paddingHorizontal: 20\n  },\n  retryText: {\n    color: '#fff',\n    fontWeight: 'bold'\n  }\n});\nexport default ClientsScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "FlatList", "TouchableOpacity", "StyleSheet", "<PERSON><PERSON>", "ActivityIndicator", "TextInput", "SafeAreaView", "Linking", "jsxs", "_jsxs", "jsx", "_jsx", "ClientsScreen", "_ref", "_route$params", "navigation", "route", "_useState", "_useState2", "_slicedToArray", "clients", "setClients", "_useState3", "_useState4", "filteredClients", "setFilteredClients", "_useState5", "_useState6", "loading", "setLoading", "_useState7", "_useState8", "error", "setError", "_useState9", "_useState0", "searchText", "setSearchText", "API_BASE_URL", "user", "params", "fetchClients", "filterClients", "_ref2", "_asyncToGenerator", "response", "fetch", "data", "json", "success", "err", "console", "apply", "arguments", "filtered", "filter", "client", "nom", "toLowerCase", "includes", "prenom", "ville", "handleConsommationPress", "log", "navigate", "handleLocationPress", "_ref3", "idclient", "ok", "contract", "alert", "redirectToGoogleMaps", "message", "_x", "_ref4", "secteur_latitude", "secteur_longitude", "secteurUrl", "secteur_nom", "text", "style", "onPress", "_onPress", "openURL", "setTimeout", "posx", "posy", "showExactLocationOption", "exactUrl", "_x2", "_x3", "_onPress2", "renderClientItem", "_ref5", "item", "styles", "clientCard", "children", "clientInfo", "clientName", "clientDetail", "adresse", "tel", "email", "statut", "statusContainer", "statusText", "statusActive", "statusInactive", "buttonContainer", "actionButton", "buttonIcon", "buttonText", "locationButton", "loadingContainer", "size", "color", "loadingText", "container", "header", "title", "subtitle", "searchContainer", "searchInput", "placeholder", "value", "onChangeText", "<PERSON><PERSON><PERSON><PERSON>", "errorText", "retryButton", "retryText", "renderItem", "keyExtractor", "toString", "contentContainerStyle", "listContainer", "showsVerticalScrollIndicator", "refreshing", "onRefresh", "create", "flex", "backgroundColor", "justifyContent", "alignItems", "marginTop", "fontSize", "padding", "fontWeight", "opacity", "borderRadius", "borderWidth", "borderColor", "marginBottom", "elevation", "shadowColor", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "paddingHorizontal", "paddingVertical", "alignSelf", "flexDirection", "marginHorizontal", "textAlign"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Facturation stage/samle-react-app/mobile/src/screens/ClientsScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  View,\n  Text,\n  FlatList,\n  TouchableOpacity,\n  StyleSheet,\n  Alert,\n  ActivityIndicator,\n  TextInput,\n  SafeAreaView,\n  Linking,\n} from 'react-native';\n\nconst ClientsScreen = ({ navigation, route }) => {\n  const [clients, setClients] = useState([]);\n  const [filteredClients, setFilteredClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [searchText, setSearchText] = useState('');\n\n  const API_BASE_URL = 'http://***********:4000'; // IP locale détectée\n  const user = route.params?.user;\n\n  useEffect(() => {\n    fetchClients();\n  }, []);\n\n  useEffect(() => {\n    filterClients();\n  }, [clients, searchText]);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/clients`);\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        setClients(data.data);\n        setError('');\n      } else {\n        setError('Aucun client trouvé');\n      }\n    } catch (err) {\n      console.error('Erreur lors de la récupération des clients:', err);\n      setError('Erreur de connexion au serveur');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterClients = () => {\n    if (!searchText) {\n      setFilteredClients(clients);\n    } else {\n      const filtered = clients.filter(client =>\n        client.nom.toLowerCase().includes(searchText.toLowerCase()) ||\n        client.prenom.toLowerCase().includes(searchText.toLowerCase()) ||\n        client.ville.toLowerCase().includes(searchText.toLowerCase())\n      );\n      setFilteredClients(filtered);\n    }\n  };\n\n  const handleConsommationPress = (client) => {\n    console.log('Navigation vers consommation pour:', client.nom, client.prenom);\n    navigation.navigate('Consommation', { client, user });\n  };\n\n  const handleLocationPress = async (client) => {\n    try {\n      console.log('🗺️ Localisation demandée pour:', client.nom, client.prenom);\n\n      // Récupérer le contrat avec les informations du secteur\n      const response = await fetch(`${API_BASE_URL}/api/client-contract/${client.idclient}`);\n      const data = await response.json();\n\n      if (!response.ok || !data.success || !data.contract) {\n        Alert.alert(\n          'Aucun Contrat',\n          `Aucun contrat trouvé pour ${client.nom} ${client.prenom}.\\n\\nImpossible d'afficher la localisation.`\n        );\n        return;\n      }\n\n      const contract = data.contract;\n      console.log('✅ Contrat trouvé:', contract);\n\n      // Redirection vers Google Maps\n      await redirectToGoogleMaps(client, contract);\n\n    } catch (error) {\n      console.error('❌ Erreur localisation:', error);\n      Alert.alert('Erreur', `Erreur de connexion: ${error.message}`);\n    }\n  };\n\n  const redirectToGoogleMaps = async (client, contract) => {\n    try {\n      // Étape 1: Ouvrir le secteur si disponible\n      if (contract.secteur_latitude && contract.secteur_longitude) {\n        const secteurUrl = `https://www.google.com/maps?q=${contract.secteur_latitude},${contract.secteur_longitude}&z=15`;\n        \n        Alert.alert(\n          '🗺️ Localisation - Secteur',\n          `Client: ${client.nom} ${client.prenom}\\n\\nSecteur: ${contract.secteur_nom || 'Non défini'}\\n\\nOuvrir la vue du secteur dans Google Maps ?`,\n          [\n            { text: 'Annuler', style: 'cancel' },\n            {\n              text: 'Ouvrir Secteur',\n              onPress: async () => {\n                await Linking.openURL(secteurUrl);\n                \n                // Proposer la position exacte après 2 secondes\n                setTimeout(() => {\n                  if (contract.posx && contract.posy) {\n                    showExactLocationOption(client, contract);\n                  } else {\n                    Alert.alert(\n                      'Secteur Ouvert',\n                      `Vue du secteur ouverte dans Google Maps.\\n\\nPosition exacte du compteur non disponible.`\n                    );\n                  }\n                }, 2000);\n              }\n            }\n          ]\n        );\n      } else if (contract.posx && contract.posy) {\n        // Seulement la position exacte disponible\n        const exactUrl = `https://www.google.com/maps?q=${contract.posx},${contract.posy}&z=18`;\n        await Linking.openURL(exactUrl);\n        Alert.alert(\n          'Position Exacte Ouverte',\n          `Position du compteur ouverte dans Google Maps.\\n\\nInformations du secteur non disponibles.`\n        );\n      } else {\n        Alert.alert(\n          'Localisation Non Disponible',\n          `Aucune coordonnée disponible pour ${client.nom} ${client.prenom}.\\n\\nVeuillez contacter l'administrateur.`\n        );\n      }\n    } catch (error) {\n      console.error('Erreur ouverture Google Maps:', error);\n      Alert.alert('Erreur', 'Impossible d\\'ouvrir Google Maps');\n    }\n  };\n\n  const showExactLocationOption = (client, contract) => {\n    Alert.alert(\n      '🏠 Position Exacte',\n      `Voulez-vous maintenant voir la position exacte du compteur de ${client.nom} ${client.prenom} ?`,\n      [\n        { text: 'Non', style: 'cancel' },\n        {\n          text: 'Ouvrir Position Exacte',\n          onPress: async () => {\n            const exactUrl = `https://www.google.com/maps?q=${contract.posx},${contract.posy}&z=18`;\n            await Linking.openURL(exactUrl);\n            Alert.alert(\n              'Position Exacte Ouverte',\n              `Position exacte du compteur ouverte dans Google Maps.`\n            );\n          }\n        }\n      ]\n    );\n  };\n\n  const renderClientItem = ({ item }) => (\n    <View style={styles.clientCard}>\n      <View style={styles.clientInfo}>\n        <Text style={styles.clientName}>{item.nom} {item.prenom}</Text>\n        <Text style={styles.clientDetail}>📍 {item.adresse}</Text>\n        <Text style={styles.clientDetail}>🏙️ {item.ville}</Text>\n        <Text style={styles.clientDetail}>📞 {item.tel}</Text>\n        <Text style={styles.clientDetail}>📧 {item.email}</Text>\n        {item.statut && (\n          <View style={styles.statusContainer}>\n            <Text style={[\n              styles.statusText,\n              item.statut === 'Actif' ? styles.statusActive : styles.statusInactive\n            ]}>\n              {item.statut}\n            </Text>\n          </View>\n        )}\n      </View>\n\n      <View style={styles.buttonContainer}>\n        <TouchableOpacity\n          style={styles.actionButton}\n          onPress={() => handleConsommationPress(item)}\n        >\n          <Text style={styles.buttonIcon}>💧</Text>\n          <Text style={styles.buttonText}>Consommation</Text>\n        </TouchableOpacity>\n\n        <TouchableOpacity\n          style={[styles.actionButton, styles.locationButton]}\n          onPress={() => handleLocationPress(item)}\n        >\n          <Text style={styles.buttonIcon}>📍</Text>\n          <Text style={styles.buttonText}>Localisation</Text>\n        </TouchableOpacity>\n      </View>\n    </View>\n  );\n\n  if (loading) {\n    return (\n      <View style={styles.loadingContainer}>\n        <ActivityIndicator size=\"large\" color=\"#007AFF\" />\n        <Text style={styles.loadingText}>Chargement des clients...</Text>\n      </View>\n    );\n  }\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <View style={styles.header}>\n        <Text style={styles.title}>👥 Mes Clients</Text>\n        <Text style={styles.subtitle}>\n          Bonjour {user?.nom} {user?.prenom}\n        </Text>\n      </View>\n\n      <View style={styles.searchContainer}>\n        <TextInput\n          style={styles.searchInput}\n          placeholder=\"🔍 Rechercher un client...\"\n          value={searchText}\n          onChangeText={setSearchText}\n        />\n      </View>\n\n      {error ? (\n        <View style={styles.errorContainer}>\n          <Text style={styles.errorText}>❌ {error}</Text>\n          <TouchableOpacity style={styles.retryButton} onPress={fetchClients}>\n            <Text style={styles.retryText}>Réessayer</Text>\n          </TouchableOpacity>\n        </View>\n      ) : (\n        <FlatList\n          data={filteredClients}\n          renderItem={renderClientItem}\n          keyExtractor={(item) => item.idclient.toString()}\n          contentContainerStyle={styles.listContainer}\n          showsVerticalScrollIndicator={false}\n          refreshing={loading}\n          onRefresh={fetchClients}\n        />\n      )}\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  loadingText: {\n    marginTop: 10,\n    fontSize: 16,\n    color: '#666',\n  },\n  header: {\n    backgroundColor: '#007AFF',\n    padding: 20,\n    alignItems: 'center',\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#fff',\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#fff',\n    opacity: 0.9,\n  },\n  searchContainer: {\n    padding: 15,\n  },\n  searchInput: {\n    backgroundColor: '#fff',\n    borderRadius: 10,\n    padding: 15,\n    fontSize: 16,\n    borderWidth: 1,\n    borderColor: '#ddd',\n  },\n  listContainer: {\n    padding: 15,\n  },\n  clientCard: {\n    backgroundColor: '#fff',\n    borderRadius: 10,\n    padding: 15,\n    marginBottom: 15,\n    elevation: 2,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 2 },\n    shadowOpacity: 0.1,\n    shadowRadius: 2,\n  },\n  clientInfo: {\n    marginBottom: 15,\n  },\n  clientName: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    color: '#333',\n    marginBottom: 5,\n  },\n  clientDetail: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 2,\n  },\n  statusContainer: {\n    marginTop: 5,\n  },\n  statusText: {\n    fontSize: 12,\n    fontWeight: 'bold',\n    paddingHorizontal: 8,\n    paddingVertical: 4,\n    borderRadius: 10,\n    alignSelf: 'flex-start',\n  },\n  statusActive: {\n    backgroundColor: '#d4edda',\n    color: '#155724',\n  },\n  statusInactive: {\n    backgroundColor: '#f8d7da',\n    color: '#721c24',\n  },\n  buttonContainer: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n  },\n  actionButton: {\n    flex: 1,\n    backgroundColor: '#007AFF',\n    borderRadius: 8,\n    padding: 12,\n    alignItems: 'center',\n    marginHorizontal: 5,\n  },\n  locationButton: {\n    backgroundColor: '#28a745',\n  },\n  buttonIcon: {\n    fontSize: 20,\n    marginBottom: 5,\n  },\n  buttonText: {\n    color: '#fff',\n    fontSize: 12,\n    fontWeight: 'bold',\n  },\n  errorContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n  },\n  errorText: {\n    fontSize: 16,\n    color: '#dc3545',\n    textAlign: 'center',\n    marginBottom: 20,\n  },\n  retryButton: {\n    backgroundColor: '#007AFF',\n    borderRadius: 8,\n    padding: 12,\n    paddingHorizontal: 20,\n  },\n  retryText: {\n    color: '#fff',\n    fontWeight: 'bold',\n  },\n});\n\nexport default ClientsScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,iBAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,OAAA;AAAA,SAAAC,IAAA,IAAAC,KAAA,EAAAC,GAAA,IAAAC,IAAA;AAcnD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA,EAA8B;EAAA,IAAAC,aAAA;EAAA,IAAxBC,UAAU,GAAAF,IAAA,CAAVE,UAAU;IAAEC,KAAK,GAAAH,IAAA,CAALG,KAAK;EACxC,IAAAC,SAAA,GAA8BrB,QAAQ,CAAC,EAAE,CAAC;IAAAsB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAnCG,OAAO,GAAAF,UAAA;IAAEG,UAAU,GAAAH,UAAA;EAC1B,IAAAI,UAAA,GAA8C1B,QAAQ,CAAC,EAAE,CAAC;IAAA2B,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAnDE,eAAe,GAAAD,UAAA;IAAEE,kBAAkB,GAAAF,UAAA;EAC1C,IAAAG,UAAA,GAA8B9B,QAAQ,CAAC,IAAI,CAAC;IAAA+B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAArCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAA0BlC,QAAQ,CAAC,EAAE,CAAC;IAAAmC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAA/BE,KAAK,GAAAD,UAAA;IAAEE,QAAQ,GAAAF,UAAA;EACtB,IAAAG,UAAA,GAAoCtC,QAAQ,CAAC,EAAE,CAAC;IAAAuC,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAAzCE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAEhC,IAAMG,YAAY,GAAG,yBAAyB;EAC9C,IAAMC,IAAI,IAAAzB,aAAA,GAAGE,KAAK,CAACwB,MAAM,qBAAZ1B,aAAA,CAAcyB,IAAI;EAE/B1C,SAAS,CAAC,YAAM;IACd4C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN5C,SAAS,CAAC,YAAM;IACd6C,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACtB,OAAO,EAAEgB,UAAU,CAAC,CAAC;EAEzB,IAAMK,YAAY;IAAA,IAAAE,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAC/B,IAAI;QACFf,UAAU,CAAC,IAAI,CAAC;QAChB,IAAMgB,QAAQ,SAASC,KAAK,CAAC,GAAGR,YAAY,cAAc,CAAC;QAC3D,IAAMS,IAAI,SAASF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;UAC7B1B,UAAU,CAAC0B,IAAI,CAACA,IAAI,CAAC;UACrBd,QAAQ,CAAC,EAAE,CAAC;QACd,CAAC,MAAM;UACLA,QAAQ,CAAC,qBAAqB,CAAC;QACjC;MACF,CAAC,CAAC,OAAOiB,GAAG,EAAE;QACZC,OAAO,CAACnB,KAAK,CAAC,6CAA6C,EAAEkB,GAAG,CAAC;QACjEjB,QAAQ,CAAC,gCAAgC,CAAC;MAC5C,CAAC,SAAS;QACRJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBAlBKY,YAAYA,CAAA;MAAA,OAAAE,KAAA,CAAAS,KAAA,OAAAC,SAAA;IAAA;EAAA,GAkBjB;EAED,IAAMX,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B,IAAI,CAACN,UAAU,EAAE;MACfX,kBAAkB,CAACL,OAAO,CAAC;IAC7B,CAAC,MAAM;MACL,IAAMkC,QAAQ,GAAGlC,OAAO,CAACmC,MAAM,CAAC,UAAAC,MAAM;QAAA,OACpCA,MAAM,CAACC,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IAC3DF,MAAM,CAACI,MAAM,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IAC9DF,MAAM,CAACK,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC;MAAA,CAC/D,CAAC;MACDjC,kBAAkB,CAAC6B,QAAQ,CAAC;IAC9B;EACF,CAAC;EAED,IAAMQ,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIN,MAAM,EAAK;IAC1CL,OAAO,CAACY,GAAG,CAAC,oCAAoC,EAAEP,MAAM,CAACC,GAAG,EAAED,MAAM,CAACI,MAAM,CAAC;IAC5E7C,UAAU,CAACiD,QAAQ,CAAC,cAAc,EAAE;MAAER,MAAM,EAANA,MAAM;MAAEjB,IAAI,EAAJA;IAAK,CAAC,CAAC;EACvD,CAAC;EAED,IAAM0B,mBAAmB;IAAA,IAAAC,KAAA,GAAAtB,iBAAA,CAAG,WAAOY,MAAM,EAAK;MAC5C,IAAI;QACFL,OAAO,CAACY,GAAG,CAAC,iCAAiC,EAAEP,MAAM,CAACC,GAAG,EAAED,MAAM,CAACI,MAAM,CAAC;QAGzE,IAAMf,QAAQ,SAASC,KAAK,CAAC,GAAGR,YAAY,wBAAwBkB,MAAM,CAACW,QAAQ,EAAE,CAAC;QACtF,IAAMpB,IAAI,SAASF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAI,CAACH,QAAQ,CAACuB,EAAE,IAAI,CAACrB,IAAI,CAACE,OAAO,IAAI,CAACF,IAAI,CAACsB,QAAQ,EAAE;UACnDlE,KAAK,CAACmE,KAAK,CACT,eAAe,EACf,6BAA6Bd,MAAM,CAACC,GAAG,IAAID,MAAM,CAACI,MAAM,6CAC1D,CAAC;UACD;QACF;QAEA,IAAMS,QAAQ,GAAGtB,IAAI,CAACsB,QAAQ;QAC9BlB,OAAO,CAACY,GAAG,CAAC,mBAAmB,EAAEM,QAAQ,CAAC;QAG1C,MAAME,oBAAoB,CAACf,MAAM,EAAEa,QAAQ,CAAC;MAE9C,CAAC,CAAC,OAAOrC,KAAK,EAAE;QACdmB,OAAO,CAACnB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C7B,KAAK,CAACmE,KAAK,CAAC,QAAQ,EAAE,wBAAwBtC,KAAK,CAACwC,OAAO,EAAE,CAAC;MAChE;IACF,CAAC;IAAA,gBA1BKP,mBAAmBA,CAAAQ,EAAA;MAAA,OAAAP,KAAA,CAAAd,KAAA,OAAAC,SAAA;IAAA;EAAA,GA0BxB;EAED,IAAMkB,oBAAoB;IAAA,IAAAG,KAAA,GAAA9B,iBAAA,CAAG,WAAOY,MAAM,EAAEa,QAAQ,EAAK;MACvD,IAAI;QAEF,IAAIA,QAAQ,CAACM,gBAAgB,IAAIN,QAAQ,CAACO,iBAAiB,EAAE;UAC3D,IAAMC,UAAU,GAAG,iCAAiCR,QAAQ,CAACM,gBAAgB,IAAIN,QAAQ,CAACO,iBAAiB,OAAO;UAElHzE,KAAK,CAACmE,KAAK,CACT,4BAA4B,EAC5B,WAAWd,MAAM,CAACC,GAAG,IAAID,MAAM,CAACI,MAAM,gBAAgBS,QAAQ,CAACS,WAAW,IAAI,YAAY,iDAAiD,EAC3I,CACE;YAAEC,IAAI,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAS,CAAC,EACpC;YACED,IAAI,EAAE,gBAAgB;YACtBE,OAAO;cAAA,IAAAC,QAAA,GAAAtC,iBAAA,CAAE,aAAY;gBACnB,MAAMrC,OAAO,CAAC4E,OAAO,CAACN,UAAU,CAAC;gBAGjCO,UAAU,CAAC,YAAM;kBACf,IAAIf,QAAQ,CAACgB,IAAI,IAAIhB,QAAQ,CAACiB,IAAI,EAAE;oBAClCC,uBAAuB,CAAC/B,MAAM,EAAEa,QAAQ,CAAC;kBAC3C,CAAC,MAAM;oBACLlE,KAAK,CAACmE,KAAK,CACT,gBAAgB,EAChB,yFACF,CAAC;kBACH;gBACF,CAAC,EAAE,IAAI,CAAC;cACV,CAAC;cAAA,SAdDW,OAAOA,CAAA;gBAAA,OAAAC,QAAA,CAAA9B,KAAA,OAAAC,SAAA;cAAA;cAAA,OAAP4B,OAAO;YAAA;UAeT,CAAC,CAEL,CAAC;QACH,CAAC,MAAM,IAAIZ,QAAQ,CAACgB,IAAI,IAAIhB,QAAQ,CAACiB,IAAI,EAAE;UAEzC,IAAME,QAAQ,GAAG,iCAAiCnB,QAAQ,CAACgB,IAAI,IAAIhB,QAAQ,CAACiB,IAAI,OAAO;UACvF,MAAM/E,OAAO,CAAC4E,OAAO,CAACK,QAAQ,CAAC;UAC/BrF,KAAK,CAACmE,KAAK,CACT,yBAAyB,EACzB,4FACF,CAAC;QACH,CAAC,MAAM;UACLnE,KAAK,CAACmE,KAAK,CACT,6BAA6B,EAC7B,qCAAqCd,MAAM,CAACC,GAAG,IAAID,MAAM,CAACI,MAAM,2CAClE,CAAC;QACH;MACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdmB,OAAO,CAACnB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD7B,KAAK,CAACmE,KAAK,CAAC,QAAQ,EAAE,kCAAkC,CAAC;MAC3D;IACF,CAAC;IAAA,gBAjDKC,oBAAoBA,CAAAkB,GAAA,EAAAC,GAAA;MAAA,OAAAhB,KAAA,CAAAtB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAiDzB;EAED,IAAMkC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAI/B,MAAM,EAAEa,QAAQ,EAAK;IACpDlE,KAAK,CAACmE,KAAK,CACT,oBAAoB,EACpB,iEAAiEd,MAAM,CAACC,GAAG,IAAID,MAAM,CAACI,MAAM,IAAI,EAChG,CACE;MAAEmB,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS,CAAC,EAChC;MACED,IAAI,EAAE,wBAAwB;MAC9BE,OAAO;QAAA,IAAAU,SAAA,GAAA/C,iBAAA,CAAE,aAAY;UACnB,IAAM4C,QAAQ,GAAG,iCAAiCnB,QAAQ,CAACgB,IAAI,IAAIhB,QAAQ,CAACiB,IAAI,OAAO;UACvF,MAAM/E,OAAO,CAAC4E,OAAO,CAACK,QAAQ,CAAC;UAC/BrF,KAAK,CAACmE,KAAK,CACT,yBAAyB,EACzB,uDACF,CAAC;QACH,CAAC;QAAA,SAPDW,OAAOA,CAAA;UAAA,OAAAU,SAAA,CAAAvC,KAAA,OAAAC,SAAA;QAAA;QAAA,OAAP4B,OAAO;MAAA;IAQT,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAMW,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,KAAA;IAAA,IAAMC,IAAI,GAAAD,KAAA,CAAJC,IAAI;IAAA,OAC9BrF,KAAA,CAACX,IAAI;MAACkF,KAAK,EAAEe,MAAM,CAACC,UAAW;MAAAC,QAAA,GAC7BxF,KAAA,CAACX,IAAI;QAACkF,KAAK,EAAEe,MAAM,CAACG,UAAW;QAAAD,QAAA,GAC7BxF,KAAA,CAACV,IAAI;UAACiF,KAAK,EAAEe,MAAM,CAACI,UAAW;UAAAF,QAAA,GAAEH,IAAI,CAACrC,GAAG,EAAC,GAAC,EAACqC,IAAI,CAAClC,MAAM;QAAA,CAAO,CAAC,EAC/DnD,KAAA,CAACV,IAAI;UAACiF,KAAK,EAAEe,MAAM,CAACK,YAAa;UAAAH,QAAA,GAAC,eAAG,EAACH,IAAI,CAACO,OAAO;QAAA,CAAO,CAAC,EAC1D5F,KAAA,CAACV,IAAI;UAACiF,KAAK,EAAEe,MAAM,CAACK,YAAa;UAAAH,QAAA,GAAC,qBAAI,EAACH,IAAI,CAACjC,KAAK;QAAA,CAAO,CAAC,EACzDpD,KAAA,CAACV,IAAI;UAACiF,KAAK,EAAEe,MAAM,CAACK,YAAa;UAAAH,QAAA,GAAC,eAAG,EAACH,IAAI,CAACQ,GAAG;QAAA,CAAO,CAAC,EACtD7F,KAAA,CAACV,IAAI;UAACiF,KAAK,EAAEe,MAAM,CAACK,YAAa;UAAAH,QAAA,GAAC,eAAG,EAACH,IAAI,CAACS,KAAK;QAAA,CAAO,CAAC,EACvDT,IAAI,CAACU,MAAM,IACV7F,IAAA,CAACb,IAAI;UAACkF,KAAK,EAAEe,MAAM,CAACU,eAAgB;UAAAR,QAAA,EAClCtF,IAAA,CAACZ,IAAI;YAACiF,KAAK,EAAE,CACXe,MAAM,CAACW,UAAU,EACjBZ,IAAI,CAACU,MAAM,KAAK,OAAO,GAAGT,MAAM,CAACY,YAAY,GAAGZ,MAAM,CAACa,cAAc,CACrE;YAAAX,QAAA,EACCH,IAAI,CAACU;UAAM,CACR;QAAC,CACH,CACP;MAAA,CACG,CAAC,EAEP/F,KAAA,CAACX,IAAI;QAACkF,KAAK,EAAEe,MAAM,CAACc,eAAgB;QAAAZ,QAAA,GAClCxF,KAAA,CAACR,gBAAgB;UACf+E,KAAK,EAAEe,MAAM,CAACe,YAAa;UAC3B7B,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQnB,uBAAuB,CAACgC,IAAI,CAAC;UAAA,CAAC;UAAAG,QAAA,GAE7CtF,IAAA,CAACZ,IAAI;YAACiF,KAAK,EAAEe,MAAM,CAACgB,UAAW;YAAAd,QAAA,EAAC;UAAE,CAAM,CAAC,EACzCtF,IAAA,CAACZ,IAAI;YAACiF,KAAK,EAAEe,MAAM,CAACiB,UAAW;YAAAf,QAAA,EAAC;UAAY,CAAM,CAAC;QAAA,CACnC,CAAC,EAEnBxF,KAAA,CAACR,gBAAgB;UACf+E,KAAK,EAAE,CAACe,MAAM,CAACe,YAAY,EAAEf,MAAM,CAACkB,cAAc,CAAE;UACpDhC,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQhB,mBAAmB,CAAC6B,IAAI,CAAC;UAAA,CAAC;UAAAG,QAAA,GAEzCtF,IAAA,CAACZ,IAAI;YAACiF,KAAK,EAAEe,MAAM,CAACgB,UAAW;YAAAd,QAAA,EAAC;UAAE,CAAM,CAAC,EACzCtF,IAAA,CAACZ,IAAI;YAACiF,KAAK,EAAEe,MAAM,CAACiB,UAAW;YAAAf,QAAA,EAAC;UAAY,CAAM,CAAC;QAAA,CACnC,CAAC;MAAA,CACf,CAAC;IAAA,CACH,CAAC;EAAA,CACR;EAED,IAAIrE,OAAO,EAAE;IACX,OACEnB,KAAA,CAACX,IAAI;MAACkF,KAAK,EAAEe,MAAM,CAACmB,gBAAiB;MAAAjB,QAAA,GACnCtF,IAAA,CAACP,iBAAiB;QAAC+G,IAAI,EAAC,OAAO;QAACC,KAAK,EAAC;MAAS,CAAE,CAAC,EAClDzG,IAAA,CAACZ,IAAI;QAACiF,KAAK,EAAEe,MAAM,CAACsB,WAAY;QAAApB,QAAA,EAAC;MAAyB,CAAM,CAAC;IAAA,CAC7D,CAAC;EAEX;EAEA,OACExF,KAAA,CAACH,YAAY;IAAC0E,KAAK,EAAEe,MAAM,CAACuB,SAAU;IAAArB,QAAA,GACpCxF,KAAA,CAACX,IAAI;MAACkF,KAAK,EAAEe,MAAM,CAACwB,MAAO;MAAAtB,QAAA,GACzBtF,IAAA,CAACZ,IAAI;QAACiF,KAAK,EAAEe,MAAM,CAACyB,KAAM;QAAAvB,QAAA,EAAC;MAAc,CAAM,CAAC,EAChDxF,KAAA,CAACV,IAAI;QAACiF,KAAK,EAAEe,MAAM,CAAC0B,QAAS;QAAAxB,QAAA,GAAC,UACpB,EAAC1D,IAAI,oBAAJA,IAAI,CAAEkB,GAAG,EAAC,GAAC,EAAClB,IAAI,oBAAJA,IAAI,CAAEqB,MAAM;MAAA,CAC7B,CAAC;IAAA,CACH,CAAC,EAEPjD,IAAA,CAACb,IAAI;MAACkF,KAAK,EAAEe,MAAM,CAAC2B,eAAgB;MAAAzB,QAAA,EAClCtF,IAAA,CAACN,SAAS;QACR2E,KAAK,EAAEe,MAAM,CAAC4B,WAAY;QAC1BC,WAAW,EAAC,sCAA4B;QACxCC,KAAK,EAAEzF,UAAW;QAClB0F,YAAY,EAAEzF;MAAc,CAC7B;IAAC,CACE,CAAC,EAENL,KAAK,GACJvB,KAAA,CAACX,IAAI;MAACkF,KAAK,EAAEe,MAAM,CAACgC,cAAe;MAAA9B,QAAA,GACjCxF,KAAA,CAACV,IAAI;QAACiF,KAAK,EAAEe,MAAM,CAACiC,SAAU;QAAA/B,QAAA,GAAC,SAAE,EAACjE,KAAK;MAAA,CAAO,CAAC,EAC/CrB,IAAA,CAACV,gBAAgB;QAAC+E,KAAK,EAAEe,MAAM,CAACkC,WAAY;QAAChD,OAAO,EAAExC,YAAa;QAAAwD,QAAA,EACjEtF,IAAA,CAACZ,IAAI;UAACiF,KAAK,EAAEe,MAAM,CAACmC,SAAU;UAAAjC,QAAA,EAAC;QAAS,CAAM;MAAC,CAC/B,CAAC;IAAA,CACf,CAAC,GAEPtF,IAAA,CAACX,QAAQ;MACP+C,IAAI,EAAEvB,eAAgB;MACtB2G,UAAU,EAAEvC,gBAAiB;MAC7BwC,YAAY,EAAE,SAAdA,YAAYA,CAAGtC,IAAI;QAAA,OAAKA,IAAI,CAAC3B,QAAQ,CAACkE,QAAQ,CAAC,CAAC;MAAA,CAAC;MACjDC,qBAAqB,EAAEvC,MAAM,CAACwC,aAAc;MAC5CC,4BAA4B,EAAE,KAAM;MACpCC,UAAU,EAAE7G,OAAQ;MACpB8G,SAAS,EAAEjG;IAAa,CACzB,CACF;EAAA,CACW,CAAC;AAEnB,CAAC;AAED,IAAMsD,MAAM,GAAG7F,UAAU,CAACyI,MAAM,CAAC;EAC/BrB,SAAS,EAAE;IACTsB,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACD3B,gBAAgB,EAAE;IAChB0B,IAAI,EAAE,CAAC;IACPE,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACD1B,WAAW,EAAE;IACX2B,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZ7B,KAAK,EAAE;EACT,CAAC;EACDG,MAAM,EAAE;IACNsB,eAAe,EAAE,SAAS;IAC1BK,OAAO,EAAE,EAAE;IACXH,UAAU,EAAE;EACd,CAAC;EACDvB,KAAK,EAAE;IACLyB,QAAQ,EAAE,EAAE;IACZE,UAAU,EAAE,MAAM;IAClB/B,KAAK,EAAE;EACT,CAAC;EACDK,QAAQ,EAAE;IACRwB,QAAQ,EAAE,EAAE;IACZ7B,KAAK,EAAE,MAAM;IACbgC,OAAO,EAAE;EACX,CAAC;EACD1B,eAAe,EAAE;IACfwB,OAAO,EAAE;EACX,CAAC;EACDvB,WAAW,EAAE;IACXkB,eAAe,EAAE,MAAM;IACvBQ,YAAY,EAAE,EAAE;IAChBH,OAAO,EAAE,EAAE;IACXD,QAAQ,EAAE,EAAE;IACZK,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDhB,aAAa,EAAE;IACbW,OAAO,EAAE;EACX,CAAC;EACDlD,UAAU,EAAE;IACV6C,eAAe,EAAE,MAAM;IACvBQ,YAAY,EAAE,EAAE;IAChBH,OAAO,EAAE,EAAE;IACXM,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrCC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE;EAChB,CAAC;EACD7D,UAAU,EAAE;IACVsD,YAAY,EAAE;EAChB,CAAC;EACDrD,UAAU,EAAE;IACV8C,QAAQ,EAAE,EAAE;IACZE,UAAU,EAAE,MAAM;IAClB/B,KAAK,EAAE,MAAM;IACboC,YAAY,EAAE;EAChB,CAAC;EACDpD,YAAY,EAAE;IACZ6C,QAAQ,EAAE,EAAE;IACZ7B,KAAK,EAAE,MAAM;IACboC,YAAY,EAAE;EAChB,CAAC;EACD/C,eAAe,EAAE;IACfuC,SAAS,EAAE;EACb,CAAC;EACDtC,UAAU,EAAE;IACVuC,QAAQ,EAAE,EAAE;IACZE,UAAU,EAAE,MAAM;IAClBa,iBAAiB,EAAE,CAAC;IACpBC,eAAe,EAAE,CAAC;IAClBZ,YAAY,EAAE,EAAE;IAChBa,SAAS,EAAE;EACb,CAAC;EACDvD,YAAY,EAAE;IACZkC,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE;EACT,CAAC;EACDR,cAAc,EAAE;IACdiC,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE;EACT,CAAC;EACDP,eAAe,EAAE;IACfsD,aAAa,EAAE,KAAK;IACpBrB,cAAc,EAAE;EAClB,CAAC;EACDhC,YAAY,EAAE;IACZ8B,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,SAAS;IAC1BQ,YAAY,EAAE,CAAC;IACfH,OAAO,EAAE,EAAE;IACXH,UAAU,EAAE,QAAQ;IACpBqB,gBAAgB,EAAE;EACpB,CAAC;EACDnD,cAAc,EAAE;IACd4B,eAAe,EAAE;EACnB,CAAC;EACD9B,UAAU,EAAE;IACVkC,QAAQ,EAAE,EAAE;IACZO,YAAY,EAAE;EAChB,CAAC;EACDxC,UAAU,EAAE;IACVI,KAAK,EAAE,MAAM;IACb6B,QAAQ,EAAE,EAAE;IACZE,UAAU,EAAE;EACd,CAAC;EACDpB,cAAc,EAAE;IACda,IAAI,EAAE,CAAC;IACPE,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBG,OAAO,EAAE;EACX,CAAC;EACDlB,SAAS,EAAE;IACTiB,QAAQ,EAAE,EAAE;IACZ7B,KAAK,EAAE,SAAS;IAChBiD,SAAS,EAAE,QAAQ;IACnBb,YAAY,EAAE;EAChB,CAAC;EACDvB,WAAW,EAAE;IACXY,eAAe,EAAE,SAAS;IAC1BQ,YAAY,EAAE,CAAC;IACfH,OAAO,EAAE,EAAE;IACXc,iBAAiB,EAAE;EACrB,CAAC;EACD9B,SAAS,EAAE;IACTd,KAAK,EAAE,MAAM;IACb+B,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAevI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}